from pydantic import BaseModel, <PERSON>
from typing import Optional
from datetime import datetime
from bson import ObjectId

from app.models.user import PyObjectId, UserRole


# Request Models
class MembershipCreate(BaseModel):
    user_id: str
    tenant_id: str
    role: UserRole
    invited_by: Optional[str] = None


class MembershipUpdate(BaseModel):
    role: Optional[UserRole] = None


# Response Models
class MembershipResponse(BaseModel):
    id: str = Field(alias="_id")
    user_id: str
    tenant_id: str
    role: UserRole
    joined_at: datetime
    invited_by: Optional[str] = None
    invited_at: Optional[datetime] = None

    model_config = {
        "populate_by_name": True,
        "json_encoders": {ObjectId: str}
    }


class MembershipWithDetails(MembershipResponse):
    user_name: str
    user_email: str
    tenant_name: str


# Database Models
class MembershipDB(BaseModel):
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    user_id: PyObjectId = Field(alias="userId")
    tenant_id: PyObjectId = Field(alias="tenantId")
    role: UserRole
    joined_at: datetime = Field(default_factory=datetime.utcnow)

    # Invitation tracking
    invited_by: Optional[PyObjectId] = None
    invited_at: Optional[datetime] = None
    invitation_token: Optional[str] = None
    invitation_expires_at: Optional[datetime] = None
    invitation_accepted_at: Optional[datetime] = None

    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str}
    }
