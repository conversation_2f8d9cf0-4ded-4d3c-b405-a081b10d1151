from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
from bson import ObjectId
from enum import Enum

from app.models.user import PyObjectId


class TenantPlan(str, Enum):
    FREE = "free"
    STARTER = "starter"
    PRO = "pro"
    ENTERPRISE = "enterprise"


class TenantModules(BaseModel):
    crm: bool = True
    flows: bool = True
    financial_modeling: bool = True
    analytics: bool = False
    reporting: bool = False


# Request Models
class TenantCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    plan: TenantPlan = TenantPlan.STARTER
    modules: TenantModules = TenantModules()


class TenantUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    plan: Optional[TenantPlan] = None
    modules: Optional[TenantModules] = None


# Response Models
class TenantResponse(BaseModel):
    id: str = Field(alias="_id")
    name: str
    plan: TenantPlan
    modules: TenantModules
    created_by: str
    created_at: datetime
    updated_at: datetime

    model_config = {
        "populate_by_name": True,
        "json_encoders": {ObjectId: str}
    }


class TenantWithStats(TenantResponse):
    member_count: int = 0
    storage_used: int = 0  # in bytes
    api_calls_this_month: int = 0


# Database Models
class TenantDB(BaseModel):
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    name: str
    plan: TenantPlan = TenantPlan.STARTER
    modules: TenantModules = TenantModules()
    created_by: PyObjectId
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Optional tenant settings
    settings: Dict[str, Any] = Field(default_factory=dict)
    billing_email: Optional[str] = None

    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str}
    }
