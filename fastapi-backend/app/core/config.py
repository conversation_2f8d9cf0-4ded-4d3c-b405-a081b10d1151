from pydantic_settings import BaseSettings
from pydantic import Field
from typing import List


class Settings(BaseSettings):
    # Application
    app_name: str = Field(default="Aizako API", alias="APP_NAME")
    version: str = Field(default="1.0.0", alias="VERSION")
    debug: bool = Field(default=False, alias="DEBUG")

    # Database
    mongodb_url: str = Field(alias="MONGODB_URI")
    mongodb_db_name: str = Field(default="aizako-suite-dev", alias="MONGODB_DB_NAME")

    # Security
    secret_key: str = Field(alias="SECRET_KEY")
    algorithm: str = Field(default="HS256", alias="ALG<PERSON><PERSON>HM")
    access_token_expire_minutes: int = Field(
        default=30, alias="ACCESS_TOKEN_EXPIRE_MINUTES")

    # CORS
    allowed_origins: List[str] = Field(
        default=[
            "http://localhost:3000",
            "https://localhost:3000",
            "https://*.vercel.app"
        ],
        alias="ALLOWED_ORIGINS"
    )

    # Rate limiting
    rate_limit_per_minute: int = Field(default=60, alias="RATE_LIMIT_PER_MINUTE")

    model_config = {
        "env_file": ".env",
        "case_sensitive": False,
        "extra": "forbid"
    }


# Create settings instance
settings = Settings()
