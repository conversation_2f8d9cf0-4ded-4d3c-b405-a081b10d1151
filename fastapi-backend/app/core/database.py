import logging
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from app.core.config import settings
from app.core.mongodb_fix import get_mongodb_client

logger = logging.getLogger(__name__)


class Database:
    client: AsyncIOMotorClient = None
    database: AsyncIOMotorDatabase = None


db = Database()


async def get_database() -> AsyncIOMotorDatabase:
    """Get database instance for dependency injection."""
    return db.database


async def connect_to_mongo():
    """Create database connection."""
    logger.info("Connecting to MongoDB...")

    try:
        # Use the MongoDB fix module to handle SSL issues
        db.client = get_mongodb_client(settings.mongodb_url)

        db.database = db.client[settings.mongodb_db_name]

        # Test the connection
        await db.client.admin.command('ping')
        logger.info(
            f"Successfully connected to MongoDB database: {
                settings.mongodb_db_name}")

    except Exception as e:
        logger.error(f"Failed to connect to MongoDB: {e}")
        raise


async def close_mongo_connection():
    """Close database connection."""
    logger.info("Closing MongoDB connection...")

    if db.client:
        db.client.close()
        logger.info("MongoDB connection closed")


# Collection getters for clean access
def get_users_collection():
    return db.database.users


def get_tenants_collection():
    return db.database.tenants


def get_memberships_collection():
    return db.database.memberships


def get_crm_companies_collection():
    return db.database.crm_companies


def get_crm_contacts_collection():
    return db.database.crm_contacts


def get_crm_opportunities_collection():
    return db.database.crm_opportunities


def get_crm_activities_collection():
    return db.database.crm_activities
