"""MongoDB Atlas connection fixes for deployment environments."""
import ssl
import certifi
from motor.motor_asyncio import AsyncIOMotorClient


def get_mongodb_client(mongodb_url: str, **kwargs):
    """
    Create a MongoDB client with proper SSL configuration.

    Uses certifi for up-to-date CA certificates including ISRG Root X1 (Let's Encrypt)
    and GTS Root R1-R4 (Google Trust Services) used by MongoDB Atlas.
    """
    import os
    import logging

    logger = logging.getLogger(__name__)

    # Base configuration with proper TLS settings
    client_kwargs = {
        "maxPoolSize": kwargs.get("maxPoolSize", 10),
        "minPoolSize": kwargs.get("minPoolSize", 10),
        "retryWrites": kwargs.get("retryWrites", True),
        "w": kwargs.get("w", "majority"),
        "serverSelectionTimeoutMS": kwargs.get("serverSelectionTimeoutMS", 5000),
        "connectTimeoutMS": kwargs.get("connectTimeoutMS", 10000),
        "socketTimeoutMS": kwargs.get("socketTimeoutMS", 20000),
        # Always use TLS for MongoDB Atlas
        "tls": True,
        # Use certifi's CA bundle which includes ISRG Root X1 and GTS roots
        "tlsCAFile": certifi.where(),
    }

    if os.getenv("FLY_APP_NAME"):
        logger.info("Running on Fly.io - using certifi CA bundle for MongoDB Atlas")

    return AsyncIOMotorClient(mongodb_url, **client_kwargs)


def create_ssl_context():
    """
    Create a custom SSL context for MongoDB connections.
    This can be used if the default SSL configuration fails.
    """
    context = ssl.create_default_context()
    context.check_hostname = False
    context.verify_mode = ssl.CERT_NONE
    return context
