from datetime import datetime
from typing import Optional, <PERSON><PERSON>
from bson import ObjectId

from app.core.database import (
    get_users_collection, get_tenants_collection, get_memberships_collection
)
from app.core.security import (
    verify_password, get_password_hash, create_access_token, create_session_data
)
from app.models.user import UserD<PERSON>, UserSignUp, UserRole, UserStatus
from app.models.tenant import TenantDB, TenantPlan
from app.models.membership import MembershipDB


class AuthService:
    """Service for authentication operations."""

    @staticmethod
    async def authenticate_user(
            email: str, password: str) -> Optional[Tuple[UserDB, str]]:
        """Authenticate user and return user data with tenant_id."""
        users_collection = get_users_collection()

        # Find user by email
        user_doc = await users_collection.find_one({"email": email})
        if not user_doc:
            return None

        # Verify password
        if not verify_password(password, user_doc["password_hash"]):
            return None

        user = UserDB(**user_doc)

        # Get user's default tenant or first membership
        tenant_id = None
        if user.default_tenant_id:
            tenant_id = str(user.default_tenant_id)
        else:
            # Find first membership
            memberships_collection = get_memberships_collection()
            membership = await memberships_collection.find_one({"user_id": user.id})
            if membership:
                tenant_id = str(membership["tenant_id"])

        # Update last login
        await users_collection.update_one(
            {"_id": user.id},
            {"$set": {"last_login_at": datetime.utcnow()}}
        )

        return user, tenant_id

    @staticmethod
    async def create_user_and_tenant(user_data: UserSignUp) -> Tuple[UserDB, TenantDB]:
        """Create new user with their own tenant."""
        users_collection = get_users_collection()
        tenants_collection = get_tenants_collection()
        memberships_collection = get_memberships_collection()

        # Check if user already exists
        existing_user = await users_collection.find_one({"email": user_data.email})
        if existing_user:
            raise ValueError("User with this email already exists")

        # Create user
        user_db = UserDB(
            email=user_data.email,
            password_hash=get_password_hash(user_data.password),
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            status=UserStatus.ACTIVE
        )

        # Create tenant
        tenant_db = TenantDB(
            name=user_data.tenant_name,
            plan=TenantPlan.STARTER,
            created_by=user_db.id
        )

        # Set user's default tenant
        user_db.default_tenant_id = tenant_db.id

        # Save user and tenant (convert _id strings back to ObjectIds)
        user_data = user_db.model_dump(by_alias=True)
        tenant_data = tenant_db.model_dump(by_alias=True)

        # Convert string _id back to ObjectId for MongoDB storage
        user_data['_id'] = ObjectId(user_data['_id'])
        tenant_data['_id'] = ObjectId(tenant_data['_id'])

        await users_collection.insert_one(user_data)
        await tenants_collection.insert_one(tenant_data)

        # Create membership (owner role)
        membership_db = MembershipDB(
            user_id=user_db.id,
            tenant_id=tenant_db.id,
            role=UserRole.OWNER
        )

        membership_data = membership_db.model_dump(by_alias=True)
        # Convert ObjectId strings back to actual ObjectIds for MongoDB
        membership_data['_id'] = ObjectId(membership_data['_id'])
        membership_data['userId'] = ObjectId(membership_data['userId'])
        membership_data['tenantId'] = ObjectId(membership_data['tenantId'])

        await memberships_collection.insert_one(membership_data)

        return user_db, tenant_db

    @staticmethod
    def create_access_token_for_user(user: UserDB, tenant_id: str) -> str:
        """Create JWT access token for user."""
        session_data = create_session_data(
            user_id=str(user.id),
            tenant_id=tenant_id,
            email=user.email
        )
        return create_access_token(session_data)

    @staticmethod
    async def get_user_by_id(user_id: str) -> Optional[UserDB]:
        """Get user by ID."""
        users_collection = get_users_collection()
        user_doc = await users_collection.find_one({"_id": ObjectId(user_id)})

        if not user_doc:
            return None

        return UserDB(**user_doc)

    @staticmethod
    async def get_user_memberships(user_id: str):
        """Get all memberships for a user with tenant details."""
        memberships_collection = get_memberships_collection()

        pipeline = [
            {"$match": {"user_id": ObjectId(user_id)}},
            {
                "$lookup": {
                    "from": "tenants",
                    "localField": "tenant_id",
                    "foreignField": "_id",
                    "as": "tenant"
                }
            },
            {"$unwind": "$tenant"},
            {
                "$project": {
                    "_id": 1,
                    "tenant_id": 1,
                    "role": 1,
                    "joined_at": 1,
                    "tenant_name": "$tenant.name",
                    "tenant_plan": "$tenant.plan",
                    "tenant_modules": "$tenant.modules"
                }
            }
        ]

        cursor = memberships_collection.aggregate(pipeline)
        return await cursor.to_list(length=None)
