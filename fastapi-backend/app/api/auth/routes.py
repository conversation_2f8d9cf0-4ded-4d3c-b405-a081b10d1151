from fastapi import APIRouter, HTTPException, status, Depends
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.database import get_database
from app.models.user import (
    UserSignIn, UserSignUp, TokenResponse, UserResponse,
    AcceptInviteRequest, UserWithTenants
)
from app.models.tenant import TenantResponse
from app.services.auth_service import AuthService
from app.api.dependencies import get_current_active_user

router = APIRouter()


@router.post("/signin", response_model=TokenResponse)
async def sign_in(
    user_data: UserSignIn,
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Sign in user and return JWT token."""
    try:
        # Authenticate user
        auth_result = await AuthService.authenticate_user(
            user_data.email, user_data.password
        )
        if not auth_result:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )

        user, tenant_id = auth_result

        # Create access token
        access_token = AuthService.create_access_token_for_user(user, tenant_id)

        # Prepare response
        user_response = UserResponse(
            id=str(
                user.id),
            email=user.email,
            first_name=user.first_name,
            last_name=user.last_name,
            status=user.status,
            default_tenant_id=str(
                user.default_tenant_id) if user.default_tenant_id else None,
            created_at=user.created_at,
            last_login_at=user.last_login_at)

        # Get tenant info if available
        current_tenant = None
        if tenant_id:
            from app.core.database import get_tenants_collection
            from bson import ObjectId

            tenants_collection = get_tenants_collection()
            tenant_doc = await tenants_collection.find_one({"_id": ObjectId(tenant_id)})
            if tenant_doc:
                current_tenant = TenantResponse(
                    id=str(tenant_doc["_id"]),
                    name=tenant_doc["name"],
                    plan=tenant_doc["plan"],
                    modules=tenant_doc["modules"],
                    created_by=str(tenant_doc["created_by"]),
                    created_at=tenant_doc["created_at"],
                    updated_at=tenant_doc["updated_at"]
                )

        return TokenResponse(
            access_token=access_token,
            user=user_response,
            current_tenant=current_tenant
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Sign in failed"
        )


@router.post("/signup", response_model=TokenResponse)
async def sign_up(
    user_data: UserSignUp,
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Sign up new user with their own tenant."""
    try:
        # Create user and tenant
        user, tenant = await AuthService.create_user_and_tenant(user_data)

        # Create access token
        access_token = AuthService.create_access_token_for_user(user, str(tenant.id))

        # Prepare response
        user_response = UserResponse(
            id=str(
                user.id),
            email=user.email,
            first_name=user.first_name,
            last_name=user.last_name,
            status=user.status,
            default_tenant_id=str(
                user.default_tenant_id) if user.default_tenant_id else None,
            created_at=user.created_at,
            last_login_at=user.last_login_at)

        tenant_response = TenantResponse(
            id=str(tenant.id),
            name=tenant.name,
            plan=tenant.plan,
            modules=tenant.modules,
            created_by=str(tenant.created_by),
            created_at=tenant.created_at,
            updated_at=tenant.updated_at
        )

        return TokenResponse(
            access_token=access_token,
            user=user_response,
            current_tenant=tenant_response
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Account creation failed"
        )


@router.get("/me", response_model=UserWithTenants)
async def get_current_user(
    current_user: UserResponse = Depends(get_current_active_user)
):
    """Get current user with their tenant memberships."""
    try:
        # Get user's memberships
        memberships = await AuthService.get_user_memberships(current_user.id)

        # Format memberships for response
        from app.models.membership import MembershipResponse
        membership_responses = []
        for membership in memberships:
            membership_responses.append(MembershipResponse(
                id=str(membership["_id"]),
                user_id=str(membership["user_id"]),
                tenant_id=str(membership["tenant_id"]),
                role=membership["role"],
                joined_at=membership["joined_at"]
            ))

        return UserWithTenants(
            **current_user.dict(),
            memberships=membership_responses
        )

    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user profile"
        )


@router.post("/signout")
async def sign_out():
    """Sign out user (client should discard token)."""
    return {"message": "Successfully signed out"}


@router.post("/accept-invite/{token}", response_model=TokenResponse)
async def accept_invite(
    token: str,
    invite_data: AcceptInviteRequest,
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Accept invitation and complete user registration."""
    # This would implement invitation acceptance logic
    # For now, return a placeholder response
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Invitation system not yet implemented"
    )


@router.post("/forgot-password")
async def forgot_password(email: str):
    """Request password reset."""
    # This would implement password reset logic
    # For now, return a placeholder response
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Password reset not yet implemented"
    )
