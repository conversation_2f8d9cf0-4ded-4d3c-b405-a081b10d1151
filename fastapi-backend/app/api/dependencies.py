from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from motor.motor_asyncio import AsyncIOMotorDatabase
from bson import ObjectId

from app.core.database import (
    get_database, get_users_collection, get_memberships_collection
)
from app.core.security import verify_token
from app.models.user import UserResponse, UserRole

security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncIOMotorDatabase = Depends(get_database)
) -> UserResponse:
    """Get current authenticated user."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        # Verify token
        payload = verify_token(credentials.credentials)
        if payload is None:
            raise credentials_exception

        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception

        # Get user from database
        users_collection = get_users_collection()
        user_doc = await users_collection.find_one({"_id": ObjectId(user_id)})

        if user_doc is None:
            raise credentials_exception

        # Convert to UserResponse (ensure ObjectId fields are strings)
        user_doc["id"] = str(user_doc["_id"])
        if user_doc.get("default_tenant_id"):
            user_doc["default_tenant_id"] = str(user_doc["default_tenant_id"])
        # Remove the _id field since we now have the string id
        del user_doc["_id"]
        return UserResponse(**user_doc)

    except Exception:
        raise credentials_exception


async def get_current_active_user(
    current_user: UserResponse = Depends(get_current_user)
) -> UserResponse:
    """Get current active user (not suspended)."""
    if current_user.status != "active":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


async def get_current_tenant_id(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> str:
    """Get current tenant ID from JWT token."""
    payload = verify_token(credentials.credentials)
    if payload is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials"
        )

    tenant_id = payload.get("tenant_id")
    if tenant_id is None:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No tenant context"
        )

    return tenant_id


class RequireRole:
    """Dependency factory for role-based access control."""

    def __init__(self, required_role: UserRole):
        self.required_role = required_role

    async def __call__(
        self,
        current_user: UserResponse = Depends(get_current_active_user),
        tenant_id: str = Depends(get_current_tenant_id),
        db: AsyncIOMotorDatabase = Depends(get_database)
    ):
        # Check user's role in current tenant
        memberships_collection = get_memberships_collection()
        membership = await memberships_collection.find_one({
            "user_id": ObjectId(current_user.id),
            "tenant_id": ObjectId(tenant_id)
        })

        if not membership:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="No access to this tenant"
            )

        user_role = membership["role"]

        # Role hierarchy: owner > admin > member > viewer
        role_hierarchy = {
            UserRole.VIEWER: 0,
            UserRole.MEMBER: 1,
            UserRole.ADMIN: 2,
            UserRole.OWNER: 3
        }

        user_level = role_hierarchy.get(user_role, 0)
        required_level = role_hierarchy.get(self.required_role, 0)
        if user_level < required_level:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required: {self.required_role.value}"
            )

        return current_user


# Pre-configured role dependencies
require_owner = RequireRole(UserRole.OWNER)
require_admin = RequireRole(UserRole.ADMIN)
require_member = RequireRole(UserRole.MEMBER)
require_viewer = RequireRole(UserRole.VIEWER)
