import logging
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from app.core.config import settings
from app.core.database import connect_to_mongo, close_mongo_connection
from app.api.auth.routes import router as auth_router

# Configure logging
logging.basicConfig(
    level=logging.INFO if settings.debug else logging.WARNING,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Handle startup and shutdown events."""
    # Startup
    logger.info("Starting up Aizako API...")
    await connect_to_mongo()
    logger.info("Aizako API started successfully")

    yield

    # Shutdown
    logger.info("Shutting down Aizako API...")
    await close_mongo_connection()
    logger.info("Aizako API shutdown complete")


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    description="Multi-tenant CRM and Financial Modeling API",
    version=settings.version,
    lifespan=lifespan,
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Include routers
app.include_router(
    auth_router,
    prefix="/api/auth",
    tags=["Authentication"]
)

# Health check endpoint


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": settings.app_name,
        "version": settings.version
    }

# Root endpoint


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "Welcome to Aizako API",
        "version": settings.version,
        "docs": "/docs" if settings.debug else "Documentation disabled in production"
    }
