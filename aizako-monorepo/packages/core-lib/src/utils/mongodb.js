import mongoose from 'mongoose';
// Connection options
const options = {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    autoIndex: true, // Build indexes
    maxPoolSize: 10, // Maintain up to 10 socket connections
    serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
    socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
    family: 4 // Use IPv4, skip trying IPv6
};
// Global is used here to maintain a cached connection across hot reloads
// in development. This prevents connections growing exponentially
// during API Route usage.
const cached = { conn: null, promise: null };
/**
 * Connect to MongoDB
 * @param uri MongoDB connection URI
 * @returns Promise that resolves when connection is established
 */
export async function connectToMongoDB(uri) {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = options;
        mongoose.set('strictQuery', false);
        cached.promise = mongoose.connect(uri, opts).then((mongoose) => {
            console.log('MongoDB connected successfully');
            return mongoose;
        });
    }
    try {
        cached.conn = await cached.promise;
    }
    catch (e) {
        cached.promise = null;
        throw e;
    }
    return cached.conn;
}
/**
 * Disconnect from MongoDB
 * @returns Promise that resolves when disconnection is complete
 */
export async function disconnectFromMongoDB() {
    if (cached.conn) {
        await mongoose.disconnect();
        cached.conn = null;
        cached.promise = null;
        console.log('MongoDB disconnected');
    }
}
/**
 * Test MongoDB connection
 * @param uri MongoDB connection URI
 * @returns Promise that resolves to true if connection is successful, false otherwise
 */
export async function testMongoDBConnection(uri) {
    try {
        await connectToMongoDB(uri);
        return true;
    }
    catch (error) {
        console.error('MongoDB connection test failed:', error);
        return false;
    }
}
/**
 * Add tenant filter to MongoDB query
 * @param query MongoDB query
 * @param tenantId Tenant ID
 * @returns MongoDB query with tenant filter
 */
export function addTenantFilter(query, tenantId) {
    return query.where('tenantId').equals(tenantId);
}
