import { z } from 'zod';
import { fromZodError } from 'zod-validation-error';
/**
 * Email validation schema
 */
export const emailSchema = z
    .string()
    .email('Invalid email format')
    .min(3, 'Email too short')
    .max(254, 'Email too long')
    .transform(email => email.toLowerCase().trim());
/**
 * Password validation schema
 */
export const passwordSchema = z
    .string()
    .min(8, 'Password must be at least 8 characters long')
    .max(128, 'Password must be no more than 128 characters long')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number')
    .regex(/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>?]/, 'Password must contain at least one special character');
/**
 * Name validation schema
 */
export const nameSchema = z
    .string()
    .min(1, 'Name is required')
    .max(100, 'Name too long')
    .regex(/^[a-zA-Z\s'-]+$/, 'Name can only contain letters, spaces, hyphens, and apostrophes')
    .transform(name => name.trim());
/**
 * Tenant name validation schema
 */
export const tenantNameSchema = z
    .string()
    .min(2, 'Tenant name must be at least 2 characters')
    .max(50, 'Tenant name must be no more than 50 characters')
    .regex(/^[a-zA-Z0-9\s\-_.]+$/, 'Tenant name can only contain letters, numbers, spaces, hyphens, underscores, and periods')
    .transform(name => name.trim());
/**
 * MongoDB ObjectId validation schema
 */
export const objectIdSchema = z
    .string()
    .regex(/^[0-9a-fA-F]{24}$/, 'Invalid ObjectId format');
/**
 * Role validation schema
 */
export const roleSchema = z.enum(['owner', 'admin', 'member', 'viewer'], {
    errorMap: () => ({ message: 'Role must be one of: owner, admin, member, viewer' }),
});
/**
 * User status validation schema
 */
export const userStatusSchema = z.enum(['active', 'invited', 'suspended'], {
    errorMap: () => ({ message: 'Status must be one of: active, invited, suspended' }),
});
/**
 * Tenant plan validation schema
 */
export const tenantPlanSchema = z.enum(['free', 'basic', 'pro', 'enterprise'], {
    errorMap: () => ({ message: 'Plan must be one of: free, basic, pro, enterprise' }),
});
/**
 * Sign up validation schema
 */
export const signUpSchema = z.object({
    email: emailSchema,
    password: passwordSchema,
    firstName: nameSchema,
    lastName: nameSchema,
    tenantName: tenantNameSchema,
});
/**
 * Sign in validation schema
 */
export const signInSchema = z.object({
    email: emailSchema,
    password: z.string().min(1, 'Password is required'),
});
/**
 * Password reset request schema
 */
export const passwordResetRequestSchema = z.object({
    email: emailSchema,
});
/**
 * Password reset confirmation schema
 */
export const passwordResetConfirmSchema = z.object({
    token: z.string().min(1, 'Reset token is required'),
    password: passwordSchema,
});
/**
 * Invitation creation schema
 */
export const invitationCreateSchema = z.object({
    email: emailSchema,
    role: roleSchema,
    tenantId: objectIdSchema,
});
/**
 * Invitation acceptance schema
 */
export const invitationAcceptSchema = z.object({
    token: z.string().min(1, 'Invitation token is required'),
    firstName: nameSchema,
    lastName: nameSchema,
    password: passwordSchema,
});
/**
 * User profile update schema
 */
export const userProfileUpdateSchema = z.object({
    firstName: nameSchema.optional(),
    lastName: nameSchema.optional(),
    email: emailSchema.optional(),
}).refine((data) => Object.keys(data).length > 0, 'At least one field must be provided for update');
/**
 * Password change schema
 */
export const passwordChangeSchema = z.object({
    currentPassword: z.string().min(1, 'Current password is required'),
    newPassword: passwordSchema,
});
/**
 * Tenant update schema
 */
export const tenantUpdateSchema = z.object({
    name: tenantNameSchema.optional(),
    plan: tenantPlanSchema.optional(),
}).refine((data) => Object.keys(data).length > 0, 'At least one field must be provided for update');
/**
 * Membership update schema
 */
export const membershipUpdateSchema = z.object({
    role: roleSchema,
    userId: objectIdSchema,
    tenantId: objectIdSchema,
});
/**
 * Generic pagination schema
 */
export const paginationSchema = z.object({
    page: z
        .string()
        .optional()
        .transform(val => val ? parseInt(val, 10) : 1)
        .pipe(z.number().min(1, 'Page must be at least 1')),
    limit: z
        .string()
        .optional()
        .transform(val => val ? parseInt(val, 10) : 10)
        .pipe(z.number().min(1, 'Limit must be at least 1').max(100, 'Limit cannot exceed 100')),
});
/**
 * Search schema
 */
export const searchSchema = z.object({
    q: z.string().min(1, 'Search query is required').max(100, 'Search query too long'),
    type: z.enum(['users', 'tenants', 'all']).optional().default('all'),
});
/**
 * Validate data against a Zod schema
 * @param schema Zod schema to validate against
 * @param data Data to validate
 * @returns Validated data
 * @throws Error if validation fails
 */
export function validateData(schema, data) {
    try {
        return schema.parse(data);
    }
    catch (error) {
        if (error instanceof z.ZodError) {
            const validationError = fromZodError(error);
            throw new Error(validationError.message);
        }
        throw error;
    }
}
/**
 * Safely validate data against a Zod schema
 * @param schema Zod schema to validate against
 * @param data Data to validate
 * @returns Object with success flag and validated data or error
 */
export function safeValidateData(schema, data) {
    const result = schema.safeParse(data);
    if (result.success) {
        return { success: true, data: result.data };
    }
    const validationError = fromZodError(result.error);
    return { success: false, error: validationError.message };
}
/**
 * Validation error formatter
 */
export function formatValidationError(error) {
    return error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
    }));
}
/**
 * Safe validation function that returns result or error
 */
export function safeValidate(schema, data) {
    const result = schema.safeParse(data);
    if (result.success) {
        return { success: true, data: result.data };
    }
    return {
        success: false,
        errors: formatValidationError(result.error),
    };
}
/**
 * Validate request body
 */
export async function validateRequestBody(request, schema) {
    try {
        const body = await request.json();
        return safeValidate(schema, body);
    }
    catch (error) {
        return {
            success: false,
            errors: [{ field: 'body', message: 'Invalid JSON in request body' }],
        };
    }
}
/**
 * Validate URL search params
 */
export function validateSearchParams(searchParams, schema) {
    const params = Object.fromEntries(searchParams.entries());
    return safeValidate(schema, params);
}
