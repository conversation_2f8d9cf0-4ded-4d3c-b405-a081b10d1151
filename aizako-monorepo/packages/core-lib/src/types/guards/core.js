/**
 * Type guard for BaseEntity
 */
export function isBaseEntity(value) {
    if (typeof value !== 'object' || value === null) {
        return false;
    }
    const entity = value;
    return (typeof entity.id === 'string' &&
        entity.createdAt instanceof Date &&
        entity.updatedAt instanceof Date);
}
/**
 * Type guard for TenantScopedEntity
 */
export function isTenantScopedEntity(value) {
    if (!isBaseEntity(value)) {
        return false;
    }
    const entity = value;
    return typeof entity.tenantId === 'string';
}
