import nodemailer from 'nodemailer';
/**
 * Email service class
 */
export class EmailService {
    constructor() {
        Object.defineProperty(this, "transporter", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: null
        });
        Object.defineProperty(this, "defaultFrom", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.defaultFrom = process.env.EMAIL_FROM || '<EMAIL>';
        this.initializeTransporter();
    }
    initializeTransporter() {
        const emailHost = process.env.EMAIL_HOST;
        const emailPort = process.env.EMAIL_PORT;
        const emailUser = process.env.EMAIL_USER;
        const emailPass = process.env.EMAIL_PASS;
        if (!emailHost || !emailPort || !emailUser || !emailPass) {
            console.warn('Email configuration missing. Email functionality will be disabled.');
            return;
        }
        try {
            this.transporter = nodemailer.createTransport({
                host: emailHost,
                port: parseInt(emailPort),
                secure: emailPort === '465', // true for 465, false for other ports
                auth: {
                    user: emailUser,
                    pass: emailPass,
                },
            });
        }
        catch (error) {
            console.error('Failed to initialize email transporter:', error);
        }
    }
    /**
     * Send an email
     */
    async sendEmail(emailData) {
        if (!this.transporter) {
            console.error('Email transporter not configured');
            return false;
        }
        try {
            const result = await this.transporter.sendMail({
                from: emailData.from || this.defaultFrom,
                to: emailData.to,
                subject: emailData.subject,
                html: emailData.html,
                text: emailData.text,
            });
            console.log('Email sent:', result.messageId);
            return true;
        }
        catch (error) {
            console.error('Failed to send email:', error);
            return false;
        }
    }
    /**
     * Send invitation email
     */
    async sendInvitation(to, inviterName, tenantName, inviteToken, role) {
        const inviteUrl = `${process.env.APP_URL || 'http://localhost:3000'}/auth/accept-invite?token=${inviteToken}`;
        const html = generateInvitationTemplate({
            inviterName,
            tenantName,
            inviteUrl,
            role,
        });
        return this.sendEmail({
            to,
            subject: `You're invited to join ${tenantName} on Aizako`,
            html,
            text: `You've been invited by ${inviterName} to join ${tenantName} as a ${role}. Click here to accept: ${inviteUrl}`,
        });
    }
    /**
     * Send password reset email
     */
    async sendPasswordReset(to, resetToken, userName) {
        const resetUrl = `${process.env.APP_URL || 'http://localhost:3000'}/auth/reset-password?token=${resetToken}`;
        const html = generatePasswordResetTemplate({
            userName: userName || 'User',
            resetUrl,
        });
        return this.sendEmail({
            to,
            subject: 'Reset your Aizako password',
            html,
            text: `Click here to reset your password: ${resetUrl}`,
        });
    }
    /**
     * Send welcome email
     */
    async sendWelcome(to, userName, tenantName) {
        const html = generateWelcomeTemplate({
            userName,
            tenantName,
            loginUrl: `${process.env.APP_URL || 'http://localhost:3000'}/auth/login`,
        });
        return this.sendEmail({
            to,
            subject: `Welcome to ${tenantName} on Aizako!`,
            html,
            text: `Welcome to ${tenantName}, ${userName}! Your account has been created successfully.`,
        });
    }
    /**
     * Verify transporter connection
     */
    async verifyConnection() {
        if (!this.transporter) {
            return false;
        }
        try {
            await this.transporter.verify();
            return true;
        }
        catch (error) {
            console.error('Email connection verification failed:', error);
            return false;
        }
    }
}
/**
 * Generate invitation email template
 */
function generateInvitationTemplate(data) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Invitation to join ${data.tenantName}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
        .content { padding: 30px; background: #f9f9f9; }
        .button { display: inline-block; padding: 12px 30px; background: #2563eb; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { padding: 20px; text-align: center; color: #666; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>You're Invited!</h1>
        </div>
        <div class="content">
          <h2>Join ${data.tenantName} on Aizako</h2>
          <p>Hi there!</p>
          <p><strong>${data.inviterName}</strong> has invited you to join <strong>${data.tenantName}</strong> as a <strong>${data.role}</strong>.</p>
          <p>Aizako is a comprehensive business management platform that helps teams collaborate and grow together.</p>
          <a href="${data.inviteUrl}" class="button">Accept Invitation</a>
          <p>If the button doesn't work, copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #666;">${data.inviteUrl}</p>
          <p><small>This invitation will expire in 7 days.</small></p>
        </div>
        <div class="footer">
          <p>© ${new Date().getFullYear()} Aizako. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}
/**
 * Generate password reset email template
 */
function generatePasswordResetTemplate(data) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Reset your password</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #dc2626; color: white; padding: 20px; text-align: center; }
        .content { padding: 30px; background: #f9f9f9; }
        .button { display: inline-block; padding: 12px 30px; background: #dc2626; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { padding: 20px; text-align: center; color: #666; font-size: 14px; }
        .warning { background: #fef3c7; border-left: 4px solid #f59e0b; padding: 15px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Password Reset</h1>
        </div>
        <div class="content">
          <h2>Reset your password</h2>
          <p>Hi ${data.userName},</p>
          <p>You requested to reset your password for your Aizako account. Click the button below to create a new password:</p>
          <a href="${data.resetUrl}" class="button">Reset Password</a>
          <p>If the button doesn't work, copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #666;">${data.resetUrl}</p>
          <div class="warning">
            <p><strong>Security Notice:</strong></p>
            <ul>
              <li>This link will expire in 1 hour</li>
              <li>If you didn't request this reset, you can safely ignore this email</li>
              <li>Your password won't change until you create a new one</li>
            </ul>
          </div>
        </div>
        <div class="footer">
          <p>© ${new Date().getFullYear()} Aizako. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}
/**
 * Generate welcome email template
 */
function generateWelcomeTemplate(data) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Welcome to ${data.tenantName}!</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #059669; color: white; padding: 20px; text-align: center; }
        .content { padding: 30px; background: #f9f9f9; }
        .button { display: inline-block; padding: 12px 30px; background: #059669; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { padding: 20px; text-align: center; color: #666; font-size: 14px; }
        .features { background: white; padding: 20px; margin: 20px 0; border-radius: 5px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Welcome to Aizako!</h1>
        </div>
        <div class="content">
          <h2>Hi ${data.userName}!</h2>
          <p>Welcome to <strong>${data.tenantName}</strong> on Aizako! We're excited to have you on board.</p>
          <p>Your account has been created successfully and you're ready to start exploring our platform.</p>
          
          <div class="features">
            <h3>What's next?</h3>
            <ul>
              <li>Explore your dashboard and customize your workspace</li>
              <li>Connect with your team members</li>
              <li>Set up your profile and preferences</li>
              <li>Start managing your business workflows</li>
            </ul>
          </div>
          
          <a href="${data.loginUrl}" class="button">Access Your Account</a>
          
          <p>If you have any questions or need help getting started, don't hesitate to reach out to our support team.</p>
        </div>
        <div class="footer">
          <p>© ${new Date().getFullYear()} Aizako. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}
// Export singleton instance
export const emailService = new EmailService();
