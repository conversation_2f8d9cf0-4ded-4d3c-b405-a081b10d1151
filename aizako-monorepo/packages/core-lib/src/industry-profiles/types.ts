export type Archetype = 'product' | 'service' | 'saas' | 'mixed';

export interface IndustryProfile {
  id: string;
  label: string;
  synonyms: string[];
  archetype: Archetype;
  defaults: {
    currency: string;
    drivers: {
      revenue: { kind: string };
      grossMarginPct: number;
    };
  };
  ranges: Record<string, [number, number]>;
  tooltips: Record<string, string>;
  examples: Record<string, string>;
  extras?: Record<string, [number, number]>;
}

export interface WizardStep {
  id: string;
  answer: string;
}

export interface WizardRequest {
  steps: WizardStep[];
  freeText?: string;
  preferredProfileId?: string;
  locale?: string;
}

export interface WizardResponse {
  archetype: Archetype;
  profileId: string;
  confidence: number;
  prefill: Record<string, any>;
  needsConfirmation?: string[];
  suggestedRanges?: Record<string, [number, number]>;
  notes?: string;
}