import d2c from './d2c_ecommerce.json';
import agency from './agency_consulting.json';
import saas from './saas_seat.json';
import marketplace from './marketplace.json';
import subbox from './subscription_box.json';
import generic from './generic.json';

export const profiles = [d2c, agency, saas, marketplace, subbox, generic] as const;
export type Profile = typeof profiles[number];

/**
 * Match a profile based on free text description or preferred ID
 */
export function matchProfile(freeText?: string, preferredId?: string): Profile {
  // First try preferred ID if provided
  if (preferredId) {
    const found = profiles.find(p => p.id === preferredId);
    if (found) return found as Profile;
  }

  // Then try synonym matching
  if (freeText) {
    const text = freeText.toLowerCase().trim();
    for (const profile of profiles) {
      if (profile.synonyms?.some(synonym => text.includes(synonym.toLowerCase()))) {
        return profile as Profile;
      }
    }
  }

  // Fallback to generic profile
  return generic as Profile;
}

/**
 * Clamp a numeric value to the profile's defined range for a field
 */
export function clampToRange(value: number, key: string, profile: Profile): number {
  const ranges = profile.ranges as any;
  const range = ranges?.[key];
  
  if (!range || !Array.isArray(range) || range.length !== 2) {
    return value;
  }
  
  const [min, max] = range;
  return Math.max(min, Math.min(max, value));
}

/**
 * Get typical (midpoint) value for a field from profile ranges
 */
export function typical(key: string, profile: Profile): number | undefined {
  const ranges = profile.ranges as any;
  const range = ranges?.[key];
  
  if (!range || !Array.isArray(range) || range.length !== 2) {
    return undefined;
  }
  
  const [min, max] = range;
  return Math.round(((min + max) / 2) * 100) / 100;
}

/**
 * Get all available archetype values
 */
export function getArchetypes(): string[] {
  return ['product', 'service', 'saas', 'mixed'];
}

/**
 * Get profiles by archetype
 */
export function getProfilesByArchetype(archetype: string): Profile[] {
  return profiles.filter(p => p.archetype === archetype) as Profile[];
}

/**
 * Validate if a profile has required ranges for an archetype
 */
export function validateProfileForArchetype(profile: Profile, archetype: string): boolean {
  const ranges = profile.ranges as any;
  
  switch (archetype) {
    case 'product':
      return !!(ranges?.aov && ranges?.momGrowthPct && ranges?.grossMarginPct);
    case 'service':
      return !!(ranges?.rate && ranges?.billableHours && ranges?.grossMarginPct);
    case 'saas':
      return !!(ranges?.arpu && ranges?.churnPct && ranges?.grossMarginPct);
    default:
      return true;
  }
}

export * from './types';