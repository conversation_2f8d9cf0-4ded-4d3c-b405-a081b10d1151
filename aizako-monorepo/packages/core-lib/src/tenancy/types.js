import { z } from 'zod';
// Tenant type
export const TenantSchema = z.object({
    id: z.string(),
    name: z.string(),
    slug: z.string(),
    plan: z.string(),
    features: z.array(z.string()),
    limits: z.record(z.string(), z.number()),
    domains: z.array(z.string()).optional(),
    status: z.enum(['active', 'inactive', 'suspended', 'trial']).default('active'),
    trialEndsAt: z.date().optional(),
    settings: z.record(z.string(), z.any()).optional(),
    createdAt: z.date(),
    updatedAt: z.date(),
});
// Tenant domain type
export const TenantDomainSchema = z.object({
    id: z.string(),
    tenantId: z.string(),
    domain: z.string(),
    verified: z.boolean().default(false),
    primary: z.boolean().default(false),
    verificationToken: z.string().optional(),
    verificationMethod: z.enum(['dns', 'file', 'email']).optional(),
    createdAt: z.date(),
    updatedAt: z.date(),
});
// Tenant user type
export const TenantUserSchema = z.object({
    id: z.string(),
    tenantId: z.string(),
    userId: z.string(),
    role: z.enum(['owner', 'admin', 'member', 'guest']).default('member'),
    permissions: z.array(z.string()).optional(),
    invitedBy: z.string().optional(),
    invitedAt: z.date().optional(),
    joinedAt: z.date().optional(),
    status: z.enum(['active', 'invited', 'inactive']).default('active'),
    createdAt: z.date(),
    updatedAt: z.date(),
});
// Tenant context type
export const TenantContextSchema = z.object({
    tenant: TenantSchema.nullable(),
    loading: z.boolean(),
    error: z.string().nullable(),
});
// License claim type
export const LicenseClaimSchema = z.object({
    tenantId: z.string(),
    plan: z.string(),
    features: z.array(z.string()),
    limits: z.record(z.string(), z.number()),
    exp: z.number(),
});
// Type guard for Tenant
export function isTenant(value) {
    return TenantSchema.safeParse(value).success;
}
// Type guard for TenantDomain
export function isTenantDomain(value) {
    return TenantDomainSchema.safeParse(value).success;
}
// Type guard for TenantUser
export function isTenantUser(value) {
    return TenantUserSchema.safeParse(value).success;
}
// Type guard for LicenseClaim
export function isLicenseClaim(value) {
    return LicenseClaimSchema.safeParse(value).success;
}
