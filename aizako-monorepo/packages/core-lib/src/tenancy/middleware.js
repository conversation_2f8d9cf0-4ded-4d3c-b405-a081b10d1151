/**
 * Middleware to enforce tenant isolation
 */
export function tenantIsolationMiddleware(req, res, next) {
    // Extract tenant ID from request
    const tenantId = req.headers['x-tenant-id'];
    if (!tenantId) {
        res.status(401).json({
            success: false,
            error: 'Tenant ID is required',
            message: 'Please provide a tenant ID in the x-tenant-id header'
        });
        return;
    }
    // Attach tenant ID to request
    req.tenantId = tenantId;
    // Continue to next middleware
    next();
}
/**
 * Middleware to validate tenant access
 */
export function tenantAccessMiddleware(tenantService) {
    return async (req, res, next) => {
        try {
            // Extract tenant ID from request
            const tenantId = req.headers['x-tenant-id'];
            if (!tenantId) {
                res.status(401).json({
                    success: false,
                    error: 'Tenant ID is required',
                    message: 'Please provide a tenant ID in the x-tenant-id header'
                });
                return;
            }
            // Get user ID from authenticated user
            const userId = req.user?.id;
            if (!userId) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                    message: 'You must be authenticated to access this resource'
                });
                return;
            }
            // Check if user has access to tenant
            const hasAccess = await tenantService.checkUserTenantAccess(userId, tenantId);
            if (!hasAccess) {
                res.status(403).json({
                    success: false,
                    error: 'Access denied',
                    message: 'You do not have access to this tenant'
                });
                return;
            }
            // Attach tenant ID to request
            req.tenantId = tenantId;
            // Continue to next middleware
            next();
        }
        catch (error) {
            console.error('Error in tenant access middleware:', error);
            res.status(500).json({
                success: false,
                error: 'Internal server error',
                message: error.message || 'An error occurred while validating tenant access'
            });
        }
    };
}
/**
 * Middleware to check license claims
 */
export function licenseMiddleware(req, res, next) {
    // Extract license claims from request
    const licenseClaims = req.licenseClaims;
    if (!licenseClaims) {
        res.status(401).json({
            success: false,
            error: 'License claims are required',
            message: 'No license claims found for this tenant'
        });
        return;
    }
    // Check if license is expired
    const now = Math.floor(Date.now() / 1000);
    if (licenseClaims.exp < now) {
        res.status(402).json({
            success: false,
            error: 'License expired',
            message: 'Your license has expired. Please renew your subscription.'
        });
        return;
    }
    // Continue to next middleware
    next();
}
/**
 * Middleware to check feature access
 * @param feature Feature to check
 */
export function featureMiddleware(feature) {
    return (req, res, next) => {
        // Extract license claims from request
        const licenseClaims = req.licenseClaims;
        if (!licenseClaims) {
            res.status(401).json({
                success: false,
                error: 'License claims are required',
                message: 'No license claims found for this tenant'
            });
            return;
        }
        // Check if feature is available
        if (!licenseClaims.features.includes(feature)) {
            res.status(402).json({
                success: false,
                error: 'Feature not available',
                message: `The feature '${feature}' is not available in your current plan. Please upgrade to access this feature.`
            });
            return;
        }
        // Continue to next middleware
        next();
    };
}
/**
 * Middleware to check module access
 * @param module Module to check
 */
export function moduleMiddleware(module) {
    return (req, res, next) => {
        // Extract license claims from request
        const licenseClaims = req.licenseClaims;
        if (!licenseClaims) {
            res.status(401).json({
                success: false,
                error: 'License claims are required',
                message: 'No license claims found for this tenant'
            });
            return;
        }
        // Check if module is available
        if (!licenseClaims.features.includes(module)) {
            res.status(402).json({
                success: false,
                error: 'Module not available',
                message: `The module '${module}' is not available in your current plan. Please upgrade to access this module.`
            });
            return;
        }
        // Continue to next middleware
        next();
    };
}
/**
 * Middleware to check resource limits
 * @param resourceType Resource type to check
 * @param getResourceCount Function to get current resource count
 */
export function resourceLimitMiddleware(resourceType, getResourceCount) {
    return async (req, res, next) => {
        try {
            // Extract license claims from request
            const licenseClaims = req.licenseClaims;
            if (!licenseClaims) {
                res.status(401).json({
                    success: false,
                    error: 'License claims are required',
                    message: 'No license claims found for this tenant'
                });
                return;
            }
            // Get resource limit
            const limit = licenseClaims.limits[resourceType];
            if (!limit) {
                // No limit defined, allow access
                next();
                return;
            }
            // Get current resource count
            const count = await getResourceCount(req.tenantId);
            // Check if limit is exceeded
            if (count >= limit) {
                res.status(402).json({
                    success: false,
                    error: 'Resource limit exceeded',
                    message: `You have reached the limit of ${limit} ${resourceType} in your current plan. Please upgrade to add more.`
                });
                return;
            }
            // Continue to next middleware
            next();
        }
        catch (error) {
            console.error(`Error in resource limit middleware for ${resourceType}:`, error);
            res.status(500).json({
                success: false,
                error: 'Internal server error',
                message: error.message || 'An error occurred while checking resource limits'
            });
        }
    };
}
