import { jsx as _jsx } from "react/jsx-runtime";
import { createContext, useContext, useEffect, useState } from 'react';
import { useAuth } from '../auth';
// Create context
const TenantContext = createContext(undefined);
/**
 * Tenant Provider component
 */
export function TenantProvider({ children, initialTenantId }) {
    const { user, isAuthenticated } = useAuth();
    const [tenant, setTenant] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    // Load tenant data when user changes or initialTenantId is provided
    useEffect(() => {
        const loadTenant = async () => {
            if (!isAuthenticated) {
                setTenant(null);
                setIsLoading(false);
                return;
            }
            try {
                setIsLoading(true);
                setError(null);
                if (initialTenantId) {
                    // Load tenant by ID
                    const tenantData = await fetchTenantById(initialTenantId);
                    setTenant(tenantData);
                }
                else {
                    // Load user's default tenant
                    const tenants = await fetchUserTenants();
                    if (tenants.length > 0) {
                        setTenant(tenants[0]);
                    }
                    else {
                        setTenant(null);
                    }
                }
            }
            catch (err) {
                console.error('Error loading tenant:', err);
                setError(err.message || 'Failed to load tenant');
                setTenant(null);
            }
            finally {
                setIsLoading(false);
            }
        };
        loadTenant();
    }, [isAuthenticated, user, initialTenantId]);
    // Fetch tenant by ID
    const fetchTenantById = async (tenantId) => {
        // This is a placeholder for the actual API call
        // In a real implementation, you would call your API
        return {
            id: tenantId,
            name: 'Demo Tenant',
            slug: 'demo-tenant',
            plan: 'pro',
            features: ['crm', 'invoicing'],
            limits: { contacts: 1000, users: 10 },
            status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
        };
    };
    // Fetch user's tenants
    const fetchUserTenants = async () => {
        // This is a placeholder for the actual API call
        // In a real implementation, you would call your API
        return [
            {
                id: '1',
                name: 'Demo Tenant',
                slug: 'demo-tenant',
                plan: 'pro',
                features: ['crm', 'invoicing'],
                limits: { contacts: 1000, users: 10 },
                status: 'active',
                createdAt: new Date(),
                updatedAt: new Date(),
            }
        ];
    };
    // Set current tenant
    const setCurrentTenant = async (tenantId) => {
        try {
            setIsLoading(true);
            setError(null);
            const tenantData = await fetchTenantById(tenantId);
            setTenant(tenantData);
        }
        catch (err) {
            console.error('Error setting tenant:', err);
            setError(err.message || 'Failed to set tenant');
            throw err;
        }
        finally {
            setIsLoading(false);
        }
    };
    // Create tenant
    const createTenant = async (tenantData) => {
        try {
            setIsLoading(true);
            setError(null);
            // This is a placeholder for the actual API call
            // In a real implementation, you would call your API
            const newTenant = {
                id: Math.random().toString(36).substring(2, 9),
                name: tenantData.name || 'New Tenant',
                slug: tenantData.slug || 'new-tenant',
                plan: tenantData.plan || 'free',
                features: tenantData.features || [],
                limits: tenantData.limits || {},
                status: 'active',
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            setTenant(newTenant);
            return newTenant;
        }
        catch (err) {
            console.error('Error creating tenant:', err);
            setError(err.message || 'Failed to create tenant');
            throw err;
        }
        finally {
            setIsLoading(false);
        }
    };
    // Update tenant
    const updateTenant = async (tenantId, tenantData) => {
        try {
            setIsLoading(true);
            setError(null);
            // This is a placeholder for the actual API call
            // In a real implementation, you would call your API
            const updatedTenant = {
                ...(tenant || {}),
                ...tenantData,
                id: tenantId,
                updatedAt: new Date(),
            };
            setTenant(updatedTenant);
            return updatedTenant;
        }
        catch (err) {
            console.error('Error updating tenant:', err);
            setError(err.message || 'Failed to update tenant');
            throw err;
        }
        finally {
            setIsLoading(false);
        }
    };
    // Delete tenant
    const deleteTenant = async (tenantId) => {
        try {
            setIsLoading(true);
            setError(null);
            // This is a placeholder for the actual API call
            // In a real implementation, you would call your API
            if (tenant?.id === tenantId) {
                setTenant(null);
            }
            return true;
        }
        catch (err) {
            console.error('Error deleting tenant:', err);
            setError(err.message || 'Failed to delete tenant');
            throw err;
        }
        finally {
            setIsLoading(false);
        }
    };
    // Get user tenants
    const getUserTenants = async () => {
        try {
            setIsLoading(true);
            setError(null);
            return await fetchUserTenants();
        }
        catch (err) {
            console.error('Error getting user tenants:', err);
            setError(err.message || 'Failed to get user tenants');
            throw err;
        }
        finally {
            setIsLoading(false);
        }
    };
    const contextValue = {
        tenant,
        isLoading,
        error,
        setCurrentTenant,
        createTenant,
        updateTenant,
        deleteTenant,
        getUserTenants
    };
    return (_jsx(TenantContext.Provider, { value: contextValue, children: children }));
}
/**
 * Hook for using tenant context
 */
export function useTenant() {
    const context = useContext(TenantContext);
    if (context === undefined) {
        throw new Error('useTenant must be used within a TenantProvider');
    }
    return context;
}
