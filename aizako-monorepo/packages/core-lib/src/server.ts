// Server-only exports (Node.js/API routes only)
// All server-side functionality needed by API routes

// Database exports
export * from './db/mongo';

// Models exports
export * from './models';

// Session functions
export {
  createSessionCookie,
  clearSessionCookie,
  getSession,
  createDevSession,
  generateSecureToken,
  hashToken,
  verifyToken,
  type SessionData,
  type RequestLike,
  type CookieOptions,
} from './auth/session';

// Password functions
export {
  hashPassword,
  verifyPassword,
  validatePassword,
  generateSecurePassword,
} from './auth/password';

// Auth guards
export {
  requireAuth,
  requireTenantAccess,
  requireRole,
  requireAdmin,
  requireOwner,
  createAuthenticatedRequest,
  RateLimiter,
  authRateLimiter,
  requireRateLimit,
  getClientIdentifier,
  addSecurityHeaders,
  generateCSRFToken,
  validateCSRFToken,
  hasRole,
  ROLE_HIERARCHY,
  type AuthGuardResult,
  type AuthenticatedRequest,
} from './auth/guards';

// Utils exports
export * from './utils';

// Email exports
export * from './email';

// Industry profiles exports
export * from './industry-profiles';
