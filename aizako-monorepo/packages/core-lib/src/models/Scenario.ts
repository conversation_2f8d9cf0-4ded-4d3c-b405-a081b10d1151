import mongoose, { Schema, Document } from 'mongoose';

// Define the financial modeling config and projection types
// These match the types from the financial-modeling app
export interface Config {
  meta: {
    currency: string;
    start: string; // ISO date string
    periods: number;
    freq: 'monthly' | 'yearly';
  };
  opening_balances: {
    cash?: number;
    ar?: number;
    inventory?: number;
    ppne_net?: number;
    ap?: number;
    debt_current?: number;
    debt_long?: number;
    retained_earnings?: number;
  };
  drivers: {
    revenue: {
      start_run_rate: number;
      mth_growth_pct: number;
    };
    gross_margin_pct: number;
    opex: {
      fixed: number;
      variable_pct_of_rev: number;
    };
    capex: {
      items: Array<{ month: number; amount: number }>;
      depr_years: number;
    };
    wc: {
      dso: number;
      dpo: number;
      dio: number;
    };
    debt: {
      opening: number;
      rate_pct: number;
      term_months: number;
      amort: 'annuity' | 'interest_only' | 'bullet';
      draws: Array<{ month: number; amount: number }>;
    };
    tax: {
      rate_pct: number;
      payments_lag_mths: number;
    };
  };
}

// Projection result types for financial statements
export interface PnLRow {
  period: number;
  date: string;
  revenue: number;
  cogs: number;
  gross_profit: number;
  opex_fixed: number;
  opex_variable: number;
  total_opex: number;
  ebitda: number;
  depreciation: number;
  ebit: number;
  interest_expense: number;
  ebt: number;
  tax_expense: number;
  net_income: number;
}

export interface BSRow {
  period: number;
  date: string;
  // Assets
  cash: number;
  ar: number;
  inventory: number;
  total_current_assets: number;
  ppne_gross: number;
  accumulated_depreciation: number;
  ppne_net: number;
  total_assets: number;
  // Liabilities
  ap: number;
  debt_current: number;
  total_current_liabilities: number;
  debt_long: number;
  total_liabilities: number;
  // Equity
  retained_earnings: number;
  total_equity: number;
  total_liab_equity: number;
}

export interface CFRow {
  period: number;
  date: string;
  net_income: number;
  depreciation: number;
  change_ar: number;
  change_inventory: number;
  change_ap: number;
  cfo: number;
  capex: number;
  cfi: number;
  debt_draws: number;
  debt_principal_payments: number;
  cff: number;
  net_change_cash: number;
  beginning_cash: number;
  ending_cash: number;
}

export interface Metrics {
  period: number;
  date: string;
  revenue_growth_pct: number;
  gross_margin_pct: number;
  ebitda_margin_pct: number;
  net_margin_pct: number;
  asset_turnover: number;
  debt_to_equity: number;
  current_ratio: number;
  cash_conversion_days: number;
}

export interface ProjectionResult {
  pnl: PnLRow[];
  bs: BSRow[];
  cf: CFRow[];
  metrics: Metrics[];
}

export interface IScenario extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  industry: string;
  tenantId: mongoose.Types.ObjectId;
  config: Config;
  snapshot: ProjectionResult;
  version: number;
  createdAt: Date;
  updatedAt: Date;
  belongsToTenant(tenantId: string): boolean;
}

export interface IScenarioModel extends mongoose.Model<IScenario> {
  findByTenant(tenantId: string): Promise<IScenario[]>;
  findByTenantAndId(tenantId: string, scenarioId: string): Promise<IScenario | null>;
}

const ScenarioSchema = new Schema<IScenario>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  industry: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50
  },
  tenantId: {
    type: Schema.Types.ObjectId,
    ref: 'Tenant',
    required: true,
    index: true
  },
  config: {
    type: Schema.Types.Mixed,
    required: true
  },
  snapshot: {
    type: Schema.Types.Mixed,
    required: true
  },
  version: {
    type: Number,
    required: true,
    default: 1,
    min: 1
  }
}, {
  timestamps: true,
  collection: 'scenarios'
});

// Indexes for efficient querying
ScenarioSchema.index({ tenantId: 1, updatedAt: -1 });
ScenarioSchema.index({ tenantId: 1, name: 1 });

// Instance methods
ScenarioSchema.methods.toListItem = function() {
  return {
    id: this._id,
    name: this.name,
    industry: this.industry,
    updatedAt: this.updatedAt
  };
};

ScenarioSchema.methods.belongsToTenant = function(tenantId: string): boolean {
  return this.tenantId.toString() === tenantId;
};

// Static methods
ScenarioSchema.statics.findByTenant = function(tenantId: string) {
  return this.find({ tenantId }).select('name industry updatedAt').sort({ updatedAt: -1 });
};

ScenarioSchema.statics.findByTenantAndId = function(tenantId: string, scenarioId: string) {
  return this.findOne({ _id: scenarioId, tenantId });
};

export const Scenario = (mongoose.models.Scenario as IScenarioModel) || mongoose.model<IScenario, IScenarioModel>('Scenario', ScenarioSchema);