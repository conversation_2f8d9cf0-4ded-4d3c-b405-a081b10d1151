import mongoose, { Schema } from 'mongoose';
const UserSchema = new Schema({
    email: {
        type: String,
        required: true,
        unique: true,
        lowercase: true,
        trim: true,
        index: true
    },
    passwordHash: {
        type: String,
        required: true
    },
    fullName: {
        type: String,
        trim: true
    },
    defaultTenantId: {
        type: Schema.Types.ObjectId,
        ref: 'Tenant'
    },
    status: {
        type: String,
        enum: ['active', 'invited', 'suspended'],
        default: 'active',
        required: true
    },
    lastLoginAt: {
        type: Date
    }
}, {
    timestamps: true, // Automatically adds createdAt and updatedAt
    collection: 'users'
});
// Indexes
UserSchema.index({ email: 1 }, { unique: true });
UserSchema.index({ defaultTenantId: 1 });
UserSchema.index({ status: 1 });
UserSchema.index({ createdAt: -1 });
// Virtual for tenant lookup
UserSchema.virtual('defaultTenant', {
    ref: 'Tenant',
    localField: 'defaultTenantId',
    foreignField: '_id',
    justOne: true
});
// Ensure virtual fields are serialized
UserSchema.set('toJSON', { virtuals: true });
UserSchema.set('toObject', { virtuals: true });
export const User = mongoose.models.User || mongoose.model('User', UserSchema);
