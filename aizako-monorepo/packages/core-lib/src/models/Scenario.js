import mongoose, { Schema } from 'mongoose';
const ScenarioSchema = new Schema({
    name: {
        type: String,
        required: true,
        trim: true,
        maxlength: 100
    },
    industry: {
        type: String,
        required: true,
        trim: true,
        maxlength: 50
    },
    tenantId: {
        type: Schema.Types.ObjectId,
        ref: 'Tenant',
        required: true,
        index: true
    },
    config: {
        type: Schema.Types.Mixed,
        required: true
    },
    snapshot: {
        type: Schema.Types.Mixed,
        required: true
    },
    version: {
        type: Number,
        required: true,
        default: 1,
        min: 1
    }
}, {
    timestamps: true,
    collection: 'scenarios'
});
// Indexes for efficient querying
ScenarioSchema.index({ tenantId: 1, updatedAt: -1 });
ScenarioSchema.index({ tenantId: 1, name: 1 });
// Instance methods
ScenarioSchema.methods.toListItem = function () {
    return {
        id: this._id,
        name: this.name,
        industry: this.industry,
        updatedAt: this.updatedAt
    };
};
ScenarioSchema.methods.belongsToTenant = function (tenantId) {
    return this.tenantId.toString() === tenantId;
};
// Static methods
ScenarioSchema.statics.findByTenant = function (tenantId) {
    return this.find({ tenantId }).select('name industry updatedAt').sort({ updatedAt: -1 });
};
ScenarioSchema.statics.findByTenantAndId = function (tenantId, scenarioId) {
    return this.findOne({ _id: scenarioId, tenantId });
};
export const Scenario = mongoose.model('Scenario', ScenarioSchema);
