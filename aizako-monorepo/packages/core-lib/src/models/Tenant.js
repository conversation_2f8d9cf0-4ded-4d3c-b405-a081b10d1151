import mongoose, { Schema } from 'mongoose';
const TenantSchema = new Schema({
    name: {
        type: String,
        required: true,
        trim: true,
        maxlength: 100
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        index: true
    },
    plan: {
        type: String,
        enum: ['free', 'pro', 'enterprise'],
        default: 'free'
    },
    modules: {
        type: {
            crm: { type: Boolean, default: false },
            flows: { type: Boolean, default: false },
            financialModeling: { type: Boolean, default: false }
        },
        default: {
            crm: false,
            flows: false,
            financialModeling: false
        }
    }
}, {
    timestamps: { createdAt: true, updatedAt: false }, // Only track creation
    collection: 'tenants'
});
// Indexes
TenantSchema.index({ createdBy: 1 });
TenantSchema.index({ createdAt: -1 });
TenantSchema.index({ plan: 1 });
// Virtual for creator lookup
TenantSchema.virtual('creator', {
    ref: 'User',
    localField: 'createdBy',
    foreignField: '_id',
    justOne: true
});
// Method to check if module is enabled
TenantSchema.methods.hasModule = function (moduleName) {
    return this.modules?.[moduleName] === true;
};
// Method to enable a module
TenantSchema.methods.enableModule = function (moduleName) {
    if (!this.modules) {
        this.modules = {};
    }
    this.modules[moduleName] = true;
};
// Method to disable a module
TenantSchema.methods.disableModule = function (moduleName) {
    if (this.modules) {
        this.modules[moduleName] = false;
    }
};
// Ensure virtual fields are serialized
TenantSchema.set('toJSON', { virtuals: true });
TenantSchema.set('toObject', { virtuals: true });
export const Tenant = mongoose.models.Tenant || mongoose.model('Tenant', TenantSchema);
