import mongoose, { Schema } from 'mongoose';
const InvitationSchema = new Schema({
    tenantId: {
        type: Schema.Types.ObjectId,
        ref: 'Tenant',
        required: true,
        index: true
    },
    email: {
        type: String,
        required: true,
        lowercase: true,
        trim: true,
        index: true
    },
    role: {
        type: String,
        enum: ['owner', 'admin', 'member', 'viewer'],
        required: true,
        default: 'member'
    },
    tokenHash: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    expiresAt: {
        type: Date,
        required: true,
        index: { expireAfterSeconds: 0 } // TTL index
    },
    invitedBy: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        index: true
    },
    acceptedAt: {
        type: Date
    }
}, {
    timestamps: { createdAt: true, updatedAt: false },
    collection: 'invitations'
});
// Indexes
InvitationSchema.index({ tokenHash: 1 }, { unique: true });
InvitationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }); // TTL index
InvitationSchema.index({ tenantId: 1, email: 1 });
InvitationSchema.index({ invitedBy: 1 });
InvitationSchema.index({ createdAt: -1 });
// Virtual for tenant lookup
InvitationSchema.virtual('tenant', {
    ref: 'Tenant',
    localField: 'tenantId',
    foreignField: '_id',
    justOne: true
});
// Virtual for inviter lookup
InvitationSchema.virtual('inviter', {
    ref: 'User',
    localField: 'invitedBy',
    foreignField: '_id',
    justOne: true
});
// Instance method to check if invitation is valid (not expired and not accepted)
InvitationSchema.methods.isValid = function () {
    return !this.acceptedAt && this.expiresAt > new Date();
};
// Instance method to check if invitation is expired
InvitationSchema.methods.isExpired = function () {
    return this.expiresAt <= new Date();
};
// Instance method to check if invitation is accepted
InvitationSchema.methods.isAccepted = function () {
    return !!this.acceptedAt;
};
// Instance method to mark invitation as accepted
InvitationSchema.methods.markAccepted = function () {
    this.acceptedAt = new Date();
};
// Static method to create invitation with expiry
InvitationSchema.statics.createWithExpiry = function (data, daysToExpire = 7) {
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + daysToExpire);
    return new this({
        ...data,
        expiresAt
    });
};
// Static method to find valid invitation by token hash
InvitationSchema.statics.findValidByTokenHash = function (tokenHash) {
    return this.findOne({
        tokenHash,
        expiresAt: { $gt: new Date() },
        acceptedAt: { $exists: false }
    });
};
// Ensure virtual fields are serialized
InvitationSchema.set('toJSON', { virtuals: true });
InvitationSchema.set('toObject', { virtuals: true });
export const Invitation = (mongoose.models.Invitation || mongoose.model('Invitation', InvitationSchema));
