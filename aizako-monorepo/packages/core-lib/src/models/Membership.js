import mongoose, { Schema } from 'mongoose';
const MembershipSchema = new Schema({
    userId: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        index: true
    },
    tenantId: {
        type: Schema.Types.ObjectId,
        ref: 'Tenant',
        required: true,
        index: true
    },
    role: {
        type: String,
        enum: ['owner', 'admin', 'member', 'viewer'],
        required: true,
        default: 'member'
    },
    status: {
        type: String,
        enum: ['active', 'invited'],
        required: true,
        default: 'active'
    }
}, {
    timestamps: { createdAt: true, updatedAt: false },
    collection: 'memberships'
});
// Compound indexes for efficient queries
MembershipSchema.index({ tenantId: 1, userId: 1 }, { unique: true });
MembershipSchema.index({ userId: 1, status: 1 });
MembershipSchema.index({ tenantId: 1, role: 1 });
MembershipSchema.index({ tenantId: 1, status: 1 });
MembershipSchema.index({ createdAt: -1 });
// Virtual for user lookup
MembershipSchema.virtual('user', {
    ref: 'User',
    localField: 'userId',
    foreignField: '_id',
    justOne: true
});
// Virtual for tenant lookup
MembershipSchema.virtual('tenant', {
    ref: 'Tenant',
    localField: 'tenantId',
    foreignField: '_id',
    justOne: true
});
// Static methods for role hierarchy checks
MembershipSchema.statics.getRoleHierarchy = function () {
    return {
        viewer: 0,
        member: 1,
        admin: 2,
        owner: 3
    };
};
MembershipSchema.statics.hasPermission = function (userRole, requiredRole) {
    const hierarchy = {
        viewer: 0,
        member: 1,
        admin: 2,
        owner: 3
    };
    return hierarchy[userRole] >= hierarchy[requiredRole];
};
// Instance method to check if this membership has required role
MembershipSchema.methods.hasRole = function (requiredRole) {
    const hierarchy = {
        viewer: 0,
        member: 1,
        admin: 2,
        owner: 3
    };
    return hierarchy[this.role] >= hierarchy[requiredRole];
};
// Instance method to check if membership is active
MembershipSchema.methods.isActive = function () {
    return this.status === 'active';
};
// Ensure virtual fields are serialized
MembershipSchema.set('toJSON', { virtuals: true });
MembershipSchema.set('toObject', { virtuals: true });
export const Membership = mongoose.models.Membership || mongoose.model('Membership', MembershipSchema);
