import mongoose, { Schema } from 'mongoose';
const PasswordResetTokenSchema = new Schema({
    userId: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        index: true
    },
    tokenHash: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    expiresAt: {
        type: Date,
        required: true,
        index: { expireAfterSeconds: 0 } // TTL index
    },
    usedAt: {
        type: Date
    }
}, {
    timestamps: { createdAt: true, updatedAt: false },
    collection: 'password_reset_tokens'
});
// Indexes
PasswordResetTokenSchema.index({ tokenHash: 1 }, { unique: true });
PasswordResetTokenSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }); // TTL index
PasswordResetTokenSchema.index({ userId: 1, createdAt: -1 });
PasswordResetTokenSchema.index({ createdAt: -1 });
// Virtual for user lookup
PasswordResetTokenSchema.virtual('user', {
    ref: 'User',
    localField: 'userId',
    foreignField: '_id',
    justOne: true
});
// Instance method to check if token is valid (not expired and not used)
PasswordResetTokenSchema.methods.isValid = function () {
    return !this.usedAt && this.expiresAt > new Date();
};
// Instance method to check if token is expired
PasswordResetTokenSchema.methods.isExpired = function () {
    return this.expiresAt <= new Date();
};
// Instance method to check if token is used
PasswordResetTokenSchema.methods.isUsed = function () {
    return !!this.usedAt;
};
// Instance method to mark token as used
PasswordResetTokenSchema.methods.markUsed = function () {
    this.usedAt = new Date();
};
// Static method to create token with expiry
PasswordResetTokenSchema.statics.createWithExpiry = function (data, hoursToExpire = 1) {
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + hoursToExpire);
    return new this({
        ...data,
        expiresAt
    });
};
// Static method to find valid token by token hash
PasswordResetTokenSchema.statics.findValidByTokenHash = function (tokenHash) {
    return this.findOne({
        tokenHash,
        expiresAt: { $gt: new Date() },
        usedAt: { $exists: false }
    });
};
// Static method to invalidate all existing tokens for a user
PasswordResetTokenSchema.statics.invalidateAllForUser = function (userId) {
    return this.updateMany({
        userId,
        usedAt: { $exists: false },
        expiresAt: { $gt: new Date() }
    }, { usedAt: new Date() });
};
// Ensure virtual fields are serialized
PasswordResetTokenSchema.set('toJSON', { virtuals: true });
PasswordResetTokenSchema.set('toObject', { virtuals: true });
export const PasswordResetToken = (mongoose.models.PasswordResetToken || mongoose.model('PasswordResetToken', PasswordResetTokenSchema));
