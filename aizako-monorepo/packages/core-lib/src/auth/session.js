import * as crypto from 'crypto';
// Default cookie configuration
const COOKIE_NAME = 'aiz_sess';
const DEFAULT_COOKIE_OPTIONS = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    path: '/'
};
/**
 * Create a signed and encrypted session cookie
 * @param sessionData The session data to store
 * @param options Cookie options (optional)
 * @returns Set-Cookie header value
 */
export function createSessionCookie(sessionData, options = {}) {
    const sessionSecret = process.env.SESSION_SECRET;
    if (!sessionSecret) {
        throw new Error('SESSION_SECRET environment variable is required');
    }
    // Serialize and encrypt session data
    const payload = JSON.stringify(sessionData);
    const encryptedData = encrypt(payload, sessionSecret);
    // Create signed cookie value
    const signedValue = sign(encryptedData, sessionSecret);
    // Merge options with defaults
    const cookieOptions = { ...DEFAULT_COOKIE_OPTIONS, ...options };
    // Build cookie string
    let cookieString = `${COOKIE_NAME}=${signedValue}`;
    if (cookieOptions.maxAge) {
        cookieString += `; Max-Age=${Math.floor(cookieOptions.maxAge / 1000)}`;
        const expires = new Date(Date.now() + cookieOptions.maxAge);
        cookieString += `; Expires=${expires.toUTCString()}`;
    }
    if (cookieOptions.path) {
        cookieString += `; Path=${cookieOptions.path}`;
    }
    if (cookieOptions.httpOnly) {
        cookieString += '; HttpOnly';
    }
    if (cookieOptions.secure) {
        cookieString += '; Secure';
    }
    if (cookieOptions.sameSite) {
        cookieString += `; SameSite=${cookieOptions.sameSite}`;
    }
    return cookieString;
}
/**
 * Clear the session cookie
 * @returns Set-Cookie header value that clears the cookie
 */
export function clearSessionCookie() {
    const cookieOptions = { ...DEFAULT_COOKIE_OPTIONS, maxAge: 0 };
    let cookieString = `${COOKIE_NAME}=; Max-Age=0; Expires=Thu, 01 Jan 1970 00:00:00 GMT`;
    if (cookieOptions.path) {
        cookieString += `; Path=${cookieOptions.path}`;
    }
    if (cookieOptions.httpOnly) {
        cookieString += '; HttpOnly';
    }
    if (cookieOptions.secure) {
        cookieString += '; Secure';
    }
    if (cookieOptions.sameSite) {
        cookieString += `; SameSite=${cookieOptions.sameSite}`;
    }
    return cookieString;
}
/**
 * Get session data from request
 * Parses signed and encrypted session cookies
 *
 * @param req Request object with cookies and headers
 * @returns Session data or null if not authenticated
 */
export async function getSession(req) {
    try {
        // Get session cookie
        const sessionCookie = req.cookies.get(COOKIE_NAME)?.value;
        if (!sessionCookie) {
            // Development fallback using headers
            if (process.env.NODE_ENV === 'development') {
                return getDevSessionFromHeaders(req);
            }
            return null;
        }
        // Parse the signed and encrypted session cookie
        const sessionData = await parseSessionCookie(sessionCookie);
        if (!sessionData || !sessionData.userId || !sessionData.tenantId) {
            return null;
        }
        return {
            userId: sessionData.userId,
            tenantId: sessionData.tenantId
        };
    }
    catch (error) {
        console.error('Error parsing session:', error);
        return null;
    }
}
/**
 * Parse signed and encrypted session cookie
 */
async function parseSessionCookie(cookieValue) {
    try {
        const sessionSecret = process.env.SESSION_SECRET;
        if (!sessionSecret) {
            throw new Error('SESSION_SECRET environment variable is required');
        }
        // Verify signature first
        const encryptedData = unsign(cookieValue, sessionSecret);
        if (!encryptedData) {
            console.warn('Session cookie signature verification failed');
            return null;
        }
        // Decrypt the session data
        const decryptedData = decrypt(encryptedData, sessionSecret);
        // Parse JSON
        const sessionData = JSON.parse(decryptedData);
        return sessionData;
    }
    catch (error) {
        console.error('Error parsing session cookie:', error);
        return null;
    }
}
/**
 * Development fallback using headers
 * Only used in development mode for testing
 */
function getDevSessionFromHeaders(req) {
    const userId = req.headers.get('x-user-id');
    const tenantId = req.headers.get('x-tenant-id');
    if (userId && tenantId) {
        return { userId, tenantId };
    }
    return null;
}
/**
 * Sign a value using HMAC
 */
function sign(value, secret) {
    const signature = crypto
        .createHmac('sha256', secret)
        .update(value)
        .digest('base64url');
    return `${value}.${signature}`;
}
/**
 * Unsign a value and verify HMAC signature
 */
function unsign(signedValue, secret) {
    const lastDotIndex = signedValue.lastIndexOf('.');
    if (lastDotIndex === -1) {
        return null;
    }
    const value = signedValue.substring(0, lastDotIndex);
    const signature = signedValue.substring(lastDotIndex + 1);
    const expectedSignature = crypto
        .createHmac('sha256', secret)
        .update(value)
        .digest('base64url');
    // Use constant-time comparison to prevent timing attacks
    if (!crypto.timingSafeEqual(Buffer.from(signature, 'base64url'), Buffer.from(expectedSignature, 'base64url'))) {
        return null;
    }
    return value;
}
/**
 * Encrypt data using AES-256-GCM
 */
function encrypt(data, secret) {
    // Derive key from secret
    const key = crypto.scryptSync(secret, 'aizako-session-salt', 32);
    // Generate random IV
    const iv = crypto.randomBytes(16);
    // Create cipher
    const cipher = crypto.createCipher('aes-256-gcm', key);
    cipher.setAAD(Buffer.from('aizako-session', 'utf8'));
    // Encrypt data
    let encrypted = cipher.update(data, 'utf8', 'base64url');
    encrypted += cipher.final('base64url');
    // Get auth tag
    const authTag = cipher.getAuthTag();
    // Combine IV + encrypted data + auth tag
    const combined = Buffer.concat([
        iv,
        Buffer.from(encrypted, 'base64url'),
        authTag
    ]);
    return combined.toString('base64url');
}
/**
 * Decrypt data using AES-256-GCM
 */
function decrypt(encryptedData, secret) {
    // Derive key from secret
    const key = crypto.scryptSync(secret, 'aizako-session-salt', 32);
    // Parse combined data
    const combined = Buffer.from(encryptedData, 'base64url');
    const authTag = combined.subarray(-16);
    const encrypted = combined.subarray(16, -16);
    // Create decipher
    const decipher = crypto.createDecipher('aes-256-gcm', key);
    decipher.setAAD(Buffer.from('aizako-session', 'utf8'));
    decipher.setAuthTag(authTag);
    // Decrypt data
    let decrypted = decipher.update(encrypted, undefined, 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
}
/**
 * Create a session for testing/development
 * This is a utility function for development and testing
 */
export function createDevSession(userId, tenantId) {
    return createSessionCookie({ userId, tenantId });
}
/**
 * Generate a secure random token for invitations and password resets
 */
export function generateSecureToken() {
    return crypto.randomBytes(32).toString('base64url');
}
/**
 * Hash a token for secure storage
 */
export function hashToken(token) {
    return crypto.createHash('sha256').update(token).digest('hex');
}
/**
 * Compare tokens in constant time to prevent timing attacks
 */
export function verifyToken(token, hashedToken) {
    const tokenHash = hashToken(token);
    try {
        return crypto.timingSafeEqual(Buffer.from(tokenHash, 'hex'), Buffer.from(hashedToken, 'hex'));
    }
    catch {
        return false;
    }
}
