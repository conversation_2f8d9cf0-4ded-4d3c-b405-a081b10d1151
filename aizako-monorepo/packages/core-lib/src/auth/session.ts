import * as crypto from 'crypto';

/**
 * Session data stored in cookies
 */
export interface SessionData {
  userId: string;
  tenantId: string;
}

/**
 * Simple request interface for cookie access
 */
export interface RequestLike {
  cookies: {
    get(name: string): { value: string } | undefined;
  };
  headers: {
    get(name: string): string | null;
  };
}

/**
 * Cookie options for session cookies
 */
export interface CookieOptions {
  httpOnly?: boolean;
  secure?: boolean;
  sameSite?: 'strict' | 'lax' | 'none';
  maxAge?: number;
  path?: string;
}

// Default cookie configuration
const COOKIE_NAME = 'aiz_sess';
const DEFAULT_COOKIE_OPTIONS: CookieOptions = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'lax',
  maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
  path: '/'
};

/**
 * Create a signed and encrypted session cookie
 * @param sessionData The session data to store
 * @param options Cookie options (optional)
 * @returns Set-Cookie header value
 */
export function createSessionCookie(
  sessionData: SessionData, 
  options: CookieOptions = {}
): string {
  const sessionSecret = process.env.SESSION_SECRET;
  if (!sessionSecret) {
    throw new Error('SESSION_SECRET environment variable is required');
  }

  // Serialize and encrypt session data
  const payload = JSON.stringify(sessionData);
  const encryptedData = encrypt(payload, sessionSecret);
  
  // Create signed cookie value
  const signedValue = sign(encryptedData, sessionSecret);
  
  // Merge options with defaults
  const cookieOptions = { ...DEFAULT_COOKIE_OPTIONS, ...options };
  
  // Build cookie string
  let cookieString = `${COOKIE_NAME}=${signedValue}`;
  
  if (cookieOptions.maxAge) {
    cookieString += `; Max-Age=${Math.floor(cookieOptions.maxAge / 1000)}`;
    const expires = new Date(Date.now() + cookieOptions.maxAge);
    cookieString += `; Expires=${expires.toUTCString()}`;
  }
  
  if (cookieOptions.path) {
    cookieString += `; Path=${cookieOptions.path}`;
  }
  
  if (cookieOptions.httpOnly) {
    cookieString += '; HttpOnly';
  }
  
  if (cookieOptions.secure) {
    cookieString += '; Secure';
  }
  
  if (cookieOptions.sameSite) {
    cookieString += `; SameSite=${cookieOptions.sameSite}`;
  }
  
  return cookieString;
}

/**
 * Clear the session cookie
 * @returns Set-Cookie header value that clears the cookie
 */
export function clearSessionCookie(): string {
  const cookieOptions = { ...DEFAULT_COOKIE_OPTIONS, maxAge: 0 };
  
  let cookieString = `${COOKIE_NAME}=; Max-Age=0; Expires=Thu, 01 Jan 1970 00:00:00 GMT`;
  
  if (cookieOptions.path) {
    cookieString += `; Path=${cookieOptions.path}`;
  }
  
  if (cookieOptions.httpOnly) {
    cookieString += '; HttpOnly';
  }
  
  if (cookieOptions.secure) {
    cookieString += '; Secure';
  }
  
  if (cookieOptions.sameSite) {
    cookieString += `; SameSite=${cookieOptions.sameSite}`;
  }
  
  return cookieString;
}

/**
 * Get session data from request
 * Parses signed and encrypted session cookies
 *
 * @param req Request object with cookies and headers
 * @returns Session data or null if not authenticated
 */
export async function getSession(req: RequestLike): Promise<SessionData | null> {
  try {
    // Get session cookie
    const sessionCookie = req.cookies.get(COOKIE_NAME)?.value;

    if (!sessionCookie) {
      // Development fallback using headers
      if (process.env.NODE_ENV === 'development') {
        return getDevSessionFromHeaders(req);
      }
      return null;
    }

    // Parse the signed and encrypted session cookie
    const sessionData = await parseSessionCookie(sessionCookie);

    if (!sessionData || !sessionData.userId || !sessionData.tenantId) {
      return null;
    }

    // Validate that we have proper string IDs, not objects
    if (typeof sessionData.userId !== 'string' || typeof sessionData.tenantId !== 'string') {
      console.error('Session contains non-string IDs - clearing corrupted session:', {
        userIdType: typeof sessionData.userId,
        tenantIdType: typeof sessionData.tenantId,
        userId: sessionData.userId,
        tenantId: sessionData.tenantId
      });
      return null;
    }

    // Additional validation to ensure IDs look like MongoDB ObjectIds
    const objectIdRegex = /^[0-9a-fA-F]{24}$/;
    if (!objectIdRegex.test(sessionData.userId) || !objectIdRegex.test(sessionData.tenantId)) {
      console.error('Session contains invalid ObjectId format - clearing corrupted session:', {
        userId: sessionData.userId,
        tenantId: sessionData.tenantId
      });
      return null;
    }

    return {
      userId: sessionData.userId,
      tenantId: sessionData.tenantId
    };
  } catch (error) {
    console.error('Error parsing session:', error);
    return null;
  }
}

/**
 * Parse signed and encrypted session cookie
 */
async function parseSessionCookie(cookieValue: string): Promise<SessionData | null> {
  try {
    const sessionSecret = process.env.SESSION_SECRET;
    if (!sessionSecret) {
      throw new Error('SESSION_SECRET environment variable is required');
    }

    // Verify signature first
    const encryptedData = unsign(cookieValue, sessionSecret);
    if (!encryptedData) {
      console.warn('Session cookie signature verification failed');
      return null;
    }

    // Decrypt the session data
    const decryptedData = decrypt(encryptedData, sessionSecret);

    // Parse JSON with additional validation
    const sessionData = JSON.parse(decryptedData);

    // Validate the parsed data structure
    if (!sessionData || typeof sessionData !== 'object') {
      console.error('Parsed session data is not a valid object:', sessionData);
      return null;
    }

    // Ensure required fields exist and are of correct type
    if (!sessionData.userId || !sessionData.tenantId) {
      console.error('Session data missing required fields:', sessionData);
      return null;
    }

    // Convert any non-string IDs to strings (in case of object contamination)
    if (typeof sessionData.userId === 'object' && sessionData.userId._id) {
      console.warn('Session userId was an object, converting to string:', sessionData.userId);
      sessionData.userId = sessionData.userId._id.toString();
    }

    if (typeof sessionData.tenantId === 'object' && sessionData.tenantId._id) {
      console.warn('Session tenantId was an object, converting to string:', sessionData.tenantId);
      sessionData.tenantId = sessionData.tenantId._id.toString();
    }

    return {
      userId: String(sessionData.userId),
      tenantId: String(sessionData.tenantId)
    };
  } catch (error) {
    console.error('Error parsing session cookie:', error);
    return null;
  }
}

/**
 * Development fallback using headers
 * Only used in development mode for testing
 */
function getDevSessionFromHeaders(req: RequestLike): SessionData | null {
  const userId = req.headers.get('x-user-id');
  const tenantId = req.headers.get('x-tenant-id');

  if (userId && tenantId) {
    return { userId, tenantId };
  }

  return null;
}

/**
 * Sign a value using HMAC
 */
function sign(value: string, secret: string): string {
  const signature = crypto
    .createHmac('sha256', secret)
    .update(value)
    .digest('base64url');
    
  return `${value}.${signature}`;
}

/**
 * Unsign a value and verify HMAC signature
 */
function unsign(signedValue: string, secret: string): string | null {
  const lastDotIndex = signedValue.lastIndexOf('.');
  if (lastDotIndex === -1) {
    return null;
  }
  
  const value = signedValue.substring(0, lastDotIndex);
  const signature = signedValue.substring(lastDotIndex + 1);
  
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(value)
    .digest('base64url');
  
  // Use constant-time comparison to prevent timing attacks
  if (!crypto.timingSafeEqual(
    Buffer.from(signature, 'base64url'),
    Buffer.from(expectedSignature, 'base64url')
  )) {
    return null;
  }
  
  return value;
}

/**
 * Encrypt data using AES-256-CBC with proper IV handling
 */
function encrypt(data: string, secret: string): string {
  // Derive key from secret
  const key = crypto.scryptSync(secret, 'aizako-session-salt', 32);
  
  // Generate random IV
  const iv = crypto.randomBytes(16);
  
  // Create cipher using proper API
  const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
  
  // Encrypt data
  let encrypted = cipher.update(data, 'utf8', 'base64');
  encrypted += cipher.final('base64');
  
  // Combine IV + encrypted data
  const combined = Buffer.concat([
    iv,
    Buffer.from(encrypted, 'base64')
  ]);
  
  return combined.toString('base64url');
}

/**
 * Decrypt data using AES-256-CBC (matching the encrypt function)
 */
function decrypt(encryptedData: string, secret: string): string {
  // Derive key from secret
  const key = crypto.scryptSync(secret, 'aizako-session-salt', 32);
  
  // Parse combined data
  const combined = Buffer.from(encryptedData, 'base64url');
  const iv = combined.subarray(0, 16);
  const encrypted = combined.subarray(16);
  
  // Create decipher using proper API
  const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
  
  // Decrypt data
  let decrypted = decipher.update(encrypted, undefined, 'utf8');
  decrypted += decipher.final('utf8');
  
  return decrypted;
}

/**
 * Create a session for testing/development
 * This is a utility function for development and testing
 */
export function createDevSession(userId: string, tenantId: string): string {
  return createSessionCookie({ userId, tenantId });
}

/**
 * Generate a secure random token for invitations and password resets
 */
export function generateSecureToken(): string {
  return crypto.randomBytes(32).toString('base64url');
}

/**
 * Hash a token for secure storage
 */
export function hashToken(token: string): string {
  return crypto.createHash('sha256').update(token).digest('hex');
}

/**
 * Compare tokens in constant time to prevent timing attacks
 */
export function verifyToken(token: string, hashedToken: string): boolean {
  const tokenHash = hashToken(token);
  
  try {
    return crypto.timingSafeEqual(
      Buffer.from(tokenHash, 'hex'),
      Buffer.from(hashedToken, 'hex')
    );
  } catch {
    return false;
  }
}