import { getSession } from './session';
/**
 * Role hierarchy for permission checking
 */
export const ROLE_HIERARCHY = {
    owner: 4,
    admin: 3,
    member: 2,
    viewer: 1,
};
/**
 * Check if user has required role or higher
 */
export function hasRole(userRole, requiredRole) {
    const userLevel = ROLE_HIERARCHY[userRole] || 0;
    const requiredLevel = ROLE_HIERARCHY[requiredRole] || 0;
    return userLevel >= requiredLevel;
}
/**
 * Basic authentication guard
 * Checks if user is authenticated via session
 */
export async function requireAuth(req) {
    try {
        const session = await getSession(req);
        if (!session) {
            return {
                success: false,
                error: 'Authentication required',
                status: 401,
            };
        }
        return {
            success: true,
            user: session,
        };
    }
    catch (error) {
        console.error('Auth guard error:', error);
        return {
            success: false,
            error: 'Authentication failed',
            status: 401,
        };
    }
}
/**
 * Tenant access guard
 * Checks if user has access to specific tenant
 */
export async function requireTenantAccess(req, tenantId) {
    const authResult = await requireAuth(req);
    if (!authResult.success) {
        return authResult;
    }
    if (authResult.user.tenantId !== tenantId) {
        return {
            success: false,
            error: 'Access denied to this tenant',
            status: 403,
        };
    }
    return authResult;
}
/**
 * Role-based access guard
 * Checks if user has required role within their tenant
 */
export async function requireRole(req, _requiredRole) {
    const authResult = await requireAuth(req);
    if (!authResult.success) {
        return authResult;
    }
    // Note: Role checking requires membership lookup
    // This is a simplified version - full implementation would query Membership model
    // For now, we assume the role is available in session or needs to be fetched
    return authResult;
}
/**
 * Admin guard - requires admin or owner role
 */
export async function requireAdmin(req) {
    return requireRole(req, 'admin');
}
/**
 * Owner guard - requires owner role
 */
export async function requireOwner(req) {
    return requireRole(req, 'owner');
}
/**
 * Create authenticated request object
 */
export function createAuthenticatedRequest(req, user) {
    return Object.assign(req, { user });
}
/**
 * Rate limiting utility
 */
export class RateLimiter {
    constructor(maxAttempts = 5, windowMs = 15 * 60 * 1000 // 15 minutes
    ) {
        Object.defineProperty(this, "maxAttempts", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: maxAttempts
        });
        Object.defineProperty(this, "windowMs", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: windowMs
        });
        Object.defineProperty(this, "attempts", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: new Map()
        });
    }
    /**
     * Check if request should be rate limited
     */
    isRateLimited(identifier) {
        const now = Date.now();
        const record = this.attempts.get(identifier);
        if (!record || now > record.resetTime) {
            // Reset or create new record
            this.attempts.set(identifier, {
                count: 1,
                resetTime: now + this.windowMs,
            });
            return false;
        }
        if (record.count >= this.maxAttempts) {
            return true;
        }
        // Increment attempt count
        record.count += 1;
        return false;
    }
    /**
     * Get remaining attempts for identifier
     */
    getRemainingAttempts(identifier) {
        const now = Date.now();
        const record = this.attempts.get(identifier);
        if (!record || now > record.resetTime) {
            return this.maxAttempts;
        }
        return Math.max(0, this.maxAttempts - record.count);
    }
    /**
     * Get reset time for identifier
     */
    getResetTime(identifier) {
        const record = this.attempts.get(identifier);
        return record ? record.resetTime : null;
    }
    /**
     * Clear attempts for identifier
     */
    clearAttempts(identifier) {
        this.attempts.delete(identifier);
    }
    /**
     * Clean up expired records
     */
    cleanup() {
        const now = Date.now();
        for (const [key, record] of this.attempts.entries()) {
            if (now > record.resetTime) {
                this.attempts.delete(key);
            }
        }
    }
}
/**
 * Create rate limiter for authentication endpoints
 */
export const authRateLimiter = new RateLimiter(5, 15 * 60 * 1000); // 5 attempts per 15 minutes
/**
 * IP-based rate limiting guard
 */
export function requireRateLimit(req, identifier) {
    const clientId = identifier || getClientIdentifier(req);
    if (authRateLimiter.isRateLimited(clientId)) {
        return {
            success: false,
            error: 'Too many attempts. Please try again later.',
            status: 429,
        };
    }
    return { success: true };
}
/**
 * Get client identifier for rate limiting
 */
export function getClientIdentifier(req) {
    // Try to get real IP from various headers
    const forwarded = req.headers.get('x-forwarded-for');
    const realIp = req.headers.get('x-real-ip');
    const remoteAddr = req.headers.get('remote-addr');
    const ip = forwarded?.split(',')[0] || realIp || remoteAddr || 'unknown';
    return ip.trim();
}
/**
 * Security headers utility
 */
export function addSecurityHeaders(response) {
    // Clone response to avoid modifying original
    const newResponse = new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
    });
    // Add security headers
    newResponse.headers.set('X-Content-Type-Options', 'nosniff');
    newResponse.headers.set('X-Frame-Options', 'DENY');
    newResponse.headers.set('X-XSS-Protection', '1; mode=block');
    newResponse.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    newResponse.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
    return newResponse;
}
/**
 * CSRF token utilities
 */
export function generateCSRFToken() {
    return Math.random().toString(36).substring(2, 15) +
        Math.random().toString(36).substring(2, 15);
}
export function validateCSRFToken(token, sessionToken) {
    // In a real implementation, you'd store CSRF tokens securely
    // This is a simplified version
    return token === sessionToken && token.length > 20;
}
