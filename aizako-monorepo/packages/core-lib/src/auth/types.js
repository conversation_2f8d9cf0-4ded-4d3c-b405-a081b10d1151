import { z } from 'zod';
// User type
export const UserSchema = z.object({
    id: z.string(),
    email: z.string().email(),
    displayName: z.string().nullable(),
    photoURL: z.string().nullable(),
    tenantId: z.string(),
    role: z.enum(['admin', 'user', 'guest']),
    createdAt: z.date(),
    updatedAt: z.date(),
});
// Auth state type
export const AuthStateSchema = z.object({
    user: UserSchema.nullable(),
    loading: z.boolean(),
    error: z.string().nullable(),
});
// Firebase config type
export const FirebaseConfigSchema = z.object({
    apiKey: z.string(),
    projectId: z.string(),
    appId: z.string(),
    measurementId: z.string().optional(),
});
// Type guard for User
export function isUser(value) {
    return UserSchema.safeParse(value).success;
}
// Type guard for FirebaseConfig
export function isFirebaseConfig(value) {
    return FirebaseConfigSchema.safeParse(value).success;
}
