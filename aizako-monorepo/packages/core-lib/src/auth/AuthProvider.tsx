'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { AuthContextType, User } from './types';

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: React.ReactNode;
  onAuthStateChanged?: (user: User | null) => void;
}

/**
 * Session-based Auth Provider component
 * Uses session cookies and API endpoints for authentication
 */
export function AuthProvider({ 
  children, 
  onAuthStateChanged 
}: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for existing session on mount
    checkCurrentUser();
  }, []);

  const checkCurrentUser = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/auth/me', {
        credentials: 'include',
      });
      
      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
        
        if (onAuthStateChanged) {
          onAuthStateChanged(data.user);
        }
      } else {
        setUser(null);
        if (onAuthStateChanged) {
          onAuthStateChanged(null);
        }
      }
    } catch (error) {
      console.error('Failed to check current user:', error);
      setUser(null);
      if (onAuthStateChanged) {
        onAuthStateChanged(null);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithEmailPassword = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/auth/signin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Sign in failed');
      }

      const data = await response.json();
      setUser(data.user);
      
      if (onAuthStateChanged) {
        onAuthStateChanged(data.user);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithGoogle = async () => {
    throw new Error('Google sign-in not implemented in session-based auth');
  };

  const register = async (email: string, password: string, displayName?: string) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ 
          email, 
          password, 
          firstName: displayName?.split(' ')[0] || '',
          lastName: displayName?.split(' ').slice(1).join(' ') || ''
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Sign up failed');
      }

      const data = await response.json();
      setUser(data.user);
      
      if (onAuthStateChanged) {
        onAuthStateChanged(data.user);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setIsLoading(true);
    try {
      await fetch('/api/auth/signout', {
        method: 'POST',
        credentials: 'include',
      });
      
      setUser(null);
      if (onAuthStateChanged) {
        onAuthStateChanged(null);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const contextValue: AuthContextType = {
    isAuthenticated: !!user,
    isLoading,
    user,
    loginWithEmailPassword,
    loginWithGoogle,
    register,
    logout,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

/**
 * Hook for using auth context
 */
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}