import { jsx as _jsx } from "react/jsx-runtime";
import { createContext, useContext, useEffect, useState } from 'react';
import { getCurrentUser, signInWithEmailAndPassword, signInWithGoogle as firebaseSignInWithGoogle, signOut as firebaseSignOut, createUserWithEmailAndPassword, getIdToken } from './firebase';
// Create context
const AuthContext = createContext(undefined);
/**
 * Auth Provider component
 */
export function AuthProvider({ children, onAuthStateChanged, syncUserWithBackend }) {
    const [user, setUser] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    // Listen for auth state changes
    useEffect(() => {
        const checkAuthState = async () => {
            try {
                const currentUser = await getCurrentUser();
                setUser(currentUser);
                if (onAuthStateChanged) {
                    onAuthStateChanged(currentUser);
                }
                if (syncUserWithBackend && currentUser) {
                    const token = await getIdToken();
                    await syncUserWithBackend(currentUser, token);
                }
            }
            catch (error) {
                console.error('Error checking auth state:', error);
            }
            finally {
                setIsLoading(false);
            }
        };
        checkAuthState();
    }, [onAuthStateChanged, syncUserWithBackend]);
    // Login with email/password
    const loginWithEmailPassword = async (email, password) => {
        try {
            setIsLoading(true);
            const user = await signInWithEmailAndPassword(email, password);
            setUser(user);
            if (onAuthStateChanged) {
                onAuthStateChanged(user);
            }
            if (syncUserWithBackend) {
                const token = await getIdToken();
                await syncUserWithBackend(user, token);
            }
        }
        catch (error) {
            console.error('Login error:', error);
            throw error;
        }
        finally {
            setIsLoading(false);
        }
    };
    // Login with Google
    const loginWithGoogle = async () => {
        try {
            setIsLoading(true);
            const user = await firebaseSignInWithGoogle();
            setUser(user);
            if (onAuthStateChanged) {
                onAuthStateChanged(user);
            }
            if (syncUserWithBackend) {
                const token = await getIdToken();
                await syncUserWithBackend(user, token);
            }
        }
        catch (error) {
            console.error('Google login error:', error);
            throw error;
        }
        finally {
            setIsLoading(false);
        }
    };
    // Register with email/password
    const register = async (email, password, displayName) => {
        try {
            setIsLoading(true);
            const user = await createUserWithEmailAndPassword(email, password, displayName || '');
            setUser(user);
            if (onAuthStateChanged) {
                onAuthStateChanged(user);
            }
            if (syncUserWithBackend) {
                const token = await getIdToken();
                await syncUserWithBackend(user, token);
            }
        }
        catch (error) {
            console.error('Registration error:', error);
            throw error;
        }
        finally {
            setIsLoading(false);
        }
    };
    // Logout function
    const logout = async () => {
        try {
            setIsLoading(true);
            await firebaseSignOut();
            setUser(null);
            if (onAuthStateChanged) {
                onAuthStateChanged(null);
            }
        }
        catch (error) {
            console.error('Logout error:', error);
            throw error;
        }
        finally {
            setIsLoading(false);
        }
    };
    const contextValue = {
        isAuthenticated: !!user,
        isLoading,
        user,
        loginWithEmailPassword,
        loginWithGoogle,
        register,
        logout
    };
    return (_jsx(AuthContext.Provider, { value: contextValue, children: children }));
}
/**
 * Hook for using auth context
 */
export function useAuth() {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}
