import * as argon2 from 'argon2';
/**
 * Hash password using Argon2id with secure defaults
 * @param password Plain text password
 * @returns Hashed password string
 */
export async function hashPassword(password) {
    if (!password) {
        throw new Error('Password is required');
    }
    try {
        return await argon2.hash(password, {
            type: argon2.argon2id,
            memoryCost: 2 ** 16, // 64MB
            timeCost: 3,
            parallelism: 1,
        });
    }
    catch (error) {
        throw new Error(`Failed to hash password: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
/**
 * Verify password against hash using Argon2id
 * @param password Plain text password
 * @param hash Hashed password from database
 * @returns True if password matches, false otherwise
 */
export async function verifyPassword(password, hash) {
    if (!password || !hash) {
        return false;
    }
    try {
        return await argon2.verify(hash, password);
    }
    catch (error) {
        console.error('Password verification error:', error);
        return false;
    }
}
/**
 * Check if password meets minimum security requirements
 * @param password Password to validate
 * @returns Object with validation result and errors
 */
export function validatePassword(password) {
    const errors = [];
    if (!password) {
        errors.push('Password is required');
        return { valid: false, errors };
    }
    if (password.length < 8) {
        errors.push('Password must be at least 8 characters long');
    }
    if (password.length > 128) {
        errors.push('Password must be no more than 128 characters long');
    }
    if (!/[a-z]/.test(password)) {
        errors.push('Password must contain at least one lowercase letter');
    }
    if (!/[A-Z]/.test(password)) {
        errors.push('Password must contain at least one uppercase letter');
    }
    if (!/[0-9]/.test(password)) {
        errors.push('Password must contain at least one number');
    }
    if (!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>?]/.test(password)) {
        errors.push('Password must contain at least one special character');
    }
    return { valid: errors.length === 0, errors };
}
/**
 * Generate a secure random password
 * @param length Password length (default: 16)
 * @returns Randomly generated password
 */
export function generateSecurePassword(length = 16) {
    if (length < 8 || length > 128) {
        throw new Error('Password length must be between 8 and 128 characters');
    }
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    const allChars = lowercase + uppercase + numbers + symbols;
    // Ensure at least one character from each category
    let password = '';
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += symbols[Math.floor(Math.random() * symbols.length)];
    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
    }
    // Shuffle the password to randomize positions
    return password.split('').sort(() => Math.random() - 0.5).join('');
}
