import mongoose, { Connection } from 'mongoose';

declare global {
  // eslint-disable-next-line no-var
  var mongoose: {
    conn: Connection | null;
    promise: Promise<Connection> | null;
  };
}

const cached = global.mongoose || { conn: null, promise: null };

if (!global.mongoose) {
  global.mongoose = cached;
}

export async function getMongo(): Promise<Connection> {
  const MONGODB_URI = process.env.MONGODB_URI;

  if (!MONGODB_URI) {
    throw new Error('MONGODB_URI missing');
  }

  if (cached.conn) {
    console.log('Using cached MongoDB connection');
    return cached.conn;
  }

  if (!cached.promise) {
    const opts = {
      bufferCommands: false,
      maxPoolSize: 10,
      minPoolSize: 2,
      serverSelectionTimeoutMS: 10000, // Increased from 5000ms
      socketTimeoutMS: 45000,
      connectTimeoutMS: 10000,
      heartbeatFrequencyMS: 10000,
      retryWrites: true,
      retryReads: true,
      maxIdleTimeMS: 30000,
    };

    console.log('Creating new MongoDB connection...');
    cached.promise = mongoose
      .connect(MONGODB_URI, opts)
      .then((mongoose) => {
        console.log('MongoDB connected successfully');
        
        // Set up connection event listeners for monitoring
        mongoose.connection.on('disconnected', () => {
          console.warn('MongoDB disconnected');
        });
        
        mongoose.connection.on('reconnected', () => {
          console.log('MongoDB reconnected');
        });
        
        mongoose.connection.on('error', (error) => {
          console.error('MongoDB connection error:', error);
        });
        
        return mongoose.connection;
      })
      .catch((error) => {
        console.error('MongoDB connection error:', error);
        cached.promise = null;
        throw error;
      });
  }

  try {
    cached.conn = await cached.promise;
  } catch (e) {
    cached.promise = null;
    throw e;
  }

  return cached.conn;
}

export async function closeMongo(): Promise<void> {
  if (cached.conn) {
    await cached.conn.close();
    cached.conn = null;
    cached.promise = null;
    console.log('MongoDB connection closed');
  }
}

export async function pingMongo(): Promise<boolean> {
  try {
    const conn = await getMongo();
    if (!conn.db) {
      console.error('MongoDB connection has no db property');
      return false;
    }
    await conn.db.admin().ping();
    return true;
  } catch (error) {
    console.error('MongoDB ping failed:', error);
    return false;
  }
}

// Alias for consistency with new auth system
export const connectMongo = getMongo;

// Re-export ObjectId for convenience
export { ObjectId } from 'mongodb';