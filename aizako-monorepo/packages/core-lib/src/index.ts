// Main exports from core-lib
// This file should only export client-safe modules to avoid bundling Node.js dependencies

// Re-export only types to avoid conflicts
export type { User as AuthUser } from './auth/types';
export type { Tenant as TenantType } from './tenancy/types';

// Note: 
// - For client components (React): import from '@aizako/core-lib/client'
// - For server components/API routes: import from '@aizako/core-lib/server'
// - For server-only utilities: import from '@aizako/core-lib/server-only'
