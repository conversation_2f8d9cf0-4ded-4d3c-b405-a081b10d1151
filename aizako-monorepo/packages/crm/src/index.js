/**
 * Aizako CRM Module
 *
 * This module provides Customer Relationship Management functionality for the Aizako platform.
 * It includes models, services, components, hooks, and utilities for managing contacts,
 * companies, opportunities, activities, tasks, proposals, and more.
 *
 * @module @aizako/crm
 */
// Components exports - explicit to avoid conflicts
export { ProposalForm, ProposalViewer, ProposalList, ProposalSender, ProposalAIGenerator, ProposalDownloadOptions as ProposalDownloadOptionsComponent, ProposalAnalytics, } from './components';
// Hooks exports
export * from './hooks';
// Model classes exports (runtime values)
export { Contact, Company, Opportunity, Activity, Task, Document as CRMDocument, Proposal, ProposalAnalyticsEvent, } from './models';
// Services exports
export * from './services';
// Utils exports
export * from './utils';
// API exports
export * from './api';
// Constants
export const CRM_MODULE_VERSION = '1.0.0';
export const CRM_MODULE_NAME = 'crm';
// API Routes
export const CRM_API_ROUTES = {
    CONTACTS: '/api/crm/contacts',
    COMPANIES: '/api/crm/companies',
    OPPORTUNITIES: '/api/crm/opportunities',
    ACTIVITIES: '/api/crm/activities',
    TASKS: '/api/crm/tasks',
    DOCUMENTS: '/api/crm/documents',
    PROPOSALS: '/api/crm/proposals',
    EMAIL_TEMPLATES: '/api/crm/email-templates',
    EMAIL_TRACKING: '/api/crm/email-tracking',
    SEQUENCES: '/api/crm/sequences',
    WORKFLOWS: '/api/crm/workflows',
    TAGS: '/api/crm/tags',
    TENANT_DOMAINS: '/api/crm/tenant-domains',
};
