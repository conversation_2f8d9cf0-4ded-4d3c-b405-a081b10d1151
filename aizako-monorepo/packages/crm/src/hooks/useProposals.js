import { useState, useEffect, useCallback } from 'react';
import { useTenant } from '@aizako/core-lib';
import { proposalsApi } from '../api/client/proposals';
import { ApiError } from '../api/client/api-client';
/**
 * Hook for managing proposals
 *
 * This hook provides methods for fetching, creating, updating, and deleting proposals.
 * It also provides methods for sending and downloading proposals.
 *
 * @param options Options for fetching proposals
 * @returns Proposal management methods and state
 *
 * @example
 * ```tsx
 * const {
 *   proposals,
 *   total,
 *   isLoading,
 *   createProposal
 * } = useProposals({
 *   page: 1,
 *   limit: 10
 * });
 * ```
 */
export function useProposals(options = {}) {
    const { tenant } = useTenant();
    const [proposals, setProposals] = useState([]);
    const [total, setTotal] = useState(0);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc', status, opportunityId, companyId, contactId, search, } = options;
    // Note: Tenant handling is done through the apiClient automatically
    // Fetch proposals
    const fetchProposals = useCallback(async () => {
        if (!tenant)
            return;
        setIsLoading(true);
        setError(null);
        try {
            // Use the API client to fetch proposals
            const result = await proposalsApi.getProposals({
                page,
                limit,
                sortBy,
                sortOrder,
                status,
                opportunityId,
                companyId,
                contactId: contactId,
                search,
            });
            setProposals(result.data);
            setTotal(result.total);
        }
        catch (err) {
            const apiError = err instanceof ApiError
                ? new Error(err.message)
                : err instanceof Error
                    ? err
                    : new Error('An unknown error occurred');
            setError(apiError);
            console.error('Error fetching proposals:', err);
        }
        finally {
            setIsLoading(false);
        }
    }, [tenant, page, limit, sortBy, sortOrder, status, opportunityId, companyId, contactId, search]);
    // Fetch proposals on mount and when dependencies change
    useEffect(() => {
        fetchProposals();
    }, [fetchProposals]);
    // Create a new proposal
    const createProposal = async (proposalData) => {
        if (!tenant) {
            throw new Error('Tenant is required');
        }
        try {
            // Use the API client to create a proposal
            const newProposal = await proposalsApi.createProposal(proposalData);
            // Refetch proposals to update the list
            fetchProposals();
            return newProposal;
        }
        catch (err) {
            console.error('Error creating proposal:', err);
            throw err;
        }
    };
    // Update a proposal
    const updateProposal = async (id, proposalData) => {
        if (!tenant) {
            throw new Error('Tenant is required');
        }
        try {
            // Use the API client to update a proposal
            const updatedProposal = await proposalsApi.updateProposal(id, proposalData);
            // Refetch proposals to update the list
            fetchProposals();
            return updatedProposal;
        }
        catch (err) {
            console.error('Error updating proposal:', err);
            throw err;
        }
    };
    // Delete a proposal
    const deleteProposal = async (id) => {
        if (!tenant) {
            throw new Error('Tenant is required');
        }
        try {
            // Use the API client to delete a proposal
            await proposalsApi.deleteProposal(id);
            // Refetch proposals to update the list
            fetchProposals();
            return true;
        }
        catch (err) {
            console.error('Error deleting proposal:', err);
            throw err;
        }
    };
    // Get a proposal by ID
    const getProposalById = async (id) => {
        if (!tenant) {
            throw new Error('Tenant is required');
        }
        try {
            // Use the API client to get a proposal by ID
            return await proposalsApi.getProposalById(id);
        }
        catch (err) {
            console.error('Error getting proposal:', err);
            throw err;
        }
    };
    // Send a proposal
    const sendProposal = async (id, options) => {
        if (!tenant) {
            throw new Error('Tenant is required');
        }
        try {
            // Use the API client to send a proposal
            const sentProposal = await proposalsApi.sendProposal(id, options);
            // Refetch proposals to update the list
            fetchProposals();
            return sentProposal;
        }
        catch (err) {
            console.error('Error sending proposal:', err);
            throw err;
        }
    };
    // Download a proposal
    const downloadProposal = async (id, options) => {
        if (!tenant) {
            throw new Error('Tenant is required');
        }
        try {
            // Use the API client to download a proposal
            return await proposalsApi.downloadProposal(id, options);
        }
        catch (err) {
            console.error('Error downloading proposal:', err);
            throw err;
        }
    };
    // Get proposals with filters
    const getProposals = async (filterOptions = {}) => {
        if (!tenant) {
            throw new Error('Tenant is required');
        }
        try {
            return await proposalsApi.getProposals({
                page: filterOptions.page || page,
                limit: filterOptions.limit || limit,
                sortBy: filterOptions.sortBy || sortBy,
                sortOrder: filterOptions.sortOrder || sortOrder,
                status: filterOptions.status || status,
                opportunityId: filterOptions.opportunityId || opportunityId,
                companyId: filterOptions.companyId || companyId,
                contactId: filterOptions.contactId || contactId,
                search: filterOptions.search || search,
            });
        }
        catch (err) {
            console.error('Error getting proposals:', err);
            throw err;
        }
    };
    return {
        proposals,
        total,
        page,
        limit,
        isLoading,
        error,
        refetch: fetchProposals,
        getProposals,
        createProposal,
        updateProposal,
        deleteProposal,
        getProposalById,
        sendProposal,
        downloadProposal,
    };
}
export default useProposals;
