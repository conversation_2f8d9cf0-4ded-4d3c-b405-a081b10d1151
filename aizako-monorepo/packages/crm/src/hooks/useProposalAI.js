import { useState, useCallback } from 'react';
import { useTenant } from '@aizako/core-lib';
import { CRM_API_ROUTES } from '../api';
/**
 * Hook for AI proposal generation
 */
export function useProposalAI(options = {}) {
    const { tenant } = useTenant();
    const [isGenerating, setIsGenerating] = useState(false);
    const [error, setError] = useState(null);
    const { opportunityId, companyId, contactIds } = options;
    // Generate a complete proposal with AI
    const generateProposal = useCallback(async (params) => {
        if (!tenant) {
            throw new Error('Tenant is required');
        }
        setIsGenerating(true);
        setError(null);
        try {
            const response = await fetch(`${CRM_API_ROUTES.PROPOSALS}/ai/generate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Tenant-ID': tenant.id,
                },
                body: JSON.stringify({
                    prompt: params.prompt,
                    model: params.model,
                    includeSections: params.includeSections,
                    opportunityId,
                    companyId,
                    contactIds,
                }),
            });
            if (!response.ok) {
                throw new Error(`Failed to generate proposal: ${response.statusText}`);
            }
            const generatedProposal = await response.json();
            return generatedProposal;
        }
        catch (err) {
            setError(err instanceof Error ? err : new Error('An unknown error occurred'));
            console.error('Error generating proposal with AI:', err);
            throw err;
        }
        finally {
            setIsGenerating(false);
        }
    }, [tenant, opportunityId, companyId, contactIds]);
    // Generate a single proposal section with AI
    const generateProposalSection = useCallback(async (sectionType, prompt, model) => {
        if (!tenant) {
            throw new Error('Tenant is required');
        }
        setIsGenerating(true);
        setError(null);
        try {
            const response = await fetch(`${CRM_API_ROUTES.PROPOSALS}/ai/generate-section`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Tenant-ID': tenant.id,
                },
                body: JSON.stringify({
                    sectionType,
                    prompt,
                    model,
                    opportunityId,
                    companyId,
                    contactIds,
                }),
            });
            if (!response.ok) {
                throw new Error(`Failed to generate section: ${response.statusText}`);
            }
            const generatedSection = await response.json();
            return generatedSection;
        }
        catch (err) {
            setError(err instanceof Error ? err : new Error('An unknown error occurred'));
            console.error('Error generating section with AI:', err);
            throw err;
        }
        finally {
            setIsGenerating(false);
        }
    }, [tenant, opportunityId, companyId, contactIds]);
    return {
        generateProposal,
        generateProposalSection,
        isGenerating,
        error,
    };
}
export default useProposalAI;
