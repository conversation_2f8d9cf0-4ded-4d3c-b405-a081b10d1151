import mongoose, { Schema } from 'mongoose';
/**
 * Company schema
 */
const CompanySchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    name: { type: String, required: true, trim: true },
    website: { type: String, trim: true },
    industry: { type: String, trim: true },
    size: { type: String, trim: true },
    location: {
        address: { type: String, trim: true },
        city: { type: String, trim: true },
        state: { type: String, trim: true },
        country: { type: String, trim: true },
        postalCode: { type: String, trim: true },
        coordinates: {
            latitude: { type: Number },
            longitude: { type: Number }
        }
    },
    description: { type: String },
    logo: { type: String, trim: true },
    status: {
        type: String,
        enum: ['lead', 'customer', 'prospect', 'partner', 'active', 'inactive'],
        default: 'lead',
        index: true
    },
    annualRevenue: { type: Number },
    employeeCount: { type: Number },
    founded: { type: Date },
    socialProfiles: {
        linkedin: { type: String, trim: true },
        twitter: { type: String, trim: true },
        facebook: { type: String, trim: true },
        instagram: { type: String, trim: true },
        other: { type: Map, of: String }
    },
    notes: { type: String },
    tags: [{ type: String, trim: true }],
    owner: { type: Schema.Types.ObjectId, ref: 'User', index: true },
    customFields: { type: Schema.Types.Mixed, default: {} },
    // Smart Interaction Timeline
    interactions: [{
            id: { type: String, required: true },
            type: {
                type: String,
                required: true,
                enum: ['email', 'call', 'meeting', 'chat', 'social', 'note', 'task', 'other']
            },
            source: { type: String, required: true },
            sourceId: { type: String },
            timestamp: { type: Date, required: true, index: true },
            summary: { type: String, required: true },
            sentiment: {
                type: String,
                enum: ['positive', 'neutral', 'negative']
            },
            direction: {
                type: String,
                enum: ['inbound', 'outbound']
            },
            participants: [{
                    id: { type: String },
                    email: { type: String },
                    name: { type: String },
                    role: { type: String }
                }],
            content: {
                text: { type: String },
                html: { type: String },
                attachments: [{
                        name: { type: String, required: true },
                        type: { type: String, required: true },
                        url: { type: String }
                    }]
            },
            metadata: { type: Schema.Types.Mixed, default: {} },
            nextAction: {
                type: {
                    type: String,
                    enum: ['email', 'call', 'meeting', 'task', 'other']
                },
                description: { type: String },
                dueDate: { type: Date },
                priority: {
                    type: String,
                    enum: ['low', 'medium', 'high']
                }
            },
            aiGenerated: { type: Boolean, required: true, default: false },
            aiConfidence: { type: Number, min: 0, max: 1 }
        }],
    // Company insights
    insights: {
        summary: { type: String },
        strengths: [{ type: String }],
        weaknesses: [{ type: String }],
        opportunities: [{ type: String }],
        threats: [{ type: String }],
        keyDecisionMakers: [{
                name: { type: String },
                title: { type: String },
                influence: {
                    type: String,
                    enum: ['high', 'medium', 'low']
                },
                contactId: { type: Schema.Types.ObjectId, ref: 'Contact' }
            }],
        competitorAnalysis: [{
                name: { type: String, required: true },
                strengths: [{ type: String }],
                weaknesses: [{ type: String }],
                marketShare: { type: Number }
            }],
        newsAndEvents: [{
                title: { type: String, required: true },
                date: { type: Date, required: true },
                summary: { type: String, required: true },
                url: { type: String },
                sentiment: {
                    type: String,
                    enum: ['positive', 'neutral', 'negative']
                }
            }],
        aiConfidence: { type: Number, min: 0, max: 1 },
        lastUpdated: { type: Date, default: Date.now }
    },
    // Graph-based company scoring
    score: {
        current: { type: Number, min: 0, max: 100 },
        previous: { type: Number, min: 0, max: 100 },
        change: { type: Number },
        lastUpdated: { type: Date },
        factors: {
            interactions: { type: Number, min: 0, max: 100, default: 0 },
            opportunities: { type: Number, min: 0, max: 100, default: 0 },
            engagement: { type: Number, min: 0, max: 100, default: 0 },
            recency: { type: Number, min: 0, max: 100, default: 0 },
            revenue: { type: Number, min: 0, max: 100, default: 0 },
            growth: { type: Number, min: 0, max: 100, default: 0 }
        },
        potentialValue: { type: Number, min: 0 },
        history: [{
                value: { type: Number, min: 0, max: 100, required: true },
                timestamp: { type: Date, required: true },
                factors: { type: Schema.Types.Mixed }
            }]
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Create indexes
CompanySchema.index({ name: 1, tenantId: 1 }, { unique: true });
CompanySchema.index({ status: 1, tenantId: 1 });
CompanySchema.index({ industry: 1, tenantId: 1 });
CompanySchema.index({ owner: 1, tenantId: 1 });
// Compound index for name search
CompanySchema.index({ tenantId: 1, name: 'text', industry: 'text' });
// Index for interactions
CompanySchema.index({ tenantId: 1, 'interactions.timestamp': -1 });
CompanySchema.index({ tenantId: 1, 'interactions.type': 1, 'interactions.timestamp': -1 });
CompanySchema.index({ tenantId: 1, 'interactions.source': 1, 'interactions.sourceId': 1 });
// Index for scoring
CompanySchema.index({ tenantId: 1, 'score.current': -1 });
CompanySchema.index({ tenantId: 1, 'score.lastUpdated': -1 });
// Create the model
export const Company = mongoose.models.Company || mongoose.model('Company', CompanySchema);
