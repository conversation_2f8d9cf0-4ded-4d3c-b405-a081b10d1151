import mongoose, { Schema } from 'mongoose';
/**
 * Tenant domain schema
 */
const TenantDomainSchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    domain: { type: String, required: true, trim: true, lowercase: true },
    isPrimary: { type: Boolean, default: false },
    status: {
        type: String,
        enum: ['pending', 'active', 'failed', 'disabled'],
        default: 'pending',
        index: true
    },
    verified: { type: Boolean, default: false, index: true },
    verificationMethod: {
        type: String,
        enum: ['dns', 'file', 'email'],
        default: 'dns'
    },
    verificationToken: { type: String },
    verificationTokenExpiresAt: { type: Date },
    verifiedAt: { type: Date },
    dnsRecords: [{
            type: {
                type: String,
                enum: ['TXT', 'CNAME', 'MX', 'A'],
                required: true
            },
            host: { type: String, required: true, trim: true },
            value: { type: String, required: true, trim: true },
            ttl: { type: Number },
            priority: { type: Number }, // For MX records
            verified: { type: Boolean, default: false },
            verifiedAt: { type: Date }
        }],
    services: [{
            service: {
                type: String,
                enum: ['email', 'tracking', 'website', 'auth', 'api', 'other'],
                required: true
            },
            enabled: { type: Boolean, default: false },
            status: {
                type: String,
                enum: ['pending', 'active', 'failed', 'disabled'],
                default: 'pending'
            },
            requiredRecords: [{
                    type: {
                        type: String,
                        enum: ['TXT', 'CNAME', 'MX', 'A'],
                        required: true
                    },
                    host: { type: String, required: true, trim: true },
                    value: { type: String, required: true, trim: true },
                    ttl: { type: Number },
                    priority: { type: Number }, // For MX records
                    verified: { type: Boolean, default: false },
                    verifiedAt: { type: Date }
                }],
            lastCheckedAt: { type: Date },
            errorMessage: { type: String },
            metadata: { type: Schema.Types.Mixed }
        }],
    // Email configuration
    emailConfig: {
        provider: {
            type: String,
            enum: ['resend', 'sendgrid', 'mailgun', 'ses', 'smtp', 'other'],
            default: 'resend'
        },
        fromEmail: { type: String, trim: true, lowercase: true },
        fromName: { type: String, trim: true },
        replyToEmail: { type: String, trim: true, lowercase: true },
        bccEmail: { type: String, trim: true, lowercase: true },
        dkimEnabled: { type: Boolean, default: true },
        spfEnabled: { type: Boolean, default: true },
        dmarcEnabled: { type: Boolean, default: true },
        trackingEnabled: { type: Boolean, default: true },
        trackingDomain: { type: String, trim: true, lowercase: true },
        unsubscribeEnabled: { type: Boolean, default: true },
        unsubscribeLink: { type: String, trim: true },
        footerEnabled: { type: Boolean, default: true },
        footerText: { type: String },
        logoEnabled: { type: Boolean, default: false },
        logoUrl: { type: String, trim: true },
        providerDomainId: { type: String },
        providerStatus: { type: String },
        providerSettings: { type: Schema.Types.Mixed }
    },
    // Tracking configuration
    trackingConfig: {
        provider: {
            type: String,
            enum: ['resend', 'sendgrid', 'mailgun', 'custom'],
            default: 'resend'
        },
        domain: { type: String, trim: true, lowercase: true },
        enabled: { type: Boolean, default: true },
        clickTracking: { type: Boolean, default: true },
        openTracking: { type: Boolean, default: true },
        linkRewriting: { type: Boolean, default: true },
        providerDomainId: { type: String },
        providerStatus: { type: String },
        providerSettings: { type: Schema.Types.Mixed }
    },
    notes: { type: String },
    tags: [{ type: String, trim: true }],
    customFields: { type: Schema.Types.Mixed, default: {} }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Create indexes
TenantDomainSchema.index({ domain: 1 }, { unique: true });
TenantDomainSchema.index({ tenantId: 1, isPrimary: 1 });
TenantDomainSchema.index({ tenantId: 1, status: 1 });
TenantDomainSchema.index({ tenantId: 1, verified: 1 });
TenantDomainSchema.index({ 'services.service': 1, 'services.status': 1, tenantId: 1 });
TenantDomainSchema.index({ 'emailConfig.provider': 1, tenantId: 1 });
TenantDomainSchema.index({ 'trackingConfig.provider': 1, tenantId: 1 });
// Create the model
export const TenantDomain = mongoose.models.TenantDomain || mongoose.model('TenantDomain', TenantDomainSchema);
export default TenantDomain;
