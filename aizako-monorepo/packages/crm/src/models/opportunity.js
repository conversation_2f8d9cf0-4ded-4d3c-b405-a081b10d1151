import mongoose, { Schema } from 'mongoose';
/**
 * Opportunity schema
 */
const OpportunitySchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    name: { type: String, required: true, trim: true },
    companyId: { type: Schema.Types.ObjectId, ref: 'Company', required: true, index: true },
    contacts: [{ type: Schema.Types.ObjectId, ref: 'Contact', index: true }],
    stage: {
        type: String,
        enum: ['qualification', 'discovery', 'proposal', 'negotiation', 'closed_won', 'closed_lost'],
        default: 'qualification',
        index: true
    },
    amount: { type: Number, required: true, index: true },
    currency: { type: String, default: 'USD', trim: true },
    closeDate: { type: Date, index: true },
    probability: { type: Number, min: 0, max: 100 },
    description: { type: String },
    source: { type: String, trim: true },
    notes: { type: String },
    tags: [{ type: String, trim: true }],
    owner: { type: Schema.Types.ObjectId, ref: 'User', index: true },
    customFields: { type: Schema.Types.Mixed, default: {} },
    // Deal tracking
    stageHistory: [{
            fromStage: { type: String, required: true },
            toStage: { type: String, required: true },
            timestamp: { type: Date, required: true },
            daysInStage: { type: Number, required: true },
            reason: { type: String },
            userId: { type: Schema.Types.ObjectId, ref: 'User' }
        }],
    expectedRevenue: { type: Number },
    forecastCategory: {
        type: String,
        enum: ['pipeline', 'best_case', 'commit', 'closed'],
        index: true
    },
    competitors: [{
            name: { type: String, required: true },
            strengths: [{ type: String }],
            weaknesses: [{ type: String }],
            winProbability: { type: Number, min: 0, max: 100 }
        }],
    // Smart Interaction Timeline
    interactions: [{
            id: { type: String, required: true },
            type: {
                type: String,
                required: true,
                enum: ['email', 'call', 'meeting', 'chat', 'social', 'note', 'task', 'other']
            },
            source: { type: String, required: true },
            sourceId: { type: String },
            timestamp: { type: Date, required: true, index: true },
            summary: { type: String, required: true },
            sentiment: {
                type: String,
                enum: ['positive', 'neutral', 'negative']
            },
            direction: {
                type: String,
                enum: ['inbound', 'outbound']
            },
            participants: [{
                    id: { type: String },
                    email: { type: String },
                    name: { type: String },
                    role: { type: String }
                }],
            content: {
                text: { type: String },
                html: { type: String },
                attachments: [{
                        name: { type: String, required: true },
                        type: { type: String, required: true },
                        url: { type: String }
                    }]
            },
            metadata: { type: Schema.Types.Mixed, default: {} },
            nextAction: {
                type: {
                    type: String,
                    enum: ['email', 'call', 'meeting', 'task', 'other']
                },
                description: { type: String },
                dueDate: { type: Date },
                priority: {
                    type: String,
                    enum: ['low', 'medium', 'high']
                }
            },
            aiGenerated: { type: Boolean, required: true, default: false },
            aiConfidence: { type: Number, min: 0, max: 1 }
        }],
    // Deal insights
    insights: {
        summary: { type: String },
        winFactors: [{ type: String }],
        riskFactors: [{ type: String }],
        nextSteps: [{ type: String }],
        keyDecisionCriteria: [{ type: String }],
        buyingProcess: { type: String },
        competitiveLandscape: { type: String },
        aiConfidence: { type: Number, min: 0, max: 1 },
        lastUpdated: { type: Date, default: Date.now }
    },
    // Objection handling
    objections: [{
            id: { type: String, required: true },
            text: { type: String, required: true },
            category: { type: String },
            status: {
                type: String,
                enum: ['active', 'addressed', 'resolved'],
                default: 'active'
            },
            severity: {
                type: String,
                enum: ['low', 'medium', 'high'],
                default: 'medium'
            },
            createdAt: { type: Date, default: Date.now },
            resolvedAt: { type: Date },
            resolution: { type: String },
            contactId: { type: Schema.Types.ObjectId, ref: 'Contact' },
            aiGenerated: { type: Boolean, default: false },
            aiSuggestedResponse: { type: String }
        }],
    // Deal scoring
    score: {
        current: { type: Number, min: 0, max: 100 },
        previous: { type: Number, min: 0, max: 100 },
        change: { type: Number },
        lastUpdated: { type: Date },
        factors: {
            engagement: { type: Number, min: 0, max: 100, default: 0 },
            momentum: { type: Number, min: 0, max: 100, default: 0 },
            champion: { type: Number, min: 0, max: 100, default: 0 },
            needAlignment: { type: Number, min: 0, max: 100, default: 0 },
            competitivePosition: { type: Number, min: 0, max: 100, default: 0 },
            budgetConfirmation: { type: Number, min: 0, max: 100, default: 0 }
        },
        winProbability: { type: Number, min: 0, max: 100, default: 0 },
        history: [{
                value: { type: Number, min: 0, max: 100, required: true },
                timestamp: { type: Date, required: true },
                factors: { type: Schema.Types.Mixed }
            }]
    },
    // Proposal data
    proposal: {
        id: { type: Schema.Types.ObjectId, ref: 'Proposal' },
        status: {
            type: String,
            enum: ['draft', 'sent', 'viewed', 'accepted', 'rejected']
        },
        sentAt: { type: Date },
        viewedAt: { type: Date },
        acceptedAt: { type: Date },
        rejectedAt: { type: Date },
        viewCount: { type: Number, default: 0 },
        lastViewedAt: { type: Date },
        feedback: { type: String }
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Create indexes
OpportunitySchema.index({ name: 1, tenantId: 1 });
OpportunitySchema.index({ companyId: 1, tenantId: 1 });
OpportunitySchema.index({ stage: 1, tenantId: 1 });
OpportunitySchema.index({ closeDate: 1, tenantId: 1 });
OpportunitySchema.index({ amount: 1, tenantId: 1 });
OpportunitySchema.index({ owner: 1, tenantId: 1 });
OpportunitySchema.index({ 'score.winProbability': -1, tenantId: 1 });
OpportunitySchema.index({ 'proposal.status': 1, tenantId: 1 });
OpportunitySchema.index({ forecastCategory: 1, tenantId: 1 });
// Compound index for name search
OpportunitySchema.index({ tenantId: 1, name: 'text', description: 'text' });
// Index for interactions
OpportunitySchema.index({ tenantId: 1, 'interactions.timestamp': -1 });
OpportunitySchema.index({ tenantId: 1, 'interactions.type': 1, 'interactions.timestamp': -1 });
// Index for objections
OpportunitySchema.index({ tenantId: 1, 'objections.status': 1 });
OpportunitySchema.index({ tenantId: 1, 'objections.severity': 1 });
// Create the model
export const Opportunity = mongoose.models.Opportunity || mongoose.model('Opportunity', OpportunitySchema);
