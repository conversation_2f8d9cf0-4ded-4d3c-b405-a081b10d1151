import mongoose, { Schema } from 'mongoose';
/**
 * Relationship schema
 */
const RelationshipSchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    sourceType: {
        type: String,
        enum: ['contact', 'company'],
        required: true,
        index: true
    },
    sourceId: { type: Schema.Types.ObjectId, required: true, index: true },
    targetType: {
        type: String,
        enum: ['contact', 'company'],
        required: true,
        index: true
    },
    targetId: { type: Schema.Types.ObjectId, required: true, index: true },
    relationshipType: { type: String, required: true, trim: true, index: true },
    strength: { type: Number, min: 1, max: 10 },
    direction: {
        type: String,
        enum: ['bidirectional', 'source_to_target', 'target_to_source'],
        default: 'bidirectional'
    },
    status: {
        type: String,
        enum: ['active', 'inactive', 'pending'],
        default: 'active',
        index: true
    },
    description: { type: String },
    notes: { type: String },
    tags: [{ type: String, trim: true }],
    metadata: { type: Schema.Types.Mixed, default: {} },
    // Relationship insights
    insights: {
        influence: {
            type: String,
            enum: ['high', 'medium', 'low']
        },
        frequency: {
            type: String,
            enum: ['daily', 'weekly', 'monthly', 'rarely']
        },
        lastInteraction: { type: Date },
        interactionCount: { type: Number, default: 0 },
        sentiment: {
            type: String,
            enum: ['positive', 'neutral', 'negative']
        },
        aiConfidence: { type: Number, min: 0, max: 1 },
        lastUpdated: { type: Date, default: Date.now }
    },
    // Verification
    verifiedAt: { type: Date },
    verifiedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    isVerified: { type: Boolean, default: false, index: true },
    // Tracking
    discoveredAt: { type: Date, default: Date.now },
    discoveredBy: { type: Schema.Types.ObjectId, ref: 'User' },
    discoverySource: { type: String, trim: true },
    customFields: { type: Schema.Types.Mixed, default: {} }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Create indexes
RelationshipSchema.index({ sourceType: 1, sourceId: 1, tenantId: 1 });
RelationshipSchema.index({ targetType: 1, targetId: 1, tenantId: 1 });
RelationshipSchema.index({ relationshipType: 1, tenantId: 1 });
RelationshipSchema.index({ strength: -1, tenantId: 1 });
RelationshipSchema.index({ status: 1, tenantId: 1 });
RelationshipSchema.index({ isVerified: 1, tenantId: 1 });
// Compound index for relationship lookup
RelationshipSchema.index({
    tenantId: 1,
    sourceType: 1,
    sourceId: 1,
    targetType: 1,
    targetId: 1
}, { unique: true });
// Compound index for text search
RelationshipSchema.index({ tenantId: 1, relationshipType: 'text', description: 'text', notes: 'text' });
// Create the model
export const Relationship = mongoose.models.Relationship || mongoose.model('Relationship', RelationshipSchema);
export default Relationship;
