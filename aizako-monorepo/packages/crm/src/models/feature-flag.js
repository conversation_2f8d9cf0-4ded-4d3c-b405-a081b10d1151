import mongoose, { Schema } from 'mongoose';
/**
 * Feature flag schema
 */
const FeatureFlagSchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    key: { type: String, required: true, trim: true, index: true },
    name: { type: String, required: true, trim: true },
    description: { type: String },
    type: {
        type: String,
        enum: ['boolean', 'string', 'number', 'json', 'multivariate'],
        required: true,
        index: true
    },
    status: {
        type: String,
        enum: ['active', 'inactive', 'archived'],
        default: 'active',
        index: true
    },
    environment: {
        type: String,
        enum: ['development', 'staging', 'production', 'all'],
        default: 'all',
        index: true
    },
    // Default values
    defaultValue: { type: Schema.Types.Mixed, required: true },
    defaultEnabled: { type: Boolean, default: false },
    // Targeting rules
    rules: [{
            id: { type: String, required: true },
            name: { type: String, required: true, trim: true },
            description: { type: String },
            conditions: [{
                    attribute: { type: String, required: true, trim: true },
                    operator: {
                        type: String,
                        enum: ['equals', 'not_equals', 'contains', 'not_contains', 'greater_than', 'less_than', 'in', 'not_in', 'exists', 'not_exists'],
                        required: true
                    },
                    value: { type: Schema.Types.Mixed, required: true },
                    valueType: {
                        type: String,
                        enum: ['string', 'number', 'boolean', 'array', 'date'],
                        required: true
                    }
                }],
            conditionLogic: {
                type: String,
                enum: ['and', 'or'],
                default: 'and'
            },
            rolloutPercentage: { type: Number, min: 0, max: 100, default: 100 },
            isActive: { type: Boolean, default: true },
            priority: { type: Number, default: 0 },
            createdAt: { type: Date, default: Date.now },
            updatedAt: { type: Date, default: Date.now }
        }],
    // Multivariate testing
    variants: [{
            id: { type: String, required: true },
            name: { type: String, required: true, trim: true },
            description: { type: String },
            value: { type: Schema.Types.Mixed, required: true },
            weight: { type: Number, min: 0, max: 100, required: true },
            isControl: { type: Boolean, default: false },
            isActive: { type: Boolean, default: true }
        }],
    // Rollout configuration
    rollout: {
        enabled: { type: Boolean, default: false },
        percentage: { type: Number, min: 0, max: 100, default: 0 },
        strategy: {
            type: String,
            enum: ['percentage', 'user_id', 'custom'],
            default: 'percentage'
        },
        customStrategy: { type: String }
    },
    // Scheduling
    schedule: {
        startDate: { type: Date },
        endDate: { type: Date },
        timezone: { type: String, default: 'UTC' },
        isScheduled: { type: Boolean, default: false }
    },
    // Prerequisites
    prerequisites: [{
            flagKey: { type: String, required: true, trim: true },
            variation: { type: Schema.Types.Mixed, required: true }
        }],
    // Analytics and tracking
    analytics: {
        impressions: { type: Number, default: 0 },
        uniqueUsers: { type: Number, default: 0 },
        conversions: { type: Number, default: 0 },
        conversionRate: { type: Number, min: 0, max: 100, default: 0 },
        lastImpression: { type: Date },
        lastConversion: { type: Date }
    },
    // Audit trail
    auditLog: [{
            action: {
                type: String,
                enum: ['created', 'updated', 'enabled', 'disabled', 'archived', 'rule_added', 'rule_removed', 'variant_added', 'variant_removed'],
                required: true
            },
            userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
            userName: { type: String, trim: true },
            timestamp: { type: Date, default: Date.now },
            changes: { type: Schema.Types.Mixed },
            reason: { type: String }
        }],
    // Metadata
    owner: { type: Schema.Types.ObjectId, ref: 'User', required: true, index: true },
    team: [{ type: Schema.Types.ObjectId, ref: 'User' }],
    createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    updatedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    tags: [{ type: String, trim: true }],
    customFields: { type: Schema.Types.Mixed, default: {} },
    // Integration settings
    integrations: {
        webhook: {
            url: { type: String, trim: true },
            events: [{ type: String, trim: true }],
            headers: { type: Map, of: String }
        },
        slack: {
            channel: { type: String, trim: true },
            events: [{ type: String, trim: true }]
        }
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Create indexes
FeatureFlagSchema.index({ key: 1, tenantId: 1 }, { unique: true });
FeatureFlagSchema.index({ status: 1, tenantId: 1 });
FeatureFlagSchema.index({ type: 1, tenantId: 1 });
FeatureFlagSchema.index({ environment: 1, tenantId: 1 });
FeatureFlagSchema.index({ owner: 1, tenantId: 1 });
FeatureFlagSchema.index({ team: 1, tenantId: 1 });
FeatureFlagSchema.index({ 'rollout.enabled': 1, tenantId: 1 });
FeatureFlagSchema.index({ 'schedule.startDate': 1, 'schedule.endDate': 1, tenantId: 1 });
FeatureFlagSchema.index({ 'analytics.impressions': -1, tenantId: 1 });
FeatureFlagSchema.index({ 'analytics.conversionRate': -1, tenantId: 1 });
// Compound index for active flags
FeatureFlagSchema.index({ tenantId: 1, status: 1, environment: 1, 'rollout.enabled': 1 });
// Compound index for text search
FeatureFlagSchema.index({ tenantId: 1, name: 'text', description: 'text', key: 'text' });
// Create the model
export const FeatureFlag = mongoose.models.FeatureFlag || mongoose.model('FeatureFlag', FeatureFlagSchema);
export default FeatureFlag;
