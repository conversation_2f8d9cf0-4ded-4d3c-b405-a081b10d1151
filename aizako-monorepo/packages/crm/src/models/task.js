import mongoose, { Schema } from 'mongoose';
/**
 * Task schema
 */
const TaskSchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    title: { type: String, required: true, trim: true },
    description: { type: String },
    status: {
        type: String,
        enum: ['not_started', 'in_progress', 'completed', 'deferred', 'canceled'],
        default: 'not_started',
        index: true
    },
    priority: {
        type: String,
        enum: ['low', 'medium', 'high', 'urgent'],
        default: 'medium',
        index: true
    },
    dueDate: { type: Date, index: true },
    startDate: { type: Date },
    completedDate: { type: Date },
    estimatedHours: { type: Number, min: 0 },
    actualHours: { type: Number, min: 0 },
    progress: { type: Number, min: 0, max: 100, default: 0 },
    contactId: { type: Schema.Types.ObjectId, ref: 'Contact', index: true },
    companyId: { type: Schema.Types.ObjectId, ref: 'Company', index: true },
    opportunityId: { type: Schema.Types.ObjectId, ref: 'Opportunity', index: true },
    owner: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    assignedTo: { type: Schema.Types.ObjectId, ref: 'User', required: true, index: true },
    completedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    parentTaskId: { type: Schema.Types.ObjectId, ref: 'Task', index: true },
    subtasks: [{
            id: { type: String, required: true },
            title: { type: String, required: true, trim: true },
            status: {
                type: String,
                enum: ['not_started', 'in_progress', 'completed', 'canceled'],
                default: 'not_started'
            },
            dueDate: { type: Date },
            completedDate: { type: Date },
            assignedTo: { type: Schema.Types.ObjectId, ref: 'User' }
        }],
    reminders: [{
            id: { type: String, required: true },
            time: { type: Date, required: true },
            sent: { type: Boolean, default: false },
            sentAt: { type: Date },
            method: {
                type: String,
                enum: ['email', 'notification', 'sms'],
                default: 'notification'
            }
        }],
    recurrence: {
        pattern: {
            type: String,
            enum: ['daily', 'weekly', 'monthly', 'yearly']
        },
        interval: { type: Number, min: 1, default: 1 },
        daysOfWeek: [{ type: Number, min: 0, max: 6 }],
        dayOfMonth: { type: Number, min: 1, max: 31 },
        monthOfYear: { type: Number, min: 0, max: 11 },
        endDate: { type: Date },
        occurrences: { type: Number, min: 1 },
        seriesId: { type: String }
    },
    isRecurring: { type: Boolean, default: false, index: true },
    source: {
        type: String,
        enum: ['manual', 'sequence', 'workflow', 'ai', 'integration'],
        default: 'manual',
        index: true
    },
    sourceId: { type: String },
    activityId: { type: Schema.Types.ObjectId, ref: 'Activity' },
    tags: [{ type: String, trim: true }],
    customFields: { type: Schema.Types.Mixed, default: {} },
    // AI-generated content
    aiGenerated: { type: Boolean, default: false, index: true },
    aiPrompt: { type: String },
    aiModel: { type: String },
    aiConfidence: { type: Number, min: 0, max: 1 },
    // AI-generated suggestions
    aiSuggestions: [{
            id: { type: String, required: true },
            type: {
                type: String,
                enum: ['next_step', 'resource', 'contact', 'timing', 'approach'],
                required: true
            },
            content: { type: String, required: true },
            confidence: { type: Number, min: 0, max: 1, required: true },
            accepted: { type: Boolean, default: false },
            acceptedAt: { type: Date },
            rejected: { type: Boolean, default: false },
            rejectedAt: { type: Date }
        }]
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Create indexes
TaskSchema.index({ title: 1, tenantId: 1 });
TaskSchema.index({ status: 1, tenantId: 1 });
TaskSchema.index({ priority: 1, tenantId: 1 });
TaskSchema.index({ dueDate: 1, tenantId: 1 });
TaskSchema.index({ assignedTo: 1, tenantId: 1 });
TaskSchema.index({ owner: 1, tenantId: 1 });
TaskSchema.index({ 'reminders.time': 1, 'reminders.sent': 1, tenantId: 1 });
TaskSchema.index({ source: 1, sourceId: 1, tenantId: 1 });
// Compound index for text search
TaskSchema.index({ tenantId: 1, title: 'text', description: 'text' });
// Create the model
export const Task = mongoose.models.Task || mongoose.model('Task', TaskSchema);
export default Task;
