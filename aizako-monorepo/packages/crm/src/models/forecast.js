import mongoose, { Schema } from 'mongoose';
/**
 * Forecast schema
 */
const ForecastSchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    name: { type: String, required: true, trim: true },
    description: { type: String },
    type: {
        type: String,
        enum: ['revenue', 'deals', 'activities', 'custom'],
        required: true,
        index: true
    },
    period: {
        startDate: { type: Date, required: true, index: true },
        endDate: { type: Date, required: true, index: true },
        quarter: { type: Number, min: 1, max: 4 },
        year: { type: Number, required: true, index: true },
        month: { type: Number, min: 1, max: 12 },
        week: { type: Number, min: 1, max: 53 }
    },
    status: {
        type: String,
        enum: ['draft', 'submitted', 'approved', 'rejected', 'archived'],
        default: 'draft',
        index: true
    },
    owner: { type: Schema.Types.ObjectId, ref: 'User', required: true, index: true },
    team: [{ type: Schema.Types.ObjectId, ref: 'User' }],
    territory: { type: String, trim: true, index: true },
    // Revenue forecast data
    revenue: {
        pipeline: { type: Number, default: 0 },
        bestCase: { type: Number, default: 0 },
        commit: { type: Number, default: 0 },
        closed: { type: Number, default: 0 },
        target: { type: Number, default: 0 },
        quota: { type: Number, default: 0 },
        attainment: { type: Number, min: 0, max: 200, default: 0 },
        opportunities: [{
                opportunityId: { type: Schema.Types.ObjectId, ref: 'Opportunity', required: true },
                name: { type: String, required: true, trim: true },
                amount: { type: Number, required: true },
                probability: { type: Number, min: 0, max: 100, required: true },
                weightedAmount: { type: Number, required: true },
                stage: { type: String, required: true },
                closeDate: { type: Date, required: true },
                owner: { type: Schema.Types.ObjectId, ref: 'User', required: true },
                category: {
                    type: String,
                    enum: ['pipeline', 'best_case', 'commit', 'closed'],
                    required: true
                },
                lastUpdated: { type: Date, default: Date.now }
            }]
    },
    // Deal count forecast
    deals: {
        pipeline: { type: Number, default: 0 },
        bestCase: { type: Number, default: 0 },
        commit: { type: Number, default: 0 },
        closed: { type: Number, default: 0 },
        target: { type: Number, default: 0 },
        opportunities: [{
                opportunityId: { type: Schema.Types.ObjectId, ref: 'Opportunity', required: true },
                name: { type: String, required: true, trim: true },
                amount: { type: Number, required: true },
                probability: { type: Number, min: 0, max: 100, required: true },
                weightedAmount: { type: Number, required: true },
                stage: { type: String, required: true },
                closeDate: { type: Date, required: true },
                owner: { type: Schema.Types.ObjectId, ref: 'User', required: true },
                category: {
                    type: String,
                    enum: ['pipeline', 'best_case', 'commit', 'closed'],
                    required: true
                },
                lastUpdated: { type: Date, default: Date.now }
            }]
    },
    // Activity forecast
    activities: {
        calls: {
            target: { type: Number, default: 0 },
            actual: { type: Number, default: 0 }
        },
        emails: {
            target: { type: Number, default: 0 },
            actual: { type: Number, default: 0 }
        },
        meetings: {
            target: { type: Number, default: 0 },
            actual: { type: Number, default: 0 }
        },
        demos: {
            target: { type: Number, default: 0 },
            actual: { type: Number, default: 0 }
        },
        proposals: {
            target: { type: Number, default: 0 },
            actual: { type: Number, default: 0 }
        }
    },
    // Historical data
    history: [{
            date: { type: Date, required: true },
            pipeline: { type: Number, required: true },
            bestCase: { type: Number, required: true },
            commit: { type: Number, required: true },
            closed: { type: Number, required: true },
            changeReason: { type: String },
            updatedBy: { type: Schema.Types.ObjectId, ref: 'User', required: true }
        }],
    // AI predictions
    aiPredictions: {
        predictedRevenue: { type: Number },
        confidence: { type: Number, min: 0, max: 1 },
        factors: [{
                factor: { type: String, required: true },
                impact: { type: Number, required: true },
                confidence: { type: Number, min: 0, max: 1, required: true }
            }],
        risks: [{
                risk: { type: String, required: true },
                probability: { type: Number, min: 0, max: 1, required: true },
                impact: { type: Number, required: true },
                mitigation: { type: String }
            }],
        opportunities: [{
                opportunity: { type: String, required: true },
                probability: { type: Number, min: 0, max: 1, required: true },
                impact: { type: Number, required: true },
                action: { type: String }
            }],
        lastUpdated: { type: Date, default: Date.now },
        model: { type: String }
    },
    // Submission tracking
    submittedAt: { type: Date },
    submittedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    approvedAt: { type: Date },
    approvedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    rejectedAt: { type: Date },
    rejectedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    rejectionReason: { type: String },
    // Comments and notes
    comments: [{
            id: { type: String, required: true },
            text: { type: String, required: true },
            userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
            userName: { type: String },
            createdAt: { type: Date, default: Date.now },
            isInternal: { type: Boolean, default: false }
        }],
    notes: { type: String },
    tags: [{ type: String, trim: true }],
    customFields: { type: Schema.Types.Mixed, default: {} }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Create indexes
ForecastSchema.index({ name: 1, tenantId: 1 });
ForecastSchema.index({ type: 1, tenantId: 1 });
ForecastSchema.index({ status: 1, tenantId: 1 });
ForecastSchema.index({ owner: 1, tenantId: 1 });
ForecastSchema.index({ 'period.year': 1, 'period.quarter': 1, tenantId: 1 });
ForecastSchema.index({ 'period.startDate': 1, 'period.endDate': 1, tenantId: 1 });
ForecastSchema.index({ territory: 1, tenantId: 1 });
ForecastSchema.index({ team: 1, tenantId: 1 });
ForecastSchema.index({ submittedAt: 1, tenantId: 1 });
ForecastSchema.index({ approvedAt: 1, tenantId: 1 });
// Compound index for forecast lookup
ForecastSchema.index({ tenantId: 1, owner: 1, 'period.year': 1, 'period.quarter': 1 });
// Compound index for text search
ForecastSchema.index({ tenantId: 1, name: 'text', description: 'text', notes: 'text' });
// Create the model
export const Forecast = mongoose.models.Forecast || mongoose.model('Forecast', ForecastSchema);
export default Forecast;
