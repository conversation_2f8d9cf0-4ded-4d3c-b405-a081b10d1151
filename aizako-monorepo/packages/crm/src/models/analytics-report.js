import mongoose, { Schema } from 'mongoose';
/**
 * Analytics report schema
 */
const AnalyticsReportSchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    name: { type: String, required: true, trim: true },
    description: { type: String },
    category: {
        type: String,
        enum: ['sales', 'marketing', 'customer_success', 'operations', 'finance', 'custom'],
        required: true,
        index: true
    },
    type: {
        type: String,
        enum: ['dashboard', 'single_chart', 'table', 'export', 'scheduled'],
        required: true,
        index: true
    },
    status: {
        type: String,
        enum: ['draft', 'published', 'archived'],
        default: 'draft',
        index: true
    },
    // Data source configuration
    dataSource: {
        entities: [{ type: String, required: true }],
        joins: [{
                entity: { type: String, required: true },
                joinField: { type: String, required: true },
                targetField: { type: String, required: true },
                type: {
                    type: String,
                    enum: ['inner', 'left', 'right', 'full'],
                    default: 'inner'
                }
            }],
        filters: [{
                field: { type: String, required: true },
                operator: {
                    type: String,
                    enum: ['equals', 'not_equals', 'contains', 'not_contains', 'greater_than', 'less_than', 'between', 'in', 'not_in', 'is_null', 'is_not_null'],
                    required: true
                },
                value: { type: Schema.Types.Mixed, required: true },
                valueType: {
                    type: String,
                    enum: ['string', 'number', 'boolean', 'date', 'array'],
                    required: true
                }
            }],
        dateRange: {
            field: { type: String },
            type: {
                type: String,
                enum: ['relative', 'absolute']
            },
            value: { type: Schema.Types.Mixed }
        }
    },
    // Visualization configuration
    charts: [{
            type: {
                type: String,
                enum: ['line', 'bar', 'pie', 'doughnut', 'area', 'scatter', 'funnel', 'gauge', 'table', 'metric'],
                required: true
            },
            title: { type: String },
            xAxis: {
                field: { type: String },
                label: { type: String },
                type: {
                    type: String,
                    enum: ['category', 'time', 'value']
                }
            },
            yAxis: {
                field: { type: String },
                label: { type: String },
                aggregation: {
                    type: String,
                    enum: ['sum', 'count', 'avg', 'min', 'max', 'distinct']
                }
            },
            series: [{
                    field: { type: String, required: true },
                    label: { type: String },
                    aggregation: {
                        type: String,
                        enum: ['sum', 'count', 'avg', 'min', 'max', 'distinct']
                    },
                    color: { type: String }
                }],
            groupBy: [{ type: String }],
            orderBy: [{
                    field: { type: String, required: true },
                    direction: {
                        type: String,
                        enum: ['asc', 'desc'],
                        default: 'asc'
                    }
                }],
            limit: { type: Number, min: 1, max: 10000 },
            colors: [{ type: String }],
            options: { type: Schema.Types.Mixed }
        }],
    // Layout configuration
    layout: {
        columns: { type: Number, min: 1, max: 12, default: 2 },
        rows: { type: Number, min: 1, max: 20, default: 2 },
        chartPositions: [{
                chartIndex: { type: Number, required: true },
                x: { type: Number, required: true, min: 0 },
                y: { type: Number, required: true, min: 0 },
                width: { type: Number, required: true, min: 1 },
                height: { type: Number, required: true, min: 1 }
            }]
    },
    // Permissions and sharing
    visibility: {
        type: String,
        enum: ['private', 'team', 'organization', 'public'],
        default: 'private',
        index: true
    },
    sharedWith: [{
            userId: { type: Schema.Types.ObjectId, ref: 'User' },
            teamId: { type: Schema.Types.ObjectId, ref: 'Team' },
            permissions: [{
                    type: String,
                    enum: ['view', 'edit', 'share'],
                    required: true
                }],
            sharedAt: { type: Date, default: Date.now },
            sharedBy: { type: Schema.Types.ObjectId, ref: 'User', required: true }
        }],
    // Scheduling and automation
    schedule: {
        enabled: { type: Boolean, default: false },
        frequency: {
            type: String,
            enum: ['daily', 'weekly', 'monthly', 'quarterly']
        },
        time: { type: String }, // HH:MM format
        dayOfWeek: { type: Number, min: 0, max: 6 },
        dayOfMonth: { type: Number, min: 1, max: 31 },
        timezone: { type: String, default: 'UTC' },
        recipients: [{
                email: { type: String, required: true, trim: true, lowercase: true },
                userId: { type: Schema.Types.ObjectId, ref: 'User' },
                format: {
                    type: String,
                    enum: ['pdf', 'excel', 'csv', 'link'],
                    required: true
                }
            }],
        lastRun: { type: Date },
        nextRun: { type: Date }
    },
    // Caching and performance
    cache: {
        enabled: { type: Boolean, default: true },
        ttl: { type: Number, default: 3600 }, // 1 hour
        lastGenerated: { type: Date },
        size: { type: Number },
        key: { type: String }
    },
    // Usage analytics
    usage: {
        viewCount: { type: Number, default: 0 },
        lastViewed: { type: Date },
        uniqueViewers: { type: Number, default: 0 },
        averageViewTime: { type: Number },
        exportCount: { type: Number, default: 0 },
        shareCount: { type: Number, default: 0 },
        popularFilters: [{
                filter: {
                    field: { type: String, required: true },
                    operator: {
                        type: String,
                        enum: ['equals', 'not_equals', 'contains', 'not_contains', 'greater_than', 'less_than', 'between', 'in', 'not_in', 'is_null', 'is_not_null'],
                        required: true
                    },
                    value: { type: Schema.Types.Mixed, required: true },
                    valueType: {
                        type: String,
                        enum: ['string', 'number', 'boolean', 'date', 'array'],
                        required: true
                    }
                },
                usageCount: { type: Number, default: 0 }
            }]
    },
    // AI insights
    aiInsights: {
        recommendations: [{ type: String }],
        trends: [{
                metric: { type: String, required: true },
                trend: {
                    type: String,
                    enum: ['increasing', 'decreasing', 'stable'],
                    required: true
                },
                confidence: { type: Number, min: 0, max: 1, required: true },
                timeframe: { type: String, required: true }
            }],
        anomalies: [{
                metric: { type: String, required: true },
                value: { type: Number, required: true },
                expected: { type: Number, required: true },
                deviation: { type: Number, required: true },
                timestamp: { type: Date, required: true }
            }],
        predictions: [{
                metric: { type: String, required: true },
                predictedValue: { type: Number, required: true },
                confidence: { type: Number, min: 0, max: 1, required: true },
                timeframe: { type: String, required: true }
            }],
        lastUpdated: { type: Date },
        model: { type: String }
    },
    // Export settings
    exportSettings: {
        formats: [{
                type: String,
                enum: ['pdf', 'excel', 'csv', 'json']
            }],
        includeCharts: { type: Boolean, default: true },
        includeData: { type: Boolean, default: true },
        pageSize: {
            type: String,
            enum: ['A4', 'A3', 'letter', 'legal'],
            default: 'A4'
        },
        orientation: {
            type: String,
            enum: ['portrait', 'landscape'],
            default: 'portrait'
        },
        branding: {
            logo: { type: String },
            colors: [{ type: String }],
            footer: { type: String }
        }
    },
    // Version control
    version: { type: Number, default: 1 },
    parentReportId: { type: Schema.Types.ObjectId, ref: 'AnalyticsReport' },
    isTemplate: { type: Boolean, default: false, index: true },
    templateCategory: { type: String, index: true },
    // Metadata
    createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    updatedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    publishedAt: { type: Date },
    publishedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    tags: [{ type: String, trim: true }],
    customFields: { type: Schema.Types.Mixed, default: {} }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Create indexes
AnalyticsReportSchema.index({ name: 1, tenantId: 1 });
AnalyticsReportSchema.index({ category: 1, tenantId: 1 });
AnalyticsReportSchema.index({ type: 1, tenantId: 1 });
AnalyticsReportSchema.index({ status: 1, tenantId: 1 });
AnalyticsReportSchema.index({ visibility: 1, tenantId: 1 });
AnalyticsReportSchema.index({ createdBy: 1, tenantId: 1 });
AnalyticsReportSchema.index({ isTemplate: 1, tenantId: 1 });
AnalyticsReportSchema.index({ templateCategory: 1, tenantId: 1 });
AnalyticsReportSchema.index({ 'sharedWith.userId': 1, tenantId: 1 });
AnalyticsReportSchema.index({ 'schedule.enabled': 1, 'schedule.nextRun': 1, tenantId: 1 });
AnalyticsReportSchema.index({ 'usage.viewCount': -1, tenantId: 1 });
AnalyticsReportSchema.index({ publishedAt: -1, tenantId: 1 });
// Compound index for published reports
AnalyticsReportSchema.index({ tenantId: 1, status: 1, visibility: 1, publishedAt: -1 });
// Compound index for text search
AnalyticsReportSchema.index({ tenantId: 1, name: 'text', description: 'text' });
// Create the model
export const AnalyticsReport = mongoose.models.AnalyticsReport || mongoose.model('AnalyticsReport', AnalyticsReportSchema);
export default AnalyticsReport;
