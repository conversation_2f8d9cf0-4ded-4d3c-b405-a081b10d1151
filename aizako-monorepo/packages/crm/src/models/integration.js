import mongoose, { Schema } from 'mongoose';
/**
 * Integration schema
 */
const IntegrationSchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    name: { type: String, required: true, trim: true },
    description: { type: String },
    type: {
        type: String,
        enum: ['email', 'calendar', 'crm', 'marketing', 'sales', 'support', 'analytics', 'communication', 'storage', 'custom'],
        required: true,
        index: true
    },
    provider: { type: String, required: true, trim: true, index: true },
    status: {
        type: String,
        enum: ['active', 'inactive', 'error', 'pending_auth', 'suspended'],
        default: 'pending_auth',
        index: true
    },
    // Authentication
    auth: {
        type: {
            type: String,
            enum: ['oauth2', 'api_key', 'basic', 'bearer', 'custom'],
            required: true
        },
        credentials: { type: Schema.Types.Mixed }, // Encrypted
        tokenExpiry: { type: Date },
        refreshToken: { type: String }, // Encrypted
        scopes: [{ type: String }],
        authUrl: { type: String },
        tokenUrl: { type: String }
    },
    // Configuration
    config: {
        baseUrl: { type: String },
        apiVersion: { type: String },
        endpoints: { type: Map, of: String },
        headers: { type: Map, of: String },
        parameters: { type: Schema.Types.Mixed },
        customSettings: { type: Schema.Types.Mixed }
    },
    // Sync configuration
    syncConfig: {
        direction: {
            type: String,
            enum: ['inbound', 'outbound', 'bidirectional'],
            required: true
        },
        frequency: {
            type: String,
            enum: ['real_time', 'hourly', 'daily', 'weekly', 'manual'],
            required: true
        },
        batchSize: { type: Number, min: 1, max: 1000, default: 100 },
        retryAttempts: { type: Number, min: 0, max: 10, default: 3 },
        retryDelay: { type: Number, min: 1, max: 3600, default: 60 },
        conflictResolution: {
            type: String,
            enum: ['source_wins', 'target_wins', 'latest_wins', 'manual'],
            default: 'latest_wins'
        },
        fieldMappings: [{
                sourceField: { type: String, required: true },
                targetField: { type: String, required: true },
                transformation: {
                    type: String,
                    enum: ['uppercase', 'lowercase', 'trim', 'date_format', 'number_format', 'custom']
                },
                customTransformation: { type: String },
                isRequired: { type: Boolean, default: false },
                defaultValue: { type: Schema.Types.Mixed }
            }]
    },
    // Data mapping
    entityMappings: [{
            sourceEntity: { type: String, required: true },
            targetEntity: { type: String, required: true },
            syncConfig: {
                direction: {
                    type: String,
                    enum: ['inbound', 'outbound', 'bidirectional'],
                    required: true
                },
                frequency: {
                    type: String,
                    enum: ['real_time', 'hourly', 'daily', 'weekly', 'manual'],
                    required: true
                },
                batchSize: { type: Number, min: 1, max: 1000, default: 100 },
                retryAttempts: { type: Number, min: 0, max: 10, default: 3 },
                retryDelay: { type: Number, min: 1, max: 3600, default: 60 },
                conflictResolution: {
                    type: String,
                    enum: ['source_wins', 'target_wins', 'latest_wins', 'manual'],
                    default: 'latest_wins'
                },
                fieldMappings: [{
                        sourceField: { type: String, required: true },
                        targetField: { type: String, required: true },
                        transformation: {
                            type: String,
                            enum: ['uppercase', 'lowercase', 'trim', 'date_format', 'number_format', 'custom']
                        },
                        customTransformation: { type: String },
                        isRequired: { type: Boolean, default: false },
                        defaultValue: { type: Schema.Types.Mixed }
                    }]
            },
            isEnabled: { type: Boolean, default: true }
        }],
    // Webhook configuration
    webhooks: [{
            id: { type: String, required: true },
            url: { type: String, required: true },
            events: [{ type: String, required: true }],
            secret: { type: String }, // Encrypted
            isActive: { type: Boolean, default: true },
            headers: { type: Map, of: String }
        }],
    // Rate limiting
    rateLimits: {
        requestsPerMinute: { type: Number, min: 1 },
        requestsPerHour: { type: Number, min: 1 },
        requestsPerDay: { type: Number, min: 1 },
        burstLimit: { type: Number, min: 1 }
    },
    // Monitoring and health
    health: {
        lastSync: { type: Date },
        lastSuccessfulSync: { type: Date },
        lastError: { type: Date },
        errorCount: { type: Number, default: 0 },
        successCount: { type: Number, default: 0 },
        isHealthy: { type: Boolean, default: true },
        uptime: { type: Number, min: 0, max: 100 }
    },
    // Error tracking
    errorHistory: [{
            timestamp: { type: Date, default: Date.now },
            type: {
                type: String,
                enum: ['auth', 'sync', 'rate_limit', 'network', 'data', 'other'],
                required: true
            },
            message: { type: String, required: true },
            details: { type: Schema.Types.Mixed },
            resolved: { type: Boolean, default: false },
            resolvedAt: { type: Date },
            resolvedBy: { type: Schema.Types.ObjectId, ref: 'User' }
        }],
    // Usage statistics
    usage: {
        totalRequests: { type: Number, default: 0 },
        totalErrors: { type: Number, default: 0 },
        averageResponseTime: { type: Number, default: 0 },
        dataTransferred: { type: Number, default: 0 },
        lastUsed: { type: Date },
        monthlyUsage: [{
                month: { type: String, required: true }, // YYYY-MM
                requests: { type: Number, default: 0 },
                errors: { type: Number, default: 0 },
                dataTransferred: { type: Number, default: 0 }
            }]
    },
    // Feature flags
    features: {
        realTimeSync: { type: Boolean, default: false },
        bulkOperations: { type: Boolean, default: false },
        customFields: { type: Boolean, default: false },
        advancedMapping: { type: Boolean, default: false },
        webhookSupport: { type: Boolean, default: false },
        rateLimitBypass: { type: Boolean, default: false }
    },
    // Compliance and security
    compliance: {
        dataRetention: { type: Number, min: 1 }, // days
        encryptionEnabled: { type: Boolean, default: true },
        auditLogging: { type: Boolean, default: true },
        gdprCompliant: { type: Boolean, default: false },
        hipaaCompliant: { type: Boolean, default: false },
        soc2Compliant: { type: Boolean, default: false }
    },
    // User management
    authorizedUsers: [{
            userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
            permissions: [{ type: String, required: true }],
            grantedAt: { type: Date, default: Date.now },
            grantedBy: { type: Schema.Types.ObjectId, ref: 'User', required: true }
        }],
    // Metadata
    createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    updatedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    installedAt: { type: Date, default: Date.now },
    lastConfigUpdate: { type: Date },
    version: { type: String },
    tags: [{ type: String, trim: true }],
    customFields: { type: Schema.Types.Mixed, default: {} }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Create indexes
IntegrationSchema.index({ name: 1, tenantId: 1 });
IntegrationSchema.index({ type: 1, tenantId: 1 });
IntegrationSchema.index({ provider: 1, tenantId: 1 });
IntegrationSchema.index({ status: 1, tenantId: 1 });
IntegrationSchema.index({ createdBy: 1, tenantId: 1 });
IntegrationSchema.index({ 'health.isHealthy': 1, tenantId: 1 });
IntegrationSchema.index({ 'health.lastSync': 1, tenantId: 1 });
IntegrationSchema.index({ 'authorizedUsers.userId': 1, tenantId: 1 });
IntegrationSchema.index({ installedAt: 1, tenantId: 1 });
// Compound index for active integrations
IntegrationSchema.index({ tenantId: 1, status: 1, type: 1 });
// Compound index for text search
IntegrationSchema.index({ tenantId: 1, name: 'text', description: 'text', provider: 'text' });
// Create the model
export const Integration = mongoose.models.Integration || mongoose.model('Integration', IntegrationSchema);
export default Integration;
