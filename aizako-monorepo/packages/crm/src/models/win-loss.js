import mongoose, { Schema } from 'mongoose';
/**
 * Win-Loss schema
 */
const WinLossSchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    opportunityId: { type: Schema.Types.ObjectId, ref: 'Opportunity', required: true, unique: true, index: true },
    outcome: {
        type: String,
        enum: ['won', 'lost'],
        required: true,
        index: true
    },
    analysisDate: { type: Date, required: true, default: Date.now, index: true },
    analysisBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    // Primary factors
    primaryReason: { type: String, required: true, trim: true },
    secondaryReasons: [{ type: String, trim: true }],
    competitorWon: { type: String, trim: true },
    // Detailed analysis
    factors: {
        product: {
            score: { type: Number, min: 1, max: 10, required: true },
            strengths: [{ type: String }],
            weaknesses: [{ type: String }],
            feedback: { type: String }
        },
        pricing: {
            score: { type: Number, min: 1, max: 10, required: true },
            competitive: { type: Boolean },
            objections: [{ type: String }],
            feedback: { type: String }
        },
        sales: {
            score: { type: Number, min: 1, max: 10, required: true },
            relationship: { type: Number, min: 1, max: 10 },
            process: { type: Number, min: 1, max: 10 },
            timing: { type: Number, min: 1, max: 10 },
            feedback: { type: String }
        },
        competition: {
            score: { type: Number, min: 1, max: 10, required: true },
            mainCompetitor: { type: String, trim: true },
            competitorAdvantages: [{ type: String }],
            ourAdvantages: [{ type: String }],
            feedback: { type: String }
        },
        decisionProcess: {
            score: { type: Number, min: 1, max: 10, required: true },
            decisionMakers: [{
                    name: { type: String, trim: true },
                    title: { type: String, trim: true },
                    influence: {
                        type: String,
                        enum: ['high', 'medium', 'low']
                    },
                    sentiment: {
                        type: String,
                        enum: ['positive', 'neutral', 'negative']
                    }
                }],
            timeline: { type: String },
            budget: { type: String },
            feedback: { type: String }
        }
    },
    // Lessons learned
    lessonsLearned: {
        whatWorked: [{ type: String }],
        whatDidntWork: [{ type: String }],
        improvements: [{ type: String }],
        recommendations: [{ type: String }]
    },
    // Customer feedback
    customerFeedback: {
        overallExperience: { type: Number, min: 1, max: 10 },
        productFit: { type: Number, min: 1, max: 10 },
        salesExperience: { type: Number, min: 1, max: 10 },
        likelihood: { type: Number, min: 1, max: 10 },
        verbatimFeedback: { type: String },
        followUpOpportunity: { type: Boolean },
        referralPotential: { type: Boolean }
    },
    // Deal details
    dealDetails: {
        finalAmount: { type: Number },
        currency: { type: String, default: 'USD' },
        salesCycle: { type: Number }, // days
        touchpoints: { type: Number },
        proposalsSent: { type: Number },
        meetingsHeld: { type: Number },
        emailsSent: { type: Number },
        callsMade: { type: Number }
    },
    // Team involvement
    teamInvolvement: [{
            userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
            role: { type: String, required: true, trim: true },
            contribution: { type: String },
            effectiveness: { type: Number, min: 1, max: 10 }
        }],
    // Action items
    actionItems: [{
            action: { type: String, required: true, trim: true },
            assignedTo: { type: Schema.Types.ObjectId, ref: 'User' },
            dueDate: { type: Date },
            priority: {
                type: String,
                enum: ['low', 'medium', 'high'],
                default: 'medium'
            },
            status: {
                type: String,
                enum: ['pending', 'in_progress', 'completed'],
                default: 'pending'
            },
            completedAt: { type: Date }
        }],
    // Follow-up
    followUp: {
        scheduledDate: { type: Date },
        type: {
            type: String,
            enum: ['check_in', 'referral_request', 'case_study', 'testimonial', 'other']
        },
        notes: { type: String },
        completed: { type: Boolean, default: false },
        completedAt: { type: Date }
    },
    // AI insights
    aiInsights: {
        predictedOutcome: {
            type: String,
            enum: ['won', 'lost']
        },
        confidence: { type: Number, min: 0, max: 1 },
        keyFactors: [{ type: String }],
        recommendations: [{ type: String }],
        similarDeals: [{ type: Schema.Types.ObjectId, ref: 'Opportunity' }],
        patterns: [{ type: String }],
        lastUpdated: { type: Date, default: Date.now }
    },
    tags: [{ type: String, trim: true }],
    isConfidential: { type: Boolean, default: false },
    customFields: { type: Schema.Types.Mixed, default: {} }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Create indexes
WinLossSchema.index({ outcome: 1, tenantId: 1 });
WinLossSchema.index({ analysisDate: -1, tenantId: 1 });
WinLossSchema.index({ analysisBy: 1, tenantId: 1 });
WinLossSchema.index({ competitorWon: 1, tenantId: 1 });
WinLossSchema.index({ 'factors.product.score': 1, tenantId: 1 });
WinLossSchema.index({ 'factors.pricing.score': 1, tenantId: 1 });
WinLossSchema.index({ 'factors.sales.score': 1, tenantId: 1 });
WinLossSchema.index({ 'customerFeedback.overallExperience': 1, tenantId: 1 });
WinLossSchema.index({ 'teamInvolvement.userId': 1, tenantId: 1 });
WinLossSchema.index({ isConfidential: 1, tenantId: 1 });
// Compound index for analysis
WinLossSchema.index({ tenantId: 1, outcome: 1, analysisDate: -1 });
// Compound index for text search
WinLossSchema.index({ tenantId: 1, primaryReason: 'text', 'lessonsLearned.whatWorked': 'text', 'lessonsLearned.whatDidntWork': 'text' });
// Create the model
export const WinLoss = mongoose.models.WinLoss || mongoose.model('WinLoss', WinLossSchema);
export default WinLoss;
