import mongoose, { Schema } from 'mongoose';
/**
 * A/B test schema
 */
const ABTestSchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    name: { type: String, required: true },
    description: { type: String },
    baseProposalId: { type: Schema.Types.ObjectId, ref: 'Proposal', required: true },
    variants: [{
            name: { type: String, required: true },
            description: { type: String },
            proposalId: { type: Schema.Types.ObjectId, ref: 'Proposal', required: true },
            trafficPercentage: { type: Number, required: true, min: 0, max: 100 },
        }],
    baseTrafficPercentage: { type: Number, required: true, min: 0, max: 100 },
    status: {
        type: String,
        required: true,
        enum: ['active', 'paused', 'completed'],
        default: 'active',
        index: true,
    },
    startDate: { type: Date, required: true, default: Date.now },
    endDate: { type: Date },
}, {
    timestamps: true,
});
// Create indexes
ABTestSchema.index({ tenantId: 1, baseProposalId: 1 });
ABTestSchema.index({ tenantId: 1, status: 1 });
ABTestSchema.index({ tenantId: 1, createdAt: -1 });
// Create the model
export const ABTest = mongoose.models.ABTest ||
    mongoose.model('ABTest', ABTestSchema);
export default ABTest;
