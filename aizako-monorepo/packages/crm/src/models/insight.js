import mongoose, { Schema } from 'mongoose';
/**
 * Insight schema
 */
const InsightSchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    type: {
        type: String,
        enum: ['opportunity', 'contact', 'company', 'activity', 'performance', 'forecast', 'trend', 'anomaly'],
        required: true,
        index: true
    },
    category: {
        type: String,
        enum: ['sales', 'marketing', 'customer_success', 'operations', 'finance', 'product', 'other'],
        required: true,
        index: true
    },
    title: { type: String, required: true, trim: true },
    description: { type: String, required: true },
    summary: { type: String },
    priority: {
        type: String,
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'medium',
        index: true
    },
    status: {
        type: String,
        enum: ['active', 'acknowledged', 'acted_upon', 'dismissed', 'expired'],
        default: 'active',
        index: true
    },
    confidence: { type: Number, min: 0, max: 100, required: true, index: true },
    impact: {
        type: String,
        enum: ['low', 'medium', 'high'],
        default: 'medium',
        index: true
    },
    urgency: {
        type: String,
        enum: ['low', 'medium', 'high'],
        default: 'medium',
        index: true
    },
    relatedEntities: [{
            type: {
                type: String,
                enum: ['contact', 'company', 'opportunity', 'activity', 'proposal', 'sequence'],
                required: true
            },
            id: { type: Schema.Types.ObjectId, required: true },
            name: { type: String, trim: true },
            relevance: { type: Number, min: 0, max: 100 }
        }],
    data: { type: Schema.Types.Mixed, default: {} },
    metrics: [{
            name: { type: String, required: true, trim: true },
            value: { type: Number, required: true },
            unit: { type: String, trim: true },
            change: { type: Number },
            changeType: {
                type: String,
                enum: ['increase', 'decrease', 'stable']
            },
            benchmark: { type: Number }
        }],
    recommendations: [{
            action: { type: String, required: true, trim: true },
            description: { type: String },
            priority: {
                type: String,
                enum: ['low', 'medium', 'high'],
                default: 'medium'
            },
            effort: {
                type: String,
                enum: ['low', 'medium', 'high'],
                default: 'medium'
            },
            impact: {
                type: String,
                enum: ['low', 'medium', 'high'],
                default: 'medium'
            },
            timeframe: { type: String, trim: true },
            resources: [{ type: String, trim: true }]
        }],
    source: {
        type: String,
        enum: ['ai', 'manual', 'system', 'integration'],
        required: true,
        index: true
    },
    sourceDetails: {
        model: { type: String, trim: true },
        algorithm: { type: String, trim: true },
        dataPoints: { type: Number },
        timeRange: {
            start: { type: Date },
            end: { type: Date }
        },
        version: { type: String, trim: true }
    },
    tags: [{ type: String, trim: true }],
    userId: { type: Schema.Types.ObjectId, ref: 'User', index: true },
    assignedTo: { type: Schema.Types.ObjectId, ref: 'User', index: true },
    acknowledgedAt: { type: Date },
    acknowledgedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    actedUponAt: { type: Date },
    actedUponBy: { type: Schema.Types.ObjectId, ref: 'User' },
    dismissedAt: { type: Date },
    dismissedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    dismissalReason: { type: String },
    expiresAt: { type: Date, index: true },
    validUntil: { type: Date, index: true },
    refreshedAt: { type: Date },
    refreshFrequency: {
        type: String,
        enum: ['hourly', 'daily', 'weekly', 'monthly', 'manual'],
        default: 'manual'
    },
    isRecurring: { type: Boolean, default: false },
    parentInsightId: { type: Schema.Types.ObjectId, ref: 'Insight' },
    customFields: { type: Schema.Types.Mixed, default: {} }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Create indexes
InsightSchema.index({ type: 1, tenantId: 1 });
InsightSchema.index({ category: 1, tenantId: 1 });
InsightSchema.index({ priority: 1, status: 1, tenantId: 1 });
InsightSchema.index({ confidence: -1, tenantId: 1 });
InsightSchema.index({ impact: 1, urgency: 1, tenantId: 1 });
InsightSchema.index({ source: 1, tenantId: 1 });
InsightSchema.index({ userId: 1, tenantId: 1 });
InsightSchema.index({ assignedTo: 1, tenantId: 1 });
InsightSchema.index({ expiresAt: 1, tenantId: 1 });
InsightSchema.index({ validUntil: 1, tenantId: 1 });
InsightSchema.index({ 'relatedEntities.type': 1, 'relatedEntities.id': 1, tenantId: 1 });
InsightSchema.index({ createdAt: -1, tenantId: 1 });
// Compound index for active insights
InsightSchema.index({ tenantId: 1, status: 1, priority: 1, createdAt: -1 });
// Compound index for text search
InsightSchema.index({ tenantId: 1, title: 'text', description: 'text', summary: 'text' });
// Create the model
export const Insight = mongoose.models.Insight || mongoose.model('Insight', InsightSchema);
export default Insight;
