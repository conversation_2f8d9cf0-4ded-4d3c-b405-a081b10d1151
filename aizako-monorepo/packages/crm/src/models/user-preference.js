import mongoose, { Schema } from 'mongoose';
/**
 * User preference schema
 */
const UserPreferenceSchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true, index: true },
    // General preferences
    general: {
        language: { type: String, default: 'en', trim: true },
        timezone: { type: String, default: 'UTC', trim: true },
        dateFormat: { type: String, default: 'MM/DD/YYYY', trim: true },
        timeFormat: {
            type: String,
            enum: ['12h', '24h'],
            default: '12h'
        },
        currency: { type: String, default: 'USD', uppercase: true },
        theme: {
            type: String,
            enum: ['light', 'dark', 'auto'],
            default: 'light'
        },
        density: {
            type: String,
            enum: ['compact', 'comfortable', 'spacious'],
            default: 'comfortable'
        },
        sidebarCollapsed: { type: Boolean, default: false },
        showWelcomeMessage: { type: Boolean, default: true },
        showTips: { type: Boolean, default: true }
    },
    // Notification preferences
    notifications: {
        email: {
            enabled: { type: Boolean, default: true },
            frequency: {
                type: String,
                enum: ['immediate', 'hourly', 'daily', 'weekly', 'never'],
                default: 'daily'
            },
            types: {
                newLeads: { type: Boolean, default: true },
                opportunityUpdates: { type: Boolean, default: true },
                taskReminders: { type: Boolean, default: true },
                meetingReminders: { type: Boolean, default: true },
                proposalUpdates: { type: Boolean, default: true },
                sequenceUpdates: { type: Boolean, default: false },
                workflowUpdates: { type: Boolean, default: false },
                systemUpdates: { type: Boolean, default: true },
                marketingUpdates: { type: Boolean, default: false }
            },
            quietHours: {
                enabled: { type: Boolean, default: false },
                start: { type: String, default: '18:00' },
                end: { type: String, default: '08:00' },
                timezone: { type: String, default: 'UTC' }
            }
        },
        push: {
            enabled: { type: Boolean, default: true },
            types: {
                newLeads: { type: Boolean, default: true },
                urgentTasks: { type: Boolean, default: true },
                meetingReminders: { type: Boolean, default: true },
                proposalViews: { type: Boolean, default: true },
                systemAlerts: { type: Boolean, default: true }
            }
        },
        inApp: {
            enabled: { type: Boolean, default: true },
            types: {
                newLeads: { type: Boolean, default: true },
                opportunityUpdates: { type: Boolean, default: true },
                taskReminders: { type: Boolean, default: true },
                meetingReminders: { type: Boolean, default: true },
                proposalUpdates: { type: Boolean, default: true },
                sequenceUpdates: { type: Boolean, default: true },
                workflowUpdates: { type: Boolean, default: true },
                systemUpdates: { type: Boolean, default: true },
                aiInsights: { type: Boolean, default: true }
            },
            position: {
                type: String,
                enum: ['top-right', 'top-left', 'bottom-right', 'bottom-left'],
                default: 'top-right'
            },
            duration: { type: Number, default: 5 }
        }
    },
    // Dashboard preferences
    dashboard: {
        layout: {
            type: String,
            enum: ['grid', 'list', 'cards'],
            default: 'grid'
        },
        widgets: [{
                id: { type: String, required: true },
                type: { type: String, required: true },
                position: {
                    x: { type: Number, required: true },
                    y: { type: Number, required: true }
                },
                size: {
                    width: { type: Number, required: true },
                    height: { type: Number, required: true }
                },
                config: { type: Schema.Types.Mixed, default: {} },
                isVisible: { type: Boolean, default: true }
            }],
        refreshInterval: { type: Number, default: 5 },
        showMetrics: { type: Boolean, default: true },
        showCharts: { type: Boolean, default: true },
        showRecentActivity: { type: Boolean, default: true },
        showUpcomingTasks: { type: Boolean, default: true },
        showPipeline: { type: Boolean, default: true }
    },
    // List view preferences
    listViews: {
        contacts: {
            columns: [{ type: String, default: ['name', 'email', 'company', 'status', 'lastActivity'] }],
            sortBy: { type: String, default: 'name' },
            sortOrder: { type: String, enum: ['asc', 'desc'], default: 'asc' },
            pageSize: { type: Number, default: 25, min: 10, max: 100 },
            filters: { type: Schema.Types.Mixed, default: {} },
            groupBy: { type: String }
        },
        companies: {
            columns: [{ type: String, default: ['name', 'industry', 'size', 'status', 'lastActivity'] }],
            sortBy: { type: String, default: 'name' },
            sortOrder: { type: String, enum: ['asc', 'desc'], default: 'asc' },
            pageSize: { type: Number, default: 25, min: 10, max: 100 },
            filters: { type: Schema.Types.Mixed, default: {} },
            groupBy: { type: String }
        },
        opportunities: {
            columns: [{ type: String, default: ['name', 'amount', 'stage', 'probability', 'closeDate'] }],
            sortBy: { type: String, default: 'closeDate' },
            sortOrder: { type: String, enum: ['asc', 'desc'], default: 'asc' },
            pageSize: { type: Number, default: 25, min: 10, max: 100 },
            filters: { type: Schema.Types.Mixed, default: {} },
            groupBy: { type: String },
            viewType: { type: String, enum: ['list', 'kanban', 'calendar'], default: 'list' }
        },
        activities: {
            columns: [{ type: String, default: ['type', 'subject', 'contact', 'date', 'status'] }],
            sortBy: { type: String, default: 'date' },
            sortOrder: { type: String, enum: ['asc', 'desc'], default: 'desc' },
            pageSize: { type: Number, default: 25, min: 10, max: 100 },
            filters: { type: Schema.Types.Mixed, default: {} },
            groupBy: { type: String }
        },
        tasks: {
            columns: [{ type: String, default: ['subject', 'priority', 'dueDate', 'assignee', 'status'] }],
            sortBy: { type: String, default: 'dueDate' },
            sortOrder: { type: String, enum: ['asc', 'desc'], default: 'asc' },
            pageSize: { type: Number, default: 25, min: 10, max: 100 },
            filters: { type: Schema.Types.Mixed, default: {} },
            groupBy: { type: String },
            showCompleted: { type: Boolean, default: false }
        },
        proposals: {
            columns: [{ type: String, default: ['title', 'contact', 'amount', 'status', 'createdAt'] }],
            sortBy: { type: String, default: 'createdAt' },
            sortOrder: { type: String, enum: ['asc', 'desc'], default: 'desc' },
            pageSize: { type: Number, default: 25, min: 10, max: 100 },
            filters: { type: Schema.Types.Mixed, default: {} },
            groupBy: { type: String }
        }
    },
    // AI preferences
    ai: {
        enabled: { type: Boolean, default: true },
        autoSuggestions: { type: Boolean, default: true },
        smartInsights: { type: Boolean, default: true },
        predictiveScoring: { type: Boolean, default: true },
        autoEnrichment: { type: Boolean, default: true },
        emailDrafting: { type: Boolean, default: true },
        meetingPrep: { type: Boolean, default: true },
        objectionHandling: { type: Boolean, default: true },
        proposalGeneration: { type: Boolean, default: true },
        followUpCoaching: { type: Boolean, default: true },
        winLossAnalysis: { type: Boolean, default: true },
        confidenceThreshold: { type: Number, default: 70, min: 0, max: 100 },
        learningMode: { type: Boolean, default: true },
        feedbackEnabled: { type: Boolean, default: true }
    },
    // Integration preferences
    integrations: {
        calendar: {
            defaultCalendar: { type: String },
            syncEnabled: { type: Boolean, default: true },
            reminderTime: { type: Number, default: 15 },
            showBusyTime: { type: Boolean, default: true },
            autoCreateMeetings: { type: Boolean, default: false }
        },
        email: {
            defaultSignature: { type: String },
            trackingEnabled: { type: Boolean, default: true },
            autoSync: { type: Boolean, default: true },
            syncFrequency: {
                type: String,
                enum: ['real_time', 'hourly', 'daily'],
                default: 'hourly'
            },
            templatePreferences: { type: Schema.Types.Mixed, default: {} }
        },
        phone: {
            defaultCountryCode: { type: String, default: '+1' },
            autoDialEnabled: { type: Boolean, default: false },
            recordingEnabled: { type: Boolean, default: false },
            transcriptionEnabled: { type: Boolean, default: false }
        }
    },
    // Workflow preferences
    workflows: {
        autoAssignment: { type: Boolean, default: false },
        defaultAssignee: { type: Schema.Types.ObjectId, ref: 'User' },
        escalationRules: [{
                condition: { type: String, required: true },
                action: { type: String, required: true },
                delay: { type: Number, required: true, min: 1 }
            }],
        approvalRequired: { type: Boolean, default: false },
        defaultApprover: { type: Schema.Types.ObjectId, ref: 'User' }
    },
    // Security preferences
    security: {
        twoFactorEnabled: { type: Boolean, default: false },
        sessionTimeout: { type: Number, default: 480, min: 15, max: 1440 }, // 8 hours default
        ipWhitelist: [{ type: String }],
        deviceTrust: { type: Boolean, default: true },
        loginNotifications: { type: Boolean, default: true },
        dataExportRestrictions: { type: Boolean, default: false }
    },
    // Privacy preferences
    privacy: {
        dataSharing: { type: Boolean, default: false },
        analyticsTracking: { type: Boolean, default: true },
        marketingCommunications: { type: Boolean, default: false },
        thirdPartyIntegrations: { type: Boolean, default: true },
        dataRetention: { type: Number, default: 2555 }, // 7 years
        rightToBeForgotten: { type: Boolean, default: false }
    },
    // Accessibility preferences
    accessibility: {
        highContrast: { type: Boolean, default: false },
        largeText: { type: Boolean, default: false },
        reducedMotion: { type: Boolean, default: false },
        screenReader: { type: Boolean, default: false },
        keyboardNavigation: { type: Boolean, default: false },
        colorBlindFriendly: { type: Boolean, default: false }
    },
    // Custom preferences
    custom: { type: Schema.Types.Mixed, default: {} },
    // Metadata
    lastUpdated: { type: Date, default: Date.now },
    version: { type: Number, default: 1 }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Create indexes
UserPreferenceSchema.index({ userId: 1, tenantId: 1 }, { unique: true });
UserPreferenceSchema.index({ lastUpdated: -1, tenantId: 1 });
UserPreferenceSchema.index({ version: 1, tenantId: 1 });
// Create the model
export const UserPreference = mongoose.models.UserPreference || mongoose.model('UserPreference', UserPreferenceSchema);
export default UserPreference;
