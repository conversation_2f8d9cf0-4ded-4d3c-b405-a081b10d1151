import mongoose, { Schema } from 'mongoose';
/**
 * Tag schema
 */
const TagSchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    name: { type: String, required: true, trim: true },
    description: { type: String },
    color: { type: String, default: '#6366F1' }, // Default indigo color
    category: { type: String, trim: true },
    isSystem: { type: Boolean, default: false },
    isArchived: { type: Boolean, default: false },
    entityTypes: [{
            type: String,
            enum: ['contact', 'company', 'opportunity', 'activity', 'task', 'document', 'proposal', 'email', 'sequence', 'workflow'],
            required: true
        }],
    usageCount: {
        contact: { type: Number, default: 0 },
        company: { type: Number, default: 0 },
        opportunity: { type: Number, default: 0 },
        activity: { type: Number, default: 0 },
        task: { type: Number, default: 0 },
        document: { type: Number, default: 0 },
        proposal: { type: Number, default: 0 },
        email: { type: Number, default: 0 },
        sequence: { type: Number, default: 0 },
        workflow: { type: Number, default: 0 },
        total: { type: Number, default: 0 }
    },
    createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    updatedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    customFields: { type: Schema.Types.Mixed, default: {} }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Create indexes
TagSchema.index({ name: 1, tenantId: 1 }, { unique: true });
TagSchema.index({ category: 1, tenantId: 1 });
TagSchema.index({ isSystem: 1, tenantId: 1 });
TagSchema.index({ isArchived: 1, tenantId: 1 });
TagSchema.index({ 'entityTypes': 1, tenantId: 1 });
TagSchema.index({ 'usageCount.total': -1, tenantId: 1 });
// Compound index for text search
TagSchema.index({ tenantId: 1, name: 'text', description: 'text' });
// Create the model
export const Tag = mongoose.models.Tag || mongoose.model('Tag', TagSchema);
export default Tag;
