import mongoose, { Schema } from 'mongoose';
/**
 * Sequence schema
 */
const SequenceSchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    name: { type: String, required: true, trim: true },
    description: { type: String },
    category: { type: String, required: true, trim: true, index: true },
    isActive: { type: Boolean, default: true, index: true },
    isShared: { type: Boolean, default: false },
    steps: [{
            id: { type: String, required: true },
            name: { type: String, required: true, trim: true },
            type: {
                type: String,
                enum: ['email', 'task', 'call', 'linkedin', 'wait', 'if', 'goal'],
                required: true
            },
            description: { type: String },
            order: { type: Number, required: true },
            isActive: { type: Boolean, default: true },
            isRequired: { type: Boolean, default: true },
            delay: { type: Number, default: 0 }, // In hours
            delayType: {
                type: String,
                enum: ['fixed', 'business_days', 'calendar_days'],
                default: 'fixed'
            },
            conditions: [{
                    type: {
                        type: String,
                        enum: ['email_opened', 'email_clicked', 'email_replied', 'email_not_opened', 'email_not_clicked', 'email_not_replied', 'contact_property', 'company_property', 'opportunity_property', 'custom'],
                        required: true
                    },
                    property: { type: String },
                    operator: {
                        type: String,
                        enum: ['equals', 'not_equals', 'contains', 'not_contains', 'greater_than', 'less_than', 'exists', 'not_exists']
                    },
                    value: { type: Schema.Types.Mixed },
                    stepId: { type: String }, // Reference to another step (for email conditions)
                    timeframe: { type: Number } // In hours
                }],
            conditionType: {
                type: String,
                enum: ['all', 'any'],
                default: 'all'
            },
            // Email specific
            emailTemplateId: { type: Schema.Types.ObjectId, ref: 'EmailTemplate' },
            emailSubject: { type: String },
            emailBody: { type: String },
            // Task specific
            taskTitle: { type: String },
            taskDescription: { type: String },
            taskDueDate: { type: Date },
            taskPriority: {
                type: String,
                enum: ['low', 'medium', 'high']
            },
            // Call specific
            callTitle: { type: String },
            callDescription: { type: String },
            callScript: { type: String },
            callDuration: { type: Number },
            // LinkedIn specific
            linkedinActionType: {
                type: String,
                enum: ['connect', 'message', 'comment', 'like']
            },
            linkedinMessage: { type: String },
            // Wait specific
            waitDuration: { type: Number }, // In hours
            // If specific
            thenStepId: { type: String },
            elseStepId: { type: String },
            // Goal specific
            goalType: {
                type: String,
                enum: ['reply', 'meeting_booked', 'opportunity_created', 'custom']
            },
            goalValue: { type: String },
            // Performance metrics
            performance: {
                executed: { type: Number, default: 0 },
                completed: { type: Number, default: 0 },
                skipped: { type: Number, default: 0 },
                failed: { type: Number, default: 0 },
                completionRate: { type: Number, min: 0, max: 100, default: 0 }
            },
            // AI-generated content
            aiGenerated: { type: Boolean, default: false },
            aiPrompt: { type: String },
            aiModel: { type: String },
            aiConfidence: { type: Number, min: 0, max: 1 }
        }],
    enrollments: [{
            id: { type: String, required: true },
            contactId: { type: Schema.Types.ObjectId, ref: 'Contact', required: true },
            companyId: { type: Schema.Types.ObjectId, ref: 'Company' },
            opportunityId: { type: Schema.Types.ObjectId, ref: 'Opportunity' },
            status: {
                type: String,
                enum: ['active', 'paused', 'completed', 'stopped'],
                default: 'active'
            },
            currentStepId: { type: String, required: true },
            startedAt: { type: Date, default: Date.now },
            pausedAt: { type: Date },
            completedAt: { type: Date },
            stoppedAt: { type: Date },
            completedSteps: [{
                    stepId: { type: String, required: true },
                    completedAt: { type: Date, required: true },
                    status: {
                        type: String,
                        enum: ['completed', 'skipped', 'failed'],
                        required: true
                    },
                    activityId: { type: Schema.Types.ObjectId, ref: 'Activity' },
                    emailId: { type: String },
                    result: { type: String }
                }],
            nextStepScheduledAt: { type: Date },
            owner: { type: Schema.Types.ObjectId, ref: 'User', required: true },
            notes: { type: String }
        }],
    totalEnrollments: { type: Number, default: 0 },
    activeEnrollments: { type: Number, default: 0 },
    completedEnrollments: { type: Number, default: 0 },
    stoppedEnrollments: { type: Number, default: 0 },
    completionRate: { type: Number, min: 0, max: 100, default: 0 },
    replyRate: { type: Number, min: 0, max: 100, default: 0 },
    meetingBookedRate: { type: Number, min: 0, max: 100, default: 0 },
    opportunityCreatedRate: { type: Number, min: 0, max: 100, default: 0 },
    owner: { type: Schema.Types.ObjectId, ref: 'User', required: true, index: true },
    createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    updatedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    lastEnrollmentAt: { type: Date },
    tags: [{ type: String, trim: true }],
    customFields: { type: Schema.Types.Mixed, default: {} },
    // Adaptive sequence settings
    isAdaptive: { type: Boolean, default: false },
    adaptiveRules: [{
            id: { type: String, required: true },
            name: { type: String, required: true },
            description: { type: String },
            conditions: [{
                    type: {
                        type: String,
                        enum: ['email_opened', 'email_clicked', 'email_replied', 'email_not_opened', 'email_not_clicked', 'email_not_replied', 'contact_property', 'company_property', 'opportunity_property', 'custom'],
                        required: true
                    },
                    property: { type: String },
                    operator: {
                        type: String,
                        enum: ['equals', 'not_equals', 'contains', 'not_contains', 'greater_than', 'less_than', 'exists', 'not_exists']
                    },
                    value: { type: Schema.Types.Mixed },
                    stepId: { type: String },
                    timeframe: { type: Number }
                }],
            conditionType: {
                type: String,
                enum: ['all', 'any'],
                default: 'all'
            },
            actions: [{
                    type: {
                        type: String,
                        enum: ['add_step', 'remove_step', 'replace_step', 'change_delay', 'pause_sequence', 'stop_sequence'],
                        required: true
                    },
                    stepId: { type: String },
                    newStepId: { type: String },
                    delay: { type: Number }
                }],
            isActive: { type: Boolean, default: true },
            priority: { type: Number, default: 0 },
            executionCount: { type: Number, default: 0 },
            lastExecutedAt: { type: Date }
        }],
    // AI-generated content
    aiGenerated: { type: Boolean, default: false },
    aiPrompt: { type: String },
    aiModel: { type: String },
    aiConfidence: { type: Number, min: 0, max: 1 }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Create indexes
SequenceSchema.index({ name: 1, tenantId: 1 });
SequenceSchema.index({ category: 1, tenantId: 1 });
SequenceSchema.index({ isActive: 1, tenantId: 1 });
SequenceSchema.index({ isShared: 1, tenantId: 1 });
SequenceSchema.index({ owner: 1, tenantId: 1 });
SequenceSchema.index({ lastEnrollmentAt: 1, tenantId: 1 });
SequenceSchema.index({ 'enrollments.contactId': 1, tenantId: 1 });
SequenceSchema.index({ 'enrollments.status': 1, tenantId: 1 });
SequenceSchema.index({ 'enrollments.nextStepScheduledAt': 1, tenantId: 1 });
SequenceSchema.index({ isAdaptive: 1, tenantId: 1 });
// Compound index for text search
SequenceSchema.index({ tenantId: 1, name: 'text', description: 'text' });
// Create the model
export const Sequence = mongoose.models.Sequence || mongoose.model('Sequence', SequenceSchema);
export default Sequence;
