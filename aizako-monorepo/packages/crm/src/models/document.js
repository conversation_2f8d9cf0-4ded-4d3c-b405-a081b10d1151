import mongoose, { Schema } from 'mongoose';
/**
 * Document schema
 */
const DocumentSchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    name: { type: String, required: true, trim: true },
    description: { type: String },
    category: { type: String, required: true, trim: true, index: true },
    fileName: { type: String, required: true, trim: true },
    fileSize: { type: Number, required: true },
    fileType: { type: String, required: true, trim: true, index: true },
    fileUrl: { type: String, required: true, trim: true },
    thumbnailUrl: { type: String, trim: true },
    isPublic: { type: Boolean, default: false, index: true },
    isArchived: { type: Boolean, default: false, index: true },
    isTemplate: { type: Boolean, default: false, index: true },
    versions: [{
            versionNumber: { type: Number, required: true },
            fileName: { type: String, required: true, trim: true },
            fileSize: { type: Number, required: true },
            fileType: { type: String, required: true, trim: true },
            fileUrl: { type: String, required: true, trim: true },
            thumbnailUrl: { type: String, trim: true },
            createdAt: { type: Date, default: Date.now },
            createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
            comment: { type: String }
        }],
    currentVersion: { type: Number, default: 1 },
    shares: [{
            id: { type: String, required: true },
            type: {
                type: String,
                enum: ['link', 'email'],
                required: true
            },
            accessLevel: {
                type: String,
                enum: ['view', 'download', 'edit'],
                default: 'view'
            },
            recipient: {
                email: { type: String, trim: true, lowercase: true },
                name: { type: String, trim: true },
                contactId: { type: Schema.Types.ObjectId, ref: 'Contact' }
            },
            token: { type: String, required: true },
            expiresAt: { type: Date },
            isPasswordProtected: { type: Boolean, default: false },
            passwordHash: { type: String },
            createdAt: { type: Date, default: Date.now },
            createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
            lastAccessedAt: { type: Date },
            accessCount: { type: Number, default: 0 },
            isRevoked: { type: Boolean, default: false },
            revokedAt: { type: Date },
            revokedBy: { type: Schema.Types.ObjectId, ref: 'User' },
            notificationSent: { type: Boolean, default: false },
            notificationSentAt: { type: Date }
        }],
    linkedEntities: {
        contactIds: [{ type: Schema.Types.ObjectId, ref: 'Contact' }],
        companyIds: [{ type: Schema.Types.ObjectId, ref: 'Company' }],
        opportunityIds: [{ type: Schema.Types.ObjectId, ref: 'Opportunity' }],
        proposalIds: [{ type: Schema.Types.ObjectId, ref: 'Proposal' }]
    },
    owner: { type: Schema.Types.ObjectId, ref: 'User', required: true, index: true },
    createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    updatedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    lastViewedAt: { type: Date },
    viewCount: { type: Number, default: 0 },
    downloadCount: { type: Number, default: 0 },
    tags: [{ type: String, trim: true }],
    customFields: { type: Schema.Types.Mixed, default: {} },
    // AI-generated content
    aiGenerated: { type: Boolean, default: false, index: true },
    aiPrompt: { type: String },
    aiModel: { type: String },
    aiConfidence: { type: Number, min: 0, max: 1 },
    // AI-generated insights
    aiInsights: {
        summary: { type: String },
        keyPoints: [{ type: String }],
        entities: [{
                name: { type: String, required: true },
                type: { type: String, required: true },
                relevance: { type: Number, min: 0, max: 1, required: true }
            }],
        sentiment: {
            overall: {
                type: String,
                enum: ['positive', 'neutral', 'negative']
            },
            score: { type: Number, min: -1, max: 1 }
        },
        topics: [{ type: String }],
        lastUpdated: { type: Date, default: Date.now }
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Create indexes
DocumentSchema.index({ name: 1, tenantId: 1 });
DocumentSchema.index({ category: 1, tenantId: 1 });
DocumentSchema.index({ fileType: 1, tenantId: 1 });
DocumentSchema.index({ isPublic: 1, tenantId: 1 });
DocumentSchema.index({ isArchived: 1, tenantId: 1 });
DocumentSchema.index({ isTemplate: 1, tenantId: 1 });
DocumentSchema.index({ owner: 1, tenantId: 1 });
DocumentSchema.index({ 'shares.token': 1 });
DocumentSchema.index({ 'shares.recipient.email': 1, tenantId: 1 });
DocumentSchema.index({ 'linkedEntities.contactIds': 1, tenantId: 1 });
DocumentSchema.index({ 'linkedEntities.companyIds': 1, tenantId: 1 });
DocumentSchema.index({ 'linkedEntities.opportunityIds': 1, tenantId: 1 });
DocumentSchema.index({ aiGenerated: 1, tenantId: 1 });
// Compound index for text search
DocumentSchema.index({ tenantId: 1, name: 'text', description: 'text', fileName: 'text' });
// Create the model
export const Document = mongoose.models.Document || mongoose.model('Document', DocumentSchema);
export default Document;
