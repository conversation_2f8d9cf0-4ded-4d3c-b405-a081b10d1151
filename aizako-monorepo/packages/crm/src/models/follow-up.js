import mongoose, { Schema } from 'mongoose';
/**
 * Follow-up schema
 */
const FollowUpSchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    type: {
        type: String,
        enum: ['email', 'call', 'meeting', 'task', 'note', 'linkedin', 'other'],
        required: true,
        index: true
    },
    subject: { type: String, required: true, trim: true },
    description: { type: String },
    priority: {
        type: String,
        enum: ['low', 'medium', 'high', 'urgent'],
        default: 'medium',
        index: true
    },
    status: {
        type: String,
        enum: ['pending', 'scheduled', 'in_progress', 'completed', 'canceled', 'overdue'],
        default: 'pending',
        index: true
    },
    dueDate: { type: Date, required: true, index: true },
    completedAt: { type: Date },
    contactId: { type: Schema.Types.ObjectId, ref: 'Contact', index: true },
    companyId: { type: Schema.Types.ObjectId, ref: 'Company', index: true },
    opportunityId: { type: Schema.Types.ObjectId, ref: 'Opportunity', index: true },
    activityId: { type: Schema.Types.ObjectId, ref: 'Activity', index: true },
    proposalId: { type: Schema.Types.ObjectId, ref: 'Proposal', index: true },
    sequenceId: { type: Schema.Types.ObjectId, ref: 'Sequence', index: true },
    sequenceStepId: { type: String, index: true },
    assignedTo: { type: Schema.Types.ObjectId, ref: 'User', required: true, index: true },
    createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    completedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    // Content for different types
    content: {
        emailTemplate: { type: String },
        emailSubject: { type: String, trim: true },
        emailBody: { type: String },
        callScript: { type: String },
        meetingAgenda: { type: String },
        linkedinMessage: { type: String },
        customContent: { type: String }
    },
    // Automation
    isAutomated: { type: Boolean, default: false, index: true },
    automationSource: {
        type: String,
        enum: ['sequence', 'workflow', 'ai_suggestion', 'manual'],
        index: true
    },
    automationRuleId: { type: String, index: true },
    // Reminders
    reminders: [{
            type: {
                type: String,
                enum: ['email', 'push', 'sms'],
                required: true
            },
            timing: { type: Number, required: true }, // minutes before due date
            sent: { type: Boolean, default: false },
            sentAt: { type: Date }
        }],
    // Tracking
    attempts: [{
            attemptedAt: { type: Date, required: true },
            attemptedBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
            outcome: {
                type: String,
                enum: ['successful', 'failed', 'no_response', 'rescheduled'],
                required: true
            },
            notes: { type: String },
            nextAttemptDate: { type: Date }
        }],
    // AI suggestions
    aiSuggestions: {
        bestTime: { type: Date },
        channel: {
            type: String,
            enum: ['email', 'call', 'linkedin']
        },
        content: { type: String },
        reasoning: { type: String },
        confidence: { type: Number, min: 0, max: 1 },
        lastUpdated: { type: Date, default: Date.now }
    },
    // Dependencies
    dependsOn: [{ type: Schema.Types.ObjectId, ref: 'FollowUp' }],
    blockedBy: [{ type: Schema.Types.ObjectId, ref: 'FollowUp' }],
    // Recurrence
    isRecurring: { type: Boolean, default: false },
    recurrence: {
        pattern: {
            type: String,
            enum: ['daily', 'weekly', 'monthly', 'quarterly', 'yearly']
        },
        interval: { type: Number, min: 1, default: 1 },
        endDate: { type: Date },
        occurrences: { type: Number, min: 1 },
        seriesId: { type: String }
    },
    parentFollowUpId: { type: Schema.Types.ObjectId, ref: 'FollowUp' },
    tags: [{ type: String, trim: true }],
    customFields: { type: Schema.Types.Mixed, default: {} }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Create indexes
FollowUpSchema.index({ type: 1, tenantId: 1 });
FollowUpSchema.index({ status: 1, tenantId: 1 });
FollowUpSchema.index({ priority: 1, tenantId: 1 });
FollowUpSchema.index({ dueDate: 1, tenantId: 1 });
FollowUpSchema.index({ assignedTo: 1, status: 1, tenantId: 1 });
FollowUpSchema.index({ contactId: 1, tenantId: 1 });
FollowUpSchema.index({ companyId: 1, tenantId: 1 });
FollowUpSchema.index({ opportunityId: 1, tenantId: 1 });
FollowUpSchema.index({ sequenceId: 1, tenantId: 1 });
FollowUpSchema.index({ isAutomated: 1, tenantId: 1 });
FollowUpSchema.index({ automationSource: 1, tenantId: 1 });
FollowUpSchema.index({ isRecurring: 1, tenantId: 1 });
FollowUpSchema.index({ parentFollowUpId: 1, tenantId: 1 });
// Compound index for overdue follow-ups
FollowUpSchema.index({ tenantId: 1, status: 1, dueDate: 1 });
// Compound index for user's follow-ups
FollowUpSchema.index({ tenantId: 1, assignedTo: 1, status: 1, dueDate: 1 });
// Compound index for text search
FollowUpSchema.index({ tenantId: 1, subject: 'text', description: 'text' });
// Create the model
export const FollowUp = mongoose.models.FollowUp || mongoose.model('FollowUp', FollowUpSchema);
export default FollowUp;
