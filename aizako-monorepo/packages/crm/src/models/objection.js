import mongoose, { Schema } from 'mongoose';
/**
 * Objection schema
 */
const ObjectionSchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    title: { type: String, required: true, trim: true },
    description: { type: String, required: true },
    category: {
        type: String,
        enum: ['price', 'product', 'competition', 'timing', 'authority', 'need', 'trust', 'process', 'other'],
        required: true,
        index: true
    },
    severity: {
        type: String,
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'medium',
        index: true
    },
    frequency: { type: Number, default: 0, index: true },
    industry: { type: String, trim: true, index: true },
    companySize: {
        type: String,
        enum: ['startup', 'small', 'medium', 'large', 'enterprise'],
        index: true
    },
    stage: {
        type: String,
        enum: ['qualification', 'discovery', 'proposal', 'negotiation', 'closing'],
        index: true
    },
    responses: [{
            id: { type: String, required: true },
            response: { type: String, required: true },
            context: { type: String },
            effectiveness: { type: Number, min: 1, max: 5 },
            usageCount: { type: Number, default: 0 },
            successRate: { type: Number, min: 0, max: 100 },
            aiGenerated: { type: Boolean, default: false },
            aiModel: { type: String },
            aiConfidence: { type: Number, min: 0, max: 1 },
            createdAt: { type: Date, default: Date.now },
            createdBy: { type: Schema.Types.ObjectId, ref: 'User' },
            lastUsedAt: { type: Date },
            tags: [{ type: String, trim: true }]
        }],
    relatedObjections: [{ type: Schema.Types.ObjectId, ref: 'Objection' }],
    keywords: [{ type: String, trim: true, lowercase: true }],
    tags: [{ type: String, trim: true }],
    isCommon: { type: Boolean, default: false, index: true },
    isActive: { type: Boolean, default: true, index: true },
    // Analytics
    analytics: {
        totalOccurrences: { type: Number, default: 0 },
        successfulHandles: { type: Number, default: 0 },
        averageResolutionTime: { type: Number }, // in minutes
        conversionRate: { type: Number, min: 0, max: 100 },
        lastOccurrence: { type: Date },
        trendDirection: {
            type: String,
            enum: ['increasing', 'decreasing', 'stable']
        }
    },
    // AI insights
    insights: {
        rootCause: { type: String },
        preventionTips: [{ type: String }],
        bestPractices: [{ type: String }],
        competitorComparisons: [{
                competitor: { type: String, required: true, trim: true },
                advantage: { type: String },
                response: { type: String }
            }],
        aiConfidence: { type: Number, min: 0, max: 1 },
        lastUpdated: { type: Date, default: Date.now }
    },
    // Tracking
    createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    lastUpdatedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    customFields: { type: Schema.Types.Mixed, default: {} }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Create indexes
ObjectionSchema.index({ category: 1, tenantId: 1 });
ObjectionSchema.index({ severity: 1, tenantId: 1 });
ObjectionSchema.index({ frequency: -1, tenantId: 1 });
ObjectionSchema.index({ industry: 1, tenantId: 1 });
ObjectionSchema.index({ companySize: 1, tenantId: 1 });
ObjectionSchema.index({ stage: 1, tenantId: 1 });
ObjectionSchema.index({ isCommon: 1, tenantId: 1 });
ObjectionSchema.index({ isActive: 1, tenantId: 1 });
ObjectionSchema.index({ keywords: 1, tenantId: 1 });
ObjectionSchema.index({ createdBy: 1, tenantId: 1 });
ObjectionSchema.index({ 'analytics.totalOccurrences': -1, tenantId: 1 });
ObjectionSchema.index({ 'analytics.conversionRate': -1, tenantId: 1 });
// Compound index for common objections
ObjectionSchema.index({ tenantId: 1, isCommon: 1, frequency: -1 });
// Compound index for text search
ObjectionSchema.index({ tenantId: 1, title: 'text', description: 'text', keywords: 'text' });
// Create the model
export const Objection = mongoose.models.Objection || mongoose.model('Objection', ObjectionSchema);
export default Objection;
