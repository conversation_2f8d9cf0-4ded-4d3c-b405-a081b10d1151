import mongoose, { Schema } from 'mongoose';
/**
 * Proposal analytics event schema
 */
const ProposalAnalyticsEventSchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    proposalId: { type: Schema.Types.ObjectId, ref: 'Proposal', required: true, index: true },
    eventType: {
        type: String,
        enum: ['view', 'download', 'share', 'comment', 'accept', 'reject', 'section_view'],
        required: true,
        index: true
    },
    userId: { type: Schema.Types.ObjectId, ref: 'User' },
    userEmail: { type: String, trim: true },
    userName: { type: String, trim: true },
    ipAddress: { type: String, trim: true },
    userAgent: { type: String },
    device: { type: String, trim: true },
    location: { type: String, trim: true },
    duration: { type: Number },
    sectionId: { type: String, trim: true },
    data: { type: Schema.Types.Mixed, default: {} },
    timestamp: { type: Date, default: Date.now, index: true },
}, {
    timestamps: true,
});
// Create indexes
ProposalAnalyticsEventSchema.index({ proposalId: 1, eventType: 1, timestamp: 1, tenantId: 1 });
ProposalAnalyticsEventSchema.index({ userId: 1, tenantId: 1 });
ProposalAnalyticsEventSchema.index({ userEmail: 1, tenantId: 1 });
// Create the model
export const ProposalAnalyticsEvent = mongoose.models.ProposalAnalyticsEvent ||
    mongoose.model('ProposalAnalyticsEvent', ProposalAnalyticsEventSchema);
export default ProposalAnalyticsEvent;
