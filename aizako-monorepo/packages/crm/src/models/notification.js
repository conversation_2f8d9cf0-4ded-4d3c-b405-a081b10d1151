import mongoose, { Schema } from 'mongoose';
/**
 * Notification schema
 */
const NotificationSchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    type: {
        type: String,
        enum: ['info', 'warning', 'error', 'success', 'reminder', 'alert'],
        required: true,
        index: true
    },
    title: { type: String, required: true, trim: true },
    message: { type: String, required: true },
    category: {
        type: String,
        enum: ['system', 'activity', 'opportunity', 'contact', 'proposal', 'sequence', 'workflow', 'other'],
        required: true,
        index: true
    },
    priority: {
        type: String,
        enum: ['low', 'medium', 'high', 'urgent'],
        default: 'medium',
        index: true
    },
    status: {
        type: String,
        enum: ['unread', 'read', 'archived', 'dismissed'],
        default: 'unread',
        index: true
    },
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true, index: true },
    relatedEntity: {
        type: {
            type: String,
            enum: ['contact', 'company', 'opportunity', 'activity', 'proposal', 'sequence', 'workflow']
        },
        id: { type: Schema.Types.ObjectId },
        name: { type: String, trim: true }
    },
    actionUrl: { type: String, trim: true },
    actionLabel: { type: String, trim: true },
    data: { type: Schema.Types.Mixed, default: {} },
    channels: [{
            type: String,
            enum: ['in_app', 'email', 'sms', 'push'],
            required: true
        }],
    deliveryStatus: {
        in_app: {
            delivered: { type: Boolean, default: false },
            deliveredAt: { type: Date },
            readAt: { type: Date }
        },
        email: {
            delivered: { type: Boolean, default: false },
            deliveredAt: { type: Date },
            openedAt: { type: Date },
            clickedAt: { type: Date },
            bounced: { type: Boolean, default: false },
            error: { type: String }
        },
        sms: {
            delivered: { type: Boolean, default: false },
            deliveredAt: { type: Date },
            error: { type: String }
        },
        push: {
            delivered: { type: Boolean, default: false },
            deliveredAt: { type: Date },
            clickedAt: { type: Date },
            error: { type: String }
        }
    },
    scheduledFor: { type: Date, index: true },
    expiresAt: { type: Date, index: true },
    readAt: { type: Date },
    archivedAt: { type: Date },
    dismissedAt: { type: Date },
    metadata: { type: Schema.Types.Mixed, default: {} },
    // Batch notification support
    batchId: { type: String, index: true },
    isBatch: { type: Boolean, default: false },
    batchSize: { type: Number },
    // AI-generated notifications
    aiGenerated: { type: Boolean, default: false },
    aiModel: { type: String },
    aiConfidence: { type: Number, min: 0, max: 1 }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Create indexes
NotificationSchema.index({ userId: 1, status: 1, tenantId: 1 });
NotificationSchema.index({ type: 1, tenantId: 1 });
NotificationSchema.index({ category: 1, tenantId: 1 });
NotificationSchema.index({ priority: 1, tenantId: 1 });
NotificationSchema.index({ scheduledFor: 1, tenantId: 1 });
NotificationSchema.index({ expiresAt: 1, tenantId: 1 });
NotificationSchema.index({ 'relatedEntity.type': 1, 'relatedEntity.id': 1, tenantId: 1 });
NotificationSchema.index({ batchId: 1, tenantId: 1 });
NotificationSchema.index({ createdAt: -1, tenantId: 1 });
// Compound index for user notifications
NotificationSchema.index({ tenantId: 1, userId: 1, status: 1, createdAt: -1 });
// Compound index for text search
NotificationSchema.index({ tenantId: 1, title: 'text', message: 'text' });
// Create the model
export const Notification = mongoose.models.Notification || mongoose.model('Notification', NotificationSchema);
export default Notification;
