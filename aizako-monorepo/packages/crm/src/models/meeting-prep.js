import mongoose, { Schema } from 'mongoose';
/**
 * Meeting prep schema
 */
const MeetingPrepSchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    title: { type: String, required: true, trim: true },
    description: { type: String },
    meetingDate: { type: Date, required: true, index: true },
    duration: { type: Number, required: true, min: 15, max: 480 }, // 15 minutes to 8 hours
    type: {
        type: String,
        enum: ['discovery', 'demo', 'proposal', 'negotiation', 'closing', 'follow_up', 'check_in', 'other'],
        required: true,
        index: true
    },
    status: {
        type: String,
        enum: ['draft', 'in_progress', 'completed', 'canceled'],
        default: 'draft',
        index: true
    },
    // Related entities
    opportunityId: { type: Schema.Types.ObjectId, ref: 'Opportunity', index: true },
    contactId: { type: Schema.Types.ObjectId, ref: 'Contact', index: true },
    companyId: { type: Schema.Types.ObjectId, ref: 'Company', index: true },
    proposalId: { type: Schema.Types.ObjectId, ref: 'Proposal', index: true },
    // Meeting details
    location: { type: String, trim: true },
    meetingLink: { type: String, trim: true },
    calendarEventId: { type: String, trim: true },
    attendees: [{
            contactId: { type: Schema.Types.ObjectId, ref: 'Contact' },
            name: { type: String, required: true, trim: true },
            email: { type: String, trim: true, lowercase: true },
            title: { type: String, trim: true },
            company: { type: String, trim: true },
            role: {
                type: String,
                enum: ['organizer', 'required', 'optional', 'decision_maker', 'influencer', 'champion', 'blocker'],
                required: true
            },
            influence: {
                type: String,
                enum: ['high', 'medium', 'low'],
                required: true
            },
            sentiment: {
                type: String,
                enum: ['positive', 'neutral', 'negative', 'unknown']
            },
            lastInteraction: { type: Date },
            notes: { type: String },
            objectives: [{ type: String }],
            concerns: [{ type: String }]
        }],
    // Preparation content
    objectives: [{ type: String, required: true }],
    agenda: [{
            id: { type: String, required: true },
            topic: { type: String, required: true, trim: true },
            duration: { type: Number, required: true, min: 5 },
            type: {
                type: String,
                enum: ['introduction', 'discovery', 'presentation', 'demo', 'discussion', 'q_and_a', 'next_steps', 'closing'],
                required: true
            },
            owner: { type: String, trim: true },
            notes: { type: String },
            materials: [{ type: String }]
        }],
    // Research and context
    research: {
        companyBackground: { type: String },
        recentNews: [{
                title: { type: String, required: true, trim: true },
                url: { type: String, trim: true },
                date: { type: Date },
                summary: { type: String },
                relevance: {
                    type: String,
                    enum: ['high', 'medium', 'low']
                }
            }],
        competitorAnalysis: [{
                competitor: { type: String, required: true, trim: true },
                strengths: [{ type: String }],
                weaknesses: [{ type: String }],
                differentiators: [{ type: String }]
            }],
        stakeholderMap: [{
                name: { type: String, required: true, trim: true },
                title: { type: String, trim: true },
                influence: {
                    type: String,
                    enum: ['high', 'medium', 'low'],
                    required: true
                },
                sentiment: {
                    type: String,
                    enum: ['positive', 'neutral', 'negative', 'unknown'],
                    required: true
                },
                objectives: [{ type: String }],
                concerns: [{ type: String }]
            }],
        painPoints: [{ type: String }],
        opportunities: [{ type: String }],
        risks: [{ type: String }]
    },
    // Questions and talking points
    discoveryQuestions: [{
            category: {
                type: String,
                enum: ['business', 'technical', 'process', 'budget', 'timeline', 'decision_making'],
                required: true
            },
            question: { type: String, required: true },
            priority: {
                type: String,
                enum: ['high', 'medium', 'low'],
                default: 'medium'
            },
            followUp: [{ type: String }],
            expectedAnswer: { type: String },
            notes: { type: String }
        }],
    talkingPoints: [{
            topic: { type: String, required: true, trim: true },
            points: [{ type: String, required: true }],
            supporting_data: [{ type: String }],
            objections: [{
                    objection: { type: String, required: true },
                    response: { type: String, required: true }
                }]
        }],
    // Materials and resources
    materials: [{
            type: {
                type: String,
                enum: ['presentation', 'demo', 'case_study', 'proposal', 'contract', 'brochure', 'video', 'other'],
                required: true
            },
            name: { type: String, required: true, trim: true },
            url: { type: String, trim: true },
            description: { type: String },
            isRequired: { type: Boolean, default: false }
        }],
    // AI-generated insights
    aiInsights: {
        recommendedApproach: { type: String },
        keyTopics: [{ type: String }],
        riskFactors: [{ type: String }],
        successFactors: [{ type: String }],
        personalizedTips: [{ type: String }],
        competitiveIntel: [{ type: String }],
        confidence: { type: Number, min: 0, max: 1 },
        model: { type: String },
        lastUpdated: { type: Date, default: Date.now }
    },
    // Follow-up planning
    followUp: {
        nextSteps: [{
                action: { type: String, required: true },
                owner: { type: String, required: true, trim: true },
                dueDate: { type: Date },
                priority: {
                    type: String,
                    enum: ['high', 'medium', 'low'],
                    default: 'medium'
                }
            }],
        scheduledFollowUp: { type: Date },
        proposalDeadline: { type: Date },
        decisionTimeline: { type: String }
    },
    // Meeting outcome (post-meeting)
    outcome: {
        status: {
            type: String,
            enum: ['successful', 'partially_successful', 'unsuccessful', 'rescheduled']
        },
        attendanceRate: { type: Number, min: 0, max: 100 },
        objectivesMet: [{
                objective: { type: String, required: true },
                achieved: { type: Boolean, required: true },
                notes: { type: String }
            }],
        keyDecisions: [{ type: String }],
        actionItems: [{
                action: { type: String, required: true },
                owner: { type: String, required: true, trim: true },
                dueDate: { type: Date },
                status: {
                    type: String,
                    enum: ['pending', 'in_progress', 'completed'],
                    default: 'pending'
                }
            }],
        nextMeeting: { type: Date },
        feedback: { type: String },
        lessonsLearned: [{ type: String }]
    },
    // Collaboration
    collaborators: [{
            userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
            role: {
                type: String,
                enum: ['owner', 'contributor', 'reviewer'],
                required: true
            },
            permissions: [{ type: String }]
        }],
    // Tracking
    createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    updatedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    completedAt: { type: Date },
    tags: [{ type: String, trim: true }],
    customFields: { type: Schema.Types.Mixed, default: {} }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Create indexes
MeetingPrepSchema.index({ meetingDate: 1, tenantId: 1 });
MeetingPrepSchema.index({ type: 1, tenantId: 1 });
MeetingPrepSchema.index({ status: 1, tenantId: 1 });
MeetingPrepSchema.index({ opportunityId: 1, tenantId: 1 });
MeetingPrepSchema.index({ contactId: 1, tenantId: 1 });
MeetingPrepSchema.index({ companyId: 1, tenantId: 1 });
MeetingPrepSchema.index({ createdBy: 1, tenantId: 1 });
MeetingPrepSchema.index({ 'collaborators.userId': 1, tenantId: 1 });
MeetingPrepSchema.index({ completedAt: 1, tenantId: 1 });
// Compound index for upcoming meetings
MeetingPrepSchema.index({ tenantId: 1, status: 1, meetingDate: 1 });
// Compound index for user's meetings
MeetingPrepSchema.index({ tenantId: 1, createdBy: 1, meetingDate: 1 });
// Compound index for text search
MeetingPrepSchema.index({ tenantId: 1, title: 'text', description: 'text' });
// Create the model
export const MeetingPrep = mongoose.models.MeetingPrep || mongoose.model('MeetingPrep', MeetingPrepSchema);
export default MeetingPrep;
