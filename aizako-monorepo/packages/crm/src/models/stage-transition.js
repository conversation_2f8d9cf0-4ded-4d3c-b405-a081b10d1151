import mongoose, { Schema } from 'mongoose';
/**
 * Stage transition schema
 */
const StageTransitionSchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    entityType: {
        type: String,
        enum: ['opportunity', 'contact', 'company', 'proposal', 'sequence'],
        required: true,
        index: true
    },
    entityId: { type: Schema.Types.ObjectId, required: true, index: true },
    entityName: { type: String, trim: true },
    fromStage: { type: String, required: true, trim: true, index: true },
    toStage: { type: String, required: true, trim: true, index: true },
    transitionDate: { type: Date, required: true, default: Date.now, index: true },
    daysInPreviousStage: { type: Number, required: true, min: 0 },
    reason: { type: String, trim: true },
    reasonCategory: {
        type: String,
        enum: ['progression', 'regression', 'correction', 'automation', 'manual', 'other'],
        index: true
    },
    triggeredBy: {
        type: String,
        enum: ['user', 'automation', 'ai', 'integration', 'system'],
        required: true,
        index: true
    },
    userId: { type: Schema.Types.ObjectId, ref: 'User', index: true },
    automationId: { type: String, index: true },
    automationType: {
        type: String,
        enum: ['workflow', 'sequence', 'ai_recommendation', 'rule'],
        index: true
    },
    // Context data
    context: {
        previousValue: { type: Schema.Types.Mixed },
        newValue: { type: Schema.Types.Mixed },
        metadata: { type: Schema.Types.Mixed, default: {} },
        source: { type: String, trim: true },
        sourceId: { type: String, trim: true }
    },
    // Performance metrics
    metrics: {
        conversionRate: { type: Number, min: 0, max: 100 },
        averageTimeInStage: { type: Number, min: 0 },
        benchmarkTime: { type: Number, min: 0 },
        velocity: { type: Number },
        probability: { type: Number, min: 0, max: 100 }
    },
    // Associated activities
    activities: [{
            activityId: { type: Schema.Types.ObjectId, ref: 'Activity', required: true },
            type: { type: String, required: true, trim: true },
            date: { type: Date, required: true },
            impact: {
                type: String,
                enum: ['positive', 'neutral', 'negative'],
                required: true
            }
        }],
    // Validation and approval
    requiresApproval: { type: Boolean, default: false },
    approvedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    approvedAt: { type: Date },
    rejectedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    rejectedAt: { type: Date },
    rejectionReason: { type: String },
    // AI insights
    aiInsights: {
        predictedNextStage: { type: String, trim: true },
        predictedTimeToNext: { type: Number, min: 0 },
        riskFactors: [{ type: String }],
        recommendations: [{ type: String }],
        confidence: { type: Number, min: 0, max: 1 },
        model: { type: String, trim: true },
        lastUpdated: { type: Date, default: Date.now }
    },
    // Notifications
    notificationsSent: [{
            type: {
                type: String,
                enum: ['email', 'push', 'slack', 'webhook'],
                required: true
            },
            recipient: { type: String, required: true, trim: true },
            sentAt: { type: Date, required: true },
            status: {
                type: String,
                enum: ['sent', 'delivered', 'failed'],
                required: true
            }
        }],
    tags: [{ type: String, trim: true }],
    customFields: { type: Schema.Types.Mixed, default: {} }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Create indexes
StageTransitionSchema.index({ entityType: 1, entityId: 1, tenantId: 1 });
StageTransitionSchema.index({ fromStage: 1, toStage: 1, tenantId: 1 });
StageTransitionSchema.index({ transitionDate: -1, tenantId: 1 });
StageTransitionSchema.index({ triggeredBy: 1, tenantId: 1 });
StageTransitionSchema.index({ userId: 1, tenantId: 1 });
StageTransitionSchema.index({ automationId: 1, tenantId: 1 });
StageTransitionSchema.index({ automationType: 1, tenantId: 1 });
StageTransitionSchema.index({ reasonCategory: 1, tenantId: 1 });
StageTransitionSchema.index({ requiresApproval: 1, tenantId: 1 });
StageTransitionSchema.index({ 'activities.activityId': 1, tenantId: 1 });
// Compound index for entity transitions
StageTransitionSchema.index({ tenantId: 1, entityType: 1, entityId: 1, transitionDate: -1 });
// Compound index for stage analysis
StageTransitionSchema.index({ tenantId: 1, fromStage: 1, toStage: 1, transitionDate: -1 });
// Compound index for performance analysis
StageTransitionSchema.index({ tenantId: 1, entityType: 1, fromStage: 1, 'metrics.conversionRate': -1 });
// Compound index for text search
StageTransitionSchema.index({ tenantId: 1, reason: 'text', entityName: 'text' });
// Create the model
export const StageTransition = mongoose.models.StageTransition || mongoose.model('StageTransition', StageTransitionSchema);
export default StageTransition;
