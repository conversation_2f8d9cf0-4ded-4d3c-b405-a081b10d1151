import mongoose, { Schema } from 'mongoose';
/**
 * Attribution schema
 */
const AttributionSchema = new Schema({
    tenantId: { type: String, required: true, index: true },
    entityType: {
        type: String,
        enum: ['contact', 'opportunity', 'company'],
        required: true,
        index: true
    },
    entityId: { type: Schema.Types.ObjectId, required: true, index: true },
    entityName: { type: String, trim: true },
    conversionEvent: {
        type: String,
        enum: ['lead_created', 'opportunity_created', 'deal_won', 'meeting_scheduled', 'proposal_sent', 'custom'],
        required: true,
        index: true
    },
    conversionValue: { type: Number, required: true, min: 0 },
    conversionDate: { type: Date, required: true, index: true },
    currency: { type: String, default: 'USD', uppercase: true },
    // Journey data
    journey: {
        startDate: { type: Date, required: true },
        endDate: { type: Date, required: true },
        duration: { type: Number, required: true, min: 0 },
        touchpointCount: { type: Number, required: true, min: 1 },
        channelCount: { type: Number, required: true, min: 1 },
        touchpoints: [{
                id: { type: String, required: true },
                timestamp: { type: Date, required: true },
                channel: { type: String, required: true, trim: true },
                source: { type: String, required: true, trim: true },
                medium: { type: String, trim: true },
                campaign: { type: String, trim: true },
                content: { type: String, trim: true },
                term: { type: String, trim: true },
                activityId: { type: Schema.Types.ObjectId, ref: 'Activity' },
                activityType: { type: String, trim: true },
                value: { type: Number, min: 0 },
                weight: { type: Number, min: 0, max: 1 },
                position: { type: Number, required: true, min: 1 },
                isFirstTouch: { type: Boolean, required: true },
                isLastTouch: { type: Boolean, required: true },
                timeToNext: { type: Number, min: 0 },
                attribution: {
                    firstTouch: { type: Number, min: 0 },
                    lastTouch: { type: Number, min: 0 },
                    linear: { type: Number, min: 0 },
                    timeDecay: { type: Number, min: 0 },
                    positionBased: { type: Number, min: 0 },
                    custom: { type: Number, min: 0 }
                },
                metadata: { type: Schema.Types.Mixed, default: {} }
            }]
    },
    // Attribution models
    attributionModels: {
        firstTouch: {
            channel: { type: String, required: true, trim: true },
            source: { type: String, required: true, trim: true },
            value: { type: Number, required: true, min: 0 },
            percentage: { type: Number, required: true, min: 0, max: 100 }
        },
        lastTouch: {
            channel: { type: String, required: true, trim: true },
            source: { type: String, required: true, trim: true },
            value: { type: Number, required: true, min: 0 },
            percentage: { type: Number, required: true, min: 0, max: 100 }
        },
        linear: [{
                channel: { type: String, required: true, trim: true },
                source: { type: String, required: true, trim: true },
                value: { type: Number, required: true, min: 0 },
                percentage: { type: Number, required: true, min: 0, max: 100 }
            }],
        timeDecay: [{
                channel: { type: String, required: true, trim: true },
                source: { type: String, required: true, trim: true },
                value: { type: Number, required: true, min: 0 },
                percentage: { type: Number, required: true, min: 0, max: 100 },
                decayFactor: { type: Number, required: true, min: 0, max: 1 }
            }],
        positionBased: [{
                channel: { type: String, required: true, trim: true },
                source: { type: String, required: true, trim: true },
                value: { type: Number, required: true, min: 0 },
                percentage: { type: Number, required: true, min: 0, max: 100 },
                position: {
                    type: String,
                    enum: ['first', 'middle', 'last'],
                    required: true
                }
            }],
        custom: [{
                channel: { type: String, required: true, trim: true },
                source: { type: String, required: true, trim: true },
                value: { type: Number, required: true, min: 0 },
                percentage: { type: Number, required: true, min: 0, max: 100 },
                weight: { type: Number, required: true, min: 0, max: 1 }
            }]
    },
    // Channel analysis
    channelAnalysis: [{
            channel: { type: String, required: true, trim: true },
            touchpointCount: { type: Number, required: true, min: 0 },
            firstTouchValue: { type: Number, required: true, min: 0 },
            lastTouchValue: { type: Number, required: true, min: 0 },
            linearValue: { type: Number, required: true, min: 0 },
            timeDecayValue: { type: Number, required: true, min: 0 },
            positionBasedValue: { type: Number, required: true, min: 0 },
            customValue: { type: Number, min: 0 },
            averageTimeToNext: { type: Number, min: 0 },
            conversionRate: { type: Number, min: 0, max: 100 },
            costPerAcquisition: { type: Number, min: 0 },
            returnOnInvestment: { type: Number }
        }],
    // Campaign analysis
    campaignAnalysis: [{
            campaign: { type: String, required: true, trim: true },
            channel: { type: String, required: true, trim: true },
            source: { type: String, required: true, trim: true },
            touchpointCount: { type: Number, required: true, min: 0 },
            attributedValue: { type: Number, required: true, min: 0 },
            conversionRate: { type: Number, min: 0, max: 100 },
            costPerAcquisition: { type: Number, min: 0 },
            returnOnInvestment: { type: Number }
        }],
    // Content analysis
    contentAnalysis: [{
            content: { type: String, required: true, trim: true },
            channel: { type: String, required: true, trim: true },
            touchpointCount: { type: Number, required: true, min: 0 },
            attributedValue: { type: Number, required: true, min: 0 },
            engagementRate: { type: Number, min: 0, max: 100 },
            conversionRate: { type: Number, min: 0, max: 100 }
        }],
    // Time-based analysis
    timeAnalysis: {
        hourOfDay: [{
                hour: { type: Number, required: true, min: 0, max: 23 },
                touchpointCount: { type: Number, required: true, min: 0 },
                attributedValue: { type: Number, required: true, min: 0 }
            }],
        dayOfWeek: [{
                day: { type: Number, required: true, min: 0, max: 6 },
                touchpointCount: { type: Number, required: true, min: 0 },
                attributedValue: { type: Number, required: true, min: 0 }
            }],
        monthOfYear: [{
                month: { type: Number, required: true, min: 1, max: 12 },
                touchpointCount: { type: Number, required: true, min: 0 },
                attributedValue: { type: Number, required: true, min: 0 }
            }]
    },
    // AI insights
    aiInsights: {
        keyInfluencers: [{
                factor: { type: String, required: true },
                impact: { type: Number, required: true },
                confidence: { type: Number, min: 0, max: 1, required: true }
            }],
        optimizationRecommendations: [{
                recommendation: { type: String, required: true },
                expectedImpact: { type: Number, required: true },
                confidence: { type: Number, min: 0, max: 1, required: true },
                priority: {
                    type: String,
                    enum: ['high', 'medium', 'low'],
                    required: true
                }
            }],
        predictedChannelPerformance: [{
                channel: { type: String, required: true, trim: true },
                predictedValue: { type: Number, required: true, min: 0 },
                confidence: { type: Number, min: 0, max: 1, required: true },
                timeframe: { type: String, required: true }
            }],
        anomalies: [{
                type: { type: String, required: true },
                description: { type: String, required: true },
                severity: {
                    type: String,
                    enum: ['high', 'medium', 'low'],
                    required: true
                },
                timestamp: { type: Date, required: true }
            }],
        lastUpdated: { type: Date },
        model: { type: String }
    },
    // Quality metrics
    quality: {
        dataCompleteness: { type: Number, min: 0, max: 100 },
        touchpointAccuracy: { type: Number, min: 0, max: 100 },
        timelineConsistency: { type: Number, min: 0, max: 100 },
        sourceReliability: { type: Number, min: 0, max: 100 },
        overallScore: { type: Number, min: 0, max: 100 },
        issues: [{ type: String }]
    },
    // Configuration
    config: {
        lookbackWindow: { type: Number, min: 1, max: 365, default: 90 },
        attributionWindow: { type: Number, min: 1, max: 365, default: 30 },
        excludedChannels: [{ type: String, trim: true }],
        customWeights: { type: Map, of: Number },
        decayRate: { type: Number, min: 0, max: 1, default: 0.7 },
        positionWeights: {
            first: { type: Number, min: 0, max: 1, default: 0.4 },
            middle: { type: Number, min: 0, max: 1, default: 0.2 },
            last: { type: Number, min: 0, max: 1, default: 0.4 }
        }
    },
    // Metadata
    calculatedAt: { type: Date, default: Date.now, index: true },
    calculatedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    version: { type: Number, default: 1 },
    isActive: { type: Boolean, default: true, index: true },
    tags: [{ type: String, trim: true }],
    customFields: { type: Schema.Types.Mixed, default: {} }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Create indexes
AttributionSchema.index({ entityType: 1, entityId: 1, tenantId: 1 });
AttributionSchema.index({ conversionEvent: 1, tenantId: 1 });
AttributionSchema.index({ conversionDate: -1, tenantId: 1 });
AttributionSchema.index({ conversionValue: -1, tenantId: 1 });
AttributionSchema.index({ 'journey.startDate': 1, 'journey.endDate': 1, tenantId: 1 });
AttributionSchema.index({ 'journey.touchpoints.channel': 1, tenantId: 1 });
AttributionSchema.index({ 'journey.touchpoints.source': 1, tenantId: 1 });
AttributionSchema.index({ 'journey.touchpoints.campaign': 1, tenantId: 1 });
AttributionSchema.index({ 'attributionModels.firstTouch.channel': 1, tenantId: 1 });
AttributionSchema.index({ 'attributionModels.lastTouch.channel': 1, tenantId: 1 });
AttributionSchema.index({ calculatedAt: -1, tenantId: 1 });
AttributionSchema.index({ isActive: 1, tenantId: 1 });
// Compound index for entity attribution
AttributionSchema.index({ tenantId: 1, entityType: 1, entityId: 1, isActive: 1, calculatedAt: -1 });
// Compound index for conversion analysis
AttributionSchema.index({ tenantId: 1, conversionEvent: 1, conversionDate: -1, conversionValue: -1 });
// Create the model
export const Attribution = mongoose.models.Attribution || mongoose.model('Attribution', AttributionSchema);
export default Attribution;
