/**
 * Format a number as currency
 * @param amount Amount to format
 * @param currency Currency code (default: USD)
 * @param locale Locale (default: en-US)
 * @returns Formatted currency string
 */
export function formatCurrency(amount, currency = 'USD', locale = 'en-US') {
    return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency,
    }).format(amount);
}
/**
 * Format a date
 * @param date Date to format
 * @param options Intl.DateTimeFormatOptions
 * @param locale Locale (default: en-US)
 * @returns Formatted date string
 */
export function formatDate(date, options = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
}, locale = 'en-US') {
    if (!date)
        return '';
    const dateObj = typeof date === 'string' || typeof date === 'number'
        ? new Date(date)
        : date;
    return new Intl.DateTimeFormat(locale, options).format(dateObj);
}
/**
 * Format a number
 * @param value Number to format
 * @param options Intl.NumberFormatOptions
 * @param locale Locale (default: en-US)
 * @returns Formatted number string
 */
export function formatNumber(value, options = {}, locale = 'en-US') {
    return new Intl.NumberFormat(locale, options).format(value);
}
/**
 * Format a percentage
 * @param value Value to format as percentage (0-1)
 * @param decimals Number of decimal places
 * @param locale Locale (default: en-US)
 * @returns Formatted percentage string
 */
export function formatPercentage(value, decimals = 1, locale = 'en-US') {
    return new Intl.NumberFormat(locale, {
        style: 'percent',
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals,
    }).format(value);
}
/**
 * Format a phone number
 * @param phoneNumber Phone number to format
 * @param format Format pattern (default: (xxx) xxx-xxxx)
 * @returns Formatted phone number
 */
export function formatPhoneNumber(phoneNumber, format = '(xxx) xxx-xxxx') {
    // Remove all non-numeric characters
    const cleaned = phoneNumber.replace(/\D/g, '');
    // Check if the input is valid
    if (cleaned.length === 0) {
        return '';
    }
    // Replace x with digits from the cleaned number
    let formatted = format;
    let digitIndex = 0;
    for (let i = 0; i < formatted.length; i++) {
        if (formatted[i] === 'x') {
            if (digitIndex < cleaned.length) {
                formatted = formatted.substring(0, i) + cleaned[digitIndex] + formatted.substring(i + 1);
                digitIndex++;
            }
            else {
                // If we run out of digits, replace remaining x's with placeholder
                formatted = formatted.substring(0, i) + '_' + formatted.substring(i + 1);
            }
        }
    }
    return formatted;
}
/**
 * Format file size
 * @param bytes Size in bytes
 * @param decimals Number of decimal places
 * @returns Formatted file size string
 */
export function formatFileSize(bytes, decimals = 2) {
    if (bytes === 0)
        return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}
/**
 * Truncate text with ellipsis
 * @param text Text to truncate
 * @param maxLength Maximum length
 * @param ellipsis Ellipsis string (default: ...)
 * @returns Truncated text
 */
export function truncateText(text, maxLength, ellipsis = '...') {
    if (text.length <= maxLength) {
        return text;
    }
    return text.substring(0, maxLength - ellipsis.length) + ellipsis;
}
/**
 * Format a name (first + last)
 * @param firstName First name
 * @param lastName Last name
 * @returns Formatted name
 */
export function formatName(firstName, lastName) {
    if (!firstName && !lastName)
        return '';
    if (!firstName)
        return lastName || '';
    if (!lastName)
        return firstName;
    return `${firstName} ${lastName}`;
}
/**
 * Format an address
 * @param address Address object
 * @param singleLine Whether to format as a single line
 * @returns Formatted address
 */
export function formatAddress(address, singleLine = false) {
    const { street1, street2, city, state, postalCode, country } = address;
    if (!street1 && !city)
        return '';
    const parts = [
        street1,
        street2,
        city && state ? `${city}, ${state}` : city || state,
        postalCode,
        country,
    ].filter(Boolean);
    return singleLine ? parts.join(', ') : parts.join('\n');
}
