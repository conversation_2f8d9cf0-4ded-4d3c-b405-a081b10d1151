import { useEffect, useCallback } from 'react';
/**
 * Parse a key combination string into its components
 * @param keyCombination Key combination string (e.g., 'Ctrl+S')
 * @returns Object with key and modifiers
 */
const parseKeyCombination = (keyCombination) => {
    const parts = keyCombination.split('+');
    const key = parts.pop()?.toLowerCase() || '';
    const ctrl = parts.includes('Ctrl') || parts.includes('Control');
    const shift = parts.includes('Shift');
    const alt = parts.includes('Alt');
    const meta = parts.includes('Meta') || parts.includes('Cmd') || parts.includes('Command');
    return { key, ctrl, shift, alt, meta };
};
/**
 * Check if an event matches a key combination
 * @param event Keyboard event
 * @param keyCombination Key combination to check
 * @returns Whether the event matches the key combination
 */
const matchesKeyCombination = (event, keyCombination) => {
    const { key, ctrl, shift, alt, meta } = parseKeyCombination(keyCombination);
    // Check if the key matches
    const eventKey = event.key.toLowerCase();
    const keyMatches = eventKey === key;
    // Check if modifiers match
    const ctrlMatches = event.ctrlKey === ctrl;
    const shiftMatches = event.shiftKey === shift;
    const altMatches = event.altKey === alt;
    const metaMatches = event.metaKey === meta;
    return keyMatches && ctrlMatches && shiftMatches && altMatches && metaMatches;
};
/**
 * Hook to register keyboard shortcuts
 * @param shortcuts Array of keyboard shortcuts
 */
export const useKeyboardShortcuts = (shortcuts) => {
    const handleKeyDown = useCallback((event) => {
        // Skip if the event target is an input element and allowInInputs is not set
        const target = event.target;
        const isInput = target.tagName === 'INPUT' ||
            target.tagName === 'TEXTAREA' ||
            target.isContentEditable;
        // Find matching shortcut
        for (const shortcut of shortcuts) {
            if (shortcut.disabled)
                continue;
            if (isInput && !shortcut.allowInInputs)
                continue;
            if (matchesKeyCombination(event, shortcut.key)) {
                if (shortcut.preventDefault !== false) {
                    event.preventDefault();
                }
                shortcut.action();
                break;
            }
        }
    }, [shortcuts]);
    useEffect(() => {
        // Only register shortcuts if there are any
        if (shortcuts.length === 0)
            return;
        // Add event listener
        document.addEventListener('keydown', handleKeyDown);
        // Clean up
        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [shortcuts, handleKeyDown]);
};
/**
 * Common keyboard shortcuts for the proposal editor
 */
export const proposalEditorShortcuts = [
    {
        key: 'Ctrl+S',
        action: () => { }, // To be implemented by the component
        description: 'Save proposal',
        preventDefault: true,
    },
    {
        key: 'Ctrl+P',
        action: () => { }, // To be implemented by the component
        description: 'Preview proposal',
        preventDefault: true,
    },
    {
        key: 'Ctrl+D',
        action: () => { }, // To be implemented by the component
        description: 'Download proposal',
        preventDefault: true,
    },
    {
        key: 'Ctrl+E',
        action: () => { }, // To be implemented by the component
        description: 'Send proposal',
        preventDefault: true,
    },
    {
        key: 'Ctrl+N',
        action: () => { }, // To be implemented by the component
        description: 'Add new section',
        preventDefault: true,
    },
    {
        key: 'Escape',
        action: () => { }, // To be implemented by the component
        description: 'Close dialog',
        allowInInputs: true,
    },
];
/**
 * Component to display keyboard shortcuts help
 */
export const getKeyboardShortcutsHelp = (shortcuts) => {
    return shortcuts
        .filter(shortcut => !shortcut.disabled)
        .map(shortcut => ({
        key: shortcut.key,
        description: shortcut.description,
    }));
};
/**
 * Format a key combination for display
 * @param keyCombination Key combination string (e.g., 'Ctrl+S')
 * @returns Formatted key combination (e.g., '⌘S' on Mac, 'Ctrl+S' on Windows)
 */
export const formatKeyCombination = (keyCombination) => {
    const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
    if (isMac) {
        return keyCombination
            .replace(/Ctrl\+|Control\+/g, '⌃')
            .replace(/Alt\+/g, '⌥')
            .replace(/Shift\+/g, '⇧')
            .replace(/Meta\+|Cmd\+|Command\+/g, '⌘')
            .replace(/\+/g, '');
    }
    return keyCombination
        .replace(/Meta\+|Cmd\+|Command\+/g, 'Win+')
        .replace(/Control\+/g, 'Ctrl+');
};
export default {
    useKeyboardShortcuts,
    proposalEditorShortcuts,
    getKeyboardShortcutsHelp,
    formatKeyCombination,
};
