import DOMPurify from 'dompurify';
/**
 * Sanitize HTML content to prevent XSS attacks
 * @param html HTML content to sanitize
 * @returns Sanitized HTML
 */
export function sanitizeHtml(html) {
    // Configure DOMPurify
    const config = {
        ALLOWED_TAGS: [
            'a', 'b', 'blockquote', 'br', 'caption', 'code', 'dd', 'div',
            'dl', 'dt', 'em', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'hr', 'i',
            'img', 'li', 'nl', 'ol', 'p', 'pre', 'span', 'strike', 'strong',
            'sub', 'sup', 'table', 'tbody', 'td', 'tfoot', 'th', 'thead', 'tr', 'u', 'ul'
        ],
        ALLOWED_ATTR: [
            'alt', 'class', 'color', 'colspan', 'href', 'id', 'rowspan',
            'src', 'style', 'target', 'title', 'width', 'height'
        ],
        ALLOW_DATA_ATTR: false,
        USE_PROFILES: { html: true },
        FORBID_TAGS: ['script', 'style', 'iframe', 'frame', 'object', 'embed', 'form', 'input', 'button'],
        FORBID_ATTR: ['onerror', 'onload', 'onclick', 'onmouseover'],
    };
    // Sanitize the HTML
    return DOMPurify.sanitize(html, config);
}
/**
 * Strip all HTML tags from a string
 * @param html HTML content to strip
 * @returns Plain text without HTML tags
 */
export function stripHtml(html) {
    // First sanitize the HTML to prevent XSS
    const sanitized = sanitizeHtml(html);
    // Create a temporary DOM element
    const tempElement = document.createElement('div');
    tempElement.innerHTML = sanitized;
    // Return the text content
    return tempElement.textContent || tempElement.innerText || '';
}
/**
 * Truncate HTML content to a specified length
 * @param html HTML content to truncate
 * @param length Maximum length
 * @param suffix Suffix to add when truncated
 * @returns Truncated HTML
 */
export function truncateHtml(html, length = 100, suffix = '...') {
    // First sanitize the HTML to prevent XSS
    const sanitized = sanitizeHtml(html);
    // Create a temporary DOM element
    const tempElement = document.createElement('div');
    tempElement.innerHTML = sanitized;
    // Get the text content
    const text = tempElement.textContent || tempElement.innerText || '';
    // Truncate the text
    if (text.length <= length) {
        return sanitized;
    }
    // Find the last space before the length
    const truncated = text.substring(0, length);
    const lastSpace = truncated.lastIndexOf(' ');
    // Truncate at the last space
    const truncatedText = truncated.substring(0, lastSpace > 0 ? lastSpace : length);
    // Create a new element with the truncated text
    const truncatedElement = document.createElement('div');
    truncatedElement.textContent = truncatedText + suffix;
    return truncatedElement.innerHTML;
}
/**
 * Extract plain text from HTML and limit to a specified number of words
 * @param html HTML content
 * @param wordCount Maximum number of words
 * @param suffix Suffix to add when truncated
 * @returns Truncated plain text
 */
export function extractTextFromHtml(html, wordCount = 50, suffix = '...') {
    // Strip HTML tags
    const text = stripHtml(html);
    // Split into words
    const words = text.split(/\s+/);
    // Truncate to word count
    if (words.length <= wordCount) {
        return text;
    }
    // Join the words and add suffix
    return words.slice(0, wordCount).join(' ') + suffix;
}
/**
 * Check if a string contains HTML tags
 * @param text Text to check
 * @returns True if the text contains HTML tags
 */
export function containsHtml(text) {
    const htmlRegex = /<[^>]*>/;
    return htmlRegex.test(text);
}
/**
 * Convert plain text to HTML with line breaks
 * @param text Plain text
 * @returns HTML with line breaks
 */
export function textToHtml(text) {
    if (!text)
        return '';
    // Escape HTML special characters
    const escaped = text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
    // Convert line breaks to <br> tags
    return escaped.replace(/\n/g, '<br>');
}
export default {
    sanitizeHtml,
    stripHtml,
    truncateHtml,
    extractTextFromHtml,
    containsHtml,
    textToHtml,
};
