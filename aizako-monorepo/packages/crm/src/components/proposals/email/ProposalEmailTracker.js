import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import React, { useState, useEffect } from 'react';
import { Box, Typography, Button, Card, CardContent, Grid, Divider, Chip, CircularProgress, Alert, IconButton, Tooltip, List, ListItem, ListItemText, ListItemSecondaryAction, Paper, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Checkbox, FormControlLabel, useTheme, useMediaQuery, } from '@mui/material';
import { Email as EmailIcon, Refresh as RefreshIcon, OpenInNew as OpenInNewIcon, Visibility as ViewIcon, VisibilityOff as HiddenIcon, Send as SendIcon, ContentCopy as CopyIcon, } from '@mui/icons-material';
import { format } from 'date-fns';
import { ProposalService } from '../../../services/proposal-service';
import { EmailTrackingService } from '../../../services/email-tracking-service';
import { formatPercentage } from '../../../utils/formatters';
/**
 * ProposalEmailTracker Component
 *
 * This component displays email tracking information for a proposal and allows
 * sending new emails with the proposal.
 */
const ProposalEmailTracker = ({ proposalId, tenantId, }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    // State
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [proposal, setProposal] = useState(null);
    const [emailEvents, setEmailEvents] = useState([]);
    const [sendDialogOpen, setSendDialogOpen] = useState(false);
    const [sendLoading, setSendLoading] = useState(false);
    const [sendError, setSendError] = useState(null);
    const [emailTemplates, setEmailTemplates] = useState([]);
    // Fetch proposal and email events
    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);
                // Fetch proposal
                const proposalData = await ProposalService.getProposalById(proposalId, tenantId);
                setProposal(proposalData);
                // Fetch email events
                const eventsData = await EmailTrackingService.getEmailEventsByProposal(proposalId, tenantId);
                setEmailEvents(eventsData);
                // Fetch email templates
                const templatesData = await EmailTrackingService.getEmailTemplates(tenantId);
                setEmailTemplates(templatesData);
            }
            catch (err) {
                console.error('Error fetching data:', err);
                setError('Failed to load data. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchData();
    }, [proposalId, tenantId]);
    // Handle refresh
    const handleRefresh = () => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);
                // Fetch email events
                const eventsData = await EmailTrackingService.getEmailEventsByProposal(proposalId, tenantId);
                setEmailEvents(eventsData);
            }
            catch (err) {
                console.error('Error fetching data:', err);
                setError('Failed to load data. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchData();
    };
    // Handle send email
    const handleSendEmail = async (formData) => {
        try {
            setSendLoading(true);
            setSendError(null);
            // Send email
            await EmailTrackingService.sendProposalEmail({
                proposalId,
                ...formData,
            }, tenantId);
            // Refresh email events
            handleRefresh();
            // Close dialog
            setSendDialogOpen(false);
        }
        catch (err) {
            console.error('Error sending email:', err);
            setSendError('Failed to send email. Please try again.');
        }
        finally {
            setSendLoading(false);
        }
    };
    // Calculate email stats
    const calculateStats = () => {
        const totalEmails = emailEvents.filter(event => event.type === 'sent').length;
        const openedEmails = emailEvents.filter(event => event.type === 'opened').length;
        const clickedEmails = emailEvents.filter(event => event.type === 'clicked').length;
        const openRate = totalEmails > 0 ? openedEmails / totalEmails : 0;
        const clickRate = openedEmails > 0 ? clickedEmails / openedEmails : 0;
        return {
            totalEmails,
            openedEmails,
            clickedEmails,
            openRate,
            clickRate,
        };
    };
    // Group events by email
    const groupEventsByEmail = () => {
        const emailGroups = {};
        emailEvents.forEach(event => {
            if (!emailGroups[event.emailId]) {
                emailGroups[event.emailId] = [];
            }
            emailGroups[event.emailId].push(event);
        });
        return Object.values(emailGroups).map(events => {
            // Find the sent event
            const sentEvent = events.find(event => event.type === 'sent');
            if (!sentEvent)
                return null;
            // Find other events
            const openedEvents = events.filter(event => event.type === 'opened');
            const clickedEvents = events.filter(event => event.type === 'clicked');
            return {
                emailId: sentEvent.emailId,
                recipient: sentEvent.recipient,
                subject: sentEvent.subject,
                sentAt: sentEvent.timestamp,
                opened: openedEvents.length > 0,
                openedAt: openedEvents.length > 0 ? openedEvents[0].timestamp : null,
                openCount: openedEvents.length,
                clicked: clickedEvents.length > 0,
                clickedAt: clickedEvents.length > 0 ? clickedEvents[0].timestamp : null,
                clickCount: clickedEvents.length,
                events,
            };
        }).filter(Boolean).sort((a, b) => new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime());
    };
    const stats = calculateStats();
    const emailGroups = groupEventsByEmail();
    // Render loading state
    if (loading && !proposal) {
        return (_jsx(Box, { display: "flex", justifyContent: "center", alignItems: "center", height: "200px", children: _jsx(CircularProgress, {}) }));
    }
    // Render error state
    if (error) {
        return (_jsxs(Alert, { severity: "error", sx: { mb: 2 }, children: [error, _jsx(Button, { variant: "outlined", size: "small", onClick: handleRefresh, startIcon: _jsx(RefreshIcon, {}), sx: { ml: 2 }, children: "Retry" })] }));
    }
    return (_jsxs(Box, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 3, children: [_jsx(Typography, { variant: "h6", children: "Email Tracking" }), _jsxs(Box, { children: [_jsx(Button, { variant: "outlined", startIcon: _jsx(RefreshIcon, {}), onClick: handleRefresh, sx: { mr: 1 }, size: isMobile ? "small" : "medium", children: "Refresh" }), _jsx(Button, { variant: "contained", startIcon: _jsx(SendIcon, {}), onClick: () => setSendDialogOpen(true), size: isMobile ? "small" : "medium", children: "Send Email" })] })] }), _jsxs(Grid, { container: true, spacing: 3, mb: 3, children: [_jsx(Grid, { item: true, xs: 12, sm: 6, md: 4, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "subtitle2", color: "text.secondary", gutterBottom: true, children: "Emails Sent" }), _jsx(Typography, { variant: "h4", children: stats.totalEmails }), _jsxs(Box, { display: "flex", alignItems: "center", mt: 1, children: [_jsx(SendIcon, { fontSize: "small", color: "primary", sx: { mr: 1 } }), _jsx(Typography, { variant: "body2", color: "text.secondary", children: proposal?.status === 'sent' ? 'Proposal Sent' : 'Proposal Not Sent' })] })] }) }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 4, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "subtitle2", color: "text.secondary", gutterBottom: true, children: "Open Rate" }), _jsx(Typography, { variant: "h4", children: formatPercentage(stats.openRate) }), _jsxs(Box, { display: "flex", alignItems: "center", mt: 1, children: [_jsx(ViewIcon, { fontSize: "small", color: "info", sx: { mr: 1 } }), _jsxs(Typography, { variant: "body2", color: "text.secondary", children: [stats.openedEmails, " Opens"] })] })] }) }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 4, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "subtitle2", color: "text.secondary", gutterBottom: true, children: "Click Rate" }), _jsx(Typography, { variant: "h4", children: formatPercentage(stats.clickRate) }), _jsxs(Box, { display: "flex", alignItems: "center", mt: 1, children: [_jsx(OpenInNewIcon, { fontSize: "small", color: "success", sx: { mr: 1 } }), _jsxs(Typography, { variant: "body2", color: "text.secondary", children: [stats.clickedEmails, " Clicks"] })] })] }) }) })] }), emailGroups.length === 0 ? (_jsx(Alert, { severity: "info", children: "No emails have been sent for this proposal yet." })) : (_jsx(Paper, { variant: "outlined", children: _jsx(List, { disablePadding: true, children: emailGroups.map((email, index) => (_jsxs(React.Fragment, { children: [_jsxs(ListItem, { children: [_jsx(ListItemText, { primary: _jsxs(Box, { display: "flex", alignItems: "center", children: [_jsx(EmailIcon, { sx: { mr: 1, color: theme.palette.primary.main } }), _jsx(Typography, { variant: "subtitle1", children: email.subject })] }), secondary: _jsxs(Box, { mt: 0.5, children: [_jsxs(Typography, { variant: "body2", children: ["To: ", email.recipient] }), _jsxs(Typography, { variant: "body2", color: "text.secondary", children: ["Sent: ", format(new Date(email.sentAt), 'PPp')] }), _jsxs(Box, { display: "flex", alignItems: "center", mt: 0.5, flexWrap: "wrap", gap: 0.5, children: [email.opened ? (_jsx(Chip, { icon: _jsx(ViewIcon, {}), label: `Opened ${email.openCount > 1 ? `(${email.openCount}x)` : ''}`, color: "info", size: "small" })) : (_jsx(Chip, { icon: _jsx(HiddenIcon, {}), label: "Not Opened", variant: "outlined", size: "small" })), email.clicked ? (_jsx(Chip, { icon: _jsx(OpenInNewIcon, {}), label: `Clicked ${email.clickCount > 1 ? `(${email.clickCount}x)` : ''}`, color: "success", size: "small" })) : (_jsx(Chip, { icon: _jsx(OpenInNewIcon, {}), label: "Not Clicked", variant: "outlined", size: "small" }))] })] }) }), _jsxs(ListItemSecondaryAction, { children: [_jsx(Tooltip, { title: "View Email", children: _jsx(IconButton, { edge: "end", children: _jsx(OpenInNewIcon, {}) }) }), _jsx(Tooltip, { title: "Copy Email ID", children: _jsx(IconButton, { edge: "end", onClick: () => {
                                                        navigator.clipboard.writeText(email.emailId);
                                                    }, children: _jsx(CopyIcon, {}) }) })] })] }), index < emailGroups.length - 1 && _jsx(Divider, {})] }, email.emailId))) }) })), _jsxs(Dialog, { open: sendDialogOpen, onClose: () => setSendDialogOpen(false), maxWidth: "md", fullWidth: true, children: [_jsx(DialogTitle, { children: "Send Proposal Email" }), _jsxs(DialogContent, { children: [sendError && (_jsx(Alert, { severity: "error", sx: { mb: 2 }, children: sendError })), _jsxs(FormControl, { fullWidth: true, margin: "normal", children: [_jsx(InputLabel, { children: "Email Template" }), _jsxs(Select, { defaultValue: "", label: "Email Template", id: "template", children: [_jsx(MenuItem, { value: "", children: _jsx("em", { children: "No Template (Custom)" }) }), emailTemplates.map((template) => (_jsx(MenuItem, { value: template._id, children: template.name }, template._id)))] })] }), _jsx(TextField, { label: "To", fullWidth: true, margin: "normal", id: "to", defaultValue: proposal?.contacts?.map((contact) => contact.email).join(', ') || '' }), _jsx(TextField, { label: "Subject", fullWidth: true, margin: "normal", id: "subject", defaultValue: `${proposal?.title} - Proposal` }), _jsx(TextField, { label: "Message", fullWidth: true, margin: "normal", multiline: true, rows: 6, id: "message", defaultValue: `Dear Client,

I'm pleased to share our proposal with you. Please review it at your convenience.

You can view the proposal by clicking the link below:
${proposal?.publicUrl}

Please let me know if you have any questions or would like to discuss any aspect of the proposal.

Best regards,
[Your Name]` }), _jsxs(Box, { mt: 2, children: [_jsx(FormControlLabel, { control: _jsx(Checkbox, { defaultChecked: true, id: "trackOpens" }), label: "Track Opens" }), _jsx(FormControlLabel, { control: _jsx(Checkbox, { defaultChecked: true, id: "trackClicks" }), label: "Track Clicks" })] }), _jsxs(Box, { mt: 2, children: [_jsx(FormControlLabel, { control: _jsx(Checkbox, { id: "schedule" }), label: "Schedule Email" }), _jsx(TextField, { label: "Schedule Date", type: "datetime-local", fullWidth: true, margin: "normal", id: "scheduleDate", disabled: true, InputLabelProps: {
                                            shrink: true,
                                        } })] })] }), _jsxs(DialogActions, { children: [_jsx(Button, { onClick: () => setSendDialogOpen(false), children: "Cancel" }), _jsx(Button, { variant: "contained", onClick: () => {
                                    const to = document.getElementById('to').value;
                                    const subject = document.getElementById('subject').value;
                                    const message = document.getElementById('message').value;
                                    const template = document.getElementById('template').value;
                                    const trackOpens = document.getElementById('trackOpens').checked;
                                    const trackClicks = document.getElementById('trackClicks').checked;
                                    const schedule = document.getElementById('schedule').checked;
                                    const scheduleDate = document.getElementById('scheduleDate').value;
                                    handleSendEmail({
                                        to,
                                        subject,
                                        message,
                                        templateId: template || undefined,
                                        trackOpens,
                                        trackClicks,
                                        schedule,
                                        scheduleDate: schedule ? scheduleDate : undefined,
                                    });
                                }, disabled: sendLoading, startIcon: sendLoading ? _jsx(CircularProgress, { size: 20 }) : _jsx(SendIcon, {}), children: sendLoading ? 'Sending...' : 'Send Email' })] })] })] }));
};
export default ProposalEmailTracker;
