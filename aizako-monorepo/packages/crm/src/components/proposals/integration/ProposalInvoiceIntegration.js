import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Box, Typography, Button, Card, CardContent, Grid, Chip, CircularProgress, Alert, IconButton, Tooltip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Paper, useTheme, useMediaQuery, Switch, FormControlLabel, Checkbox, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, } from '@mui/material';
import { Receipt as InvoiceIcon, Add as AddIcon, Refresh as RefreshIcon, OpenInNew as OpenInNewIcon, Send as SendIcon, Link as LinkIcon, } from '@mui/icons-material';
import { format } from 'date-fns';
import { ProposalService } from '../../../services/proposal-service';
import { InvoiceService } from '../../../services/invoice-service';
import { formatCurrency } from '../../../utils/formatters';
/**
 * ProposalInvoiceIntegration Component
 *
 * This component displays invoices associated with a proposal and allows
 * creating new invoices from the proposal data.
 */
const ProposalInvoiceIntegration = ({ proposalId, tenantId, onViewInvoice, }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    // State
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [proposal, setProposal] = useState(null);
    const [invoices, setInvoices] = useState([]);
    const [createDialogOpen, setCreateDialogOpen] = useState(false);
    const [createLoading, setCreateLoading] = useState(false);
    const [createError, setCreateError] = useState(null);
    const [invoiceTitle, setInvoiceTitle] = useState('');
    const [invoiceDescription, setInvoiceDescription] = useState('');
    const [dueDate, setDueDate] = useState('');
    const [includeAllItems, setIncludeAllItems] = useState(true);
    const [selectedItems, setSelectedItems] = useState([]);
    // Fetch proposal and invoices
    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);
                // Fetch proposal
                const proposalData = await ProposalService.getProposalById(proposalId, tenantId);
                setProposal(proposalData);
                // Set default invoice title and description
                if (proposalData) {
                    setInvoiceTitle(`Invoice for ${proposalData.title}`);
                    setInvoiceDescription(`Invoice for proposal: ${proposalData.title}`);
                }
                // Set default due date (30 days from now)
                const date = new Date();
                date.setDate(date.getDate() + 30);
                setDueDate(date.toISOString().split('T')[0]);
                // Fetch invoices
                const invoicesData = await InvoiceService.getInvoicesByProposal(proposalId, tenantId);
                setInvoices(invoicesData);
            }
            catch (err) {
                console.error('Error fetching data:', err);
                setError('Failed to load data. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchData();
    }, [proposalId, tenantId]);
    // Handle refresh
    const handleRefresh = () => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);
                // Fetch invoices
                const invoicesData = await InvoiceService.getInvoicesByProposal(proposalId, tenantId);
                setInvoices(invoicesData);
            }
            catch (err) {
                console.error('Error fetching data:', err);
                setError('Failed to load data. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchData();
    };
    // Handle create invoice
    const handleCreateInvoice = async () => {
        try {
            setCreateLoading(true);
            setCreateError(null);
            // Prepare invoice data
            const invoiceData = {
                title: invoiceTitle,
                description: invoiceDescription,
                proposalId,
                dueDate: new Date(dueDate),
                items: includeAllItems
                    ? proposal.pricing?.items
                    : proposal.pricing?.items.filter((item) => selectedItems.includes(item.id)),
                companyId: proposal.companyId,
                contactIds: proposal.contactIds,
            };
            // Create invoice
            await InvoiceService.createInvoice(invoiceData, tenantId);
            // Refresh invoices
            handleRefresh();
            // Close dialog
            setCreateDialogOpen(false);
        }
        catch (err) {
            console.error('Error creating invoice:', err);
            setCreateError('Failed to create invoice. Please try again.');
        }
        finally {
            setCreateLoading(false);
        }
    };
    // Handle item selection
    const handleItemSelection = (itemId) => {
        if (selectedItems.includes(itemId)) {
            setSelectedItems(selectedItems.filter(id => id !== itemId));
        }
        else {
            setSelectedItems([...selectedItems, itemId]);
        }
    };
    // Render loading state
    if (loading && !proposal) {
        return (_jsx(Box, { display: "flex", justifyContent: "center", alignItems: "center", height: "200px", children: _jsx(CircularProgress, {}) }));
    }
    // Render error state
    if (error) {
        return (_jsxs(Alert, { severity: "error", sx: { mb: 2 }, children: [error, _jsx(Button, { variant: "outlined", size: "small", onClick: handleRefresh, startIcon: _jsx(RefreshIcon, {}), sx: { ml: 2 }, children: "Retry" })] }));
    }
    return (_jsxs(Box, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 3, children: [_jsx(Typography, { variant: "h6", children: "Invoices" }), _jsxs(Box, { children: [_jsx(Button, { variant: "outlined", startIcon: _jsx(RefreshIcon, {}), onClick: handleRefresh, sx: { mr: 1 }, size: isMobile ? "small" : "medium", children: "Refresh" }), _jsx(Button, { variant: "contained", startIcon: _jsx(AddIcon, {}), onClick: () => setCreateDialogOpen(true), size: isMobile ? "small" : "medium", disabled: !proposal?.pricing?.items || proposal.pricing.items.length === 0, children: "Create Invoice" })] })] }), proposal && (_jsx(Card, { sx: { mb: 3 }, children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "subtitle1", gutterBottom: true, children: "Proposal Summary" }), _jsxs(Grid, { container: true, spacing: 2, children: [_jsxs(Grid, { item: true, xs: 12, sm: 6, md: 3, children: [_jsx(Typography, { variant: "body2", color: "text.secondary", children: "Title" }), _jsx(Typography, { variant: "body1", noWrap: true, children: proposal.title })] }), _jsxs(Grid, { item: true, xs: 12, sm: 6, md: 3, children: [_jsx(Typography, { variant: "body2", color: "text.secondary", children: "Status" }), _jsx(Chip, { label: proposal.status.toUpperCase(), color: proposal.status === 'accepted' ? 'success' :
                                                proposal.status === 'rejected' ? 'error' :
                                                    proposal.status === 'viewed' ? 'info' :
                                                        proposal.status === 'sent' ? 'primary' :
                                                            'default', size: "small" })] }), _jsxs(Grid, { item: true, xs: 12, sm: 6, md: 3, children: [_jsx(Typography, { variant: "body2", color: "text.secondary", children: "Total Amount" }), _jsx(Typography, { variant: "body1", fontWeight: "bold", children: proposal.pricing?.total ? formatCurrency(proposal.pricing.total) : 'N/A' })] }), _jsxs(Grid, { item: true, xs: 12, sm: 6, md: 3, children: [_jsx(Typography, { variant: "body2", color: "text.secondary", children: "Created" }), _jsx(Typography, { variant: "body1", children: format(new Date(proposal.createdAt), 'PPP') })] })] })] }) })), invoices.length === 0 ? (_jsxs(Alert, { severity: "info", children: ["No invoices found for this proposal. ", proposal?.pricing?.items && proposal.pricing.items.length > 0 ? 'Click "Create Invoice" to create a new invoice.' : 'This proposal does not have any pricing items to invoice.'] })) : (_jsx(TableContainer, { component: Paper, children: _jsxs(Table, { children: [_jsx(TableHead, { children: _jsxs(TableRow, { children: [_jsx(TableCell, { children: "Invoice" }), _jsx(TableCell, { children: "Status" }), _jsx(TableCell, { children: "Amount" }), _jsx(TableCell, { children: "Due Date" }), _jsx(TableCell, { children: "Created" }), _jsx(TableCell, { children: "Actions" })] }) }), _jsx(TableBody, { children: invoices.map((invoice) => (_jsxs(TableRow, { children: [_jsx(TableCell, { children: _jsxs(Box, { display: "flex", alignItems: "center", children: [_jsx(InvoiceIcon, { sx: { mr: 1, color: theme.palette.primary.main } }), _jsx(Typography, { variant: "body1", children: invoice.title })] }) }), _jsx(TableCell, { children: _jsx(Chip, { label: invoice.status.toUpperCase(), color: invoice.status === 'paid' ? 'success' :
                                                invoice.status === 'overdue' ? 'error' :
                                                    invoice.status === 'sent' ? 'primary' :
                                                        'default', size: "small" }) }), _jsx(TableCell, { children: formatCurrency(invoice.total) }), _jsx(TableCell, { children: format(new Date(invoice.dueDate), 'PPP') }), _jsx(TableCell, { children: format(new Date(invoice.createdAt), 'PPP') }), _jsxs(TableCell, { children: [_jsx(Tooltip, { title: "View Invoice", children: _jsx(IconButton, { size: "small", onClick: () => onViewInvoice && onViewInvoice(invoice._id), children: _jsx(OpenInNewIcon, {}) }) }), invoice.status === 'draft' && (_jsx(Tooltip, { title: "Send Invoice", children: _jsx(IconButton, { size: "small", onClick: () => {
                                                        // Handle send invoice
                                                    }, children: _jsx(SendIcon, {}) }) })), invoice.publicUrl && (_jsx(Tooltip, { title: "Copy Public Link", children: _jsx(IconButton, { size: "small", onClick: () => {
                                                        navigator.clipboard.writeText(invoice.publicUrl);
                                                    }, children: _jsx(LinkIcon, {}) }) }))] })] }, invoice._id))) })] }) })), _jsxs(Dialog, { open: createDialogOpen, onClose: () => setCreateDialogOpen(false), maxWidth: "md", fullWidth: true, children: [_jsx(DialogTitle, { children: "Create Invoice from Proposal" }), _jsxs(DialogContent, { children: [createError && (_jsx(Alert, { severity: "error", sx: { mb: 2 }, children: createError })), _jsx(TextField, { label: "Invoice Title", value: invoiceTitle, onChange: (e) => setInvoiceTitle(e.target.value), fullWidth: true, margin: "normal", required: true }), _jsx(TextField, { label: "Invoice Description", value: invoiceDescription, onChange: (e) => setInvoiceDescription(e.target.value), fullWidth: true, margin: "normal", multiline: true, rows: 2 }), _jsx(TextField, { label: "Due Date", type: "date", value: dueDate, onChange: (e) => setDueDate(e.target.value), fullWidth: true, margin: "normal", required: true, InputLabelProps: {
                                    shrink: true,
                                } }), _jsxs(Box, { mt: 3, children: [_jsx(Typography, { variant: "subtitle1", gutterBottom: true, children: "Invoice Items" }), _jsx(FormControlLabel, { control: _jsx(Switch, { checked: includeAllItems, onChange: (e) => setIncludeAllItems(e.target.checked), color: "primary" }), label: "Include all items from proposal" }), !includeAllItems && proposal?.pricing?.items && (_jsx(TableContainer, { component: Paper, sx: { mt: 2 }, children: _jsxs(Table, { children: [_jsx(TableHead, { children: _jsxs(TableRow, { children: [_jsx(TableCell, { padding: "checkbox" }), _jsx(TableCell, { children: "Item" }), _jsx(TableCell, { children: "Quantity" }), _jsx(TableCell, { children: "Unit Price" }), _jsx(TableCell, { children: "Total" })] }) }), _jsx(TableBody, { children: proposal.pricing.items.map((item) => (_jsxs(TableRow, { children: [_jsx(TableCell, { padding: "checkbox", children: _jsx(Checkbox, { checked: selectedItems.includes(item.id), onChange: () => handleItemSelection(item.id) }) }), _jsx(TableCell, { children: item.name }), _jsx(TableCell, { children: item.quantity }), _jsx(TableCell, { children: formatCurrency(item.unitPrice) }), _jsx(TableCell, { children: formatCurrency(item.total) })] }, item.id))) })] }) })), _jsxs(Box, { mt: 2, children: [_jsx(Typography, { variant: "subtitle2", gutterBottom: true, children: "Total Amount:" }), _jsx(Typography, { variant: "h6", color: "primary", children: formatCurrency(includeAllItems
                                                    ? proposal?.pricing?.total || 0
                                                    : proposal?.pricing?.items
                                                        .filter((item) => selectedItems.includes(item.id))
                                                        .reduce((sum, item) => sum + item.total, 0) || 0) })] })] })] }), _jsxs(DialogActions, { children: [_jsx(Button, { onClick: () => setCreateDialogOpen(false), children: "Cancel" }), _jsx(Button, { variant: "contained", onClick: handleCreateInvoice, disabled: createLoading || !invoiceTitle || !dueDate || (!includeAllItems && selectedItems.length === 0), startIcon: createLoading ? _jsx(CircularProgress, { size: 20 }) : null, children: createLoading ? 'Creating...' : 'Create Invoice' })] })] })] }));
};
export default ProposalInvoiceIntegration;
