import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Box, Typography, Button, Card, CardContent, CardActions, Grid, Divider, CircularProgress, Alert, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Paper, List, ListItem, useTheme, useMediaQuery, FormControlLabel, Checkbox, } from '@mui/material';
import { Event as CalendarIcon, Add as AddIcon, Refresh as RefreshIcon, OpenInNew as OpenInNewIcon, Delete as DeleteIcon, Edit as EditIcon, Videocam as VideoIcon, Phone as PhoneIcon, LocationOn as LocationIcon, People as PeopleIcon, AccessTime as TimeIcon, } from '@mui/icons-material';
import { format, addMinutes } from 'date-fns';
import { ProposalService } from '../../../services/proposal-service';
import { CalendarService } from '../../../services/calendar-service';
import { useAuth } from '../../../hooks/useAuth';
/**
 * ProposalCalendarIntegration Component
 *
 * This component displays calendar events associated with a proposal and allows
 * scheduling follow-up meetings.
 */
const ProposalCalendarIntegration = ({ proposalId, tenantId, onViewEvent, }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const { user } = useAuth();
    // State
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [proposal, setProposal] = useState(null);
    const [events, setEvents] = useState([]);
    const [createDialogOpen, setCreateDialogOpen] = useState(false);
    const [createLoading, setCreateLoading] = useState(false);
    const [createError, setCreateError] = useState(null);
    const [eventTitle, setEventTitle] = useState('');
    const [eventDescription, setEventDescription] = useState('');
    const [eventType, setEventType] = useState('meeting');
    const [eventLocation, setEventLocation] = useState('');
    const [eventStartDate, setEventStartDate] = useState('');
    const [eventStartTime, setEventStartTime] = useState('');
    const [eventDuration, setEventDuration] = useState(30);
    const [eventAttendees, setEventAttendees] = useState([]);
    const [availableAttendees, setAvailableAttendees] = useState([]);
    const [sendInvites, setSendInvites] = useState(true);
    const [addVideoConference, setAddVideoConference] = useState(false);
    const [addReminder, setAddReminder] = useState(true);
    const [reminderTime, setReminderTime] = useState(15);
    // Fetch proposal and events
    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);
                // Fetch proposal
                const proposalData = await ProposalService.getProposalById(proposalId, tenantId);
                setProposal(proposalData);
                // Set default event title and description
                if (proposalData) {
                    setEventTitle(`Follow-up for ${proposalData.title}`);
                    setEventDescription(`Follow-up meeting to discuss proposal: ${proposalData.title}`);
                }
                // Set default start date and time (next business day at 10 AM)
                const now = new Date();
                const nextDay = new Date(now);
                nextDay.setDate(now.getDate() + 1);
                // Skip to Monday if it's Friday
                if (nextDay.getDay() === 6)
                    nextDay.setDate(nextDay.getDate() + 2);
                if (nextDay.getDay() === 0)
                    nextDay.setDate(nextDay.getDate() + 1);
                nextDay.setHours(10, 0, 0, 0);
                setEventStartDate(nextDay.toISOString().split('T')[0]);
                setEventStartTime('10:00');
                // Fetch events
                const eventsData = await CalendarService.getEventsByProposal(proposalId, tenantId);
                setEvents(eventsData);
                // Fetch available attendees
                const contactIds = proposalData?.contactIds || [];
                const teamMembers = await CalendarService.getTeamMembers(tenantId);
                // TODO: Fetch actual contact objects from contactIds
                // For now, create placeholder contact objects
                const contacts = contactIds.map((contactId) => ({
                    _id: contactId,
                    name: `Contact ${contactId}`,
                    email: `contact-${contactId}@example.com`,
                }));
                setAvailableAttendees([
                    ...contacts.map((contact) => ({
                        id: contact._id,
                        name: contact.name,
                        email: contact.email,
                        type: 'contact',
                    })),
                    ...teamMembers.map((member) => ({
                        id: member._id,
                        name: member.name,
                        email: member.email,
                        type: 'team',
                    })),
                ]);
                // Set default attendees (proposal contacts and current user)
                setEventAttendees([
                    ...contacts.map((contact) => contact._id),
                    user?.id || '',
                ].filter(Boolean));
            }
            catch (err) {
                console.error('Error fetching data:', err);
                setError('Failed to load data. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchData();
    }, [proposalId, tenantId, user?.id]);
    // Handle refresh
    const handleRefresh = () => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);
                // Fetch events
                const eventsData = await CalendarService.getEventsByProposal(proposalId, tenantId);
                setEvents(eventsData);
            }
            catch (err) {
                console.error('Error fetching data:', err);
                setError('Failed to load data. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchData();
    };
    // Handle create event
    const handleCreateEvent = async () => {
        try {
            setCreateLoading(true);
            setCreateError(null);
            // Calculate end time
            const startDateTime = new Date(`${eventStartDate}T${eventStartTime}`);
            const endDateTime = addMinutes(startDateTime, eventDuration);
            // Calculate reminder time
            const reminderDateTime = addMinutes(startDateTime, -reminderTime);
            // Prepare event data
            const eventData = {
                title: eventTitle,
                description: eventDescription,
                proposalId,
                start: startDateTime.toISOString(),
                end: endDateTime.toISOString(),
                location: eventLocation,
                type: eventType,
                attendees: eventAttendees.map(id => {
                    const attendee = availableAttendees.find(a => a.id === id);
                    return {
                        id,
                        name: attendee?.name || '',
                        email: attendee?.email || '',
                        type: attendee?.type || 'contact',
                    };
                }),
                sendInvites,
                addVideoConference,
                reminder: addReminder ? {
                    time: reminderDateTime.toISOString(),
                    method: 'email',
                } : undefined,
            };
            // Create event
            await CalendarService.createEvent(eventData, tenantId);
            // Refresh events
            handleRefresh();
            // Close dialog
            setCreateDialogOpen(false);
        }
        catch (err) {
            console.error('Error creating event:', err);
            setCreateError('Failed to create event. Please try again.');
        }
        finally {
            setCreateLoading(false);
        }
    };
    // Handle delete event
    const handleDeleteEvent = async (eventId) => {
        try {
            setLoading(true);
            setError(null);
            await CalendarService.deleteEvent(eventId, tenantId);
            // Refresh events
            handleRefresh();
        }
        catch (err) {
            console.error('Error deleting event:', err);
            setError('Failed to delete event. Please try again.');
            setLoading(false);
        }
    };
    // Handle attendee selection
    const handleAttendeeSelection = (attendeeId) => {
        if (eventAttendees.includes(attendeeId)) {
            setEventAttendees(eventAttendees.filter(id => id !== attendeeId));
        }
        else {
            setEventAttendees([...eventAttendees, attendeeId]);
        }
    };
    // Render loading state
    if (loading && !proposal) {
        return (_jsx(Box, { display: "flex", justifyContent: "center", alignItems: "center", height: "200px", children: _jsx(CircularProgress, {}) }));
    }
    // Render error state
    if (error) {
        return (_jsxs(Alert, { severity: "error", sx: { mb: 2 }, children: [error, _jsx(Button, { variant: "outlined", size: "small", onClick: handleRefresh, startIcon: _jsx(RefreshIcon, {}), sx: { ml: 2 }, children: "Retry" })] }));
    }
    return (_jsxs(Box, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 3, children: [_jsx(Typography, { variant: "h6", children: "Calendar Events" }), _jsxs(Box, { children: [_jsx(Button, { variant: "outlined", startIcon: _jsx(RefreshIcon, {}), onClick: handleRefresh, sx: { mr: 1 }, size: isMobile ? "small" : "medium", children: "Refresh" }), _jsx(Button, { variant: "contained", startIcon: _jsx(AddIcon, {}), onClick: () => setCreateDialogOpen(true), size: isMobile ? "small" : "medium", children: "Schedule Follow-up" })] })] }), events.length === 0 ? (_jsx(Alert, { severity: "info", children: "No events scheduled for this proposal. Click \"Schedule Follow-up\" to create a new event." })) : (_jsx(Grid, { container: true, spacing: 3, children: events.map((event) => (_jsx(Grid, { item: true, xs: 12, sm: 6, md: 4, children: _jsxs(Card, { children: [_jsxs(CardContent, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "flex-start", children: [_jsxs(Box, { display: "flex", alignItems: "center", children: [event.type === 'meeting' && _jsx(PeopleIcon, { color: "primary", sx: { mr: 1 } }), event.type === 'call' && _jsx(PhoneIcon, { color: "primary", sx: { mr: 1 } }), event.type === 'task' && _jsx(CalendarIcon, { color: "primary", sx: { mr: 1 } }), _jsx(Typography, { variant: "h6", noWrap: true, sx: { maxWidth: '180px' }, children: event.title })] }), _jsx(IconButton, { size: "small", color: "error", onClick: () => handleDeleteEvent(event._id), children: _jsx(DeleteIcon, {}) })] }), _jsx(Typography, { variant: "body2", color: "text.secondary", gutterBottom: true, children: event.description }), _jsx(Divider, { sx: { my: 1 } }), _jsxs(Box, { display: "flex", alignItems: "center", mb: 1, children: [_jsx(TimeIcon, { fontSize: "small", sx: { mr: 1, color: theme.palette.text.secondary } }), _jsx(Typography, { variant: "body2", children: format(new Date(event.start), 'PPP') })] }), _jsxs(Box, { display: "flex", alignItems: "center", mb: 1, children: [_jsx(TimeIcon, { fontSize: "small", sx: { mr: 1, color: theme.palette.text.secondary } }), _jsxs(Typography, { variant: "body2", children: [format(new Date(event.start), 'p'), " - ", format(new Date(event.end), 'p')] })] }), event.location && (_jsxs(Box, { display: "flex", alignItems: "center", mb: 1, children: [event.videoConferenceUrl ? (_jsx(VideoIcon, { fontSize: "small", sx: { mr: 1, color: theme.palette.text.secondary } })) : (_jsx(LocationIcon, { fontSize: "small", sx: { mr: 1, color: theme.palette.text.secondary } })), _jsx(Typography, { variant: "body2", noWrap: true, sx: { maxWidth: '200px' }, children: event.location })] })), _jsxs(Box, { display: "flex", alignItems: "center", children: [_jsx(PeopleIcon, { fontSize: "small", sx: { mr: 1, color: theme.palette.text.secondary } }), _jsxs(Typography, { variant: "body2", children: [event.attendees?.length || 0, " Attendees"] })] })] }), _jsxs(CardActions, { children: [_jsx(Button, { size: "small", startIcon: _jsx(OpenInNewIcon, {}), onClick: () => onViewEvent && onViewEvent(event._id), children: "View" }), _jsx(Button, { size: "small", startIcon: _jsx(EditIcon, {}), children: "Edit" })] })] }) }, event._id))) })), _jsxs(Dialog, { open: createDialogOpen, onClose: () => setCreateDialogOpen(false), maxWidth: "md", fullWidth: true, children: [_jsx(DialogTitle, { children: "Schedule Follow-up" }), _jsxs(DialogContent, { children: [createError && (_jsx(Alert, { severity: "error", sx: { mb: 2 }, children: createError })), _jsx(TextField, { label: "Event Title", value: eventTitle, onChange: (e) => setEventTitle(e.target.value), fullWidth: true, margin: "normal", required: true }), _jsx(TextField, { label: "Description", value: eventDescription, onChange: (e) => setEventDescription(e.target.value), fullWidth: true, margin: "normal", multiline: true, rows: 2 }), _jsxs(FormControl, { fullWidth: true, margin: "normal", children: [_jsx(InputLabel, { children: "Event Type" }), _jsxs(Select, { value: eventType, onChange: (e) => setEventType(e.target.value), label: "Event Type", children: [_jsx(MenuItem, { value: "meeting", children: "Meeting" }), _jsx(MenuItem, { value: "call", children: "Call" }), _jsx(MenuItem, { value: "task", children: "Task" })] })] }), _jsxs(Grid, { container: true, spacing: 2, sx: { mt: 1 }, children: [_jsx(Grid, { item: true, xs: 12, sm: 6, children: _jsx(TextField, { label: "Date", type: "date", value: eventStartDate, onChange: (e) => setEventStartDate(e.target.value), fullWidth: true, required: true, InputLabelProps: {
                                                shrink: true,
                                            } }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, children: _jsx(TextField, { label: "Time", type: "time", value: eventStartTime, onChange: (e) => setEventStartTime(e.target.value), fullWidth: true, required: true, InputLabelProps: {
                                                shrink: true,
                                            } }) })] }), _jsxs(Grid, { container: true, spacing: 2, sx: { mt: 1 }, children: [_jsx(Grid, { item: true, xs: 12, sm: 6, children: _jsxs(FormControl, { fullWidth: true, children: [_jsx(InputLabel, { children: "Duration" }), _jsxs(Select, { value: eventDuration, onChange: (e) => setEventDuration(Number(e.target.value)), label: "Duration", children: [_jsx(MenuItem, { value: 15, children: "15 minutes" }), _jsx(MenuItem, { value: 30, children: "30 minutes" }), _jsx(MenuItem, { value: 45, children: "45 minutes" }), _jsx(MenuItem, { value: 60, children: "1 hour" }), _jsx(MenuItem, { value: 90, children: "1.5 hours" }), _jsx(MenuItem, { value: 120, children: "2 hours" })] })] }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, children: _jsx(TextField, { label: "Location", value: eventLocation, onChange: (e) => setEventLocation(e.target.value), fullWidth: true, placeholder: "Office, Zoom, etc." }) })] }), _jsxs(Box, { mt: 3, children: [_jsx(Typography, { variant: "subtitle1", gutterBottom: true, children: "Attendees" }), _jsx(Paper, { variant: "outlined", sx: { p: 2, maxHeight: '200px', overflow: 'auto' }, children: _jsx(List, { dense: true, children: availableAttendees.map((attendee) => (_jsx(ListItem, { children: _jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: eventAttendees.includes(attendee.id), onChange: () => handleAttendeeSelection(attendee.id) }), label: _jsxs(Box, { children: [_jsx(Typography, { variant: "body2", children: attendee.name }), _jsxs(Typography, { variant: "caption", color: "text.secondary", children: [attendee.email, " (", attendee.type === 'contact' ? 'Contact' : 'Team Member', ")"] })] }) }) }, attendee.id))) }) })] }), _jsxs(Box, { mt: 3, children: [_jsx(Typography, { variant: "subtitle1", gutterBottom: true, children: "Options" }), _jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: sendInvites, onChange: (e) => setSendInvites(e.target.checked) }), label: "Send calendar invites to attendees" }), _jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: addVideoConference, onChange: (e) => setAddVideoConference(e.target.checked) }), label: "Add video conferencing link" }), _jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: addReminder, onChange: (e) => setAddReminder(e.target.checked) }), label: "Add reminder" }), addReminder && (_jsxs(FormControl, { fullWidth: true, margin: "normal", children: [_jsx(InputLabel, { children: "Reminder Time" }), _jsxs(Select, { value: reminderTime, onChange: (e) => setReminderTime(Number(e.target.value)), label: "Reminder Time", children: [_jsx(MenuItem, { value: 5, children: "5 minutes before" }), _jsx(MenuItem, { value: 10, children: "10 minutes before" }), _jsx(MenuItem, { value: 15, children: "15 minutes before" }), _jsx(MenuItem, { value: 30, children: "30 minutes before" }), _jsx(MenuItem, { value: 60, children: "1 hour before" }), _jsx(MenuItem, { value: 120, children: "2 hours before" }), _jsx(MenuItem, { value: 1440, children: "1 day before" })] })] }))] })] }), _jsxs(DialogActions, { children: [_jsx(Button, { onClick: () => setCreateDialogOpen(false), children: "Cancel" }), _jsx(Button, { variant: "contained", onClick: handleCreateEvent, disabled: createLoading || !eventTitle || !eventStartDate || !eventStartTime || eventAttendees.length === 0, startIcon: createLoading ? _jsx(CircularProgress, { size: 20 }) : null, children: createLoading ? 'Scheduling...' : 'Schedule Event' })] })] })] }));
};
export default ProposalCalendarIntegration;
