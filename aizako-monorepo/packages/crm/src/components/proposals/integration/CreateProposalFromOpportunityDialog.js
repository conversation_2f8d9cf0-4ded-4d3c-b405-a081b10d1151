import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import React, { useState } from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, FormControl, InputLabel, Select, MenuItem, Typography, Box, Switch, FormControlLabel, Chip, CircularProgress, Alert, Stepper, Step, StepLabel, FormGroup, Checkbox, Grid, useTheme, } from '@mui/material';
import { AutoAwesome as AIIcon, Business as BusinessIcon, Person as PersonIcon, AttachMoney as MoneyIcon, } from '@mui/icons-material';
import { formatCurrency } from '../../../utils/formatters';
/**
 * CreateProposalFromOpportunityDialog Component
 *
 * This component displays a dialog for creating a new proposal from opportunity data.
 */
const CreateProposalFromOpportunityDialog = ({ open, onClose, onSubmit, opportunity, }) => {
    const theme = useTheme();
    // State
    const [activeStep, setActiveStep] = useState(0);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [useAI, setUseAI] = useState(true);
    const [title, setTitle] = useState(`Proposal for ${opportunity?.company?.name || 'Client'}`);
    const [description, setDescription] = useState(`Proposal for ${opportunity?.name || 'Opportunity'}`);
    const [aiPrompt, setAiPrompt] = useState('');
    const [aiModel, setAiModel] = useState('claude-3-opus-20240229');
    const [includeSections, setIncludeSections] = useState({
        executiveSummary: true,
        solution: true,
        timeline: true,
        pricing: true,
        team: false,
        testimonials: false,
        terms: true,
    });
    const [includeContacts, setIncludeContacts] = useState(opportunity?.contactIds || []);
    // Reset state when dialog opens
    React.useEffect(() => {
        if (open) {
            setActiveStep(0);
            setLoading(false);
            setError(null);
            setUseAI(true);
            setTitle(`Proposal for ${opportunity?.company?.name || 'Client'}`);
            setDescription(`Proposal for ${opportunity?.name || 'Opportunity'}`);
            setAiPrompt('');
            setAiModel('claude-3-opus-20240229');
            setIncludeSections({
                executiveSummary: true,
                solution: true,
                timeline: true,
                pricing: true,
                team: false,
                testimonials: false,
                terms: true,
            });
            setIncludeContacts(opportunity?.contactIds || []);
        }
    }, [open, opportunity]);
    // Handle next step
    const handleNext = () => {
        setActiveStep((prevStep) => prevStep + 1);
    };
    // Handle back step
    const handleBack = () => {
        setActiveStep((prevStep) => prevStep - 1);
    };
    // Handle submit
    const handleSubmit = () => {
        // Validate
        if (!title) {
            setError('Please enter a proposal title.');
            return;
        }
        // Prepare data
        const proposalData = {
            title,
            description,
            status: 'draft',
            opportunityId: opportunity._id,
            companyId: opportunity.companyId,
            contactIds: includeContacts,
        };
        // Add AI data if using AI
        if (useAI) {
            proposalData.aiGenerated = true;
            proposalData.aiPrompt = aiPrompt || `Create a proposal for ${opportunity.name}`;
            proposalData.aiModel = aiModel;
            proposalData.includeSections = includeSections;
        }
        // Submit
        onSubmit(proposalData);
    };
    // Handle section toggle
    const handleSectionToggle = (section) => {
        setIncludeSections({
            ...includeSections,
            [section]: !includeSections[section],
        });
    };
    // Handle contact toggle
    const handleContactToggle = (contactId) => {
        if (includeContacts.includes(contactId)) {
            setIncludeContacts(includeContacts.filter(id => id !== contactId));
        }
        else {
            setIncludeContacts([...includeContacts, contactId]);
        }
    };
    // Steps
    const steps = ['Basic Information', 'Content Options', 'Review'];
    return (_jsxs(Dialog, { open: open, onClose: onClose, maxWidth: "md", fullWidth: true, children: [_jsx(DialogTitle, { children: "Create Proposal from Opportunity" }), _jsxs(DialogContent, { children: [_jsx(Stepper, { activeStep: activeStep, sx: { mb: 3 }, children: steps.map((label) => (_jsx(Step, { children: _jsx(StepLabel, { children: label }) }, label))) }), error && (_jsx(Alert, { severity: "error", sx: { mb: 2 }, children: error })), activeStep === 0 && (_jsxs(Box, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Basic Information" }), _jsx(TextField, { label: "Proposal Title", value: title, onChange: (e) => setTitle(e.target.value), fullWidth: true, margin: "normal", required: true }), _jsx(TextField, { label: "Proposal Description", value: description, onChange: (e) => setDescription(e.target.value), fullWidth: true, margin: "normal", multiline: true, rows: 2 }), _jsxs(Box, { mt: 3, children: [_jsx(Typography, { variant: "subtitle1", gutterBottom: true, children: "Opportunity Details" }), _jsxs(Grid, { container: true, spacing: 2, children: [_jsxs(Grid, { item: true, xs: 12, sm: 6, children: [_jsxs(Box, { display: "flex", alignItems: "center", mb: 1, children: [_jsx(BusinessIcon, { color: "primary", sx: { mr: 1 } }), _jsx(Typography, { variant: "body1", children: opportunity.company?.name || 'No Company' })] }), _jsxs(Box, { display: "flex", alignItems: "center", mb: 1, children: [_jsx(MoneyIcon, { color: "success", sx: { mr: 1 } }), _jsx(Typography, { variant: "body1", children: formatCurrency(opportunity.amount || 0) })] })] }), _jsxs(Grid, { item: true, xs: 12, sm: 6, children: [_jsxs(Box, { display: "flex", alignItems: "center", mb: 1, children: [_jsx(PersonIcon, { color: "info", sx: { mr: 1 } }), _jsxs(Typography, { variant: "body1", children: [opportunity.contacts?.length || 0, " Contacts"] })] }), _jsx(Chip, { label: opportunity.stage, color: opportunity.stage === 'won' ? 'success' :
                                                            opportunity.stage === 'lost' ? 'error' :
                                                                'primary' })] })] })] })] })), activeStep === 1 && (_jsxs(Box, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Content Options" }), _jsx(FormControlLabel, { control: _jsx(Switch, { checked: useAI, onChange: (e) => setUseAI(e.target.checked), color: "primary" }), label: _jsxs(Box, { display: "flex", alignItems: "center", children: [_jsx(AIIcon, { color: "primary", sx: { mr: 1 } }), _jsx(Typography, { variant: "body1", children: "Generate content with AI" })] }), sx: { mb: 2 } }), useAI && (_jsxs(_Fragment, { children: [_jsx(TextField, { label: "AI Prompt (Optional)", value: aiPrompt, onChange: (e) => setAiPrompt(e.target.value), fullWidth: true, margin: "normal", multiline: true, rows: 3, placeholder: "Enter specific instructions for the AI to follow when generating the proposal...", helperText: "Leave blank to use default prompt based on opportunity details" }), _jsxs(FormControl, { fullWidth: true, margin: "normal", children: [_jsx(InputLabel, { children: "AI Model" }), _jsxs(Select, { value: aiModel, onChange: (e) => setAiModel(e.target.value), label: "AI Model", children: [_jsx(MenuItem, { value: "claude-3-opus-20240229", children: "Claude 3 Opus (Highest Quality)" }), _jsx(MenuItem, { value: "claude-3-sonnet-20240229", children: "Claude 3 Sonnet (Balanced)" }), _jsx(MenuItem, { value: "claude-3-haiku-20240307", children: "Claude 3 Haiku (Fastest)" })] })] }), _jsx(Typography, { variant: "subtitle1", gutterBottom: true, sx: { mt: 2 }, children: "Sections to Include" }), _jsx(FormGroup, { children: _jsxs(Grid, { container: true, spacing: 2, children: [_jsxs(Grid, { item: true, xs: 12, sm: 6, children: [_jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: includeSections.executiveSummary, onChange: () => handleSectionToggle('executiveSummary') }), label: "Executive Summary" }), _jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: includeSections.solution, onChange: () => handleSectionToggle('solution') }), label: "Solution" }), _jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: includeSections.timeline, onChange: () => handleSectionToggle('timeline') }), label: "Timeline" })] }), _jsxs(Grid, { item: true, xs: 12, sm: 6, children: [_jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: includeSections.pricing, onChange: () => handleSectionToggle('pricing') }), label: "Pricing" }), _jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: includeSections.team, onChange: () => handleSectionToggle('team') }), label: "Team" }), _jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: includeSections.testimonials, onChange: () => handleSectionToggle('testimonials') }), label: "Testimonials" }), _jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: includeSections.terms, onChange: () => handleSectionToggle('terms') }), label: "Terms & Conditions" })] })] }) })] })), _jsx(Typography, { variant: "subtitle1", gutterBottom: true, sx: { mt: 3 }, children: "Include Contacts" }), opportunity.contacts?.length > 0 ? (_jsx(FormGroup, { children: _jsx(Grid, { container: true, spacing: 2, children: opportunity.contacts.map((contact) => (_jsx(Grid, { item: true, xs: 12, sm: 6, children: _jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: includeContacts.includes(contact._id), onChange: () => handleContactToggle(contact._id) }), label: `${contact.name} (${contact.title || 'No Title'})` }) }, contact._id))) }) })) : (_jsx(Typography, { variant: "body2", color: "text.secondary", children: "No contacts associated with this opportunity." }))] })), activeStep === 2 && (_jsxs(Box, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Review" }), _jsxs(Grid, { container: true, spacing: 3, children: [_jsxs(Grid, { item: true, xs: 12, sm: 6, children: [_jsx(Typography, { variant: "subtitle1", gutterBottom: true, children: "Proposal Details" }), _jsxs(Box, { mb: 2, children: [_jsx(Typography, { variant: "body2", color: "text.secondary", children: "Title" }), _jsx(Typography, { variant: "body1", children: title })] }), _jsxs(Box, { mb: 2, children: [_jsx(Typography, { variant: "body2", color: "text.secondary", children: "Description" }), _jsx(Typography, { variant: "body1", children: description })] }), _jsxs(Box, { mb: 2, children: [_jsx(Typography, { variant: "body2", color: "text.secondary", children: "Content Generation" }), _jsx(Typography, { variant: "body1", children: useAI ? 'AI-Generated' : 'Manual' })] }), useAI && (_jsxs(Box, { mb: 2, children: [_jsx(Typography, { variant: "body2", color: "text.secondary", children: "AI Model" }), _jsx(Typography, { variant: "body1", children: aiModel === 'claude-3-opus-20240229' ? 'Claude 3 Opus' :
                                                            aiModel === 'claude-3-sonnet-20240229' ? 'Claude 3 Sonnet' :
                                                                'Claude 3 Haiku' })] }))] }), _jsxs(Grid, { item: true, xs: 12, sm: 6, children: [_jsx(Typography, { variant: "subtitle1", gutterBottom: true, children: "Included Sections" }), _jsx(Box, { display: "flex", flexWrap: "wrap", gap: 1, mb: 2, children: useAI ? (_jsxs(_Fragment, { children: [includeSections.executiveSummary && _jsx(Chip, { label: "Executive Summary" }), includeSections.solution && _jsx(Chip, { label: "Solution" }), includeSections.timeline && _jsx(Chip, { label: "Timeline" }), includeSections.pricing && _jsx(Chip, { label: "Pricing" }), includeSections.team && _jsx(Chip, { label: "Team" }), includeSections.testimonials && _jsx(Chip, { label: "Testimonials" }), includeSections.terms && _jsx(Chip, { label: "Terms & Conditions" })] })) : (_jsx(Typography, { variant: "body2", color: "text.secondary", children: "No sections will be pre-generated. You'll need to add them manually." })) }), _jsx(Typography, { variant: "subtitle1", gutterBottom: true, children: "Included Contacts" }), _jsx(Box, { display: "flex", flexWrap: "wrap", gap: 1, children: includeContacts.length > 0 ? (opportunity.contacts
                                                    .filter((contact) => includeContacts.includes(contact._id))
                                                    .map((contact) => (_jsx(Chip, { label: contact.name }, contact._id)))) : (_jsx(Typography, { variant: "body2", color: "text.secondary", children: "No contacts included." })) })] })] }), _jsx(Alert, { severity: "info", sx: { mt: 3 }, children: _jsx(Typography, { variant: "body2", children: useAI ?
                                        'The proposal will be generated with AI based on the opportunity details. You can edit it after creation.' :
                                        'You will need to manually add content to the proposal after creation.' }) })] }))] }), _jsxs(DialogActions, { children: [_jsx(Button, { onClick: onClose, children: "Cancel" }), activeStep > 0 && (_jsx(Button, { onClick: handleBack, children: "Back" })), activeStep < steps.length - 1 ? (_jsx(Button, { variant: "contained", onClick: handleNext, disabled: activeStep === 0 && !title, children: "Next" })) : (_jsx(Button, { variant: "contained", onClick: handleSubmit, disabled: loading, children: loading ? _jsx(CircularProgress, { size: 24 }) : 'Create Proposal' }))] })] }));
};
export default CreateProposalFromOpportunityDialog;
