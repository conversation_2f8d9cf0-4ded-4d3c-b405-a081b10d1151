import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Box, Typography, Button, Grid, Chip, CircularProgress, Alert, IconButton, Tooltip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Paper, List, ListItem, ListItemText, useTheme, useMediaQuery, Avatar, ListItemAvatar, LinearProgress, } from '@mui/material';
import { Assignment as TaskIcon, Add as AddIcon, Refresh as RefreshIcon, Delete as DeleteIcon, Edit as EditIcon, CheckCircle as CompleteIcon, RadioButtonUnchecked as IncompleteIcon, Flag as PriorityIcon, Person as AssigneeIcon, CalendarToday as DueDateIcon, AccessTime as TimeIcon, } from '@mui/icons-material';
import { format, addDays } from 'date-fns';
import { ProposalService } from '../../../services/proposal-service';
import { TaskService } from '../../../services/task-service';
import { useAuth } from '../../../hooks/useAuth';
/**
 * ProposalTaskIntegration Component
 *
 * This component displays tasks associated with a proposal and allows
 * creating new tasks.
 */
const ProposalTaskIntegration = ({ proposalId, tenantId, onViewTask, }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const { user } = useAuth();
    // State
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [proposal, setProposal] = useState(null);
    const [tasks, setTasks] = useState([]);
    const [createDialogOpen, setCreateDialogOpen] = useState(false);
    const [createLoading, setCreateLoading] = useState(false);
    const [createError, setCreateError] = useState(null);
    const [taskTitle, setTaskTitle] = useState('');
    const [taskDescription, setTaskDescription] = useState('');
    const [taskPriority, setTaskPriority] = useState('medium');
    const [taskDueDate, setTaskDueDate] = useState('');
    const [taskAssignee, setTaskAssignee] = useState('');
    const [taskEstimatedTime, setTaskEstimatedTime] = useState(30);
    const [availableAssignees, setAvailableAssignees] = useState([]);
    const [editDialogOpen, setEditDialogOpen] = useState(false);
    const [selectedTask, setSelectedTask] = useState(null);
    // Fetch proposal and tasks
    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);
                // Fetch proposal
                const proposalData = await ProposalService.getProposalById(proposalId, tenantId);
                setProposal(proposalData);
                // Set default task title and description
                if (proposalData) {
                    setTaskTitle(`Follow-up for ${proposalData.title}`);
                    setTaskDescription(`Follow-up task for proposal: ${proposalData.title}`);
                }
                // Set default due date (3 days from now)
                const dueDate = addDays(new Date(), 3);
                setTaskDueDate(dueDate.toISOString().split('T')[0]);
                // Fetch tasks
                const tasksData = await TaskService.getTasksByProposal(proposalId, tenantId);
                setTasks(tasksData);
                // Fetch available assignees
                const teamMembers = await TaskService.getTeamMembers(tenantId);
                setAvailableAssignees(teamMembers);
                // Set default assignee (current user)
                setTaskAssignee(user?.id || '');
            }
            catch (err) {
                console.error('Error fetching data:', err);
                setError('Failed to load data. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchData();
    }, [proposalId, tenantId, user?.id]);
    // Handle refresh
    const handleRefresh = () => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);
                // Fetch tasks
                const tasksData = await TaskService.getTasksByProposal(proposalId, tenantId);
                setTasks(tasksData);
            }
            catch (err) {
                console.error('Error fetching data:', err);
                setError('Failed to load data. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchData();
    };
    // Handle create task
    const handleCreateTask = async () => {
        try {
            setCreateLoading(true);
            setCreateError(null);
            // Prepare task data
            const taskData = {
                title: taskTitle,
                description: taskDescription,
                proposalId,
                priority: taskPriority,
                dueDate: new Date(taskDueDate),
                assigneeId: taskAssignee,
                estimatedTime: taskEstimatedTime,
                status: 'todo',
            };
            // Create task
            await TaskService.createTask(taskData, tenantId);
            // Refresh tasks
            handleRefresh();
            // Close dialog
            setCreateDialogOpen(false);
        }
        catch (err) {
            console.error('Error creating task:', err);
            setCreateError('Failed to create task. Please try again.');
        }
        finally {
            setCreateLoading(false);
        }
    };
    // Handle update task
    const handleUpdateTask = async () => {
        try {
            setCreateLoading(true);
            setCreateError(null);
            if (!selectedTask)
                return;
            // Prepare task data
            const taskData = {
                title: taskTitle,
                description: taskDescription,
                priority: taskPriority,
                dueDate: new Date(taskDueDate),
                assigneeId: taskAssignee,
                estimatedTime: taskEstimatedTime,
            };
            // Update task
            await TaskService.updateTask(selectedTask._id, taskData, tenantId);
            // Refresh tasks
            handleRefresh();
            // Close dialog
            setEditDialogOpen(false);
        }
        catch (err) {
            console.error('Error updating task:', err);
            setCreateError('Failed to update task. Please try again.');
        }
        finally {
            setCreateLoading(false);
        }
    };
    // Handle delete task
    const handleDeleteTask = async (taskId) => {
        try {
            setLoading(true);
            setError(null);
            await TaskService.deleteTask(taskId, tenantId);
            // Refresh tasks
            handleRefresh();
        }
        catch (err) {
            console.error('Error deleting task:', err);
            setError('Failed to delete task. Please try again.');
            setLoading(false);
        }
    };
    // Handle complete task
    const handleCompleteTask = async (taskId, isComplete) => {
        try {
            setLoading(true);
            setError(null);
            await TaskService.updateTask(taskId, {
                status: isComplete ? 'done' : 'todo',
            }, tenantId);
            // Refresh tasks
            handleRefresh();
        }
        catch (err) {
            console.error('Error updating task status:', err);
            setError('Failed to update task status. Please try again.');
            setLoading(false);
        }
    };
    // Handle edit task
    const handleEditTask = (task) => {
        setSelectedTask(task);
        setTaskTitle(task.title);
        setTaskDescription(task.description || '');
        setTaskPriority(task.priority);
        setTaskDueDate(task.dueDate ? new Date(task.dueDate).toISOString().split('T')[0] : '');
        setTaskAssignee(task.assigneeId || '');
        setTaskEstimatedTime(task.estimatedTime || 30);
        setEditDialogOpen(true);
    };
    // Get priority color
    const getPriorityColor = (priority) => {
        switch (priority) {
            case 'high':
                return theme.palette.error.main;
            case 'medium':
                return theme.palette.warning.main;
            case 'low':
                return theme.palette.success.main;
            default:
                return theme.palette.primary.main;
        }
    };
    // Get assignee name
    const getAssigneeName = (assigneeId) => {
        const assignee = availableAssignees.find(a => a._id === assigneeId);
        return assignee ? assignee.name : 'Unassigned';
    };
    // Render loading state
    if (loading && !proposal) {
        return (_jsx(Box, { display: "flex", justifyContent: "center", alignItems: "center", height: "200px", children: _jsx(CircularProgress, {}) }));
    }
    // Render error state
    if (error) {
        return (_jsxs(Alert, { severity: "error", sx: { mb: 2 }, children: [error, _jsx(Button, { variant: "outlined", size: "small", onClick: handleRefresh, startIcon: _jsx(RefreshIcon, {}), sx: { ml: 2 }, children: "Retry" })] }));
    }
    return (_jsxs(Box, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 3, children: [_jsx(Typography, { variant: "h6", children: "Tasks" }), _jsxs(Box, { children: [_jsx(Button, { variant: "outlined", startIcon: _jsx(RefreshIcon, {}), onClick: handleRefresh, sx: { mr: 1 }, size: isMobile ? "small" : "medium", children: "Refresh" }), _jsx(Button, { variant: "contained", startIcon: _jsx(AddIcon, {}), onClick: () => setCreateDialogOpen(true), size: isMobile ? "small" : "medium", children: "Create Task" })] })] }), tasks.length === 0 ? (_jsx(Alert, { severity: "info", children: "No tasks created for this proposal. Click \"Create Task\" to create a new task." })) : (_jsx(List, { children: tasks.map((task) => (_jsxs(Paper, { sx: { mb: 2 }, children: [_jsxs(ListItem, { secondaryAction: _jsxs(Box, { children: [_jsx(Tooltip, { title: task.status === 'done' ? 'Mark as Incomplete' : 'Mark as Complete', children: _jsx(IconButton, { edge: "end", onClick: () => handleCompleteTask(task._id, task.status !== 'done'), children: task.status === 'done' ? (_jsx(CompleteIcon, { color: "success" })) : (_jsx(IncompleteIcon, {})) }) }), _jsx(Tooltip, { title: "Edit Task", children: _jsx(IconButton, { edge: "end", onClick: () => handleEditTask(task), children: _jsx(EditIcon, {}) }) }), _jsx(Tooltip, { title: "Delete Task", children: _jsx(IconButton, { edge: "end", color: "error", onClick: () => handleDeleteTask(task._id), children: _jsx(DeleteIcon, {}) }) })] }), children: [_jsx(ListItemAvatar, { children: _jsx(Avatar, { sx: { bgcolor: task.status === 'done' ? theme.palette.success.main : theme.palette.primary.main }, children: _jsx(TaskIcon, {}) }) }), _jsx(ListItemText, { primary: _jsx(Typography, { variant: "subtitle1", sx: {
                                            textDecoration: task.status === 'done' ? 'line-through' : 'none',
                                        }, children: task.title }), secondary: _jsxs(Box, { children: [task.description && (_jsx(Typography, { variant: "body2", color: "text.secondary", gutterBottom: true, children: task.description })), _jsxs(Box, { display: "flex", flexWrap: "wrap", gap: 1, mt: 1, children: [_jsx(Chip, { icon: _jsx(PriorityIcon, {}), label: `${task.priority.charAt(0).toUpperCase() + task.priority.slice(1)} Priority`, size: "small", sx: {
                                                            bgcolor: `${getPriorityColor(task.priority)}20`,
                                                            color: getPriorityColor(task.priority),
                                                        } }), _jsx(Chip, { icon: _jsx(AssigneeIcon, {}), label: getAssigneeName(task.assigneeId), size: "small" }), task.dueDate && (_jsx(Chip, { icon: _jsx(DueDateIcon, {}), label: `Due: ${format(new Date(task.dueDate), 'PPP')}`, size: "small", color: new Date(task.dueDate) < new Date() && task.status !== 'done' ? 'error' : 'default' })), task.estimatedTime && (_jsx(Chip, { icon: _jsx(TimeIcon, {}), label: `${task.estimatedTime} min`, size: "small" }))] })] }) })] }), task.progress !== undefined && (_jsxs(Box, { px: 2, pb: 2, children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 0.5, children: [_jsx(Typography, { variant: "caption", color: "text.secondary", children: "Progress" }), _jsxs(Typography, { variant: "caption", color: "text.secondary", children: [task.progress, "%"] })] }), _jsx(LinearProgress, { variant: "determinate", value: task.progress, color: task.status === 'done' ? 'success' : 'primary' })] }))] }, task._id))) })), _jsxs(Dialog, { open: createDialogOpen, onClose: () => setCreateDialogOpen(false), maxWidth: "md", fullWidth: true, children: [_jsx(DialogTitle, { children: "Create Task" }), _jsxs(DialogContent, { children: [createError && (_jsx(Alert, { severity: "error", sx: { mb: 2 }, children: createError })), _jsx(TextField, { label: "Task Title", value: taskTitle, onChange: (e) => setTaskTitle(e.target.value), fullWidth: true, margin: "normal", required: true }), _jsx(TextField, { label: "Description", value: taskDescription, onChange: (e) => setTaskDescription(e.target.value), fullWidth: true, margin: "normal", multiline: true, rows: 2 }), _jsxs(Grid, { container: true, spacing: 2, sx: { mt: 1 }, children: [_jsx(Grid, { item: true, xs: 12, sm: 6, children: _jsxs(FormControl, { fullWidth: true, children: [_jsx(InputLabel, { children: "Priority" }), _jsxs(Select, { value: taskPriority, onChange: (e) => setTaskPriority(e.target.value), label: "Priority", children: [_jsx(MenuItem, { value: "high", children: "High" }), _jsx(MenuItem, { value: "medium", children: "Medium" }), _jsx(MenuItem, { value: "low", children: "Low" })] })] }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, children: _jsx(TextField, { label: "Due Date", type: "date", value: taskDueDate, onChange: (e) => setTaskDueDate(e.target.value), fullWidth: true, InputLabelProps: {
                                                shrink: true,
                                            } }) })] }), _jsxs(Grid, { container: true, spacing: 2, sx: { mt: 1 }, children: [_jsx(Grid, { item: true, xs: 12, sm: 6, children: _jsxs(FormControl, { fullWidth: true, children: [_jsx(InputLabel, { children: "Assignee" }), _jsxs(Select, { value: taskAssignee, onChange: (e) => setTaskAssignee(e.target.value), label: "Assignee", children: [_jsx(MenuItem, { value: "", children: "Unassigned" }), availableAssignees.map((assignee) => (_jsx(MenuItem, { value: assignee._id, children: assignee.name }, assignee._id)))] })] }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, children: _jsxs(FormControl, { fullWidth: true, children: [_jsx(InputLabel, { children: "Estimated Time (minutes)" }), _jsxs(Select, { value: taskEstimatedTime, onChange: (e) => setTaskEstimatedTime(Number(e.target.value)), label: "Estimated Time (minutes)", children: [_jsx(MenuItem, { value: 15, children: "15 minutes" }), _jsx(MenuItem, { value: 30, children: "30 minutes" }), _jsx(MenuItem, { value: 45, children: "45 minutes" }), _jsx(MenuItem, { value: 60, children: "1 hour" }), _jsx(MenuItem, { value: 90, children: "1.5 hours" }), _jsx(MenuItem, { value: 120, children: "2 hours" }), _jsx(MenuItem, { value: 180, children: "3 hours" }), _jsx(MenuItem, { value: 240, children: "4 hours" })] })] }) })] })] }), _jsxs(DialogActions, { children: [_jsx(Button, { onClick: () => setCreateDialogOpen(false), children: "Cancel" }), _jsx(Button, { variant: "contained", onClick: handleCreateTask, disabled: createLoading || !taskTitle, startIcon: createLoading ? _jsx(CircularProgress, { size: 20 }) : null, children: createLoading ? 'Creating...' : 'Create Task' })] })] }), _jsxs(Dialog, { open: editDialogOpen, onClose: () => setEditDialogOpen(false), maxWidth: "md", fullWidth: true, children: [_jsx(DialogTitle, { children: "Edit Task" }), _jsxs(DialogContent, { children: [createError && (_jsx(Alert, { severity: "error", sx: { mb: 2 }, children: createError })), _jsx(TextField, { label: "Task Title", value: taskTitle, onChange: (e) => setTaskTitle(e.target.value), fullWidth: true, margin: "normal", required: true }), _jsx(TextField, { label: "Description", value: taskDescription, onChange: (e) => setTaskDescription(e.target.value), fullWidth: true, margin: "normal", multiline: true, rows: 2 }), _jsxs(Grid, { container: true, spacing: 2, sx: { mt: 1 }, children: [_jsx(Grid, { item: true, xs: 12, sm: 6, children: _jsxs(FormControl, { fullWidth: true, children: [_jsx(InputLabel, { children: "Priority" }), _jsxs(Select, { value: taskPriority, onChange: (e) => setTaskPriority(e.target.value), label: "Priority", children: [_jsx(MenuItem, { value: "high", children: "High" }), _jsx(MenuItem, { value: "medium", children: "Medium" }), _jsx(MenuItem, { value: "low", children: "Low" })] })] }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, children: _jsx(TextField, { label: "Due Date", type: "date", value: taskDueDate, onChange: (e) => setTaskDueDate(e.target.value), fullWidth: true, InputLabelProps: {
                                                shrink: true,
                                            } }) })] }), _jsxs(Grid, { container: true, spacing: 2, sx: { mt: 1 }, children: [_jsx(Grid, { item: true, xs: 12, sm: 6, children: _jsxs(FormControl, { fullWidth: true, children: [_jsx(InputLabel, { children: "Assignee" }), _jsxs(Select, { value: taskAssignee, onChange: (e) => setTaskAssignee(e.target.value), label: "Assignee", children: [_jsx(MenuItem, { value: "", children: "Unassigned" }), availableAssignees.map((assignee) => (_jsx(MenuItem, { value: assignee._id, children: assignee.name }, assignee._id)))] })] }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, children: _jsxs(FormControl, { fullWidth: true, children: [_jsx(InputLabel, { children: "Estimated Time (minutes)" }), _jsxs(Select, { value: taskEstimatedTime, onChange: (e) => setTaskEstimatedTime(Number(e.target.value)), label: "Estimated Time (minutes)", children: [_jsx(MenuItem, { value: 15, children: "15 minutes" }), _jsx(MenuItem, { value: 30, children: "30 minutes" }), _jsx(MenuItem, { value: 45, children: "45 minutes" }), _jsx(MenuItem, { value: 60, children: "1 hour" }), _jsx(MenuItem, { value: 90, children: "1.5 hours" }), _jsx(MenuItem, { value: 120, children: "2 hours" }), _jsx(MenuItem, { value: 180, children: "3 hours" }), _jsx(MenuItem, { value: 240, children: "4 hours" })] })] }) })] })] }), _jsxs(DialogActions, { children: [_jsx(Button, { onClick: () => setEditDialogOpen(false), children: "Cancel" }), _jsx(Button, { variant: "contained", onClick: handleUpdateTask, disabled: createLoading || !taskTitle, startIcon: createLoading ? _jsx(CircularProgress, { size: 20 }) : null, children: createLoading ? 'Updating...' : 'Update Task' })] })] })] }));
};
export default ProposalTaskIntegration;
