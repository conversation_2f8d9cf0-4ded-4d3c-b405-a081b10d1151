import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import React, { useState, useEffect } from 'react';
import { Box, Typography, Button, Card, CardContent, Grid, Divider, Chip, CircularProgress, Alert, IconButton, Tooltip, List, ListItem, ListItemText, ListItemSecondaryAction, Paper, useTheme, useMediaQuery, } from '@mui/material';
import { Add as AddIcon, Description as DescriptionIcon, Link as LinkIcon, OpenInNew as OpenInNewIcon, Refresh as RefreshIcon, AttachMoney as MoneyIcon, Business as BusinessIcon, Person as PersonIcon, Email as EmailIcon, } from '@mui/icons-material';
import { format } from 'date-fns';
import { ProposalService } from '../../../services/proposal-service';
import { OpportunityService } from '../../../services/opportunity-service';
import { formatCurrency } from '../../../utils/formatters';
import CreateProposalFromOpportunityDialog from './CreateProposalFromOpportunityDialog';
/**
 * OpportunityProposalIntegration Component
 *
 * This component displays proposals associated with an opportunity and allows
 * creating new proposals from the opportunity data.
 */
const OpportunityProposalIntegration = ({ opportunityId, tenantId, onCreateProposal, onViewProposal, }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    // State
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [proposals, setProposals] = useState([]);
    const [opportunity, setOpportunity] = useState(null);
    const [createDialogOpen, setCreateDialogOpen] = useState(false);
    // Fetch proposals and opportunity data
    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);
                // Fetch opportunity
                const opportunityData = await OpportunityService.getOpportunityById(opportunityId, tenantId);
                setOpportunity(opportunityData);
                // Fetch proposals
                const proposalsData = await ProposalService.getProposalsByOpportunity(opportunityId, tenantId);
                setProposals(proposalsData);
            }
            catch (err) {
                console.error('Error fetching data:', err);
                setError('Failed to load data. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchData();
    }, [opportunityId, tenantId]);
    // Handle refresh
    const handleRefresh = () => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);
                // Fetch opportunity
                const opportunityData = await OpportunityService.getOpportunityById(opportunityId, tenantId);
                setOpportunity(opportunityData);
                // Fetch proposals
                const proposalsData = await ProposalService.getProposalsByOpportunity(opportunityId, tenantId);
                setProposals(proposalsData);
            }
            catch (err) {
                console.error('Error fetching data:', err);
                setError('Failed to load data. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchData();
    };
    // Handle create proposal
    const handleCreateProposal = async (proposalData) => {
        try {
            setLoading(true);
            setError(null);
            // Create proposal
            const proposal = await ProposalService.createProposal({
                ...proposalData,
                opportunityId,
                companyId: opportunity.companyId,
                contactIds: opportunity.contactIds,
            }, tenantId);
            // Refresh proposals
            handleRefresh();
            // Close dialog
            setCreateDialogOpen(false);
            // Callback
            if (onCreateProposal) {
                onCreateProposal(String(proposal._id));
            }
        }
        catch (err) {
            console.error('Error creating proposal:', err);
            setError('Failed to create proposal. Please try again.');
            setLoading(false);
        }
    };
    // Render loading state
    if (loading && !opportunity) {
        return (_jsx(Box, { display: "flex", justifyContent: "center", alignItems: "center", height: "200px", children: _jsx(CircularProgress, {}) }));
    }
    // Render error state
    if (error) {
        return (_jsxs(Alert, { severity: "error", sx: { mb: 2 }, children: [error, _jsx(Button, { variant: "outlined", size: "small", onClick: handleRefresh, startIcon: _jsx(RefreshIcon, {}), sx: { ml: 2 }, children: "Retry" })] }));
    }
    return (_jsxs(Box, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2, children: [_jsx(Typography, { variant: "h6", children: "Proposals" }), _jsxs(Box, { children: [_jsx(Button, { variant: "outlined", startIcon: _jsx(RefreshIcon, {}), onClick: handleRefresh, sx: { mr: 1 }, size: isMobile ? "small" : "medium", children: "Refresh" }), _jsx(Button, { variant: "contained", startIcon: _jsx(AddIcon, {}), onClick: () => setCreateDialogOpen(true), size: isMobile ? "small" : "medium", children: "Create Proposal" })] })] }), opportunity && (_jsx(Card, { sx: { mb: 3 }, children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "subtitle1", gutterBottom: true, children: "Opportunity Summary" }), _jsxs(Grid, { container: true, spacing: 2, children: [_jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsxs(Box, { display: "flex", alignItems: "center", children: [_jsx(BusinessIcon, { color: "primary", sx: { mr: 1 } }), _jsx(Typography, { variant: "body2", noWrap: true, children: opportunity.company?.name || 'No Company' })] }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsxs(Box, { display: "flex", alignItems: "center", children: [_jsx(MoneyIcon, { color: "success", sx: { mr: 1 } }), _jsx(Typography, { variant: "body2", children: formatCurrency(opportunity.amount || 0) })] }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsxs(Box, { display: "flex", alignItems: "center", children: [_jsx(PersonIcon, { color: "info", sx: { mr: 1 } }), _jsxs(Typography, { variant: "body2", children: [opportunity.contacts?.length || 0, " Contacts"] })] }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsx(Chip, { label: opportunity.stage, color: opportunity.stage === 'won' ? 'success' :
                                            opportunity.stage === 'lost' ? 'error' :
                                                'primary', size: "small" }) })] })] }) })), proposals.length === 0 ? (_jsx(Alert, { severity: "info", children: "No proposals found for this opportunity. Create a new proposal to get started." })) : (_jsx(Paper, { variant: "outlined", children: _jsx(List, { disablePadding: true, children: proposals.map((proposal, index) => (_jsxs(React.Fragment, { children: [_jsxs(ListItem, { button: true, onClick: () => onViewProposal && onViewProposal(proposal._id), children: [_jsx(ListItemText, { primary: _jsxs(Box, { display: "flex", alignItems: "center", children: [_jsx(DescriptionIcon, { sx: { mr: 1, color: theme.palette.primary.main } }), _jsx(Typography, { variant: "subtitle1", children: proposal.title })] }), secondary: _jsxs(Box, { mt: 0.5, children: [_jsxs(Typography, { variant: "body2", color: "text.secondary", children: ["Created: ", format(new Date(proposal.createdAt), 'PPP')] }), _jsxs(Box, { display: "flex", alignItems: "center", mt: 0.5, flexWrap: "wrap", gap: 0.5, children: [_jsx(Chip, { label: proposal.status.toUpperCase(), color: proposal.status === 'accepted' ? 'success' :
                                                                proposal.status === 'rejected' ? 'error' :
                                                                    proposal.status === 'viewed' ? 'info' :
                                                                        proposal.status === 'sent' ? 'primary' :
                                                                            'default', size: "small" }), proposal.aiGenerated && (_jsx(Chip, { label: "AI Generated", size: "small" })), proposal.sections?.length > 0 && (_jsx(Chip, { label: `${proposal.sections.length} Sections`, size: "small" }))] })] }) }), _jsxs(ListItemSecondaryAction, { children: [_jsx(Tooltip, { title: "View Proposal", children: _jsx(IconButton, { edge: "end", onClick: (e) => {
                                                        e.stopPropagation();
                                                        onViewProposal && onViewProposal(proposal._id);
                                                    }, children: _jsx(OpenInNewIcon, {}) }) }), proposal.publicAccessEnabled && (_jsx(Tooltip, { title: "Copy Public Link", children: _jsx(IconButton, { edge: "end", onClick: (e) => {
                                                        e.stopPropagation();
                                                        navigator.clipboard.writeText(proposal.publicUrl);
                                                    }, children: _jsx(LinkIcon, {}) }) })), proposal.status === 'draft' && (_jsx(Tooltip, { title: "Send Proposal", children: _jsx(IconButton, { edge: "end", onClick: (e) => {
                                                        e.stopPropagation();
                                                        // Handle send proposal
                                                    }, children: _jsx(EmailIcon, {}) }) }))] })] }), index < proposals.length - 1 && _jsx(Divider, {})] }, proposal._id))) }) })), opportunity && (_jsx(CreateProposalFromOpportunityDialog, { open: createDialogOpen, onClose: () => setCreateDialogOpen(false), onSubmit: handleCreateProposal, opportunity: opportunity }))] }));
};
export default OpportunityProposalIntegration;
