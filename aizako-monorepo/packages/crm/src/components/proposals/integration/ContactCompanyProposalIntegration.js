import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import React, { useState, useEffect } from 'react';
import { Box, Typography, Button, Card, CardContent, Grid, Divider, Chip, CircularProgress, Alert, IconButton, Tooltip, List, ListItem, ListItemText, ListItemSecondaryAction, Paper, Tabs, Tab, useTheme, useMediaQuery, } from '@mui/material';
import { Description as DescriptionIcon, OpenInNew as OpenInNewIcon, Refresh as RefreshIcon, Add as AddIcon, Link as LinkIcon, Email as EmailIcon, Business as BusinessIcon, Person as PersonIcon, } from '@mui/icons-material';
import { format } from 'date-fns';
import { ProposalService } from '../../../services/proposal-service';
import { CompanyService } from '../../../services/company-service';
import { ContactService } from '../../../services/contact-service';
import CreateProposalDialog from '../CreateProposalDialog';
/**
 * ContactCompanyProposalIntegration Component
 *
 * This component displays proposals associated with a contact or company and allows
 * creating new proposals.
 */
const ContactCompanyProposalIntegration = ({ entityId, entityType, tenantId, onCreateProposal, onViewProposal, }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    // State
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [proposals, setProposals] = useState([]);
    const [entity, setEntity] = useState(null);
    const [relatedEntities, setRelatedEntities] = useState([]);
    const [createDialogOpen, setCreateDialogOpen] = useState(false);
    const [activeTab, setActiveTab] = useState('all');
    // Fetch proposals and entity data
    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);
                // Fetch entity
                if (entityType === 'contact') {
                    const contactData = await ContactService.getContactById(entityId, tenantId);
                    setEntity(contactData);
                    // Fetch related companies
                    if (contactData && contactData.companyId) {
                        const companyData = await CompanyService.getCompanyById(String(contactData.companyId), tenantId);
                        setRelatedEntities([companyData]);
                    }
                    // Fetch proposals
                    const proposalsData = await ProposalService.getProposalsByContact(entityId, tenantId);
                    setProposals(proposalsData);
                }
                else {
                    const companyData = await CompanyService.getCompanyById(entityId, tenantId);
                    setEntity(companyData);
                    // Fetch related contacts
                    const contactsData = await ContactService.getContactsByCompany(entityId, tenantId);
                    setRelatedEntities(contactsData);
                    // Fetch proposals
                    const proposalsData = await ProposalService.getProposalsByCompany(entityId, tenantId);
                    setProposals(proposalsData);
                }
            }
            catch (err) {
                console.error('Error fetching data:', err);
                setError('Failed to load data. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchData();
    }, [entityId, entityType, tenantId]);
    // Handle refresh
    const handleRefresh = () => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);
                // Fetch proposals
                if (entityType === 'contact') {
                    const proposalsData = await ProposalService.getProposalsByContact(entityId, tenantId);
                    setProposals(proposalsData);
                }
                else {
                    const proposalsData = await ProposalService.getProposalsByCompany(entityId, tenantId);
                    setProposals(proposalsData);
                }
            }
            catch (err) {
                console.error('Error fetching data:', err);
                setError('Failed to load data. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchData();
    };
    // Handle create proposal
    const handleCreateProposal = async (proposalData) => {
        try {
            setLoading(true);
            setError(null);
            // Add entity data to proposal
            if (entityType === 'contact') {
                proposalData.contactIds = [entityId];
                if (entity.companyId) {
                    proposalData.companyId = entity.companyId;
                }
            }
            else {
                proposalData.companyId = entityId;
                if (proposalData.includeAllContacts) {
                    proposalData.contactIds = relatedEntities.map(contact => contact._id);
                    delete proposalData.includeAllContacts;
                }
            }
            // Create proposal
            const proposal = await ProposalService.createProposal(proposalData, tenantId);
            // Refresh proposals
            handleRefresh();
            // Close dialog
            setCreateDialogOpen(false);
            // Callback
            if (onCreateProposal) {
                onCreateProposal(String(proposal._id));
            }
        }
        catch (err) {
            console.error('Error creating proposal:', err);
            setError('Failed to create proposal. Please try again.');
            setLoading(false);
        }
    };
    // Handle tab change
    const handleTabChange = (event, newValue) => {
        setActiveTab(newValue);
    };
    // Filter proposals by status
    const filteredProposals = activeTab === 'all'
        ? proposals
        : proposals.filter(proposal => proposal.status === activeTab);
    // Render loading state
    if (loading && !entity) {
        return (_jsx(Box, { display: "flex", justifyContent: "center", alignItems: "center", height: "200px", children: _jsx(CircularProgress, {}) }));
    }
    // Render error state
    if (error) {
        return (_jsxs(Alert, { severity: "error", sx: { mb: 2 }, children: [error, _jsx(Button, { variant: "outlined", size: "small", onClick: handleRefresh, startIcon: _jsx(RefreshIcon, {}), sx: { ml: 2 }, children: "Retry" })] }));
    }
    return (_jsxs(Box, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2, children: [_jsx(Typography, { variant: "h6", children: "Proposals" }), _jsxs(Box, { children: [_jsx(Button, { variant: "outlined", startIcon: _jsx(RefreshIcon, {}), onClick: handleRefresh, sx: { mr: 1 }, size: isMobile ? "small" : "medium", children: "Refresh" }), _jsx(Button, { variant: "contained", startIcon: _jsx(AddIcon, {}), onClick: () => setCreateDialogOpen(true), size: isMobile ? "small" : "medium", children: "Create Proposal" })] })] }), entity && (_jsx(Card, { sx: { mb: 3 }, children: _jsxs(CardContent, { children: [_jsxs(Typography, { variant: "subtitle1", gutterBottom: true, children: [entityType === 'contact' ? 'Contact' : 'Company', " Summary"] }), _jsx(Grid, { container: true, spacing: 2, children: entityType === 'contact' ? (_jsxs(_Fragment, { children: [_jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsxs(Box, { display: "flex", alignItems: "center", children: [_jsx(PersonIcon, { color: "primary", sx: { mr: 1 } }), _jsx(Typography, { variant: "body2", noWrap: true, children: entity.name })] }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsxs(Box, { display: "flex", alignItems: "center", children: [_jsx(EmailIcon, { color: "info", sx: { mr: 1 } }), _jsx(Typography, { variant: "body2", noWrap: true, children: entity.email || 'No Email' })] }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsxs(Box, { display: "flex", alignItems: "center", children: [_jsx(BusinessIcon, { color: "secondary", sx: { mr: 1 } }), _jsx(Typography, { variant: "body2", noWrap: true, children: relatedEntities[0]?.name || 'No Company' })] }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsx(Chip, { label: entity.status || 'Active', color: "primary", size: "small" }) })] })) : (_jsxs(_Fragment, { children: [_jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsxs(Box, { display: "flex", alignItems: "center", children: [_jsx(BusinessIcon, { color: "primary", sx: { mr: 1 } }), _jsx(Typography, { variant: "body2", noWrap: true, children: entity.name })] }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsxs(Box, { display: "flex", alignItems: "center", children: [_jsx(PersonIcon, { color: "info", sx: { mr: 1 } }), _jsxs(Typography, { variant: "body2", children: [relatedEntities.length, " Contacts"] })] }) }), _jsxs(Grid, { item: true, xs: 12, sm: 6, md: 3, children: [_jsx(Typography, { variant: "body2", color: "text.secondary", children: "Industry" }), _jsx(Typography, { variant: "body2", children: entity.industry || 'N/A' })] }), _jsxs(Grid, { item: true, xs: 12, sm: 6, md: 3, children: [_jsx(Typography, { variant: "body2", color: "text.secondary", children: "Size" }), _jsx(Typography, { variant: "body2", children: entity.size || 'N/A' })] })] })) })] }) })), _jsx(Paper, { sx: { mb: 3 }, children: _jsxs(Tabs, { value: activeTab, onChange: handleTabChange, indicatorColor: "primary", textColor: "primary", variant: isMobile ? "scrollable" : "fullWidth", scrollButtons: isMobile ? "auto" : undefined, children: [_jsx(Tab, { label: "All", value: "all" }), _jsx(Tab, { label: "Draft", value: "draft" }), _jsx(Tab, { label: "Sent", value: "sent" }), _jsx(Tab, { label: "Viewed", value: "viewed" }), _jsx(Tab, { label: "Accepted", value: "accepted" }), _jsx(Tab, { label: "Rejected", value: "rejected" })] }) }), filteredProposals.length === 0 ? (_jsxs(Alert, { severity: "info", children: ["No ", activeTab !== 'all' ? activeTab : '', " proposals found for this ", entityType, "."] })) : (_jsx(Paper, { variant: "outlined", children: _jsx(List, { disablePadding: true, children: filteredProposals.map((proposal, index) => (_jsxs(React.Fragment, { children: [_jsxs(ListItem, { button: true, onClick: () => onViewProposal && onViewProposal(proposal._id), children: [_jsx(ListItemText, { primary: _jsxs(Box, { display: "flex", alignItems: "center", children: [_jsx(DescriptionIcon, { sx: { mr: 1, color: theme.palette.primary.main } }), _jsx(Typography, { variant: "subtitle1", children: proposal.title })] }), secondary: _jsxs(Box, { mt: 0.5, children: [_jsxs(Typography, { variant: "body2", color: "text.secondary", children: ["Created: ", format(new Date(proposal.createdAt), 'PPP')] }), _jsxs(Box, { display: "flex", alignItems: "center", mt: 0.5, flexWrap: "wrap", gap: 0.5, children: [_jsx(Chip, { label: proposal.status.toUpperCase(), color: proposal.status === 'accepted' ? 'success' :
                                                                proposal.status === 'rejected' ? 'error' :
                                                                    proposal.status === 'viewed' ? 'info' :
                                                                        proposal.status === 'sent' ? 'primary' :
                                                                            'default', size: "small" }), proposal.aiGenerated && (_jsx(Chip, { label: "AI Generated", size: "small" })), proposal.sections?.length > 0 && (_jsx(Chip, { label: `${proposal.sections.length} Sections`, size: "small" }))] })] }) }), _jsxs(ListItemSecondaryAction, { children: [_jsx(Tooltip, { title: "View Proposal", children: _jsx(IconButton, { edge: "end", onClick: (e) => {
                                                        e.stopPropagation();
                                                        onViewProposal && onViewProposal(proposal._id);
                                                    }, children: _jsx(OpenInNewIcon, {}) }) }), proposal.publicAccessEnabled && (_jsx(Tooltip, { title: "Copy Public Link", children: _jsx(IconButton, { edge: "end", onClick: (e) => {
                                                        e.stopPropagation();
                                                        navigator.clipboard.writeText(proposal.publicUrl);
                                                    }, children: _jsx(LinkIcon, {}) }) })), proposal.status === 'draft' && (_jsx(Tooltip, { title: "Send Proposal", children: _jsx(IconButton, { edge: "end", onClick: (e) => {
                                                        e.stopPropagation();
                                                        // Handle send proposal
                                                    }, children: _jsx(EmailIcon, {}) }) }))] })] }), index < filteredProposals.length - 1 && _jsx(Divider, {})] }, proposal._id))) }) })), entity && (_jsx(CreateProposalDialog, { isOpen: createDialogOpen, onClose: () => setCreateDialogOpen(false), onSubmit: handleCreateProposal, companyId: entityType === 'company' ? entity._id : undefined, contactId: entityType === 'contact' ? entity._id : undefined }))] }));
};
export default ContactCompanyProposalIntegration;
