import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { Box, Paper, Typography, Divider, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Button, ButtonGroup, Chip, useTheme, alpha } from '@mui/material';
import { formatCurrency, formatDate } from '../../utils/formatters';
import { sanitizeHtml } from '../../utils/sanitize';
/**
 * Proposal Preview Component
 *
 * This component provides a preview of how the proposal will look when exported.
 * It supports different view modes and formats.
 */
const ProposalPreview = ({ proposal }) => {
    const theme = useTheme();
    const [viewMode, setViewMode] = useState('desktop');
    const [colorScheme, setColorScheme] = useState('professional');
    // Get primary color based on color scheme
    const getPrimaryColor = () => {
        switch (colorScheme) {
            case 'professional':
                return theme.palette.primary.main;
            case 'creative':
                return theme.palette.secondary.main;
            case 'modern':
                return theme.palette.info.main;
            case 'classic':
                return theme.palette.grey[800];
            default:
                return theme.palette.primary.main;
        }
    };
    // Get container width based on view mode
    const getContainerWidth = () => {
        switch (viewMode) {
            case 'desktop':
                return '100%';
            case 'mobile':
                return '375px';
            case 'print':
                return '210mm'; // A4 width
            default:
                return '100%';
        }
    };
    // Get container height based on view mode
    const getContainerHeight = () => {
        if (viewMode === 'print') {
            return '297mm'; // A4 height
        }
        return 'auto';
    };
    // Get container padding based on view mode
    const getContainerPadding = () => {
        switch (viewMode) {
            case 'desktop':
                return theme.spacing(4);
            case 'mobile':
                return theme.spacing(2);
            case 'print':
                return theme.spacing(4);
            default:
                return theme.spacing(4);
        }
    };
    // Render HTML content safely
    const renderHtml = (html) => {
        return _jsx("div", { dangerouslySetInnerHTML: { __html: sanitizeHtml(html) } });
    };
    // Primary color for the selected color scheme
    const primaryColor = getPrimaryColor();
    return (_jsxs(Box, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2, children: [_jsxs(ButtonGroup, { variant: "outlined", size: "small", children: [_jsx(Button, { variant: viewMode === 'desktop' ? 'contained' : 'outlined', onClick: () => setViewMode('desktop'), children: "Desktop" }), _jsx(Button, { variant: viewMode === 'mobile' ? 'contained' : 'outlined', onClick: () => setViewMode('mobile'), children: "Mobile" }), _jsx(Button, { variant: viewMode === 'print' ? 'contained' : 'outlined', onClick: () => setViewMode('print'), children: "Print" })] }), _jsxs(ButtonGroup, { variant: "outlined", size: "small", children: [_jsx(Button, { variant: colorScheme === 'professional' ? 'contained' : 'outlined', onClick: () => setColorScheme('professional'), sx: { bgcolor: colorScheme === 'professional' ? theme.palette.primary.main : 'transparent' }, children: "Professional" }), _jsx(Button, { variant: colorScheme === 'creative' ? 'contained' : 'outlined', onClick: () => setColorScheme('creative'), sx: { bgcolor: colorScheme === 'creative' ? theme.palette.secondary.main : 'transparent' }, children: "Creative" }), _jsx(Button, { variant: colorScheme === 'modern' ? 'contained' : 'outlined', onClick: () => setColorScheme('modern'), sx: { bgcolor: colorScheme === 'modern' ? theme.palette.info.main : 'transparent' }, children: "Modern" }), _jsx(Button, { variant: colorScheme === 'classic' ? 'contained' : 'outlined', onClick: () => setColorScheme('classic'), sx: { bgcolor: colorScheme === 'classic' ? theme.palette.grey[800] : 'transparent', color: colorScheme === 'classic' ? 'white' : 'inherit' }, children: "Classic" })] })] }), _jsx(Box, { sx: {
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'flex-start',
                    bgcolor: theme.palette.grey[100],
                    p: 2,
                    minHeight: '80vh',
                    borderRadius: 1,
                }, children: _jsxs(Paper, { elevation: 3, sx: {
                        width: getContainerWidth(),
                        height: getContainerHeight(),
                        p: getContainerPadding(),
                        overflow: 'auto',
                        ...(viewMode === 'print' && {
                            boxShadow: '0 0 10px rgba(0,0,0,0.1)',
                        }),
                    }, children: [_jsxs(Box, { sx: {
                                borderBottom: `4px solid ${primaryColor}`,
                                pb: 2,
                                mb: 4,
                            }, children: [_jsx(Typography, { variant: "h4", component: "h1", gutterBottom: true, sx: { color: primaryColor, fontWeight: 'bold' }, children: proposal.title || 'Untitled Proposal' }), proposal.description && (_jsx(Typography, { variant: "subtitle1", color: "text.secondary", gutterBottom: true, children: proposal.description })), _jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mt: 2, children: [_jsx(Chip, { label: proposal.status?.toUpperCase() || 'DRAFT', color: proposal.status === 'accepted' ? 'success' :
                                                proposal.status === 'rejected' ? 'error' :
                                                    proposal.status === 'sent' ? 'primary' :
                                                        proposal.status === 'viewed' ? 'info' :
                                                            'default', size: "small" }), _jsx(Typography, { variant: "body2", color: "text.secondary", children: formatDate(proposal.createdAt || new Date()) })] })] }), proposal.sections
                            ?.filter(section => section.isVisible)
                            .sort((a, b) => a.order - b.order)
                            .map((section, index) => (_jsxs(Box, { mb: 4, children: [_jsx(Typography, { variant: "h5", component: "h2", gutterBottom: true, sx: {
                                        color: primaryColor,
                                        borderBottom: `2px solid ${alpha(primaryColor, 0.3)}`,
                                        pb: 1,
                                    }, children: section.title }), _jsx(Box, { sx: { mt: 2 }, children: renderHtml(section.content) })] }, section.id))), proposal.pricing && proposal.pricing.items && proposal.pricing.items.length > 0 && (_jsxs(Box, { mb: 4, children: [_jsx(Typography, { variant: "h5", component: "h2", gutterBottom: true, sx: {
                                        color: primaryColor,
                                        borderBottom: `2px solid ${alpha(primaryColor, 0.3)}`,
                                        pb: 1,
                                    }, children: "Pricing" }), _jsx(TableContainer, { component: Paper, variant: "outlined", sx: { mt: 2 }, children: _jsxs(Table, { children: [_jsx(TableHead, { sx: { bgcolor: alpha(primaryColor, 0.1) }, children: _jsxs(TableRow, { children: [_jsx(TableCell, { sx: { fontWeight: 'bold' }, children: "Item" }), _jsx(TableCell, { align: "right", sx: { fontWeight: 'bold' }, children: "Quantity" }), _jsx(TableCell, { align: "right", sx: { fontWeight: 'bold' }, children: "Unit Price" }), _jsx(TableCell, { align: "right", sx: { fontWeight: 'bold' }, children: "Total" })] }) }), _jsxs(TableBody, { children: [proposal.pricing.items.map((item) => (_jsxs(TableRow, { children: [_jsxs(TableCell, { children: [_jsx(Typography, { variant: "body2", fontWeight: "medium", children: item.name }), item.description && (_jsx(Typography, { variant: "caption", color: "text.secondary", children: item.description }))] }), _jsx(TableCell, { align: "right", children: item.quantity }), _jsx(TableCell, { align: "right", children: formatCurrency(item.unitPrice, proposal.pricing?.currency) }), _jsx(TableCell, { align: "right", children: formatCurrency(item.total, proposal.pricing?.currency) })] }, item.id))), _jsxs(TableRow, { children: [_jsx(TableCell, { colSpan: 3, align: "right", sx: { fontWeight: 'medium' }, children: "Subtotal" }), _jsx(TableCell, { align: "right", children: formatCurrency(proposal.pricing.subtotal, proposal.pricing.currency) })] }), proposal.pricing.discount && proposal.pricing.discount > 0 && (_jsxs(TableRow, { children: [_jsx(TableCell, { colSpan: 3, align: "right", sx: { fontWeight: 'medium' }, children: "Discount" }), _jsx(TableCell, { align: "right", children: formatCurrency(proposal.pricing.discount, proposal.pricing.currency) })] })), proposal.pricing.tax && proposal.pricing.tax > 0 && (_jsxs(TableRow, { children: [_jsx(TableCell, { colSpan: 3, align: "right", sx: { fontWeight: 'medium' }, children: "Tax" }), _jsx(TableCell, { align: "right", children: formatCurrency(proposal.pricing.tax, proposal.pricing.currency) })] })), _jsxs(TableRow, { children: [_jsx(TableCell, { colSpan: 3, align: "right", sx: { fontWeight: 'bold', color: primaryColor }, children: "Total" }), _jsx(TableCell, { align: "right", sx: { fontWeight: 'bold', color: primaryColor }, children: formatCurrency(proposal.pricing.total, proposal.pricing.currency) })] })] })] }) })] })), proposal.terms && (_jsxs(Box, { mb: 4, children: [_jsx(Typography, { variant: "h5", component: "h2", gutterBottom: true, sx: {
                                        color: primaryColor,
                                        borderBottom: `2px solid ${alpha(primaryColor, 0.3)}`,
                                        pb: 1,
                                    }, children: "Terms & Conditions" }), _jsx(Typography, { variant: "body2", sx: { whiteSpace: 'pre-line', mt: 2 }, children: proposal.terms })] })), _jsx(Divider, { sx: { mt: 4, mb: 2 } }), _jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", children: [_jsxs(Typography, { variant: "caption", color: "text.secondary", children: ["Generated on ", formatDate(new Date())] }), _jsx(Typography, { variant: "caption", color: "text.secondary", children: "Confidential" })] })] }) })] }));
};
export default ProposalPreview;
