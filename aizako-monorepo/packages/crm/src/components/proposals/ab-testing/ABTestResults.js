import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Box, Typography, Grid, Card, CardContent, CircularProgress, Alert, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, LinearProgress, Tooltip, useTheme, } from '@mui/material';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, Legend, ResponsiveContainer, Cell, } from 'recharts';
import { Refresh as RefreshIcon, CheckCircle as CheckCircleIcon, EmojiEvents as TrophyIcon, TrendingUp as TrendingUpIcon, TrendingDown as TrendingDownIcon, Remove as FlatIcon, } from '@mui/icons-material';
import { ABTestingService } from '../../../services/ab-testing-service';
import { formatPercentage } from '../../../utils/formatters';
/**
 * ABTestResults Component
 *
 * This component displays the results of an A/B test.
 */
const ABTestResults = ({ testId, tenantId, }) => {
    const theme = useTheme();
    // State
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [results, setResults] = useState(null);
    // Fetch results
    useEffect(() => {
        const fetchResults = async () => {
            try {
                setLoading(true);
                setError(null);
                const response = await ABTestingService.getTestResults(testId, tenantId);
                setResults(response);
            }
            catch (err) {
                console.error('Error fetching A/B test results:', err);
                setError('Failed to load test results. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchResults();
    }, [testId, tenantId]);
    // Handle refresh
    const handleRefresh = () => {
        const fetchResults = async () => {
            try {
                setLoading(true);
                setError(null);
                const response = await ABTestingService.getTestResults(testId, tenantId);
                setResults(response);
            }
            catch (err) {
                console.error('Error fetching A/B test results:', err);
                setError('Failed to load test results. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchResults();
    };
    // Prepare chart data
    const prepareViewsChart = () => {
        if (!results)
            return [];
        const data = [
            {
                name: 'Base',
                views: results.baseProposal.views,
                uniqueViews: results.baseProposal.uniqueViews,
            },
            ...results.variants.map((variant) => ({
                name: variant.name,
                views: variant.views,
                uniqueViews: variant.uniqueViews,
            })),
        ];
        return data;
    };
    const prepareAcceptanceChart = () => {
        if (!results)
            return [];
        const data = [
            {
                name: 'Base',
                acceptances: results.baseProposal.acceptances,
                rejections: results.baseProposal.rejections,
            },
            ...results.variants.map((variant) => ({
                name: variant.name,
                acceptances: variant.acceptances,
                rejections: variant.rejections,
            })),
        ];
        return data;
    };
    const prepareAcceptanceRateChart = () => {
        if (!results)
            return [];
        const data = [
            {
                name: 'Base',
                rate: results.baseProposal.acceptanceRate * 100,
                isWinner: false,
            },
            ...results.variants.map((variant) => ({
                name: variant.name,
                rate: variant.acceptanceRate * 100,
                isWinner: results.winner === variant.name,
            })),
        ];
        return data;
    };
    // Render loading state
    if (loading) {
        return (_jsx(Box, { display: "flex", justifyContent: "center", alignItems: "center", height: "400px", children: _jsx(CircularProgress, {}) }));
    }
    // Render error state
    if (error) {
        return (_jsxs(Alert, { severity: "error", sx: { mb: 2 }, children: [error, _jsx(Button, { variant: "outlined", size: "small", onClick: handleRefresh, startIcon: _jsx(RefreshIcon, {}), sx: { ml: 2 }, children: "Retry" })] }));
    }
    // Render empty state
    if (!results) {
        return (_jsx(Alert, { severity: "info", children: "No results available for this test yet." }));
    }
    return (_jsxs(Box, { children: [_jsx(Box, { mb: 3, children: _jsxs(Grid, { container: true, spacing: 2, children: [_jsxs(Grid, { item: true, xs: 12, md: 8, children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Test Summary" }), _jsx(Typography, { variant: "body1", children: results.winner ? (_jsxs(Box, { display: "flex", alignItems: "center", children: [_jsx(TrophyIcon, { color: "success", sx: { mr: 1 } }), _jsxs("span", { children: ["Winner: ", _jsx("strong", { children: results.winner }), " with ", formatPercentage(results.variants.find((v) => v.name === results.winner)?.acceptanceRate), " acceptance rate"] })] })) : ('No clear winner yet') }), results.winner && (_jsxs(Typography, { variant: "body2", color: "text.secondary", sx: { mt: 1 }, children: ["Confidence: ", results.confidence.toFixed(1), "%"] }))] }), _jsx(Grid, { item: true, xs: 12, md: 4, children: _jsx(Box, { display: "flex", justifyContent: "flex-end", children: _jsx(Button, { variant: "outlined", startIcon: _jsx(RefreshIcon, {}), onClick: handleRefresh, children: "Refresh Results" }) }) })] }) }), _jsxs(Grid, { container: true, spacing: 3, mb: 3, children: [_jsx(Grid, { item: true, xs: 12, md: 6, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Views" }), _jsx(Box, { height: 300, children: _jsx(ResponsiveContainer, { width: "100%", height: "100%", children: _jsxs(BarChart, { data: prepareViewsChart(), margin: { top: 5, right: 30, left: 20, bottom: 5 }, children: [_jsx(CartesianGrid, { strokeDasharray: "3 3" }), _jsx(XAxis, { dataKey: "name" }), _jsx(YAxis, {}), _jsx(RechartsTooltip, {}), _jsx(Legend, {}), _jsx(Bar, { dataKey: "views", name: "Total Views", fill: theme.palette.primary.main }), _jsx(Bar, { dataKey: "uniqueViews", name: "Unique Views", fill: theme.palette.secondary.main })] }) }) })] }) }) }), _jsx(Grid, { item: true, xs: 12, md: 6, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Acceptance Rate" }), _jsx(Box, { height: 300, children: _jsx(ResponsiveContainer, { width: "100%", height: "100%", children: _jsxs(BarChart, { data: prepareAcceptanceRateChart(), margin: { top: 5, right: 30, left: 20, bottom: 5 }, children: [_jsx(CartesianGrid, { strokeDasharray: "3 3" }), _jsx(XAxis, { dataKey: "name" }), _jsx(YAxis, { domain: [0, 100] }), _jsx(RechartsTooltip, { formatter: (value) => [`${Number(value).toFixed(1)}%`, 'Acceptance Rate'] }), _jsx(Bar, { dataKey: "rate", name: "Acceptance Rate", children: prepareAcceptanceRateChart().map((entry, index) => (_jsx(Cell, { fill: entry.isWinner ? theme.palette.success.main : theme.palette.primary.main }, `cell-${index}`))) })] }) }) })] }) }) })] }), _jsx(Typography, { variant: "h6", gutterBottom: true, children: "Detailed Results" }), _jsx(TableContainer, { component: Paper, children: _jsxs(Table, { children: [_jsx(TableHead, { children: _jsxs(TableRow, { children: [_jsx(TableCell, { children: "Variant" }), _jsx(TableCell, { align: "right", children: "Views" }), _jsx(TableCell, { align: "right", children: "Unique Views" }), _jsx(TableCell, { align: "right", children: "Acceptances" }), _jsx(TableCell, { align: "right", children: "Acceptance Rate" }), _jsx(TableCell, { align: "right", children: "Improvement" })] }) }), _jsxs(TableBody, { children: [_jsxs(TableRow, { children: [_jsx(TableCell, { children: _jsx(Typography, { fontWeight: "bold", children: "Base" }) }), _jsx(TableCell, { align: "right", children: results.baseProposal.views }), _jsx(TableCell, { align: "right", children: results.baseProposal.uniqueViews }), _jsx(TableCell, { align: "right", children: results.baseProposal.acceptances }), _jsx(TableCell, { align: "right", children: formatPercentage(results.baseProposal.acceptanceRate) }), _jsx(TableCell, { align: "right", children: "-" })] }), results.variants.map((variant) => (_jsxs(TableRow, { children: [_jsx(TableCell, { children: _jsxs(Box, { display: "flex", alignItems: "center", children: [results.winner === variant.name && (_jsx(Tooltip, { title: "Winner", children: _jsx(TrophyIcon, { color: "success", sx: { mr: 1 } }) })), variant.name] }) }), _jsx(TableCell, { align: "right", children: variant.views }), _jsx(TableCell, { align: "right", children: variant.uniqueViews }), _jsx(TableCell, { align: "right", children: variant.acceptances }), _jsx(TableCell, { align: "right", children: formatPercentage(variant.acceptanceRate) }), _jsx(TableCell, { align: "right", children: _jsxs(Box, { display: "flex", alignItems: "center", justifyContent: "flex-end", children: [variant.improvement > 0 ? (_jsx(TrendingUpIcon, { color: "success", fontSize: "small", sx: { mr: 0.5 } })) : variant.improvement < 0 ? (_jsx(TrendingDownIcon, { color: "error", fontSize: "small", sx: { mr: 0.5 } })) : (_jsx(FlatIcon, { color: "action", fontSize: "small", sx: { mr: 0.5 } })), variant.improvement > 0 ? '+' : '', variant.improvement.toFixed(1), "%"] }) })] }, variant.name)))] })] }) }), _jsx(Card, { sx: { mt: 3 }, children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Statistical Significance" }), _jsxs(Box, { display: "flex", alignItems: "center", mb: 1, children: [_jsxs(Typography, { variant: "body1", sx: { mr: 2 }, children: ["Confidence: ", results.confidence.toFixed(1), "%"] }), _jsx(LinearProgress, { variant: "determinate", value: results.confidence, color: results.confidence >= 95 ? 'success' : 'primary', sx: { flexGrow: 1, height: 10, borderRadius: 5 } })] }), _jsx(Typography, { variant: "body2", color: "text.secondary", children: results.confidence >= 95 ? (_jsxs(Box, { display: "flex", alignItems: "center", children: [_jsx(CheckCircleIcon, { color: "success", fontSize: "small", sx: { mr: 1 } }), "The results are statistically significant (95% confidence level)."] })) : ('The test needs more data to reach statistical significance (95% confidence level).') })] }) })] }));
};
export default ABTestResults;
