import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, FormControl, InputLabel, Select, MenuItem, Typography, Box, Divider, IconButton, Grid, Chip, Alert, CircularProgress, Stepper, Step, StepLabel, Paper, FormHelperText, Tooltip, useTheme, } from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon, } from '@mui/icons-material';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { addDays } from 'date-fns';
import { ProposalService } from '../../../services/proposal-service';
import RichTextEditor from '../../common/RichTextEditor';
/**
 * CreateABTestDialog Component
 *
 * This component displays a dialog for creating a new A/B test.
 */
const CreateABTestDialog = ({ open, onClose, onSubmit, proposals, }) => {
    const theme = useTheme();
    // State
    const [activeStep, setActiveStep] = useState(0);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [baseProposal, setBaseProposal] = useState('');
    const [baseProposalData, setBaseProposalData] = useState(null);
    const [testName, setTestName] = useState('');
    const [testDescription, setTestDescription] = useState('');
    const [variants, setVariants] = useState([]);
    const [trafficSplit, setTrafficSplit] = useState([]);
    const [startDate, setStartDate] = useState(new Date());
    const [endDate, setEndDate] = useState(addDays(new Date(), 30));
    const [selectedSectionId, setSelectedSectionId] = useState('');
    const [editingVariantIndex, setEditingVariantIndex] = useState(-1);
    const [editingSectionContent, setEditingSectionContent] = useState('');
    // Reset state when dialog opens
    useEffect(() => {
        if (open) {
            setActiveStep(0);
            setBaseProposal('');
            setBaseProposalData(null);
            setTestName('');
            setTestDescription('');
            setVariants([]);
            setTrafficSplit([]);
            setStartDate(new Date());
            setEndDate(addDays(new Date(), 30));
            setSelectedSectionId('');
            setEditingVariantIndex(-1);
            setEditingSectionContent('');
            setError(null);
        }
    }, [open]);
    // Update traffic split when variants change
    useEffect(() => {
        if (variants.length > 0) {
            // Calculate equal distribution
            const basePercentage = 100 / (variants.length + 1);
            const variantPercentage = basePercentage;
            setTrafficSplit([
                basePercentage,
                ...Array(variants.length).fill(variantPercentage),
            ]);
        }
        else {
            setTrafficSplit([100]);
        }
    }, [variants.length]);
    // Fetch base proposal data
    useEffect(() => {
        if (baseProposal) {
            const fetchProposal = async () => {
                try {
                    setLoading(true);
                    setError(null);
                    const response = await ProposalService.getProposalById(baseProposal, 'default'); // TODO: Get tenant ID from context
                    setBaseProposalData(response);
                }
                catch (err) {
                    console.error('Error fetching proposal:', err);
                    setError('Failed to load proposal data. Please try again.');
                    setBaseProposalData(null);
                }
                finally {
                    setLoading(false);
                }
            };
            fetchProposal();
        }
        else {
            setBaseProposalData(null);
        }
    }, [baseProposal]);
    // Handle next step
    const handleNext = () => {
        setActiveStep((prevStep) => prevStep + 1);
    };
    // Handle back step
    const handleBack = () => {
        setActiveStep((prevStep) => prevStep - 1);
    };
    // Handle add variant
    const handleAddVariant = () => {
        setVariants([
            ...variants,
            {
                name: `Variant ${variants.length + 1}`,
                description: '',
                changes: [],
            },
        ]);
    };
    // Handle delete variant
    const handleDeleteVariant = (index) => {
        setVariants(variants.filter((_, i) => i !== index));
    };
    // Handle edit variant
    const handleEditVariant = (index, field, value) => {
        const updatedVariants = [...variants];
        updatedVariants[index] = {
            ...updatedVariants[index],
            [field]: value,
        };
        setVariants(updatedVariants);
    };
    // Handle edit section content
    const handleEditSectionContent = (content) => {
        setEditingSectionContent(content);
    };
    // Handle save section content
    const handleSaveSectionContent = () => {
        if (editingVariantIndex === -1 || !selectedSectionId)
            return;
        const updatedVariants = [...variants];
        const variantIndex = editingVariantIndex;
        // Check if section already exists in changes
        const sectionIndex = updatedVariants[variantIndex].changes.findIndex((change) => change.sectionId === selectedSectionId);
        if (sectionIndex !== -1) {
            // Update existing section
            updatedVariants[variantIndex].changes[sectionIndex].content = editingSectionContent;
        }
        else {
            // Add new section
            updatedVariants[variantIndex].changes.push({
                sectionId: selectedSectionId,
                content: editingSectionContent,
            });
        }
        setVariants(updatedVariants);
        setSelectedSectionId('');
        setEditingVariantIndex(-1);
        setEditingSectionContent('');
    };
    // Handle traffic split change
    const handleTrafficSplitChange = (index, value) => {
        const updatedTrafficSplit = [...trafficSplit];
        updatedTrafficSplit[index] = value;
        // Ensure total is 100%
        const total = updatedTrafficSplit.reduce((sum, val) => sum + val, 0);
        if (total !== 100) {
            // Adjust other values proportionally
            const adjustment = (100 - value) / (updatedTrafficSplit.length - 1);
            updatedTrafficSplit.forEach((_, i) => {
                if (i !== index) {
                    updatedTrafficSplit[i] = adjustment;
                }
            });
        }
        setTrafficSplit(updatedTrafficSplit);
    };
    // Handle submit
    const handleSubmit = () => {
        // Validate
        if (!baseProposal) {
            setError('Please select a base proposal.');
            return;
        }
        if (!testName) {
            setError('Please enter a test name.');
            return;
        }
        if (variants.length === 0) {
            setError('Please add at least one variant.');
            return;
        }
        // Check if all variants have changes
        const emptyVariants = variants.filter((variant) => variant.changes.length === 0);
        if (emptyVariants.length > 0) {
            setError(`The following variants have no changes: ${emptyVariants.map((v) => v.name).join(', ')}`);
            return;
        }
        // Submit
        onSubmit({
            name: testName,
            description: testDescription,
            baseProposalId: baseProposal,
            variants,
            trafficSplit,
            startDate,
            endDate,
        });
    };
    // Get section content
    const getSectionContent = (sectionId) => {
        if (!baseProposalData)
            return '';
        const section = baseProposalData.sections.find((s) => s.id === sectionId);
        return section ? section.content : '';
    };
    // Get variant section content
    const getVariantSectionContent = (variantIndex, sectionId) => {
        if (variantIndex === -1)
            return '';
        const change = variants[variantIndex].changes.find((c) => c.sectionId === sectionId);
        return change ? change.content : getSectionContent(sectionId);
    };
    // Steps
    const steps = ['Select Base Proposal', 'Create Variants', 'Configure Test'];
    return (_jsxs(Dialog, { open: open, onClose: onClose, maxWidth: "lg", fullWidth: true, children: [_jsx(DialogTitle, { children: "Create A/B Test" }), _jsxs(DialogContent, { children: [_jsx(Stepper, { activeStep: activeStep, sx: { mb: 3 }, children: steps.map((label) => (_jsx(Step, { children: _jsx(StepLabel, { children: label }) }, label))) }), error && (_jsx(Alert, { severity: "error", sx: { mb: 2 }, children: error })), activeStep === 0 && (_jsxs(Box, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Select Base Proposal" }), _jsxs(FormControl, { fullWidth: true, margin: "normal", children: [_jsx(InputLabel, { children: "Base Proposal" }), _jsxs(Select, { value: baseProposal, onChange: (e) => setBaseProposal(e.target.value), label: "Base Proposal", children: [_jsx(MenuItem, { value: "", children: _jsx("em", { children: "Select a proposal" }) }), proposals.map((proposal) => (_jsx(MenuItem, { value: proposal._id, children: proposal.title }, proposal._id)))] }), _jsx(FormHelperText, { children: "Select the proposal you want to use as the base for your A/B test." })] }), loading && (_jsx(Box, { display: "flex", justifyContent: "center", mt: 2, children: _jsx(CircularProgress, {}) })), baseProposalData && (_jsxs(Box, { mt: 3, children: [_jsx(Typography, { variant: "subtitle1", gutterBottom: true, children: "Proposal Details" }), _jsxs(Paper, { variant: "outlined", sx: { p: 2 }, children: [_jsx(Typography, { variant: "h6", children: baseProposalData.title }), _jsx(Typography, { variant: "body2", color: "text.secondary", gutterBottom: true, children: baseProposalData.description }), _jsx(Divider, { sx: { my: 1 } }), _jsxs(Typography, { variant: "subtitle2", gutterBottom: true, children: ["Sections: ", baseProposalData.sections.length] }), _jsx(Box, { display: "flex", flexWrap: "wrap", gap: 1, children: baseProposalData.sections.map((section) => (_jsx(Chip, { label: section.title, size: "small" }, section.id))) })] })] }))] })), activeStep === 1 && (_jsxs(Box, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Create Variants" }), _jsx(TextField, { label: "Test Name", value: testName, onChange: (e) => setTestName(e.target.value), fullWidth: true, margin: "normal", required: true }), _jsx(TextField, { label: "Test Description", value: testDescription, onChange: (e) => setTestDescription(e.target.value), fullWidth: true, margin: "normal", multiline: true, rows: 2 }), _jsxs(Box, { mt: 3, children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2, children: [_jsx(Typography, { variant: "subtitle1", children: "Variants" }), _jsx(Button, { variant: "outlined", startIcon: _jsx(AddIcon, {}), onClick: handleAddVariant, children: "Add Variant" })] }), variants.length === 0 ? (_jsx(Alert, { severity: "info", children: "Add at least one variant to continue." })) : (_jsx(Grid, { container: true, spacing: 2, children: variants.map((variant, index) => (_jsx(Grid, { item: true, xs: 12, children: _jsxs(Paper, { variant: "outlined", sx: { p: 2 }, children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 1, children: [_jsx(TextField, { label: "Variant Name", value: variant.name, onChange: (e) => handleEditVariant(index, 'name', e.target.value), size: "small", sx: { width: '60%' } }), _jsx(Box, { children: _jsx(Tooltip, { title: "Delete Variant", children: _jsx(IconButton, { color: "error", onClick: () => handleDeleteVariant(index), children: _jsx(DeleteIcon, {}) }) }) })] }), _jsx(TextField, { label: "Variant Description", value: variant.description, onChange: (e) => handleEditVariant(index, 'description', e.target.value), fullWidth: true, margin: "normal", size: "small", multiline: true, rows: 2 }), _jsx(Divider, { sx: { my: 1 } }), _jsx(Typography, { variant: "subtitle2", gutterBottom: true, children: "Section Changes" }), variant.changes.length === 0 ? (_jsx(Typography, { variant: "body2", color: "text.secondary", children: "No changes yet. Select a section to modify." })) : (_jsx(Box, { display: "flex", flexWrap: "wrap", gap: 1, mb: 2, children: variant.changes.map((change) => {
                                                            const section = baseProposalData?.sections.find((s) => s.id === change.sectionId);
                                                            return (_jsx(Chip, { label: section?.title || 'Unknown Section', color: "primary", variant: "outlined", onDelete: () => {
                                                                    const updatedVariants = [...variants];
                                                                    updatedVariants[index].changes = updatedVariants[index].changes.filter((c) => c.sectionId !== change.sectionId);
                                                                    setVariants(updatedVariants);
                                                                } }, change.sectionId));
                                                        }) })), _jsxs(FormControl, { fullWidth: true, margin: "normal", size: "small", children: [_jsx(InputLabel, { children: "Edit Section" }), _jsxs(Select, { value: "", onChange: (e) => {
                                                                    setSelectedSectionId(e.target.value);
                                                                    setEditingVariantIndex(index);
                                                                    setEditingSectionContent(getVariantSectionContent(index, e.target.value));
                                                                }, label: "Edit Section", children: [_jsx(MenuItem, { value: "", children: _jsx("em", { children: "Select a section" }) }), baseProposalData?.sections.map((section) => (_jsx(MenuItem, { value: section.id, children: section.title }, section.id)))] })] })] }) }, index))) }))] }), selectedSectionId && editingVariantIndex !== -1 && (_jsx(Box, { mt: 3, children: _jsxs(Paper, { variant: "outlined", sx: { p: 2 }, children: [_jsxs(Typography, { variant: "subtitle1", gutterBottom: true, children: ["Edit Section: ", baseProposalData?.sections.find((s) => s.id === selectedSectionId)?.title] }), _jsx(Box, { mb: 2, children: _jsx(RichTextEditor, { value: editingSectionContent, onChange: handleEditSectionContent, placeholder: "Enter section content..." }) }), _jsxs(Box, { display: "flex", justifyContent: "flex-end", gap: 1, children: [_jsx(Button, { onClick: () => {
                                                        setSelectedSectionId('');
                                                        setEditingVariantIndex(-1);
                                                        setEditingSectionContent('');
                                                    }, children: "Cancel" }), _jsx(Button, { variant: "contained", onClick: handleSaveSectionContent, children: "Save Changes" })] })] }) }))] })), activeStep === 2 && (_jsxs(Box, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Configure Test" }), _jsxs(Grid, { container: true, spacing: 3, children: [_jsx(Grid, { item: true, xs: 12, md: 6, children: _jsx(LocalizationProvider, { dateAdapter: AdapterDateFns, children: _jsx(DatePicker, { label: "Start Date", value: startDate, onChange: (date) => date && setStartDate(date), slotProps: { textField: { fullWidth: true, margin: 'normal' } } }) }) }), _jsx(Grid, { item: true, xs: 12, md: 6, children: _jsx(LocalizationProvider, { dateAdapter: AdapterDateFns, children: _jsx(DatePicker, { label: "End Date", value: endDate, onChange: (date) => setEndDate(date), slotProps: { textField: { fullWidth: true, margin: 'normal' } } }) }) })] }), _jsx(Typography, { variant: "subtitle1", gutterBottom: true, sx: { mt: 3 }, children: "Traffic Split" }), _jsxs(Grid, { container: true, spacing: 2, children: [_jsx(Grid, { item: true, xs: 12, children: _jsx(TextField, { label: "Base Proposal", value: trafficSplit[0], onChange: (e) => handleTrafficSplitChange(0, Number(e.target.value)), type: "number", InputProps: { inputProps: { min: 0, max: 100 } }, fullWidth: true, margin: "normal", helperText: `${baseProposalData?.title || 'Base Proposal'}` }) }), variants.map((variant, index) => (_jsx(Grid, { item: true, xs: 12, children: _jsx(TextField, { label: `Variant ${index + 1}`, value: trafficSplit[index + 1], onChange: (e) => handleTrafficSplitChange(index + 1, Number(e.target.value)), type: "number", InputProps: { inputProps: { min: 0, max: 100 } }, fullWidth: true, margin: "normal", helperText: variant.name }) }, index)))] }), _jsx(Alert, { severity: "info", sx: { mt: 3 }, children: _jsx(Typography, { variant: "body2", children: "The traffic split determines what percentage of visitors will see each variant. The total should add up to 100%." }) })] }))] }), _jsxs(DialogActions, { children: [_jsx(Button, { onClick: onClose, children: "Cancel" }), activeStep > 0 && (_jsx(Button, { onClick: handleBack, children: "Back" })), activeStep < steps.length - 1 ? (_jsx(Button, { variant: "contained", onClick: handleNext, disabled: (activeStep === 0 && !baseProposal) ||
                            (activeStep === 1 && (variants.length === 0 || !testName)), children: "Next" })) : (_jsx(Button, { variant: "contained", onClick: handleSubmit, children: "Create Test" }))] })] }));
};
export default CreateABTestDialog;
