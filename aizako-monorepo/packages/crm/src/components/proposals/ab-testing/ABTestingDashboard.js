import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Box, Typography, Button, Card, CardContent, CardActions, Grid, Divider, Chip, CircularProgress, Alert, IconButton, Tooltip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Tabs, Tab, Paper, useTheme, useMediaQuery, } from '@mui/material';
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, PlayArrow as PlayIcon, Pause as PauseIcon, CheckCircle as CheckCircleIcon, Refresh as RefreshIcon, Bar<PERSON>hart as ChartIcon, } from '@mui/icons-material';
import { formatDistance } from 'date-fns';
import { ABTestingService } from '../../../services/ab-testing-service';
import { ProposalService } from '../../../services/proposal-service';
import ABTestResults from './ABTestResults';
import CreateABTestDialog from './CreateABTestDialog';
/**
 * ABTestingDashboard Component
 *
 * This component displays a dashboard for managing A/B tests for proposals.
 */
const ABTestingDashboard = ({ tenantId, }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    // State
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [tests, setTests] = useState([]);
    const [activeTab, setActiveTab] = useState('active');
    const [createDialogOpen, setCreateDialogOpen] = useState(false);
    const [editDialogOpen, setEditDialogOpen] = useState(false);
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [resultsDialogOpen, setResultsDialogOpen] = useState(false);
    const [selectedTest, setSelectedTest] = useState(null);
    const [proposals, setProposals] = useState([]);
    // Fetch tests
    useEffect(() => {
        const fetchTests = async () => {
            try {
                setLoading(true);
                setError(null);
                let response;
                if (activeTab === 'all') {
                    response = await ABTestingService.getTests(tenantId);
                }
                else {
                    response = await ABTestingService.getTests(tenantId, activeTab);
                }
                setTests(response);
                // Fetch proposals for the create dialog
                const proposalsResponse = await ProposalService.getProposals({
                    status: ['draft', 'sent', 'viewed'],
                    limit: 100,
                    page: 1,
                }, tenantId);
                setProposals(proposalsResponse.proposals);
            }
            catch (err) {
                console.error('Error fetching A/B tests:', err);
                setError('Failed to load A/B tests. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchTests();
    }, [tenantId, activeTab]);
    // Handle tab change
    const handleTabChange = (event, newValue) => {
        setActiveTab(newValue);
    };
    // Handle refresh
    const handleRefresh = () => {
        const fetchTests = async () => {
            try {
                setLoading(true);
                setError(null);
                let response;
                if (activeTab === 'all') {
                    response = await ABTestingService.getTests(tenantId);
                }
                else {
                    response = await ABTestingService.getTests(tenantId, activeTab);
                }
                setTests(response);
            }
            catch (err) {
                console.error('Error fetching A/B tests:', err);
                setError('Failed to load A/B tests. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchTests();
    };
    // Handle create test
    const handleCreateTest = async (testData) => {
        try {
            setLoading(true);
            setError(null);
            await ABTestingService.createTest(testData, tenantId);
            // Refresh tests
            handleRefresh();
            // Close dialog
            setCreateDialogOpen(false);
        }
        catch (err) {
            console.error('Error creating A/B test:', err);
            setError('Failed to create A/B test. Please try again.');
            setLoading(false);
        }
    };
    // Handle edit test
    const handleEditTest = async (testData) => {
        try {
            setLoading(true);
            setError(null);
            await ABTestingService.updateTest(selectedTest._id, testData, tenantId);
            // Refresh tests
            handleRefresh();
            // Close dialog
            setEditDialogOpen(false);
        }
        catch (err) {
            console.error('Error updating A/B test:', err);
            setError('Failed to update A/B test. Please try again.');
            setLoading(false);
        }
    };
    // Handle delete test
    const handleDeleteTest = async (deleteVariants) => {
        try {
            setLoading(true);
            setError(null);
            await ABTestingService.deleteTest(selectedTest._id, tenantId, deleteVariants);
            // Refresh tests
            handleRefresh();
            // Close dialog
            setDeleteDialogOpen(false);
        }
        catch (err) {
            console.error('Error deleting A/B test:', err);
            setError('Failed to delete A/B test. Please try again.');
            setLoading(false);
        }
    };
    // Handle status change
    const handleStatusChange = async (testId, status) => {
        try {
            setLoading(true);
            setError(null);
            await ABTestingService.updateTest(testId, { status }, tenantId);
            // Refresh tests
            handleRefresh();
        }
        catch (err) {
            console.error('Error updating A/B test status:', err);
            setError('Failed to update A/B test status. Please try again.');
            setLoading(false);
        }
    };
    // Render loading state
    if (loading && tests.length === 0) {
        return (_jsx(Box, { display: "flex", justifyContent: "center", alignItems: "center", height: "400px", children: _jsx(CircularProgress, {}) }));
    }
    // Render error state
    if (error) {
        return (_jsxs(Alert, { severity: "error", sx: { mb: 2 }, children: [error, _jsx(Button, { variant: "outlined", size: "small", onClick: handleRefresh, startIcon: _jsx(RefreshIcon, {}), sx: { ml: 2 }, children: "Retry" })] }));
    }
    return (_jsxs(Box, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 3, children: [_jsx(Typography, { variant: "h4", children: "A/B Testing" }), _jsxs(Box, { children: [_jsx(Button, { variant: "outlined", startIcon: _jsx(RefreshIcon, {}), onClick: handleRefresh, sx: { mr: 1 }, children: "Refresh" }), _jsx(Button, { variant: "contained", startIcon: _jsx(AddIcon, {}), onClick: () => setCreateDialogOpen(true), children: "Create Test" })] })] }), _jsx(Paper, { sx: { mb: 3 }, children: _jsxs(Tabs, { value: activeTab, onChange: handleTabChange, indicatorColor: "primary", textColor: "primary", variant: isMobile ? "scrollable" : "fullWidth", scrollButtons: isMobile ? "auto" : undefined, children: [_jsx(Tab, { label: "Active", value: "active" }), _jsx(Tab, { label: "Paused", value: "paused" }), _jsx(Tab, { label: "Completed", value: "completed" }), _jsx(Tab, { label: "All", value: "all" })] }) }), tests.length === 0 ? (_jsxs(Alert, { severity: "info", children: ["No ", activeTab !== 'all' ? activeTab : '', " A/B tests found. Create a new test to get started."] })) : (_jsx(Grid, { container: true, spacing: 3, children: tests.map((test) => (_jsx(Grid, { item: true, xs: 12, md: 6, lg: 4, children: _jsxs(Card, { children: [_jsxs(CardContent, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 1, children: [_jsx(Typography, { variant: "h6", noWrap: true, sx: { maxWidth: '70%' }, children: test.name }), _jsx(Chip, { label: test.status.toUpperCase(), color: test.status === 'active' ? 'success' :
                                                    test.status === 'paused' ? 'warning' :
                                                        'default', size: "small" })] }), test.description && (_jsx(Typography, { variant: "body2", color: "text.secondary", gutterBottom: true, children: test.description })), _jsx(Divider, { sx: { my: 1 } }), _jsxs(Typography, { variant: "subtitle2", gutterBottom: true, children: ["Base Proposal: ", test.baseTrafficPercentage, "% Traffic"] }), _jsxs(Typography, { variant: "body2", gutterBottom: true, children: ["Variants: ", test.variants.length] }), _jsx(Box, { mt: 1, children: test.variants.map((variant) => (_jsx(Chip, { label: `${variant.name}: ${variant.trafficPercentage}%`, size: "small", sx: { mr: 0.5, mb: 0.5 } }, variant.name))) }), _jsxs(Typography, { variant: "caption", display: "block", color: "text.secondary", mt: 1, children: ["Started ", formatDistance(new Date(test.startDate), new Date(), { addSuffix: true })] }), test.endDate && (_jsxs(Typography, { variant: "caption", display: "block", color: "text.secondary", children: ["Ends ", formatDistance(new Date(test.endDate), new Date(), { addSuffix: true })] }))] }), _jsxs(CardActions, { children: [_jsx(Tooltip, { title: "View Results", children: _jsx(IconButton, { onClick: () => {
                                                setSelectedTest(test);
                                                setResultsDialogOpen(true);
                                            }, children: _jsx(ChartIcon, {}) }) }), _jsx(Tooltip, { title: "Edit", children: _jsx(IconButton, { onClick: () => {
                                                setSelectedTest(test);
                                                setEditDialogOpen(true);
                                            }, children: _jsx(EditIcon, {}) }) }), test.status === 'active' ? (_jsx(Tooltip, { title: "Pause", children: _jsx(IconButton, { onClick: () => handleStatusChange(test._id, 'paused'), children: _jsx(PauseIcon, {}) }) })) : test.status === 'paused' ? (_jsx(Tooltip, { title: "Resume", children: _jsx(IconButton, { onClick: () => handleStatusChange(test._id, 'active'), children: _jsx(PlayIcon, {}) }) })) : null, test.status !== 'completed' && (_jsx(Tooltip, { title: "Complete", children: _jsx(IconButton, { onClick: () => handleStatusChange(test._id, 'completed'), children: _jsx(CheckCircleIcon, {}) }) })), _jsx(Tooltip, { title: "Delete", children: _jsx(IconButton, { onClick: () => {
                                                setSelectedTest(test);
                                                setDeleteDialogOpen(true);
                                            }, color: "error", children: _jsx(DeleteIcon, {}) }) })] })] }) }, test._id))) })), _jsx(CreateABTestDialog, { open: createDialogOpen, onClose: () => setCreateDialogOpen(false), onSubmit: handleCreateTest, proposals: proposals }), selectedTest && (_jsxs(Dialog, { open: editDialogOpen, onClose: () => setEditDialogOpen(false), maxWidth: "md", fullWidth: true, children: [_jsx(DialogTitle, { children: "Edit A/B Test" }), _jsxs(DialogContent, { children: [_jsx(TextField, { label: "Name", defaultValue: selectedTest.name, fullWidth: true, margin: "normal", id: "name" }), _jsx(TextField, { label: "Description", defaultValue: selectedTest.description, fullWidth: true, margin: "normal", multiline: true, rows: 2, id: "description" }), _jsxs(FormControl, { fullWidth: true, margin: "normal", children: [_jsx(InputLabel, { children: "Status" }), _jsxs(Select, { defaultValue: selectedTest.status, label: "Status", id: "status", children: [_jsx(MenuItem, { value: "active", children: "Active" }), _jsx(MenuItem, { value: "paused", children: "Paused" }), _jsx(MenuItem, { value: "completed", children: "Completed" })] })] }), _jsx(Typography, { variant: "subtitle1", gutterBottom: true, sx: { mt: 2 }, children: "Traffic Split" }), _jsx(TextField, { label: "Base Proposal Traffic %", defaultValue: selectedTest.baseTrafficPercentage, type: "number", fullWidth: true, margin: "normal", id: "baseTrafficPercentage", InputProps: { inputProps: { min: 0, max: 100 } } }), selectedTest.variants.map((variant, index) => (_jsx(TextField, { label: `${variant.name} Traffic %`, defaultValue: variant.trafficPercentage, type: "number", fullWidth: true, margin: "normal", id: `variant-${index}`, InputProps: { inputProps: { min: 0, max: 100 } } }, variant.name)))] }), _jsxs(DialogActions, { children: [_jsx(Button, { onClick: () => setEditDialogOpen(false), children: "Cancel" }), _jsx(Button, { variant: "contained", onClick: () => {
                                    const name = document.getElementById('name').value;
                                    const description = document.getElementById('description').value;
                                    const status = document.getElementById('status').value;
                                    const baseTrafficPercentage = Number(document.getElementById('baseTrafficPercentage').value);
                                    const variants = selectedTest.variants.map((variant, index) => ({
                                        name: variant.name,
                                        trafficPercentage: Number(document.getElementById(`variant-${index}`).value),
                                    }));
                                    handleEditTest({
                                        name,
                                        description,
                                        status,
                                        baseTrafficPercentage,
                                        variants,
                                    });
                                }, children: "Save" })] })] })), _jsxs(Dialog, { open: deleteDialogOpen, onClose: () => setDeleteDialogOpen(false), children: [_jsx(DialogTitle, { children: "Delete A/B Test" }), _jsxs(DialogContent, { children: [_jsx(Typography, { variant: "body1", gutterBottom: true, children: "Are you sure you want to delete this A/B test?" }), _jsxs(FormControl, { fullWidth: true, margin: "normal", children: [_jsx(InputLabel, { children: "Delete Variant Proposals" }), _jsxs(Select, { defaultValue: "false", label: "Delete Variant Proposals", id: "deleteVariants", children: [_jsx(MenuItem, { value: "false", children: "No, keep variant proposals" }), _jsx(MenuItem, { value: "true", children: "Yes, delete variant proposals" })] })] })] }), _jsxs(DialogActions, { children: [_jsx(Button, { onClick: () => setDeleteDialogOpen(false), children: "Cancel" }), _jsx(Button, { variant: "contained", color: "error", onClick: () => {
                                    const deleteVariants = document.getElementById('deleteVariants').value === 'true';
                                    handleDeleteTest(deleteVariants);
                                }, children: "Delete" })] })] }), selectedTest && (_jsxs(Dialog, { open: resultsDialogOpen, onClose: () => setResultsDialogOpen(false), maxWidth: "lg", fullWidth: true, children: [_jsxs(DialogTitle, { children: ["Test Results: ", selectedTest.name] }), _jsx(DialogContent, { children: _jsx(ABTestResults, { testId: selectedTest._id, tenantId: tenantId }) }), _jsx(DialogActions, { children: _jsx(Button, { onClick: () => setResultsDialogOpen(false), children: "Close" }) })] }))] }));
};
export default ABTestingDashboard;
