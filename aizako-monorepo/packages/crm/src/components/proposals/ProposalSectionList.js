import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, Button, Divider, useTheme, useMediaQuery, Skeleton, Paper, List, ListItem, ListItemText, ListItemSecondaryAction, IconButton, Tooltip, Collapse, Pagination } from '@mui/material';
import { ExpandMore as ExpandMoreIcon, ExpandLess as ExpandLessIcon, Visibility as VisibilityIcon, VisibilityOff as VisibilityOffIcon, Edit as EditIcon, Delete as DeleteIcon, Add as AddIcon } from '@mui/icons-material';
/**
 * ProposalSectionList Component
 *
 * This component displays a paginated list of proposal sections with lazy loading.
 */
const ProposalSectionList = ({ sections, loading = false, onEditSection, onDeleteSection, onToggleSectionVisibility, onAddSection, onSectionClick, selectedSectionId, pageSize = 5 }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    // State for pagination
    const [page, setPage] = useState(1);
    const [expandedSections, setExpandedSections] = useState({});
    // Calculate total pages
    const totalPages = Math.ceil(sections.length / pageSize);
    // Get current page sections
    const getCurrentPageSections = useCallback(() => {
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        return sections.slice(startIndex, endIndex);
    }, [sections, page, pageSize]);
    const [visibleSections, setVisibleSections] = useState(getCurrentPageSections());
    // Update visible sections when page or sections change
    useEffect(() => {
        setVisibleSections(getCurrentPageSections());
    }, [page, sections, getCurrentPageSections]);
    // Handle page change
    const handlePageChange = (event, value) => {
        setPage(value);
        // Scroll to top of list
        const listElement = document.getElementById('proposal-section-list');
        if (listElement) {
            listElement.scrollTop = 0;
        }
    };
    // Toggle section expansion
    const toggleSectionExpansion = (sectionId) => {
        setExpandedSections(prev => ({
            ...prev,
            [sectionId]: !prev[sectionId]
        }));
    };
    // Render loading skeleton
    if (loading) {
        return (_jsxs(Box, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2, children: [_jsx(Typography, { variant: "h6", children: "Sections" }), _jsx(Skeleton, { variant: "rectangular", width: 100, height: 36 })] }), [...Array(3)].map((_, index) => (_jsx(Box, { mb: 2, children: _jsx(Skeleton, { variant: "rectangular", height: 60 }) }, index)))] }));
    }
    return (_jsxs(Box, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2, children: [_jsxs(Typography, { variant: "h6", children: ["Sections (", sections.length, ")"] }), _jsx(Button, { startIcon: _jsx(AddIcon, {}), variant: "contained", color: "primary", onClick: onAddSection, size: isMobile ? "small" : "medium", children: "Add Section" })] }), _jsxs(Paper, { variant: "outlined", id: "proposal-section-list", children: [_jsx(List, { disablePadding: true, children: visibleSections.length === 0 ? (_jsx(ListItem, { children: _jsx(ListItemText, { primary: "No sections", secondary: "Click 'Add Section' to create a new section" }) })) : (visibleSections.map((section) => (_jsxs(React.Fragment, { children: [_jsxs(ListItem, { button: true, selected: selectedSectionId === section.id, onClick: () => onSectionClick(section.id), sx: {
                                        borderLeft: selectedSectionId === section.id
                                            ? `4px solid ${theme.palette.primary.main}`
                                            : '4px solid transparent',
                                        opacity: section.isVisible ? 1 : 0.6,
                                        bgcolor: selectedSectionId === section.id
                                            ? alpha(theme.palette.primary.main, 0.1)
                                            : 'transparent',
                                    }, children: [_jsx(ListItemText, { primary: _jsx(Typography, { variant: "subtitle1", fontWeight: selectedSectionId === section.id ? 'bold' : 'normal', sx: {
                                                    overflow: 'hidden',
                                                    textOverflow: 'ellipsis',
                                                    whiteSpace: 'nowrap',
                                                    maxWidth: { xs: '150px', sm: '250px', md: '300px' }
                                                }, children: section.title }), secondary: _jsxs(Typography, { variant: "body2", color: "text.secondary", sx: {
                                                    display: '-webkit-box',
                                                    WebkitLineClamp: 1,
                                                    WebkitBoxOrient: 'vertical',
                                                    overflow: 'hidden',
                                                    textOverflow: 'ellipsis',
                                                }, children: [section.type.charAt(0).toUpperCase() + section.type.slice(1), " section", section.aiGenerated && ' • AI Generated'] }) }), _jsx(ListItemSecondaryAction, { children: _jsxs(Box, { display: "flex", children: [_jsx(Tooltip, { title: section.isVisible ? "Hide section" : "Show section", children: _jsx(IconButton, { edge: "end", onClick: (e) => {
                                                                e.stopPropagation();
                                                                onToggleSectionVisibility(section.id);
                                                            }, size: "small", children: section.isVisible ? _jsx(VisibilityIcon, {}) : _jsx(VisibilityOffIcon, {}) }) }), _jsx(Tooltip, { title: "Edit section", children: _jsx(IconButton, { edge: "end", onClick: (e) => {
                                                                e.stopPropagation();
                                                                onEditSection(section.id);
                                                            }, size: "small", children: _jsx(EditIcon, {}) }) }), _jsx(Tooltip, { title: "Delete section", children: _jsx(IconButton, { edge: "end", onClick: (e) => {
                                                                e.stopPropagation();
                                                                onDeleteSection(section.id);
                                                            }, size: "small", children: _jsx(DeleteIcon, {}) }) }), _jsx(Tooltip, { title: expandedSections[section.id] ? "Collapse" : "Expand", children: _jsx(IconButton, { edge: "end", onClick: (e) => {
                                                                e.stopPropagation();
                                                                toggleSectionExpansion(section.id);
                                                            }, size: "small", children: expandedSections[section.id] ? _jsx(ExpandLessIcon, {}) : _jsx(ExpandMoreIcon, {}) }) })] }) })] }), _jsx(Collapse, { in: expandedSections[section.id], timeout: "auto", unmountOnExit: true, children: _jsx(Box, { p: 2, bgcolor: alpha(theme.palette.background.default, 0.5), children: _jsx(Typography, { variant: "body2", sx: {
                                                display: '-webkit-box',
                                                WebkitLineClamp: 3,
                                                WebkitBoxOrient: 'vertical',
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                            }, children: section.content.replace(/<[^>]*>?/gm, '') }) }) }), _jsx(Divider, {})] }, section.id)))) }), totalPages > 1 && (_jsx(Box, { display: "flex", justifyContent: "center", p: 2, children: _jsx(Pagination, { count: totalPages, page: page, onChange: handlePageChange, color: "primary", size: isMobile ? "small" : "medium" }) }))] })] }));
};
// Helper function to create alpha color
function alpha(color, opacity) {
    return color + Math.round(opacity * 255).toString(16).padStart(2, '0');
}
export default ProposalSectionList;
