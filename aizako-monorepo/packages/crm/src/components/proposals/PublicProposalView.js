import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Box, Container, Typography, Paper, Divider, Button, CircularProgress, Alert, Chip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Dialog, DialogTitle, DialogContent, DialogActions, TextField, useTheme, alpha, AppBar, Toolbar, IconButton, Menu, MenuItem, Snackbar } from '@mui/material';
import { Download as DownloadIcon, ThumbUp as AcceptIcon, ThumbDown as RejectIcon, Share as ShareIcon, Print as PrintIcon } from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { proposalsApi } from '../../api/client/proposals';
import { formatCurrency, formatDate } from '../../utils/formatters';
import { sanitizeHtml } from '../../utils/sanitize';
/**
 * Public Proposal View Component
 *
 * This component provides a public view of a proposal.
 * It allows recipients to view, download, accept, or reject the proposal.
 */
const PublicProposalView = () => {
    const theme = useTheme();
    const { token } = useParams();
    const navigate = useNavigate();
    // State for the proposal
    const [proposal, setProposal] = useState(null);
    // UI state
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [downloadMenuAnchor, setDownloadMenuAnchor] = useState(null);
    const [shareMenuAnchor, setShareMenuAnchor] = useState(null);
    const [acceptDialogOpen, setAcceptDialogOpen] = useState(false);
    const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
    const [rejectionReason, setRejectionReason] = useState('');
    const [notification, setNotification] = useState(null);
    // Load proposal
    useEffect(() => {
        const loadProposal = async () => {
            if (!token)
                return;
            setLoading(true);
            try {
                const loadedProposal = await proposalsApi.getProposalByToken(token);
                setProposal(loadedProposal);
                // Record view
                await proposalsApi.recordView(token, {
                    referrer: document.referrer,
                    userAgent: navigator.userAgent,
                    screenSize: `${window.innerWidth}x${window.innerHeight}`,
                    timestamp: new Date().toISOString()
                });
            }
            catch (err) {
                console.error('Error loading proposal:', err);
                setError('The proposal could not be found or has expired.');
            }
            finally {
                setLoading(false);
            }
        };
        loadProposal();
    }, [token]);
    // Handle download
    const handleDownload = async (format) => {
        setDownloadMenuAnchor(null);
        if (!token || !proposal)
            return;
        try {
            setLoading(true);
            const options = {
                format,
                paperSize: 'a4',
                colorScheme: 'professional',
                includeHeader: true,
                includeFooter: true,
                includeBranding: true,
                includePageNumbers: true
            };
            const blob = await proposalsApi.downloadPublicProposal(token, options);
            // Create a download link
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${proposal.title || 'proposal'}.${format}`;
            document.body.appendChild(a);
            a.click();
            // Clean up
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            setNotification({
                message: `Proposal downloaded as ${format.toUpperCase()}`,
                severity: 'success'
            });
        }
        catch (err) {
            console.error(`Failed to download proposal as ${format.toUpperCase()}:`, err);
            setNotification({
                message: `Failed to download proposal as ${format.toUpperCase()}`,
                severity: 'error'
            });
        }
        finally {
            setLoading(false);
        }
    };
    // Handle share
    const handleShare = (method) => {
        setShareMenuAnchor(null);
        if (!token)
            return;
        const url = window.location.href;
        if (method === 'copy') {
            navigator.clipboard.writeText(url);
            setNotification({
                message: 'Link copied to clipboard',
                severity: 'success'
            });
        }
        else if (method === 'email') {
            const subject = encodeURIComponent(`Proposal: ${proposal?.title || 'Shared Proposal'}`);
            const body = encodeURIComponent(`I'd like to share this proposal with you: ${url}`);
            window.location.href = `mailto:?subject=${subject}&body=${body}`;
        }
    };
    // Handle print
    const handlePrint = () => {
        window.print();
    };
    // Handle accept
    const handleAccept = async () => {
        if (!token || !proposal)
            return;
        try {
            setLoading(true);
            await proposalsApi.acceptProposal(proposal._id?.toString() || '');
            setAcceptDialogOpen(false);
            setNotification({
                message: 'Proposal accepted successfully',
                severity: 'success'
            });
            // Reload the proposal
            const updatedProposal = await proposalsApi.getProposalByToken(token);
            setProposal(updatedProposal);
        }
        catch (err) {
            console.error('Error accepting proposal:', err);
            setNotification({
                message: 'Failed to accept proposal',
                severity: 'error'
            });
        }
        finally {
            setLoading(false);
        }
    };
    // Handle reject
    const handleReject = async () => {
        if (!token || !proposal)
            return;
        try {
            setLoading(true);
            await proposalsApi.rejectProposal(proposal._id?.toString() || '', rejectionReason);
            setRejectDialogOpen(false);
            setRejectionReason('');
            setNotification({
                message: 'Proposal rejected',
                severity: 'success'
            });
            // Reload the proposal
            const updatedProposal = await proposalsApi.getProposalByToken(token);
            setProposal(updatedProposal);
        }
        catch (err) {
            console.error('Error rejecting proposal:', err);
            setNotification({
                message: 'Failed to reject proposal',
                severity: 'error'
            });
        }
        finally {
            setLoading(false);
        }
    };
    // Render HTML content safely
    const renderHtml = (html) => {
        return _jsx("div", { dangerouslySetInnerHTML: { __html: sanitizeHtml(html) } });
    };
    // Render loading state
    if (loading && !proposal) {
        return (_jsx(Box, { display: "flex", justifyContent: "center", alignItems: "center", minHeight: "100vh", children: _jsx(CircularProgress, {}) }));
    }
    // Render error state
    if (error || !proposal) {
        return (_jsx(Container, { maxWidth: "md", sx: { py: 8 }, children: _jsxs(Paper, { sx: { p: 4, textAlign: 'center' }, children: [_jsx(Typography, { variant: "h5", color: "error", gutterBottom: true, children: "Proposal Not Found" }), _jsx(Typography, { variant: "body1", paragraph: true, children: error || 'The proposal you are looking for could not be found or has expired.' }), _jsx(Button, { variant: "contained", onClick: () => navigate('/'), children: "Return Home" })] }) }));
    }
    return (_jsxs(Box, { children: [_jsx(AppBar, { position: "static", color: "default", elevation: 1, children: _jsxs(Toolbar, { children: [_jsx(Typography, { variant: "h6", component: "div", sx: { flexGrow: 1 }, children: proposal.title }), _jsxs(Box, { children: [proposal.downloadEnabled && (_jsx(IconButton, { color: "inherit", onClick: (e) => setDownloadMenuAnchor(e.currentTarget), children: _jsx(DownloadIcon, {}) })), _jsx(IconButton, { color: "inherit", onClick: (e) => setShareMenuAnchor(e.currentTarget), children: _jsx(ShareIcon, {}) }), _jsx(IconButton, { color: "inherit", onClick: handlePrint, children: _jsx(PrintIcon, {}) }), proposal.status !== 'accepted' && proposal.status !== 'rejected' && (_jsxs(_Fragment, { children: [_jsx(IconButton, { color: "primary", onClick: () => setAcceptDialogOpen(true), children: _jsx(AcceptIcon, {}) }), _jsx(IconButton, { color: "error", onClick: () => setRejectDialogOpen(true), children: _jsx(RejectIcon, {}) })] }))] })] }) }), (proposal.status === 'accepted' || proposal.status === 'rejected') && (_jsx(Box, { sx: {
                    bgcolor: proposal.status === 'accepted' ? 'success.main' : 'error.main',
                    color: 'white',
                    py: 1,
                    textAlign: 'center',
                }, children: _jsxs(Typography, { variant: "subtitle1", children: [proposal.status === 'accepted' ? 'This proposal has been accepted' : 'This proposal has been rejected', proposal.status === 'accepted' && proposal.acceptedAt && (_jsxs(_Fragment, { children: [" on ", formatDate(proposal.acceptedAt)] })), proposal.status === 'rejected' && proposal.rejectedAt && (_jsxs(_Fragment, { children: [" on ", formatDate(proposal.rejectedAt)] }))] }) })), _jsxs(Container, { maxWidth: "md", sx: { py: 4 }, children: [_jsxs(Paper, { sx: { p: 4, mb: 4 }, children: [_jsxs(Box, { sx: {
                                    borderBottom: `4px solid ${theme.palette.primary.main}`,
                                    pb: 2,
                                    mb: 4,
                                }, children: [_jsx(Typography, { variant: "h4", component: "h1", gutterBottom: true, sx: { color: theme.palette.primary.main, fontWeight: 'bold' }, children: proposal.title }), proposal.description && (_jsx(Typography, { variant: "subtitle1", color: "text.secondary", gutterBottom: true, children: proposal.description })), _jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mt: 2, children: [_jsx(Chip, { label: proposal.status?.toUpperCase() || 'DRAFT', color: proposal.status === 'accepted' ? 'success' :
                                                    proposal.status === 'rejected' ? 'error' :
                                                        proposal.status === 'sent' ? 'primary' :
                                                            proposal.status === 'viewed' ? 'info' :
                                                                'default', size: "small" }), _jsx(Typography, { variant: "body2", color: "text.secondary", children: formatDate(proposal.createdAt) })] })] }), proposal.sections
                                ?.filter(section => section.isVisible)
                                .sort((a, b) => a.order - b.order)
                                .map((section, index) => (_jsxs(Box, { mb: 4, children: [_jsx(Typography, { variant: "h5", component: "h2", gutterBottom: true, sx: {
                                            color: theme.palette.primary.main,
                                            borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.3)}`,
                                            pb: 1,
                                        }, children: section.title }), _jsx(Box, { sx: { mt: 2 }, children: renderHtml(section.content) })] }, section.id))), proposal.pricing && proposal.pricing.items && proposal.pricing.items.length > 0 && (_jsxs(Box, { mb: 4, children: [_jsx(Typography, { variant: "h5", component: "h2", gutterBottom: true, sx: {
                                            color: theme.palette.primary.main,
                                            borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.3)}`,
                                            pb: 1,
                                        }, children: "Pricing" }), _jsx(TableContainer, { component: Paper, variant: "outlined", sx: { mt: 2 }, children: _jsxs(Table, { children: [_jsx(TableHead, { sx: { bgcolor: alpha(theme.palette.primary.main, 0.1) }, children: _jsxs(TableRow, { children: [_jsx(TableCell, { sx: { fontWeight: 'bold' }, children: "Item" }), _jsx(TableCell, { align: "right", sx: { fontWeight: 'bold' }, children: "Quantity" }), _jsx(TableCell, { align: "right", sx: { fontWeight: 'bold' }, children: "Unit Price" }), _jsx(TableCell, { align: "right", sx: { fontWeight: 'bold' }, children: "Total" })] }) }), _jsxs(TableBody, { children: [proposal.pricing.items.map((item) => (_jsxs(TableRow, { children: [_jsxs(TableCell, { children: [_jsx(Typography, { variant: "body2", fontWeight: "medium", children: item.name }), item.description && (_jsx(Typography, { variant: "caption", color: "text.secondary", children: item.description }))] }), _jsx(TableCell, { align: "right", children: item.quantity }), _jsx(TableCell, { align: "right", children: formatCurrency(item.unitPrice, proposal.pricing?.currency) }), _jsx(TableCell, { align: "right", children: formatCurrency(item.total, proposal.pricing?.currency) })] }, item.id))), _jsxs(TableRow, { children: [_jsx(TableCell, { colSpan: 3, align: "right", sx: { fontWeight: 'medium' }, children: "Subtotal" }), _jsx(TableCell, { align: "right", children: formatCurrency(proposal.pricing.subtotal, proposal.pricing.currency) })] }), proposal.pricing.discount && proposal.pricing.discount > 0 && (_jsxs(TableRow, { children: [_jsx(TableCell, { colSpan: 3, align: "right", sx: { fontWeight: 'medium' }, children: "Discount" }), _jsx(TableCell, { align: "right", children: formatCurrency(proposal.pricing.discount, proposal.pricing.currency) })] })), proposal.pricing.tax && proposal.pricing.tax > 0 && (_jsxs(TableRow, { children: [_jsx(TableCell, { colSpan: 3, align: "right", sx: { fontWeight: 'medium' }, children: "Tax" }), _jsx(TableCell, { align: "right", children: formatCurrency(proposal.pricing.tax, proposal.pricing.currency) })] })), _jsxs(TableRow, { children: [_jsx(TableCell, { colSpan: 3, align: "right", sx: { fontWeight: 'bold', color: theme.palette.primary.main }, children: "Total" }), _jsx(TableCell, { align: "right", sx: { fontWeight: 'bold', color: theme.palette.primary.main }, children: formatCurrency(proposal.pricing.total, proposal.pricing.currency) })] })] })] }) })] })), proposal.terms && (_jsxs(Box, { mb: 4, children: [_jsx(Typography, { variant: "h5", component: "h2", gutterBottom: true, sx: {
                                            color: theme.palette.primary.main,
                                            borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.3)}`,
                                            pb: 1,
                                        }, children: "Terms & Conditions" }), _jsx(Typography, { variant: "body2", sx: { whiteSpace: 'pre-line', mt: 2 }, children: proposal.terms })] })), _jsx(Divider, { sx: { mt: 4, mb: 2 } }), _jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", children: [_jsxs(Typography, { variant: "caption", color: "text.secondary", children: ["Generated on ", formatDate(proposal.createdAt)] }), _jsx(Typography, { variant: "caption", color: "text.secondary", children: "Confidential" })] })] }), proposal.status !== 'accepted' && proposal.status !== 'rejected' && (_jsxs(Box, { display: "flex", justifyContent: "center", gap: 2, mb: 4, children: [_jsx(Button, { variant: "contained", color: "primary", size: "large", startIcon: _jsx(AcceptIcon, {}), onClick: () => setAcceptDialogOpen(true), children: "Accept Proposal" }), _jsx(Button, { variant: "outlined", color: "error", size: "large", startIcon: _jsx(RejectIcon, {}), onClick: () => setRejectDialogOpen(true), children: "Decline Proposal" })] }))] }), _jsxs(Menu, { anchorEl: downloadMenuAnchor, open: Boolean(downloadMenuAnchor), onClose: () => setDownloadMenuAnchor(null), children: [proposal.downloadFormats?.includes('pdf') && (_jsx(MenuItem, { onClick: () => handleDownload('pdf'), children: "Download as PDF" })), proposal.downloadFormats?.includes('docx') && (_jsx(MenuItem, { onClick: () => handleDownload('docx'), children: "Download as DOCX" })), proposal.downloadFormats?.includes('md') && (_jsx(MenuItem, { onClick: () => handleDownload('md'), children: "Download as Markdown" }))] }), _jsxs(Menu, { anchorEl: shareMenuAnchor, open: Boolean(shareMenuAnchor), onClose: () => setShareMenuAnchor(null), children: [_jsx(MenuItem, { onClick: () => handleShare('copy'), children: "Copy Link" }), _jsx(MenuItem, { onClick: () => handleShare('email'), children: "Share via Email" })] }), _jsxs(Dialog, { open: acceptDialogOpen, onClose: () => setAcceptDialogOpen(false), maxWidth: "xs", fullWidth: true, children: [_jsx(DialogTitle, { children: "Accept Proposal" }), _jsxs(DialogContent, { children: [_jsx(Typography, { variant: "body1", paragraph: true, children: "Are you sure you want to accept this proposal?" }), _jsx(Typography, { variant: "body2", color: "text.secondary", children: "By accepting, you agree to the terms and conditions outlined in the proposal." })] }), _jsxs(DialogActions, { children: [_jsx(Button, { onClick: () => setAcceptDialogOpen(false), children: "Cancel" }), _jsx(Button, { variant: "contained", color: "primary", onClick: handleAccept, disabled: loading, children: loading ? 'Processing...' : 'Accept' })] })] }), _jsxs(Dialog, { open: rejectDialogOpen, onClose: () => setRejectDialogOpen(false), maxWidth: "xs", fullWidth: true, children: [_jsx(DialogTitle, { children: "Decline Proposal" }), _jsxs(DialogContent, { children: [_jsx(Typography, { variant: "body1", paragraph: true, children: "Are you sure you want to decline this proposal?" }), _jsx(TextField, { label: "Reason (Optional)", value: rejectionReason, onChange: (e) => setRejectionReason(e.target.value), fullWidth: true, multiline: true, rows: 3, margin: "normal", variant: "outlined", placeholder: "Please provide a reason for declining..." })] }), _jsxs(DialogActions, { children: [_jsx(Button, { onClick: () => setRejectDialogOpen(false), children: "Cancel" }), _jsx(Button, { variant: "contained", color: "error", onClick: handleReject, disabled: loading, children: loading ? 'Processing...' : 'Decline' })] })] }), _jsx(Snackbar, { open: !!notification, autoHideDuration: 6000, onClose: () => setNotification(null), message: notification?.message, children: _jsx(Alert, { onClose: () => setNotification(null), severity: notification?.severity, sx: { width: '100%' }, children: notification?.message }) }), loading && (_jsx(Box, { sx: {
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    zIndex: 9999,
                }, children: _jsx(CircularProgress, { color: "primary" }) }))] }));
};
export default PublicProposalView;
