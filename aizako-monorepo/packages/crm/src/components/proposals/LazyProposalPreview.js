import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect, useRef, useCallback } from 'react';
import { Box, Typography, Paper, Divider, CircularProgress, useTheme, alpha, Button } from '@mui/material';
import { formatCurrency, formatDate } from '../../utils/formatters';
import { sanitizeHtml } from '../../utils/sanitize';
/**
 * LazyProposalPreview Component
 *
 * This component renders a proposal preview with lazy loading for sections.
 * It only renders sections that are visible in the viewport to improve performance.
 */
const LazyProposalPreview = ({ proposal, colorScheme = 'professional', viewMode = 'desktop', sectionLoadCount = 3 }) => {
    const theme = useTheme();
    const containerRef = useRef(null);
    // State for visible sections
    const [visibleSections, setVisibleSections] = useState([]);
    const [loadedSectionCount, setLoadedSectionCount] = useState(sectionLoadCount);
    const [isLoading, setIsLoading] = useState(false);
    // Get visible sections
    const getVisibleSections = useCallback(() => {
        if (!proposal.sections)
            return [];
        // Filter visible sections and sort by order
        const filteredSections = proposal.sections
            .filter(section => section.isVisible)
            .sort((a, b) => a.order - b.order);
        // Return only the loaded sections
        return filteredSections.slice(0, loadedSectionCount);
    }, [proposal.sections, loadedSectionCount]);
    // Update visible sections when proposal or loadedSectionCount changes
    useEffect(() => {
        setVisibleSections(getVisibleSections());
    }, [proposal, loadedSectionCount, getVisibleSections]);
    // Handle scroll to load more sections
    const handleScroll = useCallback(() => {
        if (!containerRef.current || isLoading)
            return;
        const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
        const scrollPosition = scrollTop + clientHeight;
        const scrollThreshold = scrollHeight * 0.8; // 80% of scroll height
        if (scrollPosition >= scrollThreshold) {
            // Check if we have more sections to load
            const totalVisibleSections = proposal.sections
                .filter(section => section.isVisible)
                .length;
            if (loadedSectionCount < totalVisibleSections) {
                setIsLoading(true);
                // Simulate loading delay
                setTimeout(() => {
                    setLoadedSectionCount(prev => prev + sectionLoadCount);
                    setIsLoading(false);
                }, 500);
            }
        }
    }, [isLoading, loadedSectionCount, proposal.sections, sectionLoadCount]);
    // Add scroll event listener
    useEffect(() => {
        const container = containerRef.current;
        if (container) {
            container.addEventListener('scroll', handleScroll);
            return () => {
                container.removeEventListener('scroll', handleScroll);
            };
        }
    }, [handleScroll]);
    // Get color scheme styles
    const getColorScheme = () => {
        switch (colorScheme) {
            case 'professional':
                return {
                    primary: theme.palette.primary.main,
                    secondary: theme.palette.grey[800],
                    background: theme.palette.background.paper,
                    text: theme.palette.text.primary,
                    accent: theme.palette.primary.light,
                };
            case 'creative':
                return {
                    primary: theme.palette.secondary.main,
                    secondary: theme.palette.secondary.dark,
                    background: theme.palette.background.paper,
                    text: theme.palette.text.primary,
                    accent: theme.palette.secondary.light,
                };
            case 'modern':
                return {
                    primary: theme.palette.info.main,
                    secondary: theme.palette.grey[800],
                    background: theme.palette.background.paper,
                    text: theme.palette.text.primary,
                    accent: theme.palette.info.light,
                };
            case 'classic':
                return {
                    primary: theme.palette.grey[800],
                    secondary: theme.palette.grey[600],
                    background: theme.palette.background.paper,
                    text: theme.palette.text.primary,
                    accent: theme.palette.grey[400],
                };
            default:
                return {
                    primary: theme.palette.primary.main,
                    secondary: theme.palette.grey[800],
                    background: theme.palette.background.paper,
                    text: theme.palette.text.primary,
                    accent: theme.palette.primary.light,
                };
        }
    };
    const colors = getColorScheme();
    // Get container width based on view mode
    const getContainerWidth = () => {
        switch (viewMode) {
            case 'desktop':
                return '100%';
            case 'mobile':
                return '375px';
            case 'print':
                return '8.5in';
            default:
                return '100%';
        }
    };
    // Render HTML content safely
    const renderHtml = (html) => {
        return _jsx("div", { dangerouslySetInnerHTML: { __html: sanitizeHtml(html) } });
    };
    // Calculate total visible sections
    const totalVisibleSections = proposal.sections
        .filter(section => section.isVisible)
        .length;
    // Calculate remaining sections
    const remainingSections = totalVisibleSections - visibleSections.length;
    return (_jsxs(Box, { ref: containerRef, sx: {
            width: getContainerWidth(),
            maxHeight: '80vh',
            overflowY: 'auto',
            mx: 'auto',
            boxShadow: viewMode === 'print' ? 'none' : 3,
            bgcolor: colors.background,
            p: { xs: 2, sm: 4 },
            '@media print': {
                width: '100%',
                maxHeight: 'none',
                overflow: 'visible',
                boxShadow: 'none',
                p: 0,
            },
        }, children: [_jsxs(Box, { sx: {
                    borderBottom: `4px solid ${colors.primary}`,
                    pb: 2,
                    mb: 4,
                }, children: [_jsx(Typography, { variant: "h4", component: "h1", gutterBottom: true, sx: { color: colors.primary, fontWeight: 'bold' }, children: proposal.title }), proposal.description && (_jsx(Typography, { variant: "subtitle1", color: "text.secondary", gutterBottom: true, children: proposal.description })), _jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mt: 2, children: [_jsx(Typography, { variant: "caption", color: "text.secondary", children: formatDate(proposal.createdAt) }), _jsx(Typography, { variant: "caption", color: "text.secondary", children: proposal.status?.toUpperCase() || 'DRAFT' })] })] }), visibleSections.map((section) => (_jsxs(Box, { mb: 4, children: [_jsx(Typography, { variant: "h5", component: "h2", gutterBottom: true, sx: {
                            color: colors.primary,
                            borderBottom: `2px solid ${alpha(colors.primary, 0.3)}`,
                            pb: 1,
                        }, children: section.title }), _jsx(Box, { sx: { mt: 2 }, children: renderHtml(section.content) })] }, section.id))), isLoading && (_jsx(Box, { display: "flex", justifyContent: "center", my: 4, children: _jsx(CircularProgress, { color: "primary" }) })), remainingSections > 0 && !isLoading && (_jsx(Box, { display: "flex", justifyContent: "center", my: 4, children: _jsxs(Button, { variant: "outlined", onClick: () => {
                        setIsLoading(true);
                        setTimeout(() => {
                            setLoadedSectionCount(prev => prev + sectionLoadCount);
                            setIsLoading(false);
                        }, 500);
                    }, children: ["Load ", Math.min(remainingSections, sectionLoadCount), " more sections (", remainingSections, " remaining)"] }) })), proposal.pricing && proposal.pricing.items && proposal.pricing.items.length > 0 && (_jsxs(Box, { mb: 4, children: [_jsx(Typography, { variant: "h5", component: "h2", gutterBottom: true, sx: {
                            color: colors.primary,
                            borderBottom: `2px solid ${alpha(colors.primary, 0.3)}`,
                            pb: 1,
                        }, children: "Pricing" }), _jsx(Paper, { variant: "outlined", sx: { mt: 2 }, children: _jsxs(Box, { p: 2, children: [proposal.pricing.items.map((item) => (_jsxs(Box, { display: "flex", justifyContent: "space-between", py: 1, borderBottom: `1px solid ${theme.palette.divider}`, children: [_jsxs(Box, { children: [_jsx(Typography, { variant: "body1", fontWeight: "medium", children: item.name }), item.description && (_jsx(Typography, { variant: "body2", color: "text.secondary", children: item.description }))] }), _jsxs(Box, { textAlign: "right", children: [_jsxs(Typography, { variant: "body2", color: "text.secondary", children: [item.quantity, " x ", formatCurrency(item.unitPrice, proposal.pricing?.currency || 'USD')] }), _jsx(Typography, { variant: "body1", fontWeight: "medium", children: formatCurrency(item.total, proposal.pricing?.currency || 'USD') })] })] }, item.id))), _jsxs(Box, { display: "flex", justifyContent: "space-between", py: 2, mt: 2, borderTop: `2px solid ${theme.palette.divider}`, children: [_jsx(Typography, { variant: "h6", children: "Total" }), _jsx(Typography, { variant: "h6", color: colors.primary, children: formatCurrency(proposal.pricing.total, proposal.pricing.currency) })] })] }) })] })), proposal.terms && (_jsxs(Box, { mb: 4, children: [_jsx(Typography, { variant: "h5", component: "h2", gutterBottom: true, sx: {
                            color: colors.primary,
                            borderBottom: `2px solid ${alpha(colors.primary, 0.3)}`,
                            pb: 1,
                        }, children: "Terms & Conditions" }), _jsx(Typography, { variant: "body2", sx: { whiteSpace: 'pre-line', mt: 2 }, children: proposal.terms })] })), _jsx(Divider, { sx: { mt: 4, mb: 2 } }), _jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", children: [_jsxs(Typography, { variant: "caption", color: "text.secondary", children: ["Generated on ", formatDate(proposal.createdAt)] }), _jsx(Typography, { variant: "caption", color: "text.secondary", children: "Confidential" })] })] }));
};
export default LazyProposalPreview;
