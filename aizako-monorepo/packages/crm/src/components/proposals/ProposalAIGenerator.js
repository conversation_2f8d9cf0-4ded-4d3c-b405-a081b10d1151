import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState } from 'react';
import { <PERSON>ton, Card, Textarea, Select, Checkbox, Spinner } from '@aizako/ui-kit';
/**
 * AI-powered proposal generator component
 */
export const ProposalAIGenerator = ({ opportunity, company, contacts, onGenerate, }) => {
    const [prompt, setPrompt] = useState('');
    const [model, setModel] = useState('claude-3-opus');
    const [isGenerating, setIsGenerating] = useState(false);
    const [includeExecutiveSummary, setIncludeExecutiveSummary] = useState(true);
    const [includeSolution, setIncludeSolution] = useState(true);
    const [includeTimeline, setIncludeTimeline] = useState(true);
    const [includePricing, setIncludePricing] = useState(true);
    const [includeTeam, setIncludeTeam] = useState(false);
    const [includeTestimonials, setIncludeTestimonials] = useState(false);
    const [includeTerms, setIncludeTerms] = useState(true);
    // Generate a default prompt based on opportunity, company, and contacts
    const generateDefaultPrompt = () => {
        let defaultPrompt = 'Generate a professional business proposal';
        if (opportunity) {
            defaultPrompt += ` for the opportunity "${opportunity.name}"`;
            if (opportunity.description) {
                defaultPrompt += ` which involves ${opportunity.description}`;
            }
            if (opportunity.amount) {
                defaultPrompt += ` with an approximate value of $${opportunity.amount.toLocaleString()}`;
            }
        }
        if (company) {
            defaultPrompt += ` for ${company.name}`;
            if (company.industry) {
                defaultPrompt += ` in the ${company.industry} industry`;
            }
        }
        defaultPrompt += '. Include the following sections:';
        if (includeExecutiveSummary) {
            defaultPrompt += '\n- Executive Summary';
        }
        if (includeSolution) {
            defaultPrompt += '\n- Proposed Solution';
        }
        if (includeTimeline) {
            defaultPrompt += '\n- Project Timeline';
        }
        if (includePricing) {
            defaultPrompt += '\n- Pricing and Investment';
        }
        if (includeTeam) {
            defaultPrompt += '\n- Our Team';
        }
        if (includeTestimonials) {
            defaultPrompt += '\n- Client Testimonials';
        }
        if (includeTerms) {
            defaultPrompt += '\n- Terms and Conditions';
        }
        defaultPrompt += '\n\nMake the proposal persuasive, professional, and tailored to the client\'s needs.';
        return defaultPrompt;
    };
    // Handle prompt generation
    const handleGeneratePrompt = () => {
        setPrompt(generateDefaultPrompt());
    };
    // Handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!prompt) {
            return;
        }
        setIsGenerating(true);
        try {
            // This would be replaced with an actual API call to generate content
            // For now, we'll just simulate a delay and return mock data
            await new Promise(resolve => setTimeout(resolve, 3000));
            // Mock generated content
            const generatedContent = {
                title: opportunity ? `Proposal: ${opportunity.name}` : 'Business Proposal',
                description: 'A comprehensive solution tailored to your specific needs and objectives.',
                sections: [
                    {
                        title: 'Executive Summary',
                        content: 'This proposal outlines our comprehensive solution designed to address your specific challenges and help you achieve your business objectives. Based on our understanding of your requirements, we have developed a tailored approach that leverages our expertise and proven methodologies to deliver exceptional results.',
                        type: 'text',
                        order: 0,
                    },
                    {
                        title: 'Proposed Solution',
                        content: 'Our solution combines innovative technology with industry best practices to provide a scalable, efficient, and effective approach to addressing your needs. We will implement a phased approach that minimizes disruption while maximizing value at each stage of the project.',
                        type: 'text',
                        order: 1,
                    },
                    {
                        title: 'Project Timeline',
                        content: 'The project will be executed in three phases over a 12-week period:\n\nPhase 1 (Weeks 1-4): Discovery and Planning\n- Requirements gathering and analysis\n- Solution design and architecture\n- Project plan finalization\n\nPhase 2 (Weeks 5-10): Implementation\n- Development and configuration\n- Integration with existing systems\n- Quality assurance and testing\n\nPhase 3 (Weeks 11-12): Deployment and Training\n- System deployment\n- User training and documentation\n- Post-implementation support',
                        type: 'timeline',
                        order: 2,
                    },
                ],
                pricing: {
                    items: [
                        {
                            name: 'Discovery and Planning',
                            description: 'Requirements gathering, solution design, and project planning',
                            quantity: 1,
                            unitPrice: 5000,
                            total: 5000,
                        },
                        {
                            name: 'Implementation',
                            description: 'Development, configuration, integration, and testing',
                            quantity: 1,
                            unitPrice: 15000,
                            total: 15000,
                        },
                        {
                            name: 'Deployment and Training',
                            description: 'System deployment, user training, and documentation',
                            quantity: 1,
                            unitPrice: 5000,
                            total: 5000,
                        },
                        {
                            name: 'Monthly Support',
                            description: 'Ongoing technical support and maintenance',
                            quantity: 12,
                            unitPrice: 1000,
                            total: 12000,
                        },
                    ],
                },
                terms: 'Payment Terms:\n- 50% due upon project initiation\n- 25% due upon completion of Phase 2\n- 25% due upon project completion\n\nProject Timeline:\n- Project will commence within 2 weeks of proposal acceptance\n- Estimated completion time is 12 weeks from project start date\n\nWarranty:\n- 90-day warranty on all deliverables\n- Support included for 12 months from project completion',
                aiPrompt: prompt,
                aiModel: model,
            };
            onGenerate(generatedContent);
        }
        catch (error) {
            console.error('Error generating proposal:', error);
        }
        finally {
            setIsGenerating(false);
        }
    };
    return (_jsxs("form", { onSubmit: handleSubmit, className: "space-y-6", children: [_jsxs(Card, { className: "p-4", children: [_jsx("h2", { className: "text-xl font-semibold mb-4", children: "AI Proposal Generator" }), _jsxs("div", { className: "space-y-4", children: [_jsxs("div", { children: [_jsx("label", { htmlFor: "model", className: "block text-sm font-medium mb-1", children: "AI Model" }), _jsxs(Select, { id: "model", value: model, onChange: (e) => setModel(e.target.value), className: "w-full", children: [_jsx("option", { value: "claude-3-opus", children: "Claude 3 Opus (Most Powerful)" }), _jsx("option", { value: "claude-3-sonnet", children: "Claude 3 Sonnet (Balanced)" }), _jsx("option", { value: "claude-3-haiku", children: "Claude 3 Haiku (Fastest)" })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx("h3", { className: "text-md font-medium", children: "Include Sections" }), _jsxs("div", { className: "grid grid-cols-1 sm:grid-cols-2 gap-2", children: [_jsxs("div", { className: "flex items-center", children: [_jsx(Checkbox, { id: "includeExecutiveSummary", checked: includeExecutiveSummary, onChange: (e) => setIncludeExecutiveSummary(e.target.checked) }), _jsx("label", { htmlFor: "includeExecutiveSummary", className: "ml-2 text-sm", children: "Executive Summary" })] }), _jsxs("div", { className: "flex items-center", children: [_jsx(Checkbox, { id: "includeSolution", checked: includeSolution, onChange: (e) => setIncludeSolution(e.target.checked) }), _jsx("label", { htmlFor: "includeSolution", className: "ml-2 text-sm", children: "Proposed Solution" })] }), _jsxs("div", { className: "flex items-center", children: [_jsx(Checkbox, { id: "includeTimeline", checked: includeTimeline, onChange: (e) => setIncludeTimeline(e.target.checked) }), _jsx("label", { htmlFor: "includeTimeline", className: "ml-2 text-sm", children: "Project Timeline" })] }), _jsxs("div", { className: "flex items-center", children: [_jsx(Checkbox, { id: "includePricing", checked: includePricing, onChange: (e) => setIncludePricing(e.target.checked) }), _jsx("label", { htmlFor: "includePricing", className: "ml-2 text-sm", children: "Pricing" })] }), _jsxs("div", { className: "flex items-center", children: [_jsx(Checkbox, { id: "includeTeam", checked: includeTeam, onChange: (e) => setIncludeTeam(e.target.checked) }), _jsx("label", { htmlFor: "includeTeam", className: "ml-2 text-sm", children: "Team Members" })] }), _jsxs("div", { className: "flex items-center", children: [_jsx(Checkbox, { id: "includeTestimonials", checked: includeTestimonials, onChange: (e) => setIncludeTestimonials(e.target.checked) }), _jsx("label", { htmlFor: "includeTestimonials", className: "ml-2 text-sm", children: "Client Testimonials" })] }), _jsxs("div", { className: "flex items-center", children: [_jsx(Checkbox, { id: "includeTerms", checked: includeTerms, onChange: (e) => setIncludeTerms(e.target.checked) }), _jsx("label", { htmlFor: "includeTerms", className: "ml-2 text-sm", children: "Terms & Conditions" })] })] })] }), _jsx("div", { className: "flex justify-end", children: _jsx(Button, { type: "button", variant: "outline", onClick: handleGeneratePrompt, children: "Generate Default Prompt" }) }), _jsxs("div", { children: [_jsxs("label", { htmlFor: "prompt", className: "block text-sm font-medium mb-1", children: ["AI Prompt", _jsx("span", { className: "text-xs text-gray-500 ml-2", children: "(Describe the proposal you want to generate)" })] }), _jsx(Textarea, { id: "prompt", value: prompt, onChange: (e) => setPrompt(e.target.value), rows: 8, required: true, placeholder: "Generate a professional business proposal for..." })] }), opportunity && (_jsxs("div", { className: "p-3 bg-blue-50 border border-blue-200 rounded-md", children: [_jsx("h4", { className: "text-sm font-medium text-blue-800", children: "Opportunity Context" }), _jsxs("p", { className: "text-xs text-blue-700 mt-1", children: ["Name: ", opportunity.name, _jsx("br", {}), opportunity.description && _jsxs(_Fragment, { children: ["Description: ", opportunity.description, _jsx("br", {})] }), opportunity.amount && _jsxs(_Fragment, { children: ["Value: $", opportunity.amount.toLocaleString(), _jsx("br", {})] }), opportunity.stage && _jsxs(_Fragment, { children: ["Stage: ", opportunity.stage, _jsx("br", {})] })] })] })), company && (_jsxs("div", { className: "p-3 bg-green-50 border border-green-200 rounded-md", children: [_jsx("h4", { className: "text-sm font-medium text-green-800", children: "Company Context" }), _jsxs("p", { className: "text-xs text-green-700 mt-1", children: ["Name: ", company.name, _jsx("br", {}), company.industry && _jsxs(_Fragment, { children: ["Industry: ", company.industry, _jsx("br", {})] }), company.size && _jsxs(_Fragment, { children: ["Size: ", company.size, _jsx("br", {})] }), company.website && _jsxs(_Fragment, { children: ["Website: ", company.website, _jsx("br", {})] })] })] }))] })] }), _jsx("div", { className: "flex justify-end", children: _jsx(Button, { type: "submit", disabled: isGenerating || !prompt, children: isGenerating ? (_jsxs(_Fragment, { children: [_jsx(Spinner, { size: "sm", className: "mr-2" }), "Generating Proposal..."] })) : ('Generate Proposal') }) })] }));
};
export default ProposalAIGenerator;
