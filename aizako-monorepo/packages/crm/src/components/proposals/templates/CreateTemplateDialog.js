import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, FormControl, InputLabel, Select, MenuItem, Typography, Box, CircularProgress, Alert, IconButton, Tooltip, Grid, FormControlLabel, Switch, List, ListItem, ListItemText, ListItemSecondaryAction, Paper, Stepper, Step, StepLabel, useTheme, } from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon, DragIndicator as DragIcon, ArrowUpward as MoveUpIcon, ArrowDownward as MoveDownIcon, Edit as EditIcon, Visibility as VisibilityIcon, VisibilityOff as VisibilityOffIcon, } from '@mui/icons-material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { v4 as uuidv4 } from 'uuid';
import RichTextEditor from '../../common/RichTextEditor';
/**
 * CreateTemplateDialog Component
 *
 * This component displays a dialog for creating or editing a proposal template.
 */
const CreateTemplateDialog = ({ open, onClose, onSubmit, categories, initialData, isEditing = false, }) => {
    const theme = useTheme();
    // State
    const [activeStep, setActiveStep] = useState(0);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [name, setName] = useState('');
    const [description, setDescription] = useState('');
    const [category, setCategory] = useState('');
    const [newCategory, setNewCategory] = useState('');
    const [isShared, setIsShared] = useState(false);
    const [sections, setSections] = useState([]);
    const [editingSectionId, setEditingSectionId] = useState(null);
    const [editingSectionTitle, setEditingSectionTitle] = useState('');
    const [editingSectionType, setEditingSectionType] = useState('text');
    const [editingSectionContent, setEditingSectionContent] = useState('');
    // Reset state when dialog opens or initialData changes
    useEffect(() => {
        if (open) {
            if (initialData) {
                setName(initialData.name || '');
                setDescription(initialData.description || '');
                setCategory(initialData.category || '');
                setIsShared(initialData.isShared || false);
                setSections(initialData.sections || []);
            }
            else {
                setName('');
                setDescription('');
                setCategory('');
                setNewCategory('');
                setIsShared(false);
                setSections([]);
            }
            setActiveStep(0);
            setLoading(false);
            setError(null);
            setEditingSectionId(null);
            setEditingSectionTitle('');
            setEditingSectionType('text');
            setEditingSectionContent('');
        }
    }, [open, initialData]);
    // Handle next step
    const handleNext = () => {
        setActiveStep((prevStep) => prevStep + 1);
    };
    // Handle back step
    const handleBack = () => {
        setActiveStep((prevStep) => prevStep - 1);
    };
    // Handle submit
    const handleSubmit = () => {
        // Validate
        if (!name) {
            setError('Please enter a template name.');
            return;
        }
        if (sections.length === 0) {
            setError('Please add at least one section.');
            return;
        }
        // Prepare data
        const templateData = {
            name,
            description,
            category: category === 'new' ? newCategory : category,
            isShared,
            sections: sections.map((section, index) => ({
                ...section,
                order: index,
            })),
        };
        // Submit
        onSubmit(templateData);
    };
    // Handle add section
    const handleAddSection = () => {
        const newSection = {
            id: uuidv4(),
            title: 'New Section',
            type: 'text',
            content: '',
            order: sections.length,
            isVisible: true,
        };
        setSections([...sections, newSection]);
        setEditingSectionId(newSection.id);
        setEditingSectionTitle(newSection.title);
        setEditingSectionType(newSection.type);
        setEditingSectionContent(newSection.content);
    };
    // Handle edit section
    const handleEditSection = (sectionId) => {
        const section = sections.find((s) => s.id === sectionId);
        if (section) {
            setEditingSectionId(section.id);
            setEditingSectionTitle(section.title);
            setEditingSectionType(section.type);
            setEditingSectionContent(section.content);
        }
    };
    // Handle save section
    const handleSaveSection = () => {
        if (!editingSectionId)
            return;
        const updatedSections = sections.map((section) => {
            if (section.id === editingSectionId) {
                return {
                    ...section,
                    title: editingSectionTitle,
                    type: editingSectionType,
                    content: editingSectionContent,
                };
            }
            return section;
        });
        setSections(updatedSections);
        setEditingSectionId(null);
        setEditingSectionTitle('');
        setEditingSectionType('text');
        setEditingSectionContent('');
    };
    // Handle delete section
    const handleDeleteSection = (sectionId) => {
        setSections(sections.filter((section) => section.id !== sectionId));
        if (editingSectionId === sectionId) {
            setEditingSectionId(null);
            setEditingSectionTitle('');
            setEditingSectionType('text');
            setEditingSectionContent('');
        }
    };
    // Handle toggle section visibility
    const handleToggleSectionVisibility = (sectionId) => {
        setSections(sections.map((section) => {
            if (section.id === sectionId) {
                return {
                    ...section,
                    isVisible: !section.isVisible,
                };
            }
            return section;
        }));
    };
    // Handle move section up
    const handleMoveSectionUp = (index) => {
        if (index === 0)
            return;
        const updatedSections = [...sections];
        const temp = updatedSections[index];
        updatedSections[index] = updatedSections[index - 1];
        updatedSections[index - 1] = temp;
        setSections(updatedSections);
    };
    // Handle move section down
    const handleMoveSectionDown = (index) => {
        if (index === sections.length - 1)
            return;
        const updatedSections = [...sections];
        const temp = updatedSections[index];
        updatedSections[index] = updatedSections[index + 1];
        updatedSections[index + 1] = temp;
        setSections(updatedSections);
    };
    // Handle drag end
    const handleDragEnd = (result) => {
        if (!result.destination)
            return;
        const items = Array.from(sections);
        const [reorderedItem] = items.splice(result.source.index, 1);
        items.splice(result.destination.index, 0, reorderedItem);
        setSections(items);
    };
    // Steps
    const steps = ['Basic Information', 'Sections', 'Review'];
    return (_jsxs(Dialog, { open: open, onClose: onClose, maxWidth: "md", fullWidth: true, children: [_jsx(DialogTitle, { children: isEditing ? 'Edit Template' : 'Create Template' }), _jsxs(DialogContent, { children: [_jsx(Stepper, { activeStep: activeStep, sx: { mb: 3 }, children: steps.map((label) => (_jsx(Step, { children: _jsx(StepLabel, { children: label }) }, label))) }), error && (_jsx(Alert, { severity: "error", sx: { mb: 2 }, children: error })), activeStep === 0 && (_jsxs(Box, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Basic Information" }), _jsx(TextField, { label: "Template Name", value: name, onChange: (e) => setName(e.target.value), fullWidth: true, margin: "normal", required: true }), _jsx(TextField, { label: "Description", value: description, onChange: (e) => setDescription(e.target.value), fullWidth: true, margin: "normal", multiline: true, rows: 3 }), _jsxs(FormControl, { fullWidth: true, margin: "normal", children: [_jsx(InputLabel, { children: "Category" }), _jsxs(Select, { value: category, onChange: (e) => setCategory(e.target.value), label: "Category", children: [_jsx(MenuItem, { value: "", children: _jsx("em", { children: "No Category" }) }), categories.map((cat) => (_jsx(MenuItem, { value: cat, children: cat }, cat))), _jsx(MenuItem, { value: "new", children: _jsx("em", { children: "+ Add New Category" }) })] })] }), category === 'new' && (_jsx(TextField, { label: "New Category", value: newCategory, onChange: (e) => setNewCategory(e.target.value), fullWidth: true, margin: "normal", required: true })), _jsx(FormControlLabel, { control: _jsx(Switch, { checked: isShared, onChange: (e) => setIsShared(e.target.checked), color: "primary" }), label: "Share with team", sx: { mt: 2 } })] })), activeStep === 1 && (_jsxs(Box, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2, children: [_jsx(Typography, { variant: "h6", children: "Template Sections" }), _jsx(Button, { variant: "outlined", startIcon: _jsx(AddIcon, {}), onClick: handleAddSection, children: "Add Section" })] }), sections.length === 0 ? (_jsx(Alert, { severity: "info", children: "No sections added yet. Click \"Add Section\" to create your first section." })) : (_jsx(DragDropContext, { onDragEnd: handleDragEnd, children: _jsx(Droppable, { droppableId: "sections", children: (provided) => (_jsxs(List, { ...provided.droppableProps, ref: provided.innerRef, disablePadding: true, children: [sections.map((section, index) => (_jsx(Draggable, { draggableId: section.id, index: index, children: (provided) => (_jsx(Paper, { ref: provided.innerRef, ...provided.draggableProps, sx: { mb: 2 }, children: _jsxs(ListItem, { children: [_jsx(Box, { ...provided.dragHandleProps, sx: { mr: 1 }, children: _jsx(DragIcon, {}) }), _jsx(ListItemText, { primary: section.title, secondary: `Type: ${section.type}` }), _jsxs(ListItemSecondaryAction, { children: [_jsx(Tooltip, { title: section.isVisible ? 'Visible' : 'Hidden', children: _jsx(IconButton, { edge: "end", onClick: () => handleToggleSectionVisibility(section.id), children: section.isVisible ? (_jsx(VisibilityIcon, {})) : (_jsx(VisibilityOffIcon, {})) }) }), _jsx(Tooltip, { title: "Move Up", children: _jsx(IconButton, { edge: "end", onClick: () => handleMoveSectionUp(index), disabled: index === 0, children: _jsx(MoveUpIcon, {}) }) }), _jsx(Tooltip, { title: "Move Down", children: _jsx(IconButton, { edge: "end", onClick: () => handleMoveSectionDown(index), disabled: index === sections.length - 1, children: _jsx(MoveDownIcon, {}) }) }), _jsx(Tooltip, { title: "Edit", children: _jsx(IconButton, { edge: "end", onClick: () => handleEditSection(section.id), children: _jsx(EditIcon, {}) }) }), _jsx(Tooltip, { title: "Delete", children: _jsx(IconButton, { edge: "end", onClick: () => handleDeleteSection(section.id), children: _jsx(DeleteIcon, {}) }) })] })] }) })) }, section.id))), provided.placeholder] })) }) })), editingSectionId && (_jsx(Box, { mt: 3, children: _jsxs(Paper, { variant: "outlined", sx: { p: 2 }, children: [_jsx(Typography, { variant: "subtitle1", gutterBottom: true, children: "Edit Section" }), _jsx(TextField, { label: "Section Title", value: editingSectionTitle, onChange: (e) => setEditingSectionTitle(e.target.value), fullWidth: true, margin: "normal", required: true }), _jsxs(FormControl, { fullWidth: true, margin: "normal", children: [_jsx(InputLabel, { children: "Section Type" }), _jsxs(Select, { value: editingSectionType, onChange: (e) => setEditingSectionType(e.target.value), label: "Section Type", children: [_jsx(MenuItem, { value: "text", children: "Text" }), _jsx(MenuItem, { value: "pricing", children: "Pricing" }), _jsx(MenuItem, { value: "timeline", children: "Timeline" }), _jsx(MenuItem, { value: "team", children: "Team" }), _jsx(MenuItem, { value: "testimonials", children: "Testimonials" }), _jsx(MenuItem, { value: "images", children: "Images" }), _jsx(MenuItem, { value: "custom", children: "Custom" })] })] }), _jsxs(Box, { mt: 2, mb: 2, children: [_jsx(Typography, { variant: "subtitle2", gutterBottom: true, children: "Section Content" }), _jsx(RichTextEditor, { value: editingSectionContent, onChange: setEditingSectionContent, placeholder: "Enter section content..." })] }), _jsxs(Box, { display: "flex", justifyContent: "flex-end", gap: 1, children: [_jsx(Button, { onClick: () => {
                                                        setEditingSectionId(null);
                                                        setEditingSectionTitle('');
                                                        setEditingSectionType('text');
                                                        setEditingSectionContent('');
                                                    }, children: "Cancel" }), _jsx(Button, { variant: "contained", onClick: handleSaveSection, children: "Save Section" })] })] }) }))] })), activeStep === 2 && (_jsxs(Box, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Review Template" }), _jsxs(Grid, { container: true, spacing: 3, children: [_jsxs(Grid, { item: true, xs: 12, sm: 6, children: [_jsx(Typography, { variant: "subtitle1", gutterBottom: true, children: "Template Details" }), _jsxs(Box, { mb: 2, children: [_jsx(Typography, { variant: "body2", color: "text.secondary", children: "Name" }), _jsx(Typography, { variant: "body1", children: name })] }), _jsxs(Box, { mb: 2, children: [_jsx(Typography, { variant: "body2", color: "text.secondary", children: "Description" }), _jsx(Typography, { variant: "body1", children: description || 'No description' })] }), _jsxs(Box, { mb: 2, children: [_jsx(Typography, { variant: "body2", color: "text.secondary", children: "Category" }), _jsx(Typography, { variant: "body1", children: category === 'new' ? newCategory : category || 'No category' })] }), _jsxs(Box, { mb: 2, children: [_jsx(Typography, { variant: "body2", color: "text.secondary", children: "Sharing" }), _jsx(Typography, { variant: "body1", children: isShared ? 'Shared with team' : 'Private' })] })] }), _jsxs(Grid, { item: true, xs: 12, sm: 6, children: [_jsxs(Typography, { variant: "subtitle1", gutterBottom: true, children: ["Sections (", sections.length, ")"] }), _jsx(List, { disablePadding: true, children: sections.map((section, index) => (_jsx(ListItem, { divider: index < sections.length - 1, children: _jsx(ListItemText, { primary: `${index + 1}. ${section.title}`, secondary: `Type: ${section.type} | ${section.isVisible ? 'Visible' : 'Hidden'}` }) }, section.id))) })] })] }), _jsx(Alert, { severity: "info", sx: { mt: 3 }, children: _jsx(Typography, { variant: "body2", children: "This template will be available for creating new proposals. You can edit it later if needed." }) })] }))] }), _jsxs(DialogActions, { children: [_jsx(Button, { onClick: onClose, children: "Cancel" }), activeStep > 0 && (_jsx(Button, { onClick: handleBack, children: "Back" })), activeStep < steps.length - 1 ? (_jsx(Button, { variant: "contained", onClick: handleNext, disabled: activeStep === 0 && !name, children: "Next" })) : (_jsx(Button, { variant: "contained", onClick: handleSubmit, disabled: loading, children: loading ? _jsx(CircularProgress, { size: 24 }) : (isEditing ? 'Save Template' : 'Create Template') }))] })] }));
};
export default CreateTemplateDialog;
