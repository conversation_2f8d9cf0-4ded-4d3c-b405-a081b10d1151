import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Box, Typography, Button, Card, CardContent, CardActions, Grid, Divider, Chip, CircularProgress, Alert, IconButton, TextField, FormControl, InputLabel, Select, MenuItem, Paper, Tabs, Tab, useTheme, useMediaQuery, Menu, ListItemIcon, ListItemText, } from '@mui/material';
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, ContentCopy as CopyIcon, Search as SearchIcon, MoreVert as MoreIcon, Visibility as ViewIcon, Category as CategoryIcon, Star as StarIcon, StarBorder as StarBorderIcon, Share as ShareIcon, Refresh as RefreshIcon, } from '@mui/icons-material';
import { format } from 'date-fns';
import { ProposalTemplateService } from '../../../services/proposal-template-service';
import CreateTemplateDialog from './CreateTemplateDialog';
import { useAuth } from '../../../hooks/useAuth';
/**
 * ProposalTemplateLibrary Component
 *
 * This component displays a library of proposal templates and allows
 * creating, editing, and using templates.
 */
const ProposalTemplateLibrary = ({ tenantId, onSelectTemplate, onCreateFromTemplate, onViewTemplate, }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const { user } = useAuth();
    // State
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [templates, setTemplates] = useState([]);
    const [filteredTemplates, setFilteredTemplates] = useState([]);
    const [activeTab, setActiveTab] = useState('all');
    const [searchQuery, setSearchQuery] = useState('');
    const [categoryFilter, setCategoryFilter] = useState('all');
    const [categories, setCategories] = useState([]);
    const [createDialogOpen, setCreateDialogOpen] = useState(false);
    const [editDialogOpen, setEditDialogOpen] = useState(false);
    const [selectedTemplate, setSelectedTemplate] = useState(null);
    const [menuAnchorEl, setMenuAnchorEl] = useState(null);
    const [selectedTemplateId, setSelectedTemplateId] = useState(null);
    // Fetch templates
    useEffect(() => {
        const fetchTemplates = async () => {
            try {
                setLoading(true);
                setError(null);
                const response = await ProposalTemplateService.getTemplates(tenantId);
                setTemplates(response);
                // Extract categories
                const uniqueCategories = Array.from(new Set(response.map((template) => template.category))).filter(Boolean);
                setCategories(uniqueCategories);
            }
            catch (err) {
                console.error('Error fetching templates:', err);
                setError('Failed to load templates. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchTemplates();
    }, [tenantId]);
    // Filter templates
    useEffect(() => {
        let filtered = [...templates];
        // Filter by tab
        if (activeTab === 'my') {
            filtered = filtered.filter(template => template.createdBy === user?.id);
        }
        else if (activeTab === 'shared') {
            filtered = filtered.filter(template => template.isShared);
        }
        else if (activeTab === 'starred') {
            filtered = filtered.filter(template => template.isStarred);
        }
        // Filter by category
        if (categoryFilter !== 'all') {
            filtered = filtered.filter(template => template.category === categoryFilter);
        }
        // Filter by search query
        if (searchQuery) {
            const query = searchQuery.toLowerCase();
            filtered = filtered.filter(template => template.name.toLowerCase().includes(query) ||
                template.description.toLowerCase().includes(query));
        }
        setFilteredTemplates(filtered);
    }, [templates, activeTab, categoryFilter, searchQuery, user?.id]);
    // Handle refresh
    const handleRefresh = () => {
        const fetchTemplates = async () => {
            try {
                setLoading(true);
                setError(null);
                const response = await ProposalTemplateService.getTemplates(tenantId);
                setTemplates(response);
                // Extract categories
                const uniqueCategories = Array.from(new Set(response.map((template) => template.category))).filter(Boolean);
                setCategories(uniqueCategories);
            }
            catch (err) {
                console.error('Error fetching templates:', err);
                setError('Failed to load templates. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchTemplates();
    };
    // Handle tab change
    const handleTabChange = (event, newValue) => {
        setActiveTab(newValue);
    };
    // Handle create template
    const handleCreateTemplate = async (templateData) => {
        try {
            setLoading(true);
            setError(null);
            await ProposalTemplateService.createTemplate({
                ...templateData,
                tenantId,
            });
            // Refresh templates
            handleRefresh();
            // Close dialog
            setCreateDialogOpen(false);
        }
        catch (err) {
            console.error('Error creating template:', err);
            setError('Failed to create template. Please try again.');
            setLoading(false);
        }
    };
    // Handle edit template
    const handleEditTemplate = async (templateData) => {
        try {
            setLoading(true);
            setError(null);
            await ProposalTemplateService.updateTemplate(selectedTemplate._id, {
                ...templateData,
                tenantId,
            });
            // Refresh templates
            handleRefresh();
            // Close dialog
            setEditDialogOpen(false);
        }
        catch (err) {
            console.error('Error updating template:', err);
            setError('Failed to update template. Please try again.');
            setLoading(false);
        }
    };
    // Handle delete template
    const handleDeleteTemplate = async (templateId) => {
        try {
            setLoading(true);
            setError(null);
            await ProposalTemplateService.deleteTemplate(templateId, tenantId);
            // Refresh templates
            handleRefresh();
            // Close menu
            setMenuAnchorEl(null);
        }
        catch (err) {
            console.error('Error deleting template:', err);
            setError('Failed to delete template. Please try again.');
            setLoading(false);
        }
    };
    // Handle star template
    const handleStarTemplate = async (templateId, isStarred) => {
        try {
            setLoading(true);
            setError(null);
            await ProposalTemplateService.updateTemplate(templateId, {
                isStarred: !isStarred,
                tenantId,
            });
            // Refresh templates
            handleRefresh();
            // Close menu
            setMenuAnchorEl(null);
        }
        catch (err) {
            console.error('Error starring template:', err);
            setError('Failed to star template. Please try again.');
            setLoading(false);
        }
    };
    // Handle share template
    const handleShareTemplate = async (templateId, isShared) => {
        try {
            setLoading(true);
            setError(null);
            await ProposalTemplateService.updateTemplate(templateId, {
                isShared: !isShared,
                tenantId,
            });
            // Refresh templates
            handleRefresh();
            // Close menu
            setMenuAnchorEl(null);
        }
        catch (err) {
            console.error('Error sharing template:', err);
            setError('Failed to share template. Please try again.');
            setLoading(false);
        }
    };
    // Handle menu open
    const handleMenuOpen = (event, templateId) => {
        setMenuAnchorEl(event.currentTarget);
        setSelectedTemplateId(templateId);
    };
    // Handle menu close
    const handleMenuClose = () => {
        setMenuAnchorEl(null);
        setSelectedTemplateId(null);
    };
    // Render loading state
    if (loading && templates.length === 0) {
        return (_jsx(Box, { display: "flex", justifyContent: "center", alignItems: "center", height: "400px", children: _jsx(CircularProgress, {}) }));
    }
    // Render error state
    if (error) {
        return (_jsxs(Alert, { severity: "error", sx: { mb: 2 }, children: [error, _jsx(Button, { variant: "outlined", size: "small", onClick: handleRefresh, startIcon: _jsx(RefreshIcon, {}), sx: { ml: 2 }, children: "Retry" })] }));
    }
    return (_jsxs(Box, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 3, children: [_jsx(Typography, { variant: "h4", children: "Proposal Templates" }), _jsxs(Box, { children: [_jsx(Button, { variant: "outlined", startIcon: _jsx(RefreshIcon, {}), onClick: handleRefresh, sx: { mr: 1 }, children: "Refresh" }), _jsx(Button, { variant: "contained", startIcon: _jsx(AddIcon, {}), onClick: () => setCreateDialogOpen(true), children: "Create Template" })] })] }), _jsx(Paper, { sx: { p: 2, mb: 3 }, children: _jsxs(Grid, { container: true, spacing: 2, alignItems: "center", children: [_jsx(Grid, { item: true, xs: 12, md: 6, children: _jsx(TextField, { fullWidth: true, placeholder: "Search templates...", value: searchQuery, onChange: (e) => setSearchQuery(e.target.value), InputProps: {
                                    startAdornment: _jsx(SearchIcon, { color: "action", sx: { mr: 1 } }),
                                }, size: "small" }) }), _jsx(Grid, { item: true, xs: 12, md: 6, children: _jsxs(FormControl, { fullWidth: true, size: "small", children: [_jsx(InputLabel, { children: "Category" }), _jsxs(Select, { value: categoryFilter, onChange: (e) => setCategoryFilter(e.target.value), label: "Category", startAdornment: _jsx(CategoryIcon, { color: "action", sx: { mr: 1 } }), children: [_jsx(MenuItem, { value: "all", children: "All Categories" }), categories.map((category) => (_jsx(MenuItem, { value: category, children: category }, category)))] })] }) })] }) }), _jsx(Paper, { sx: { mb: 3 }, children: _jsxs(Tabs, { value: activeTab, onChange: handleTabChange, indicatorColor: "primary", textColor: "primary", variant: isMobile ? "scrollable" : "fullWidth", scrollButtons: isMobile ? "auto" : undefined, children: [_jsx(Tab, { label: "All Templates", value: "all" }), _jsx(Tab, { label: "My Templates", value: "my" }), _jsx(Tab, { label: "Shared Templates", value: "shared" }), _jsx(Tab, { label: "Starred Templates", value: "starred" })] }) }), filteredTemplates.length === 0 ? (_jsxs(Alert, { severity: "info", children: ["No templates found. ", activeTab !== 'all' && 'Try changing your filters or ', _jsx(Button, { color: "primary", size: "small", onClick: () => setCreateDialogOpen(true), children: "create a new template" }), "."] })) : (_jsx(Grid, { container: true, spacing: 3, children: filteredTemplates.map((template) => (_jsx(Grid, { item: true, xs: 12, sm: 6, md: 4, children: _jsxs(Card, { children: [_jsxs(CardContent, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "flex-start", children: [_jsxs(Box, { children: [_jsx(Typography, { variant: "h6", noWrap: true, sx: { maxWidth: '200px' }, children: template.name }), _jsx(Typography, { variant: "body2", color: "text.secondary", gutterBottom: true, children: template.description })] }), _jsx(Box, { children: template.isStarred && (_jsx(StarIcon, { color: "warning" })) })] }), _jsx(Divider, { sx: { my: 1 } }), _jsxs(Box, { display: "flex", flexWrap: "wrap", gap: 0.5, mb: 1, children: [template.category && (_jsx(Chip, { label: template.category, size: "small", color: "primary", variant: "outlined" })), template.isShared && (_jsx(Chip, { icon: _jsx(ShareIcon, {}), label: "Shared", size: "small" })), _jsx(Chip, { label: `${template.sections?.length || 0} Sections`, size: "small" })] }), _jsxs(Typography, { variant: "caption", display: "block", color: "text.secondary", children: ["Created ", format(new Date(template.createdAt), 'PPP')] })] }), _jsxs(CardActions, { children: [_jsx(Button, { size: "small", startIcon: _jsx(ViewIcon, {}), onClick: () => onViewTemplate && onViewTemplate(template._id), children: "View" }), _jsx(Button, { size: "small", startIcon: _jsx(CopyIcon, {}), onClick: () => onCreateFromTemplate && onCreateFromTemplate(template._id), children: "Use" }), _jsx(Box, { flexGrow: 1 }), _jsx(IconButton, { size: "small", onClick: (e) => handleMenuOpen(e, template._id), children: _jsx(MoreIcon, {}) })] })] }) }, template._id))) })), _jsx(Menu, { anchorEl: menuAnchorEl, open: Boolean(menuAnchorEl), onClose: handleMenuClose, children: selectedTemplateId && (_jsxs(_Fragment, { children: [_jsxs(MenuItem, { onClick: () => {
                                const template = templates.find(t => t._id === selectedTemplateId);
                                setSelectedTemplate(template);
                                setEditDialogOpen(true);
                                handleMenuClose();
                            }, children: [_jsx(ListItemIcon, { children: _jsx(EditIcon, { fontSize: "small" }) }), _jsx(ListItemText, { children: "Edit Template" })] }), _jsxs(MenuItem, { onClick: () => {
                                const template = templates.find(t => t._id === selectedTemplateId);
                                handleStarTemplate(selectedTemplateId, template.isStarred);
                            }, children: [_jsx(ListItemIcon, { children: templates.find(t => t._id === selectedTemplateId)?.isStarred ? (_jsx(StarBorderIcon, { fontSize: "small" })) : (_jsx(StarIcon, { fontSize: "small" })) }), _jsxs(ListItemText, { children: [templates.find(t => t._id === selectedTemplateId)?.isStarred ? 'Unstar' : 'Star', " Template"] })] }), _jsxs(MenuItem, { onClick: () => {
                                const template = templates.find(t => t._id === selectedTemplateId);
                                handleShareTemplate(selectedTemplateId, template.isShared);
                            }, children: [_jsx(ListItemIcon, { children: _jsx(ShareIcon, { fontSize: "small" }) }), _jsxs(ListItemText, { children: [templates.find(t => t._id === selectedTemplateId)?.isShared ? 'Unshare' : 'Share', " Template"] })] }), _jsx(Divider, {}), _jsxs(MenuItem, { onClick: () => handleDeleteTemplate(selectedTemplateId), children: [_jsx(ListItemIcon, { children: _jsx(DeleteIcon, { fontSize: "small", color: "error" }) }), _jsx(ListItemText, { sx: { color: theme.palette.error.main }, children: "Delete Template" })] })] })) }), _jsx(CreateTemplateDialog, { open: createDialogOpen, onClose: () => setCreateDialogOpen(false), onSubmit: handleCreateTemplate, categories: categories }), selectedTemplate && (_jsx(CreateTemplateDialog, { open: editDialogOpen, onClose: () => setEditDialogOpen(false), onSubmit: handleEditTemplate, categories: categories, initialData: selectedTemplate, isEditing: true }))] }));
};
export default ProposalTemplateLibrary;
