import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { Button, Card, Input, Textarea, Checkbox, Select } from '@aizako/ui-kit';
/**
 * Proposal sender component
 */
export const ProposalSender = ({ proposal, contacts, onSend, isLoading = false, }) => {
    const [emailData, setEmailData] = useState({
        to: [],
        cc: [],
        bcc: [],
        subject: `Proposal: ${proposal.title}`,
        message: `Dear recipient,\n\nI'm pleased to share our proposal with you. Please review it at your convenience.\n\nBest regards,`,
        includeLink: true,
        includeAttachment: false,
        attachmentFormat: 'pdf',
        expiresAt: undefined,
    });
    // Handle input change
    const handleChange = (e) => {
        const { name, value } = e.target;
        setEmailData(prev => ({
            ...prev,
            [name]: value,
        }));
    };
    // Handle checkbox change
    const handleCheckboxChange = (e) => {
        const { name, checked } = e.target;
        setEmailData(prev => ({
            ...prev,
            [name]: checked,
        }));
    };
    // Handle recipient selection
    const handleRecipientChange = (e, type) => {
        const selectedOptions = Array.from(e.target.selectedOptions).map(option => option.value);
        setEmailData(prev => ({
            ...prev,
            [type]: selectedOptions,
        }));
    };
    // Handle expiration date change
    const handleExpirationChange = (e) => {
        const value = e.target.value;
        if (value === 'none') {
            setEmailData(prev => ({
                ...prev,
                expiresAt: undefined,
            }));
            return;
        }
        const days = parseInt(value, 10);
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + days);
        setEmailData(prev => ({
            ...prev,
            expiresAt,
        }));
    };
    // Handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();
        await onSend(emailData);
    };
    return (_jsxs("form", { onSubmit: handleSubmit, className: "space-y-6", children: [_jsxs(Card, { className: "p-4", children: [_jsx("h2", { className: "text-xl font-semibold mb-4", children: "Send Proposal" }), _jsxs("div", { className: "space-y-4", children: [_jsxs("div", { children: [_jsx("label", { htmlFor: "to", className: "block text-sm font-medium mb-1", children: "To" }), _jsx(Select, { id: "to", multiple: true, value: emailData.to, onChange: (e) => handleRecipientChange(e, 'to'), required: true, className: "h-20", children: contacts.map((contact) => (_jsxs("option", { value: contact.email, children: [contact.firstName, " ", contact.lastName, " (", contact.email, ")"] }, String(contact._id)))) }), _jsx("p", { className: "text-xs text-gray-500 mt-1", children: "Hold Ctrl/Cmd to select multiple recipients" })] }), _jsxs("div", { children: [_jsx("label", { htmlFor: "cc", className: "block text-sm font-medium mb-1", children: "CC" }), _jsx(Select, { id: "cc", multiple: true, value: emailData.cc, onChange: (e) => handleRecipientChange(e, 'cc'), className: "h-20", children: contacts.map((contact) => (_jsxs("option", { value: contact.email, children: [contact.firstName, " ", contact.lastName, " (", contact.email, ")"] }, String(contact._id)))) })] }), _jsxs("div", { children: [_jsx("label", { htmlFor: "bcc", className: "block text-sm font-medium mb-1", children: "BCC" }), _jsx(Select, { id: "bcc", multiple: true, value: emailData.bcc, onChange: (e) => handleRecipientChange(e, 'bcc'), className: "h-20", children: contacts.map((contact) => (_jsxs("option", { value: contact.email, children: [contact.firstName, " ", contact.lastName, " (", contact.email, ")"] }, String(contact._id)))) })] }), _jsxs("div", { children: [_jsx("label", { htmlFor: "subject", className: "block text-sm font-medium mb-1", children: "Subject" }), _jsx(Input, { id: "subject", name: "subject", value: emailData.subject, onChange: handleChange, required: true })] }), _jsxs("div", { children: [_jsx("label", { htmlFor: "message", className: "block text-sm font-medium mb-1", children: "Message" }), _jsx(Textarea, { id: "message", name: "message", value: emailData.message, onChange: handleChange, rows: 5, required: true })] }), _jsxs("div", { className: "space-y-2", children: [_jsxs("div", { className: "flex items-center", children: [_jsx(Checkbox, { id: "includeLink", name: "includeLink", checked: emailData.includeLink, onChange: handleCheckboxChange }), _jsx("label", { htmlFor: "includeLink", className: "ml-2 text-sm", children: "Include link to proposal" })] }), _jsxs("div", { className: "flex items-center", children: [_jsx(Checkbox, { id: "includeAttachment", name: "includeAttachment", checked: emailData.includeAttachment, onChange: handleCheckboxChange }), _jsx("label", { htmlFor: "includeAttachment", className: "ml-2 text-sm", children: "Include proposal as attachment" })] }), emailData.includeAttachment && (_jsxs("div", { className: "ml-6", children: [_jsx("label", { htmlFor: "attachmentFormat", className: "block text-sm font-medium mb-1", children: "Attachment Format" }), _jsxs(Select, { id: "attachmentFormat", name: "attachmentFormat", value: emailData.attachmentFormat, onChange: handleChange, className: "w-full", children: [_jsx("option", { value: "pdf", children: "PDF" }), _jsx("option", { value: "docx", children: "DOCX" }), proposal.downloadFormats?.includes('md') && (_jsx("option", { value: "md", children: "Markdown" }))] })] }))] }), _jsxs("div", { children: [_jsx("label", { htmlFor: "expiresAt", className: "block text-sm font-medium mb-1", children: "Proposal Expiration" }), _jsxs(Select, { id: "expiresAt", onChange: handleExpirationChange, className: "w-full", defaultValue: "none", children: [_jsx("option", { value: "none", children: "No expiration" }), _jsx("option", { value: "7", children: "7 days" }), _jsx("option", { value: "14", children: "14 days" }), _jsx("option", { value: "30", children: "30 days" }), _jsx("option", { value: "60", children: "60 days" }), _jsx("option", { value: "90", children: "90 days" })] })] })] })] }), _jsx("div", { className: "flex justify-end space-x-2", children: _jsx(Button, { type: "submit", disabled: isLoading, children: isLoading ? 'Sending...' : 'Send Proposal' }) })] }));
};
export default ProposalSender;
