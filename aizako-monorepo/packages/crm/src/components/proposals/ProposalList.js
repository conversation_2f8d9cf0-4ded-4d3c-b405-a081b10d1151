import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState } from 'react';
import { Button, Card, Input, Select, Badge, Pagination } from '@aizako/ui-kit';
import { formatCurrency } from '../../utils/formatters';
/**
 * Proposal list component
 */
export const ProposalList = ({ proposals, total, page, limit, onPageChange, onLimitChange, onViewProposal, onEditProposal, onDeleteProposal, onSendProposal, isLoading = false, }) => {
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    // Format date
    const formatDate = (date) => {
        if (!date)
            return '';
        return new Date(date).toLocaleDateString();
    };
    // Get status badge color
    const getStatusBadgeColor = (status) => {
        switch (status) {
            case 'draft':
                return 'gray';
            case 'sent':
                return 'blue';
            case 'viewed':
                return 'purple';
            case 'accepted':
                return 'green';
            case 'rejected':
                return 'red';
            case 'expired':
                return 'yellow';
            default:
                return 'gray';
        }
    };
    // Filter proposals based on search term and status filter
    const filteredProposals = proposals.filter(proposal => {
        const matchesSearch = searchTerm === '' ||
            proposal.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (proposal.description && proposal.description.toLowerCase().includes(searchTerm.toLowerCase()));
        const matchesStatus = statusFilter === 'all' || proposal.status === statusFilter;
        return matchesSearch && matchesStatus;
    });
    return (_jsxs("div", { className: "space-y-4", children: [_jsxs("div", { className: "flex flex-col sm:flex-row justify-between gap-4", children: [_jsx("div", { className: "flex-1", children: _jsx(Input, { placeholder: "Search proposals...", value: searchTerm, onChange: (e) => setSearchTerm(e.target.value) }) }), _jsx("div", { className: "w-full sm:w-48", children: _jsxs(Select, { value: statusFilter, onChange: (e) => setStatusFilter(e.target.value), children: [_jsx("option", { value: "all", children: "All Statuses" }), _jsx("option", { value: "draft", children: "Draft" }), _jsx("option", { value: "sent", children: "Sent" }), _jsx("option", { value: "viewed", children: "Viewed" }), _jsx("option", { value: "accepted", children: "Accepted" }), _jsx("option", { value: "rejected", children: "Rejected" }), _jsx("option", { value: "expired", children: "Expired" })] }) })] }), isLoading ? (_jsx("div", { className: "text-center py-8", children: "Loading proposals..." })) : filteredProposals.length === 0 ? (_jsx("div", { className: "text-center py-8", children: "No proposals found" })) : (_jsxs("div", { className: "space-y-4", children: [filteredProposals.map((proposal) => (_jsx(Card, { className: "p-4", children: _jsxs("div", { className: "flex flex-col sm:flex-row justify-between", children: [_jsxs("div", { className: "space-y-2", children: [_jsxs("div", { className: "flex items-center", children: [_jsx("h3", { className: "text-lg font-medium", children: proposal.title }), _jsx(Badge, { variant: getStatusBadgeColor(proposal.status), className: "ml-2", children: proposal.status }), proposal.aiGenerated && (_jsx(Badge, { variant: "purple", className: "ml-2", children: "AI Generated" }))] }), proposal.description && (_jsx("p", { className: "text-sm text-gray-600", children: proposal.description })), _jsxs("div", { className: "flex flex-wrap gap-x-4 gap-y-1 text-sm text-gray-500", children: [_jsxs("div", { children: ["Created: ", formatDate(proposal.createdAt)] }), proposal.sentAt && (_jsxs("div", { children: ["Sent: ", formatDate(proposal.sentAt)] })), proposal.viewedAt && (_jsxs("div", { children: ["Viewed: ", formatDate(proposal.viewedAt)] })), proposal.expiresAt && (_jsxs("div", { children: ["Expires: ", formatDate(proposal.expiresAt)] })), proposal.pricing && proposal.pricing.total > 0 && (_jsxs("div", { children: ["Value: ", formatCurrency(proposal.pricing.total, proposal.pricing.currency)] }))] })] }), _jsxs("div", { className: "flex flex-row sm:flex-col justify-end gap-2 mt-4 sm:mt-0", children: [_jsx(Button, { size: "sm", variant: "outline", onClick: () => onViewProposal(proposal), children: "View" }), proposal.status === 'draft' && (_jsxs(_Fragment, { children: [_jsx(Button, { size: "sm", variant: "outline", onClick: () => onEditProposal(proposal), children: "Edit" }), _jsx(Button, { size: "sm", variant: "primary", onClick: () => onSendProposal(proposal), children: "Send" })] })), proposal.status === 'draft' && (_jsx(Button, { size: "sm", variant: "destructive", onClick: () => onDeleteProposal(proposal), children: "Delete" }))] })] }) }, String(proposal._id)))), _jsxs("div", { className: "flex justify-between items-center mt-4", children: [_jsxs("div", { className: "flex items-center space-x-2", children: [_jsx("span", { className: "text-sm text-gray-500", children: "Show" }), _jsxs(Select, { value: limit.toString(), onChange: (e) => onLimitChange(Number(e.target.value)), className: "w-16", children: [_jsx("option", { value: "10", children: "10" }), _jsx("option", { value: "20", children: "20" }), _jsx("option", { value: "50", children: "50" }), _jsx("option", { value: "100", children: "100" })] }), _jsx("span", { className: "text-sm text-gray-500", children: "per page" })] }), _jsx(Pagination, { currentPage: page, totalPages: Math.ceil(total / limit), onPageChange: onPageChange })] })] }))] }));
};
export default ProposalList;
