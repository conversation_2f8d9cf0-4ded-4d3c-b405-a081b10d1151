import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Box, Button, Card, CardContent, Divider, Grid, IconButton, TextField, Typography, Menu, MenuItem, Dialog, DialogTitle, DialogContent, Snackbar, Alert, Tabs, Tab, Paper, Tooltip, CircularProgress } from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon, DragIndicator as DragIndicatorIcon, Visibility as VisibilityIcon, VisibilityOff as VisibilityOffIcon, Save as SaveIcon, Download as DownloadIcon, Send as SendIcon, ContentCopy as DuplicateIcon, ArrowUpward as MoveUpIcon, ArrowDownward as MoveDownIcon, AutoAwesome as AIIcon } from '@mui/icons-material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { useProposals } from '../../hooks/useProposals';
import ProposalSectionEditor from './ProposalSectionEditor';
import ProposalPreview from './ProposalPreview';
import ProposalPricingEditor from './ProposalPricingEditor';
import ProposalAIGenerator from './ProposalAIGenerator';
import ProposalSendDialog from './ProposalSendDialog';
import { v4 as uuidv4 } from 'uuid';
/**
 * Proposal Editor Component
 *
 * This component provides a UI for creating and editing proposals.
 * It includes section management, real-time preview, and AI generation.
 */
const ProposalEditor = ({ proposalId, opportunityId, companyId, contactIds, onSave, onCancel }) => {
    // State for the proposal
    const [proposal, setProposal] = useState({
        title: '',
        description: '',
        sections: [],
        status: 'draft',
        downloadEnabled: true,
        downloadFormats: ['pdf', 'docx', 'md'],
        publicAccessEnabled: true,
        emailEnabled: true,
    });
    // State for UI
    const [activeTab, setActiveTab] = useState(0);
    const [loading, setLoading] = useState(false);
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(null);
    const [aiDialogOpen, setAiDialogOpen] = useState(false);
    const [sendDialogOpen, setSendDialogOpen] = useState(false);
    const [downloadMenuAnchor, setDownloadMenuAnchor] = useState(null);
    const [sectionMenuAnchor, setSectionMenuAnchor] = useState(null);
    const [activeSectionId, setActiveSectionId] = useState(null);
    // Get proposal methods from hook
    const { getProposalById, createProposal, updateProposal, downloadProposal } = useProposals();
    // Load proposal if editing an existing one
    useEffect(() => {
        const loadProposal = async () => {
            if (proposalId) {
                setLoading(true);
                try {
                    const loadedProposal = await getProposalById(proposalId);
                    setProposal(loadedProposal);
                }
                catch (err) {
                    setError('Failed to load proposal');
                    console.error(err);
                }
                finally {
                    setLoading(false);
                }
            }
            else if (opportunityId || companyId || contactIds) {
                // Initialize with related entities if creating a new proposal
                setProposal(prev => ({
                    ...prev,
                    opportunityId,
                    companyId,
                    contactIds,
                }));
            }
        };
        loadProposal();
    }, [proposalId, opportunityId, companyId, contactIds, getProposalById]);
    // Handle tab change
    const handleTabChange = (event, newValue) => {
        setActiveTab(newValue);
    };
    // Handle proposal field changes
    const handleProposalChange = (field, value) => {
        setProposal(prev => ({
            ...prev,
            [field]: value
        }));
    };
    // Add a new section
    const handleAddSection = () => {
        const newSection = {
            id: uuidv4(),
            title: 'New Section',
            content: '',
            order: proposal.sections?.length || 0,
            type: 'text',
            isVisible: true
        };
        setProposal(prev => ({
            ...prev,
            sections: [...(prev.sections || []), newSection]
        }));
    };
    // Update a section
    const handleUpdateSection = (sectionId, updates) => {
        setProposal(prev => ({
            ...prev,
            sections: prev.sections?.map(section => section.id === sectionId ? { ...section, ...updates } : section)
        }));
    };
    // Delete a section
    const handleDeleteSection = (sectionId) => {
        setProposal(prev => ({
            ...prev,
            sections: prev.sections?.filter(section => section.id !== sectionId)
                .map((section, index) => ({ ...section, order: index }))
        }));
    };
    // Duplicate a section
    const handleDuplicateSection = (sectionId) => {
        const sectionToDuplicate = proposal.sections?.find(section => section.id === sectionId);
        if (sectionToDuplicate) {
            const newSection = {
                ...sectionToDuplicate,
                id: uuidv4(),
                title: `${sectionToDuplicate.title} (Copy)`,
                order: (proposal.sections?.length || 0)
            };
            setProposal(prev => ({
                ...prev,
                sections: [...(prev.sections || []), newSection]
            }));
        }
    };
    // Toggle section visibility
    const handleToggleSectionVisibility = (sectionId) => {
        setProposal(prev => ({
            ...prev,
            sections: prev.sections?.map(section => section.id === sectionId
                ? { ...section, isVisible: !section.isVisible }
                : section)
        }));
    };
    // Move section up
    const handleMoveSectionUp = (sectionId) => {
        const sectionIndex = proposal.sections?.findIndex(section => section.id === sectionId) || 0;
        if (sectionIndex > 0) {
            const newSections = [...(proposal.sections || [])];
            const temp = newSections[sectionIndex];
            newSections[sectionIndex] = newSections[sectionIndex - 1];
            newSections[sectionIndex - 1] = temp;
            // Update order
            const updatedSections = newSections.map((section, index) => ({
                ...section,
                order: index
            }));
            setProposal(prev => ({
                ...prev,
                sections: updatedSections
            }));
        }
    };
    // Move section down
    const handleMoveSectionDown = (sectionId) => {
        const sectionIndex = proposal.sections?.findIndex(section => section.id === sectionId) || 0;
        if (sectionIndex < (proposal.sections?.length || 0) - 1) {
            const newSections = [...(proposal.sections || [])];
            const temp = newSections[sectionIndex];
            newSections[sectionIndex] = newSections[sectionIndex + 1];
            newSections[sectionIndex + 1] = temp;
            // Update order
            const updatedSections = newSections.map((section, index) => ({
                ...section,
                order: index
            }));
            setProposal(prev => ({
                ...prev,
                sections: updatedSections
            }));
        }
    };
    // Handle drag and drop reordering
    const handleDragEnd = (result) => {
        if (!result.destination)
            return;
        const items = Array.from(proposal.sections || []);
        const [reorderedItem] = items.splice(result.source.index, 1);
        items.splice(result.destination.index, 0, reorderedItem);
        // Update order
        const updatedSections = items.map((section, index) => ({
            ...section,
            order: index
        }));
        setProposal(prev => ({
            ...prev,
            sections: updatedSections
        }));
    };
    // Save the proposal
    const handleSave = async () => {
        setSaving(true);
        setError(null);
        try {
            let savedProposal;
            if (proposalId) {
                // Update existing proposal
                savedProposal = await updateProposal(proposalId, proposal);
                setSuccess('Proposal updated successfully');
            }
            else {
                // Create new proposal - ensure required fields are present
                const proposalToCreate = {
                    ...proposal,
                    title: proposal.title || 'Untitled Proposal',
                    status: proposal.status || 'draft',
                    sections: proposal.sections || [],
                    downloadEnabled: proposal.downloadEnabled ?? true,
                    downloadFormats: proposal.downloadFormats || ['pdf'],
                    publicAccessEnabled: proposal.publicAccessEnabled ?? true,
                    emailEnabled: proposal.emailEnabled ?? true,
                }; // Type assertion to handle the partial to complete conversion
                savedProposal = await createProposal(proposalToCreate);
                setSuccess('Proposal created successfully');
            }
            if (onSave) {
                onSave(savedProposal);
            }
        }
        catch (err) {
            setError('Failed to save proposal');
            console.error(err);
        }
        finally {
            setSaving(false);
        }
    };
    // Handle AI generation
    const handleAIGeneration = (generatedContent) => {
        // Convert the generated content to a partial proposal
        const generatedProposal = {
            title: generatedContent.title,
            description: generatedContent.description,
            sections: generatedContent.sections,
            pricing: generatedContent.pricing,
            terms: generatedContent.terms,
            aiGenerated: true,
            aiPrompt: generatedContent.aiPrompt,
            aiModel: generatedContent.aiModel,
        };
        setProposal(prev => ({
            ...prev,
            ...generatedProposal,
            // Preserve existing ID and metadata if editing
            ...(proposalId ? { _id: proposalId } : {})
        }));
        setAiDialogOpen(false);
    };
    // Handle download
    const handleDownload = async (format) => {
        setDownloadMenuAnchor(null);
        if (!proposalId) {
            setError('Please save the proposal before downloading');
            return;
        }
        try {
            setLoading(true);
            const options = {
                format,
                paperSize: 'a4',
                colorScheme: 'professional',
                includeHeader: true,
                includeFooter: true,
                includeBranding: true,
                includePageNumbers: true
            };
            const blob = await downloadProposal(proposalId, options);
            // Create a download link
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${proposal.title || 'proposal'}.${format}`;
            document.body.appendChild(a);
            a.click();
            // Clean up
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            setSuccess(`Proposal downloaded as ${format.toUpperCase()}`);
        }
        catch (err) {
            setError(`Failed to download proposal as ${format.toUpperCase()}`);
            console.error(err);
        }
        finally {
            setLoading(false);
        }
    };
    // Render the editor
    return (_jsxs(Box, { sx: { width: '100%' }, children: [_jsx(Paper, { sx: { mb: 2 }, children: _jsxs(Tabs, { value: activeTab, onChange: handleTabChange, indicatorColor: "primary", textColor: "primary", centered: true, variant: "fullWidth" // Full width tabs for better mobile experience
                    , children: [_jsx(Tab, { label: "Edit" }), _jsx(Tab, { label: "Preview" })] }) }), activeTab === 0 && (_jsxs(Grid, { container: true, spacing: 2, children: [_jsx(Grid, { item: true, xs: 12, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Proposal Details" }), _jsx(TextField, { label: "Title", fullWidth: true, value: proposal.title || '', onChange: (e) => handleProposalChange('title', e.target.value), margin: "normal", variant: "outlined", inputProps: { 'aria-label': 'Proposal title' } }), _jsx(TextField, { label: "Description", fullWidth: true, multiline: true, rows: 3, value: proposal.description || '', onChange: (e) => handleProposalChange('description', e.target.value), margin: "normal", variant: "outlined", inputProps: { 'aria-label': 'Proposal description' } })] }) }) }), _jsx(Grid, { item: true, xs: 12, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsxs(Box, { display: "flex", flexDirection: { xs: 'column', sm: 'row' }, justifyContent: "space-between", alignItems: { xs: 'flex-start', sm: 'center' }, mb: 2, gap: 1, children: [_jsx(Typography, { variant: "h6", children: "Sections" }), _jsxs(Box, { display: "flex", flexDirection: { xs: 'column', sm: 'row' }, width: { xs: '100%', sm: 'auto' }, gap: 1, children: [_jsx(Button, { startIcon: _jsx(AIIcon, {}), variant: "outlined", color: "secondary", onClick: () => setAiDialogOpen(true), sx: { width: { xs: '100%', sm: 'auto' } }, "aria-label": "Generate with AI" // Accessibility improvement
                                                        , children: "AI Generate" }), _jsx(Button, { startIcon: _jsx(AddIcon, {}), variant: "contained", color: "primary", onClick: handleAddSection, sx: { width: { xs: '100%', sm: 'auto' } }, "aria-label": "Add section" // Accessibility improvement
                                                        , children: "Add Section" })] })] }), _jsx(DragDropContext, { onDragEnd: handleDragEnd, children: _jsx(Droppable, { droppableId: "sections", children: (provided) => (_jsxs("div", { ...provided.droppableProps, ref: provided.innerRef, children: [proposal.sections?.sort((a, b) => a.order - b.order).map((section, index) => (_jsx(Draggable, { draggableId: section.id, index: index, children: (provided) => (_jsx(Card, { ref: provided.innerRef, ...provided.draggableProps, sx: { mb: 2, border: '1px solid #e0e0e0' }, children: _jsxs(CardContent, { children: [_jsxs(Box, { display: "flex", alignItems: "center", children: [_jsx(Box, { ...provided.dragHandleProps, sx: { mr: 1, cursor: 'grab' }, children: _jsx(DragIndicatorIcon, {}) }), _jsxs(Typography, { variant: "subtitle1", sx: { flexGrow: 1 }, children: [section.title, !section.isVisible && (_jsx(Typography, { component: "span", color: "text.secondary", sx: { ml: 1 }, children: "(Hidden)" }))] }), _jsxs(Box, { children: [_jsx(Tooltip, { title: "Move Up", children: _jsx(IconButton, { size: "small", onClick: () => handleMoveSectionUp(section.id), disabled: index === 0, children: _jsx(MoveUpIcon, { fontSize: "small" }) }) }), _jsx(Tooltip, { title: "Move Down", children: _jsx(IconButton, { size: "small", onClick: () => handleMoveSectionDown(section.id), disabled: index === (proposal.sections?.length || 0) - 1, children: _jsx(MoveDownIcon, { fontSize: "small" }) }) }), _jsx(Tooltip, { title: section.isVisible ? "Hide Section" : "Show Section", children: _jsx(IconButton, { size: "small", onClick: () => handleToggleSectionVisibility(section.id), children: section.isVisible ? (_jsx(VisibilityIcon, { fontSize: "small" })) : (_jsx(VisibilityOffIcon, { fontSize: "small" })) }) }), _jsx(Tooltip, { title: "Duplicate", children: _jsx(IconButton, { size: "small", onClick: () => handleDuplicateSection(section.id), children: _jsx(DuplicateIcon, { fontSize: "small" }) }) }), _jsx(Tooltip, { title: "Delete", children: _jsx(IconButton, { size: "small", color: "error", onClick: () => handleDeleteSection(section.id), children: _jsx(DeleteIcon, { fontSize: "small" }) }) })] })] }), _jsx(Divider, { sx: { my: 1 } }), _jsx(ProposalSectionEditor, { section: section, onChange: (updates) => handleUpdateSection(section.id, updates) })] }) })) }, section.id))), provided.placeholder] })) }) }), (!proposal.sections || proposal.sections.length === 0) && (_jsx(Typography, { color: "text.secondary", align: "center", sx: { py: 4 }, children: "No sections yet. Click \"Add Section\" to create one or use \"AI Generate\" to create a complete proposal." }))] }) }) }), _jsx(Grid, { item: true, xs: 12, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Pricing" }), _jsx(ProposalPricingEditor, { pricing: proposal.pricing, onChange: (pricing) => handleProposalChange('pricing', pricing) })] }) }) }), _jsx(Grid, { item: true, xs: 12, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Terms & Conditions" }), _jsx(TextField, { fullWidth: true, multiline: true, rows: 6, value: proposal.terms || '', onChange: (e) => handleProposalChange('terms', e.target.value), margin: "normal", variant: "outlined", placeholder: "Enter terms and conditions..." })] }) }) }), _jsx(Grid, { item: true, xs: 12, children: _jsxs(Box, { display: "flex", flexDirection: { xs: 'column', sm: 'row' }, justifyContent: "space-between", gap: 2, children: [_jsx(Button, { variant: "outlined", onClick: onCancel, sx: { width: { xs: '100%', sm: 'auto' } }, "aria-label": "Cancel editing" // Accessibility improvement
                                    , children: "Cancel" }), _jsxs(Box, { display: "flex", flexDirection: { xs: 'column', sm: 'row' }, width: { xs: '100%', sm: 'auto' }, gap: 2, children: [_jsx(Button, { variant: "outlined", startIcon: _jsx(DownloadIcon, {}), onClick: (e) => setDownloadMenuAnchor(e.currentTarget), disabled: !proposalId, sx: { width: { xs: '100%', sm: 'auto' } }, "aria-label": "Download proposal" // Accessibility improvement
                                            , children: "Download" }), _jsx(Button, { variant: "outlined", startIcon: _jsx(SendIcon, {}), onClick: () => setSendDialogOpen(true), disabled: !proposalId, sx: { width: { xs: '100%', sm: 'auto' } }, "aria-label": "Send proposal" // Accessibility improvement
                                            , children: "Send" }), _jsx(Button, { variant: "contained", color: "primary", startIcon: _jsx(SaveIcon, {}), onClick: handleSave, disabled: saving, sx: { width: { xs: '100%', sm: 'auto' } }, "aria-label": "Save proposal" // Accessibility improvement
                                            , children: saving ? 'Saving...' : 'Save' })] })] }) })] })), activeTab === 1 && (_jsx(ProposalPreview, { proposal: proposal })), _jsxs(Dialog, { open: aiDialogOpen, onClose: () => setAiDialogOpen(false), maxWidth: "md", fullWidth: true, children: [_jsx(DialogTitle, { children: "Generate Proposal with AI" }), _jsx(DialogContent, { children: _jsx(ProposalAIGenerator, { opportunity: undefined, company: undefined, contacts: undefined, onGenerate: handleAIGeneration }) })] }), _jsxs(Dialog, { open: sendDialogOpen, onClose: () => setSendDialogOpen(false), maxWidth: "md", fullWidth: true, children: [_jsx(DialogTitle, { children: "Send Proposal" }), _jsx(DialogContent, { children: proposalId && (_jsx(ProposalSendDialog, { proposalId: proposalId, onClose: () => setSendDialogOpen(false) })) })] }), _jsxs(Menu, { anchorEl: downloadMenuAnchor, open: Boolean(downloadMenuAnchor), onClose: () => setDownloadMenuAnchor(null), children: [_jsx(MenuItem, { onClick: () => handleDownload('pdf'), children: "PDF" }), _jsx(MenuItem, { onClick: () => handleDownload('docx'), children: "DOCX" }), _jsx(MenuItem, { onClick: () => handleDownload('md'), children: "Markdown" })] }), _jsx(Snackbar, { open: !!error, autoHideDuration: 6000, onClose: () => setError(null), children: _jsx(Alert, { onClose: () => setError(null), severity: "error", children: error }) }), _jsx(Snackbar, { open: !!success, autoHideDuration: 6000, onClose: () => setSuccess(null), children: _jsx(Alert, { onClose: () => setSuccess(null), severity: "success", children: success }) }), loading && (_jsx(Box, { sx: {
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    zIndex: 9999,
                }, children: _jsx(CircularProgress, { color: "primary" }) }))] }));
};
export default ProposalEditor;
