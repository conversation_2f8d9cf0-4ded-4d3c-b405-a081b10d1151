import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Box, Button, TextField, Typography, FormControlLabel, Checkbox, CircularProgress, Alert, Chip, Divider, Grid, Autocomplete, FormGroup, Paper, InputAdornment, IconButton } from '@mui/material';
import { Send as SendIcon, ContentCopy as CopyIcon, CalendarToday as CalendarIcon } from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useProposals } from '../../hooks/useProposals';
import { useContacts } from '../../hooks/useContacts';
/**
 * Proposal Send Dialog Component
 *
 * This component provides a UI for sending proposals via email.
 * It allows users to specify recipients, message, and attachment options.
 */
const ProposalSendDialog = ({ proposalId, onClose }) => {
    // Get proposal and contacts
    const { getProposalById, sendProposal } = useProposals();
    const { getContacts } = useContacts();
    // State for the proposal
    const [proposal, setProposal] = useState(null);
    const [contacts, setContacts] = useState([]);
    // State for the send options
    const [sendOptions, setSendOptions] = useState({
        to: [],
        cc: [],
        bcc: [],
        subject: '',
        message: '',
        includeLink: true,
        includeDownloadLink: true,
        includeAttachment: false,
        includeAttachments: false,
        includeViewLink: true,
        attachmentFormats: ['pdf'],
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
    });
    // UI state
    const [loading, setLoading] = useState(false);
    const [sending, setSending] = useState(false);
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(null);
    const [publicLink, setPublicLink] = useState('');
    // Load proposal and contacts
    useEffect(() => {
        const loadData = async () => {
            setLoading(true);
            try {
                // Load proposal
                const loadedProposal = await getProposalById(proposalId);
                setProposal(loadedProposal);
                // Set default subject and message
                setSendOptions(prev => ({
                    ...prev,
                    subject: `Proposal: ${loadedProposal.title}`,
                    message: `Dear recipient,\n\nI'm pleased to share our proposal "${loadedProposal.title}" with you.\n\nPlease review the attached proposal and let me know if you have any questions or would like to discuss further.\n\nBest regards,\n[Your Name]`
                }));
                // Generate public link
                if (loadedProposal.publicToken) {
                    const baseUrl = window.location.origin;
                    setPublicLink(`${baseUrl}/proposals/public/${loadedProposal.publicToken}`);
                }
                // Load contacts
                if (loadedProposal.contactIds && loadedProposal.contactIds.length > 0) {
                    const loadedContacts = await getContacts({
                        ids: loadedProposal.contactIds
                    });
                    setContacts(loadedContacts.data);
                    // Set default recipients
                    const contactEmails = loadedContacts.data
                        .filter((contact) => contact.email)
                        .map((contact) => contact.email);
                    setSendOptions(prev => ({
                        ...prev,
                        to: contactEmails
                    }));
                }
            }
            catch (err) {
                console.error('Error loading data:', err);
                setError('Failed to load proposal data');
            }
            finally {
                setLoading(false);
            }
        };
        loadData();
    }, [proposalId, getProposalById, getContacts]);
    // Handle send options change
    const handleSendOptionsChange = (field, value) => {
        setSendOptions(prev => ({
            ...prev,
            [field]: value
        }));
    };
    // Handle attachment format toggle
    const handleAttachmentFormatToggle = (format) => {
        setSendOptions(prev => {
            const formats = [...(prev.attachmentFormats || [])];
            if (formats.includes(format)) {
                return {
                    ...prev,
                    attachmentFormats: formats.filter(f => f !== format)
                };
            }
            else {
                return {
                    ...prev,
                    attachmentFormats: [...formats, format]
                };
            }
        });
    };
    // Copy public link to clipboard
    const handleCopyLink = () => {
        navigator.clipboard.writeText(publicLink);
        setSuccess('Link copied to clipboard');
        // Clear success message after 3 seconds
        setTimeout(() => {
            setSuccess(null);
        }, 3000);
    };
    // Send the proposal
    const handleSend = async () => {
        if (sendOptions.to.length === 0) {
            setError('Please add at least one recipient');
            return;
        }
        setSending(true);
        setError(null);
        try {
            await sendProposal(proposalId, sendOptions);
            setSuccess('Proposal sent successfully');
            // Close the dialog after 2 seconds
            setTimeout(() => {
                onClose();
            }, 2000);
        }
        catch (err) {
            console.error('Error sending proposal:', err);
            setError('Failed to send proposal');
        }
        finally {
            setSending(false);
        }
    };
    // Render loading state
    if (loading) {
        return (_jsx(Box, { display: "flex", justifyContent: "center", alignItems: "center", p: 4, children: _jsx(CircularProgress, {}) }));
    }
    // Render error state
    if (!proposal) {
        return (_jsxs(Box, { p: 4, children: [_jsx(Alert, { severity: "error", children: error || 'Failed to load proposal' }), _jsx(Box, { display: "flex", justifyContent: "flex-end", mt: 2, children: _jsx(Button, { onClick: onClose, children: "Close" }) })] }));
    }
    return (_jsxs(Box, { p: 2, children: [_jsxs(Typography, { variant: "h6", gutterBottom: true, children: ["Send \"", proposal.title, "\""] }), error && (_jsx(Alert, { severity: "error", sx: { mb: 3 }, children: error })), success && (_jsx(Alert, { severity: "success", sx: { mb: 3 }, children: success })), _jsxs(Grid, { container: true, spacing: 3, children: [_jsxs(Grid, { item: true, xs: 12, md: 7, children: [_jsx(Typography, { variant: "subtitle2", gutterBottom: true, children: "Recipients" }), _jsx(Autocomplete, { multiple: true, options: contacts.map(contact => contact.email || ''), value: sendOptions.to, onChange: (_, value) => handleSendOptionsChange('to', value), renderInput: (params) => (_jsx(TextField, { ...params, label: "To", variant: "outlined", margin: "normal", fullWidth: true, required: true })), renderTags: (value, getTagProps) => value.map((option, index) => (_jsx(Chip, { label: option, ...getTagProps({ index }), size: "small" }))) }), _jsx(Autocomplete, { multiple: true, options: contacts.map(contact => contact.email || ''), value: sendOptions.cc, onChange: (_, value) => handleSendOptionsChange('cc', value), renderInput: (params) => (_jsx(TextField, { ...params, label: "CC", variant: "outlined", margin: "normal", fullWidth: true })), renderTags: (value, getTagProps) => value.map((option, index) => (_jsx(Chip, { label: option, ...getTagProps({ index }), size: "small" }))) }), _jsx(Autocomplete, { multiple: true, options: contacts.map(contact => contact.email || ''), value: sendOptions.bcc, onChange: (_, value) => handleSendOptionsChange('bcc', value), renderInput: (params) => (_jsx(TextField, { ...params, label: "BCC", variant: "outlined", margin: "normal", fullWidth: true })), renderTags: (value, getTagProps) => value.map((option, index) => (_jsx(Chip, { label: option, ...getTagProps({ index }), size: "small" }))) }), _jsx(TextField, { label: "Subject", value: sendOptions.subject, onChange: (e) => handleSendOptionsChange('subject', e.target.value), fullWidth: true, variant: "outlined", margin: "normal", required: true }), _jsx(TextField, { label: "Message", value: sendOptions.message, onChange: (e) => handleSendOptionsChange('message', e.target.value), fullWidth: true, multiline: true, rows: 8, variant: "outlined", margin: "normal" }), _jsx(LocalizationProvider, { dateAdapter: AdapterDateFns, children: _jsx(DateTimePicker, { label: "Expires At", value: sendOptions.expiresAt, onChange: (date) => handleSendOptionsChange('expiresAt', date), slotProps: {
                                        textField: {
                                            fullWidth: true,
                                            margin: 'normal',
                                            variant: 'outlined',
                                            InputProps: {
                                                startAdornment: (_jsx(InputAdornment, { position: "start", children: _jsx(CalendarIcon, {}) })),
                                            },
                                        },
                                    } }) })] }), _jsxs(Grid, { item: true, xs: 12, md: 5, children: [publicLink && (_jsxs(Paper, { variant: "outlined", sx: { p: 2, mb: 3 }, children: [_jsx(Typography, { variant: "subtitle2", gutterBottom: true, children: "Public Link" }), _jsx(TextField, { value: publicLink, fullWidth: true, variant: "outlined", size: "small", InputProps: {
                                            readOnly: true,
                                            endAdornment: (_jsx(InputAdornment, { position: "end", children: _jsx(IconButton, { edge: "end", onClick: handleCopyLink, size: "small", children: _jsx(CopyIcon, { fontSize: "small" }) }) })),
                                        } }), _jsx(Typography, { variant: "caption", color: "text.secondary", sx: { mt: 1, display: 'block' }, children: "This link can be shared with anyone to view the proposal." })] })), _jsxs(Paper, { variant: "outlined", sx: { p: 2 }, children: [_jsx(Typography, { variant: "subtitle2", gutterBottom: true, children: "Email Options" }), _jsxs(FormGroup, { children: [_jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: sendOptions.includeViewLink, onChange: (e) => handleSendOptionsChange('includeViewLink', e.target.checked) }), label: "Include View Link" }), _jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: sendOptions.includeDownloadLink, onChange: (e) => handleSendOptionsChange('includeDownloadLink', e.target.checked) }), label: "Include Download Link" }), _jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: sendOptions.includeAttachments, onChange: (e) => handleSendOptionsChange('includeAttachments', e.target.checked) }), label: "Include Attachments" })] }), sendOptions.includeAttachments && (_jsxs(Box, { mt: 2, children: [_jsx(Typography, { variant: "body2", gutterBottom: true, children: "Attachment Formats" }), _jsxs(FormGroup, { row: true, children: [_jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: sendOptions.attachmentFormats?.includes('pdf'), onChange: () => handleAttachmentFormatToggle('pdf') }), label: "PDF" }), _jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: sendOptions.attachmentFormats?.includes('docx'), onChange: () => handleAttachmentFormatToggle('docx') }), label: "DOCX" })] })] }))] }), _jsxs(Paper, { variant: "outlined", sx: { p: 2, mt: 3 }, children: [_jsx(Typography, { variant: "subtitle2", gutterBottom: true, children: "Proposal Information" }), _jsxs(Box, { children: [_jsx(Typography, { variant: "body2", color: "text.secondary", children: "Status" }), _jsx(Chip, { label: proposal.status?.toUpperCase() || 'DRAFT', color: proposal.status === 'accepted' ? 'success' :
                                                    proposal.status === 'rejected' ? 'error' :
                                                        proposal.status === 'sent' ? 'primary' :
                                                            proposal.status === 'viewed' ? 'info' :
                                                                'default', size: "small", sx: { mt: 0.5 } })] }), _jsx(Divider, { sx: { my: 1.5 } }), _jsxs(Box, { children: [_jsx(Typography, { variant: "body2", color: "text.secondary", children: "Download Formats" }), _jsxs(Box, { sx: { mt: 0.5 }, children: [proposal.downloadFormats?.map((format) => (_jsx(Chip, { label: format.toUpperCase(), size: "small", sx: { mr: 0.5 } }, format))), (!proposal.downloadFormats || proposal.downloadFormats.length === 0) && (_jsx(Typography, { variant: "body2", color: "text.secondary", children: "No download formats enabled" }))] })] })] })] })] }), _jsxs(Box, { display: "flex", justifyContent: "flex-end", mt: 4, children: [_jsx(Button, { variant: "outlined", onClick: onClose, sx: { mr: 2 }, disabled: sending, children: "Cancel" }), _jsx(Button, { variant: "contained", color: "primary", onClick: handleSend, disabled: sending || sendOptions.to.length === 0, startIcon: sending ? _jsx(CircularProgress, { size: 20, color: "inherit" }) : _jsx(SendIcon, {}), children: sending ? 'Sending...' : 'Send Proposal' })] })] }));
};
export default ProposalSendDialog;
