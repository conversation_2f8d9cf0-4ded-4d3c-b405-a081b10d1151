import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Box, Button, IconButton, TextField, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Select, MenuItem, FormControl, InputLabel, InputAdornment, Tooltip } from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon, DragIndicator as DragIndicatorIcon } from '@mui/icons-material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { formatCurrency } from '../../utils/formatters';
import { v4 as uuidv4 } from 'uuid';
/**
 * Proposal Pricing Editor Component
 *
 * This component provides a UI for editing proposal pricing.
 * It supports adding, editing, and removing pricing items.
 */
const ProposalPricingEditor = ({ pricing, onChange }) => {
    // Initialize pricing with defaults if not provided
    const [pricingState, setPricingState] = useState(pricing || {
        currency: 'USD',
        items: [],
        subtotal: 0,
        total: 0
    });
    // Update pricing when props change
    useEffect(() => {
        if (pricing) {
            setPricingState(pricing);
        }
    }, [pricing]);
    // Calculate totals
    useEffect(() => {
        const subtotal = pricingState.items.reduce((sum, item) => sum + item.total, 0);
        const discount = pricingState.discount || 0;
        const tax = pricingState.tax || 0;
        const total = subtotal - discount + tax;
        setPricingState(prev => ({
            ...prev,
            subtotal,
            total
        }));
        // Notify parent component
        onChange({
            ...pricingState,
            subtotal,
            total
        });
    }, [pricingState.items, pricingState.discount, pricingState.tax]);
    // Add a new item
    const handleAddItem = () => {
        const newItem = {
            id: uuidv4(),
            name: 'New Item',
            quantity: 1,
            unitPrice: 0,
            total: 0
        };
        setPricingState(prev => ({
            ...prev,
            items: [...prev.items, newItem]
        }));
    };
    // Update an item
    const handleUpdateItem = (id, field, value) => {
        setPricingState(prev => ({
            ...prev,
            items: prev.items.map(item => {
                if (item.id === id) {
                    const updatedItem = { ...item, [field]: value };
                    // Recalculate total if quantity or unitPrice changed
                    if (field === 'quantity' || field === 'unitPrice') {
                        updatedItem.total = updatedItem.quantity * updatedItem.unitPrice;
                    }
                    return updatedItem;
                }
                return item;
            })
        }));
    };
    // Delete an item
    const handleDeleteItem = (id) => {
        setPricingState(prev => ({
            ...prev,
            items: prev.items.filter(item => item.id !== id)
        }));
    };
    // Update currency
    const handleCurrencyChange = (currency) => {
        setPricingState(prev => ({
            ...prev,
            currency
        }));
    };
    // Update discount
    const handleDiscountChange = (discount) => {
        setPricingState(prev => ({
            ...prev,
            discount
        }));
    };
    // Update tax
    const handleTaxChange = (tax) => {
        setPricingState(prev => ({
            ...prev,
            tax
        }));
    };
    // Handle drag and drop reordering
    const handleDragEnd = (result) => {
        if (!result.destination)
            return;
        const items = Array.from(pricingState.items);
        const [reorderedItem] = items.splice(result.source.index, 1);
        items.splice(result.destination.index, 0, reorderedItem);
        setPricingState(prev => ({
            ...prev,
            items
        }));
    };
    return (_jsxs(Box, { children: [_jsx(Box, { mb: 2, children: _jsxs(FormControl, { variant: "outlined", size: "small", sx: { minWidth: 120 }, children: [_jsx(InputLabel, { children: "Currency" }), _jsxs(Select, { value: pricingState.currency, onChange: (e) => handleCurrencyChange(e.target.value), label: "Currency", children: [_jsx(MenuItem, { value: "USD", children: "USD ($)" }), _jsx(MenuItem, { value: "EUR", children: "EUR (\u20AC)" }), _jsx(MenuItem, { value: "GBP", children: "GBP (\u00A3)" }), _jsx(MenuItem, { value: "CAD", children: "CAD (C$)" }), _jsx(MenuItem, { value: "AUD", children: "AUD (A$)" }), _jsx(MenuItem, { value: "JPY", children: "JPY (\u00A5)" })] })] }) }), _jsx(TableContainer, { component: Paper, variant: "outlined", children: _jsxs(Table, { size: "small", children: [_jsx(TableHead, { children: _jsxs(TableRow, { children: [_jsx(TableCell, { width: "40px" }), _jsx(TableCell, { children: "Item" }), _jsx(TableCell, { align: "right", width: "100px", children: "Quantity" }), _jsx(TableCell, { align: "right", width: "150px", children: "Unit Price" }), _jsx(TableCell, { align: "right", width: "150px", children: "Total" }), _jsx(TableCell, { width: "50px" })] }) }), _jsx(DragDropContext, { onDragEnd: handleDragEnd, children: _jsx(Droppable, { droppableId: "pricing-items", children: (provided) => (_jsxs(TableBody, { ...provided.droppableProps, ref: provided.innerRef, children: [pricingState.items.map((item, index) => (_jsx(Draggable, { draggableId: item.id, index: index, children: (provided) => (_jsxs(TableRow, { ref: provided.innerRef, ...provided.draggableProps, children: [_jsx(TableCell, { ...provided.dragHandleProps, children: _jsx(DragIndicatorIcon, { fontSize: "small", sx: { cursor: 'grab' } }) }), _jsxs(TableCell, { children: [_jsx(TextField, { value: item.name, onChange: (e) => handleUpdateItem(item.id, 'name', e.target.value), fullWidth: true, variant: "outlined", size: "small", placeholder: "Item name" }), _jsx(TextField, { value: item.description || '', onChange: (e) => handleUpdateItem(item.id, 'description', e.target.value), fullWidth: true, variant: "outlined", size: "small", placeholder: "Description (optional)", sx: { mt: 1 } })] }), _jsx(TableCell, { align: "right", children: _jsx(TextField, { type: "number", value: item.quantity, onChange: (e) => handleUpdateItem(item.id, 'quantity', Number(e.target.value)), variant: "outlined", size: "small", inputProps: { min: 1, step: 1 } }) }), _jsx(TableCell, { align: "right", children: _jsx(TextField, { type: "number", value: item.unitPrice, onChange: (e) => handleUpdateItem(item.id, 'unitPrice', Number(e.target.value)), variant: "outlined", size: "small", InputProps: {
                                                                startAdornment: (_jsx(InputAdornment, { position: "start", children: pricingState.currency === 'USD' ? '$' :
                                                                        pricingState.currency === 'EUR' ? '€' :
                                                                            pricingState.currency === 'GBP' ? '£' :
                                                                                pricingState.currency === 'CAD' ? 'C$' :
                                                                                    pricingState.currency === 'AUD' ? 'A$' :
                                                                                        pricingState.currency === 'JPY' ? '¥' : '' })),
                                                            }, inputProps: { min: 0, step: 0.01 } }) }), _jsx(TableCell, { align: "right", children: _jsx(Typography, { variant: "body2", children: formatCurrency(item.total, pricingState.currency) }) }), _jsx(TableCell, { children: _jsx(Tooltip, { title: "Delete Item", children: _jsx(IconButton, { size: "small", color: "error", onClick: () => handleDeleteItem(item.id), children: _jsx(DeleteIcon, { fontSize: "small" }) }) }) })] })) }, item.id))), provided.placeholder, _jsx(TableRow, { children: _jsx(TableCell, { colSpan: 6, align: "center", sx: { border: 'none', pt: 2 }, children: _jsx(Button, { startIcon: _jsx(AddIcon, {}), onClick: handleAddItem, variant: "outlined", size: "small", children: "Add Item" }) }) })] })) }) })] }) }), _jsxs(Box, { mt: 3, display: "flex", flexDirection: "column", alignItems: "flex-end", children: [_jsxs(Box, { display: "flex", alignItems: "center", mb: 1, children: [_jsx(Typography, { variant: "body1", sx: { mr: 2, width: 100 }, children: "Subtotal:" }), _jsx(Typography, { variant: "body1", fontWeight: "medium", children: formatCurrency(pricingState.subtotal, pricingState.currency) })] }), _jsxs(Box, { display: "flex", alignItems: "center", mb: 1, children: [_jsx(Typography, { variant: "body1", sx: { mr: 2, width: 100 }, children: "Discount:" }), _jsx(TextField, { type: "number", value: pricingState.discount || 0, onChange: (e) => handleDiscountChange(Number(e.target.value)), variant: "outlined", size: "small", InputProps: {
                                    startAdornment: (_jsx(InputAdornment, { position: "start", children: pricingState.currency === 'USD' ? '$' :
                                            pricingState.currency === 'EUR' ? '€' :
                                                pricingState.currency === 'GBP' ? '£' :
                                                    pricingState.currency === 'CAD' ? 'C$' :
                                                        pricingState.currency === 'AUD' ? 'A$' :
                                                            pricingState.currency === 'JPY' ? '¥' : '' })),
                                }, inputProps: { min: 0, step: 0.01 } })] }), _jsxs(Box, { display: "flex", alignItems: "center", mb: 1, children: [_jsx(Typography, { variant: "body1", sx: { mr: 2, width: 100 }, children: "Tax:" }), _jsx(TextField, { type: "number", value: pricingState.tax || 0, onChange: (e) => handleTaxChange(Number(e.target.value)), variant: "outlined", size: "small", InputProps: {
                                    startAdornment: (_jsx(InputAdornment, { position: "start", children: pricingState.currency === 'USD' ? '$' :
                                            pricingState.currency === 'EUR' ? '€' :
                                                pricingState.currency === 'GBP' ? '£' :
                                                    pricingState.currency === 'CAD' ? 'C$' :
                                                        pricingState.currency === 'AUD' ? 'A$' :
                                                            pricingState.currency === 'JPY' ? '¥' : '' })),
                                }, inputProps: { min: 0, step: 0.01 } })] }), _jsxs(Box, { display: "flex", alignItems: "center", children: [_jsx(Typography, { variant: "body1", fontWeight: "bold", sx: { mr: 2, width: 100 }, children: "Total:" }), _jsx(Typography, { variant: "body1", fontWeight: "bold", children: formatCurrency(pricingState.total, pricingState.currency) })] })] })] }));
};
export default ProposalPricingEditor;
