import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Box, Card, CardContent, Typography, Grid, Tabs, Tab, Paper, CircularProgress, Button, Chip, useTheme, useMediaQuery, Alert, List, ListItem, ListItemIcon, ListItemText, ListItemSecondaryAction, IconButton, Tooltip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, } from '@mui/material';
import { <PERSON><PERSON>hart, Bar, LineChart, Line, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, Legend, ResponsiveContainer, } from 'recharts';
import { Visibility as ViewIcon, Download as DownloadIcon, CheckCircle as AcceptIcon, Cancel as RejectIcon, Public as PublicIcon, AccessTime as TimeIcon, Link as LinkIcon, Refresh as RefreshIcon, Info as InfoIcon, } from '@mui/icons-material';
import { format, formatDistance } from 'date-fns';
import { ProposalService } from '../../../services/proposal-service';
import { formatPercentage } from '../../../utils/formatters';
/**
 * ProposalAnalyticsDetail Component
 *
 * This component displays detailed analytics for a single proposal.
 */
const ProposalAnalyticsDetail = ({ proposalId, }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    // State
    const [activeTab, setActiveTab] = useState(0);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [analytics, setAnalytics] = useState(null);
    const [proposal, setProposal] = useState(null);
    // Fetch analytics data
    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);
                // Fetch proposal details
                const proposalData = await ProposalService.getProposalById(proposalId, 'default'); // TODO: Get tenant ID from context
                setProposal(proposalData);
                // Fetch analytics
                const analyticsData = await ProposalService.getProposalAnalytics(proposalId, 'default'); // TODO: Get tenant ID from context
                setAnalytics(analyticsData);
            }
            catch (err) {
                console.error('Error fetching proposal analytics:', err);
                setError('Failed to load analytics data. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchData();
    }, [proposalId]);
    // Handle tab change
    const handleTabChange = (event, newValue) => {
        setActiveTab(newValue);
    };
    // Handle refresh
    const handleRefresh = () => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);
                // Fetch proposal details
                const proposalData = await ProposalService.getProposalById(proposalId, 'default'); // TODO: Get tenant ID from context
                setProposal(proposalData);
                // Fetch analytics
                const analyticsData = await ProposalService.getProposalAnalytics(proposalId, 'default'); // TODO: Get tenant ID from context
                setAnalytics(analyticsData);
            }
            catch (err) {
                console.error('Error fetching proposal analytics:', err);
                setError('Failed to load analytics data. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchData();
    };
    // Prepare chart data
    const prepareViewsByDateChart = () => {
        if (!analytics || !analytics.viewsByDate)
            return [];
        return Object.entries(analytics.viewsByDate).map(([date, count]) => ({
            date,
            views: count,
        })).sort((a, b) => a.date.localeCompare(b.date));
    };
    const prepareViewsByHourChart = () => {
        if (!analytics || !analytics.viewsByHour)
            return [];
        return Object.entries(analytics.viewsByHour).map(([hour, count]) => ({
            hour: `${hour}:00`,
            views: count,
        })).sort((a, b) => a.hour.localeCompare(b.hour));
    };
    const prepareViewsByDeviceChart = () => {
        if (!analytics || !analytics.viewsByDevice)
            return [];
        return Object.entries(analytics.viewsByDevice).map(([device, count]) => ({
            device,
            count,
        }));
    };
    const prepareDownloadsByFormatChart = () => {
        if (!analytics || !analytics.downloadsByFormat)
            return [];
        return Object.entries(analytics.downloadsByFormat).map(([format, count]) => ({
            format,
            count,
        }));
    };
    // Render loading state
    if (loading) {
        return (_jsx(Box, { display: "flex", justifyContent: "center", alignItems: "center", height: "400px", children: _jsx(CircularProgress, {}) }));
    }
    // Render error state
    if (error) {
        return (_jsxs(Alert, { severity: "error", sx: { mb: 2 }, children: [error, _jsx(Button, { variant: "outlined", size: "small", onClick: handleRefresh, startIcon: _jsx(RefreshIcon, {}), sx: { ml: 2 }, children: "Retry" })] }));
    }
    // Render empty state
    if (!analytics || !proposal) {
        return (_jsx(Alert, { severity: "info", children: "No analytics data available for this proposal." }));
    }
    // Chart colors
    const COLORS = [
        theme.palette.primary.main,
        theme.palette.secondary.main,
        theme.palette.success.main,
        theme.palette.error.main,
        theme.palette.warning.main,
        theme.palette.info.main,
    ];
    return (_jsxs(Box, { children: [_jsxs(Box, { mb: 3, children: [_jsx(Typography, { variant: "h4", gutterBottom: true, children: proposal.title }), _jsxs(Box, { display: "flex", alignItems: "center", flexWrap: "wrap", gap: 1, children: [_jsx(Chip, { label: proposal.status.toUpperCase(), color: proposal.status === 'accepted' ? 'success' :
                                    proposal.status === 'rejected' ? 'error' :
                                        proposal.status === 'viewed' ? 'info' :
                                            proposal.status === 'sent' ? 'primary' :
                                                'default', size: "small" }), _jsxs(Typography, { variant: "body2", color: "text.secondary", children: ["Created: ", format(new Date(proposal.createdAt), 'PPP')] }), proposal.sentAt && (_jsxs(Typography, { variant: "body2", color: "text.secondary", children: ["Sent: ", format(new Date(proposal.sentAt), 'PPP')] })), _jsx(Button, { variant: "outlined", size: "small", onClick: handleRefresh, startIcon: _jsx(RefreshIcon, {}), sx: { ml: 'auto' }, children: "Refresh" })] })] }), _jsxs(Grid, { container: true, spacing: 3, mb: 3, children: [_jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "subtitle2", color: "text.secondary", gutterBottom: true, children: "Total Views" }), _jsx(Typography, { variant: "h4", children: analytics.views }), _jsxs(Box, { display: "flex", alignItems: "center", mt: 1, children: [_jsx(ViewIcon, { fontSize: "small", color: "primary", sx: { mr: 1 } }), _jsxs(Typography, { variant: "body2", color: "text.secondary", children: [analytics.uniqueViews, " Unique Views"] })] })] }) }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "subtitle2", color: "text.secondary", gutterBottom: true, children: "Downloads" }), _jsx(Typography, { variant: "h4", children: analytics.downloads }), _jsxs(Box, { display: "flex", alignItems: "center", mt: 1, children: [_jsx(DownloadIcon, { fontSize: "small", color: "secondary", sx: { mr: 1 } }), _jsxs(Typography, { variant: "body2", color: "text.secondary", children: [Object.keys(analytics.downloadsByFormat || {}).length, " Formats"] })] })] }) }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "subtitle2", color: "text.secondary", gutterBottom: true, children: "Average View Time" }), _jsxs(Typography, { variant: "h4", children: [Math.round(analytics.averageViewDuration / 1000), " sec"] }), _jsxs(Box, { display: "flex", alignItems: "center", mt: 1, children: [_jsx(TimeIcon, { fontSize: "small", color: "info", sx: { mr: 1 } }), _jsxs(Typography, { variant: "body2", color: "text.secondary", children: [analytics.views > 0 ? formatPercentage(analytics.uniqueViews / analytics.views) : '0%', " Return Rate"] })] })] }) }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "subtitle2", color: "text.secondary", gutterBottom: true, children: "Public Link" }), _jsxs(Box, { display: "flex", alignItems: "center", children: [_jsx(Typography, { variant: "h6", noWrap: true, sx: { maxWidth: '100%', overflow: 'hidden', textOverflow: 'ellipsis' }, children: proposal.publicToken }), _jsx(Tooltip, { title: "Copy Link", children: _jsx(IconButton, { size: "small", onClick: () => {
                                                        navigator.clipboard.writeText(proposal.publicUrl);
                                                    }, children: _jsx(LinkIcon, { fontSize: "small" }) }) })] }), _jsxs(Box, { display: "flex", alignItems: "center", mt: 1, children: [_jsx(PublicIcon, { fontSize: "small", color: "action", sx: { mr: 1 } }), _jsx(Typography, { variant: "body2", color: "text.secondary", children: proposal.publicAccessEnabled ? 'Public Access Enabled' : 'Public Access Disabled' })] })] }) }) })] }), _jsx(Paper, { sx: { mb: 3 }, children: _jsxs(Tabs, { value: activeTab, onChange: handleTabChange, indicatorColor: "primary", textColor: "primary", variant: isMobile ? "scrollable" : "fullWidth", scrollButtons: isMobile ? "auto" : undefined, children: [_jsx(Tab, { label: "Overview" }), _jsx(Tab, { label: "Views" }), _jsx(Tab, { label: "Downloads" }), _jsx(Tab, { label: "Events" })] }) }), activeTab === 0 && (_jsxs(Grid, { container: true, spacing: 3, children: [_jsx(Grid, { item: true, xs: 12, md: 6, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Views by Date" }), _jsx(Box, { height: 300, children: _jsx(ResponsiveContainer, { width: "100%", height: "100%", children: _jsxs(LineChart, { data: prepareViewsByDateChart(), margin: { top: 5, right: 30, left: 20, bottom: 5 }, children: [_jsx(CartesianGrid, { strokeDasharray: "3 3" }), _jsx(XAxis, { dataKey: "date" }), _jsx(YAxis, {}), _jsx(RechartsTooltip, {}), _jsx(Legend, {}), _jsx(Line, { type: "monotone", dataKey: "views", stroke: theme.palette.primary.main, activeDot: { r: 8 } })] }) }) })] }) }) }), _jsx(Grid, { item: true, xs: 12, md: 6, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Views by Hour" }), _jsx(Box, { height: 300, children: _jsx(ResponsiveContainer, { width: "100%", height: "100%", children: _jsxs(BarChart, { data: prepareViewsByHourChart(), margin: { top: 5, right: 30, left: 20, bottom: 5 }, children: [_jsx(CartesianGrid, { strokeDasharray: "3 3" }), _jsx(XAxis, { dataKey: "hour" }), _jsx(YAxis, {}), _jsx(RechartsTooltip, {}), _jsx(Legend, {}), _jsx(Bar, { dataKey: "views", fill: theme.palette.primary.main })] }) }) })] }) }) }), _jsx(Grid, { item: true, xs: 12, md: 6, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Views by Device" }), _jsx(Box, { height: 300, children: _jsx(ResponsiveContainer, { width: "100%", height: "100%", children: _jsxs(PieChart, { children: [_jsx(Pie, { data: prepareViewsByDeviceChart(), cx: "50%", cy: "50%", labelLine: false, label: ({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`, outerRadius: 80, fill: "#8884d8", dataKey: "count", nameKey: "device", children: prepareViewsByDeviceChart().map((entry, index) => (_jsx(Cell, { fill: COLORS[index % COLORS.length] }, `cell-${index}`))) }), _jsx(RechartsTooltip, {})] }) }) })] }) }) }), _jsx(Grid, { item: true, xs: 12, md: 6, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Downloads by Format" }), _jsx(Box, { height: 300, children: _jsx(ResponsiveContainer, { width: "100%", height: "100%", children: _jsxs(PieChart, { children: [_jsx(Pie, { data: prepareDownloadsByFormatChart(), cx: "50%", cy: "50%", labelLine: false, label: ({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`, outerRadius: 80, fill: "#8884d8", dataKey: "count", nameKey: "format", children: prepareDownloadsByFormatChart().map((entry, index) => (_jsx(Cell, { fill: COLORS[index % COLORS.length] }, `cell-${index}`))) }), _jsx(RechartsTooltip, {})] }) }) })] }) }) })] })), activeTab === 1 && (_jsx(Grid, { container: true, spacing: 3, children: _jsx(Grid, { item: true, xs: 12, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "View History" }), _jsx(TableContainer, { children: _jsxs(Table, { children: [_jsx(TableHead, { children: _jsxs(TableRow, { children: [_jsx(TableCell, { children: "Date & Time" }), _jsx(TableCell, { children: "Location" }), _jsx(TableCell, { children: "Device" }), _jsx(TableCell, { children: "Referrer" })] }) }), _jsx(TableBody, { children: analytics.events
                                                    .filter((event) => event.eventType === 'view')
                                                    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
                                                    .map((event, index) => (_jsxs(TableRow, { children: [_jsx(TableCell, { children: format(new Date(event.timestamp), 'PPp') }), _jsx(TableCell, { children: event.data.location || 'Unknown' }), _jsx(TableCell, { children: event.data.userAgent ?
                                                                (event.data.userAgent.includes('iPhone') ? 'iPhone' :
                                                                    event.data.userAgent.includes('Android') ? 'Android' :
                                                                        event.data.userAgent.includes('Windows') ? 'Windows' :
                                                                            event.data.userAgent.includes('Mac') ? 'Mac' :
                                                                                'Other') : 'Unknown' }), _jsx(TableCell, { children: event.data.referrer || 'Direct' })] }, index))) })] }) })] }) }) }) })), activeTab === 2 && (_jsx(Grid, { container: true, spacing: 3, children: _jsx(Grid, { item: true, xs: 12, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Download History" }), _jsx(TableContainer, { children: _jsxs(Table, { children: [_jsx(TableHead, { children: _jsxs(TableRow, { children: [_jsx(TableCell, { children: "Date & Time" }), _jsx(TableCell, { children: "Format" }), _jsx(TableCell, { children: "Location" }), _jsx(TableCell, { children: "Device" })] }) }), _jsx(TableBody, { children: analytics.events
                                                    .filter((event) => event.eventType === 'download')
                                                    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
                                                    .map((event, index) => (_jsxs(TableRow, { children: [_jsx(TableCell, { children: format(new Date(event.timestamp), 'PPp') }), _jsx(TableCell, { children: event.data.format || 'Unknown' }), _jsx(TableCell, { children: event.data.location || 'Unknown' }), _jsx(TableCell, { children: event.data.userAgent ?
                                                                (event.data.userAgent.includes('iPhone') ? 'iPhone' :
                                                                    event.data.userAgent.includes('Android') ? 'Android' :
                                                                        event.data.userAgent.includes('Windows') ? 'Windows' :
                                                                            event.data.userAgent.includes('Mac') ? 'Mac' :
                                                                                'Other') : 'Unknown' })] }, index))) })] }) })] }) }) }) })), activeTab === 3 && (_jsx(Grid, { container: true, spacing: 3, children: _jsx(Grid, { item: true, xs: 12, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "All Events" }), _jsx(List, { children: analytics.events
                                        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
                                        .map((event, index) => (_jsxs(ListItem, { divider: index < analytics.events.length - 1, children: [_jsx(ListItemIcon, { children: event.eventType === 'view' ? _jsx(ViewIcon, { color: "primary" }) :
                                                    event.eventType === 'download' ? _jsx(DownloadIcon, { color: "secondary" }) :
                                                        event.eventType === 'accept' ? _jsx(AcceptIcon, { color: "success" }) :
                                                            event.eventType === 'reject' ? _jsx(RejectIcon, { color: "error" }) :
                                                                _jsx(InfoIcon, {}) }), _jsx(ListItemText, { primary: event.eventType === 'view' ? 'Proposal Viewed' :
                                                    event.eventType === 'download' ? `Proposal Downloaded (${event.data.format})` :
                                                        event.eventType === 'accept' ? 'Proposal Accepted' :
                                                            event.eventType === 'reject' ? 'Proposal Rejected' :
                                                                'Event', secondary: `${format(new Date(event.timestamp), 'PPp')} • ${formatDistance(new Date(event.timestamp), new Date(), { addSuffix: true })}` }), _jsx(ListItemSecondaryAction, { children: _jsx(Tooltip, { title: event.data.location || 'Unknown Location', children: _jsx(IconButton, { edge: "end", size: "small", children: _jsx(PublicIcon, { fontSize: "small" }) }) }) })] }, index))) })] }) }) }) }))] }));
};
export default ProposalAnalyticsDetail;
