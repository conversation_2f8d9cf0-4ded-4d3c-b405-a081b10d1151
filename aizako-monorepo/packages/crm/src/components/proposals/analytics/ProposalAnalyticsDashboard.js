import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Box, Card, CardContent, Typography, Grid, Tabs, Tab, Paper, CircularProgress, Button, Chip, useTheme, useMediaQuery, Alert, Select, MenuItem, FormControl, InputLabel, } from '@mui/material';
import { BarChart, Bar, LineChart, Line, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, } from 'recharts';
import { DatePicker, LocalizationProvider, } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { subDays } from 'date-fns';
import { Visibility as ViewIcon, CheckCircle as AcceptIcon, Cancel as RejectIcon, Refresh as RefreshIcon, } from '@mui/icons-material';
import { ProposalService } from '../../../services/proposal-service';
import { formatPercentage } from '../../../utils/formatters';
/**
 * ProposalAnalyticsDashboard Component
 *
 * This component displays analytics for proposals, including views, downloads,
 * acceptance rates, and other metrics.
 */
const ProposalAnalyticsDashboard = ({ tenantId, }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
    // State
    const [activeTab, setActiveTab] = useState(0);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [analytics, setAnalytics] = useState(null);
    const [dateRange, setDateRange] = useState({
        startDate: subDays(new Date(), 30),
        endDate: new Date(),
    });
    const [filterStatus, setFilterStatus] = useState('all');
    // Fetch analytics data
    useEffect(() => {
        const fetchAnalytics = async () => {
            try {
                setLoading(true);
                setError(null);
                const response = await ProposalService.getTenantAnalytics(tenantId, dateRange.startDate, dateRange.endDate);
                setAnalytics(response);
            }
            catch (err) {
                console.error('Error fetching analytics:', err);
                setError('Failed to load analytics data. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchAnalytics();
    }, [tenantId, dateRange]);
    // Handle tab change
    const handleTabChange = (event, newValue) => {
        setActiveTab(newValue);
    };
    // Handle date range change
    const handleStartDateChange = (date) => {
        if (date) {
            setDateRange(prev => ({
                ...prev,
                startDate: date,
            }));
        }
    };
    const handleEndDateChange = (date) => {
        if (date) {
            setDateRange(prev => ({
                ...prev,
                endDate: date,
            }));
        }
    };
    // Handle refresh
    const handleRefresh = () => {
        // Re-fetch analytics data
        const fetchAnalytics = async () => {
            try {
                setLoading(true);
                setError(null);
                const response = await ProposalService.getTenantAnalytics(tenantId, dateRange.startDate, dateRange.endDate);
                setAnalytics(response);
            }
            catch (err) {
                console.error('Error fetching analytics:', err);
                setError('Failed to load analytics data. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchAnalytics();
    };
    // Prepare chart data
    const prepareViewsByDateChart = () => {
        if (!analytics || !analytics.viewsByDate)
            return [];
        return Object.entries(analytics.viewsByDate).map(([date, count]) => ({
            date,
            views: count,
        })).sort((a, b) => a.date.localeCompare(b.date));
    };
    const prepareProposalsByStatusChart = () => {
        if (!analytics || !analytics.proposalsByStatus)
            return [];
        return Object.entries(analytics.proposalsByStatus).map(([status, count]) => ({
            status,
            count,
        }));
    };
    const prepareTopProposalsChart = () => {
        if (!analytics || !analytics.topProposals)
            return [];
        return analytics.topProposals
            .filter((proposal) => {
            if (filterStatus === 'all')
                return true;
            return proposal.status === filterStatus;
        })
            .slice(0, 5)
            .map((proposal) => ({
            name: proposal.title,
            views: proposal.views,
            downloads: proposal.downloads,
        }));
    };
    // Render loading state
    if (loading) {
        return (_jsx(Box, { display: "flex", justifyContent: "center", alignItems: "center", height: "400px", children: _jsx(CircularProgress, {}) }));
    }
    // Render error state
    if (error) {
        return (_jsxs(Alert, { severity: "error", sx: { mb: 2 }, children: [error, _jsx(Button, { variant: "outlined", size: "small", onClick: handleRefresh, startIcon: _jsx(RefreshIcon, {}), sx: { ml: 2 }, children: "Retry" })] }));
    }
    // Render empty state
    if (!analytics) {
        return (_jsx(Alert, { severity: "info", children: "No analytics data available. Create and share some proposals to see analytics." }));
    }
    // Chart colors
    const COLORS = [
        theme.palette.primary.main,
        theme.palette.secondary.main,
        theme.palette.success.main,
        theme.palette.error.main,
        theme.palette.warning.main,
        theme.palette.info.main,
    ];
    return (_jsxs(Box, { children: [_jsxs(Box, { mb: 3, children: [_jsx(Typography, { variant: "h4", gutterBottom: true, children: "Proposal Analytics" }), _jsx(Typography, { variant: "body1", color: "text.secondary", children: "Track the performance of your proposals and gain insights into customer engagement." })] }), _jsx(Paper, { sx: { p: 2, mb: 3 }, children: _jsxs(Box, { display: "flex", flexDirection: { xs: 'column', sm: 'row' }, alignItems: "center", gap: 2, children: [_jsxs(LocalizationProvider, { dateAdapter: AdapterDateFns, children: [_jsx(DatePicker, { label: "Start Date", value: dateRange.startDate, onChange: handleStartDateChange, slotProps: { textField: { size: 'small', fullWidth: true } } }), _jsx(DatePicker, { label: "End Date", value: dateRange.endDate, onChange: handleEndDateChange, slotProps: { textField: { size: 'small', fullWidth: true } } })] }), _jsx(Button, { variant: "contained", onClick: handleRefresh, startIcon: _jsx(RefreshIcon, {}), sx: { ml: { xs: 0, sm: 2 } }, children: "Refresh" })] }) }), _jsxs(Grid, { container: true, spacing: 3, mb: 3, children: [_jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "subtitle2", color: "text.secondary", gutterBottom: true, children: "Total Proposals" }), _jsx(Typography, { variant: "h4", children: analytics.totalProposals }), _jsxs(Box, { display: "flex", alignItems: "center", mt: 1, children: [_jsx(Chip, { size: "small", label: `${analytics.proposalsByStatus?.draft || 0} Draft`, sx: { mr: 1 } }), _jsx(Chip, { size: "small", label: `${analytics.proposalsByStatus?.sent || 0} Sent` })] })] }) }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "subtitle2", color: "text.secondary", gutterBottom: true, children: "Total Views" }), _jsx(Typography, { variant: "h4", children: analytics.totalViews }), _jsxs(Box, { display: "flex", alignItems: "center", mt: 1, children: [_jsx(ViewIcon, { fontSize: "small", color: "primary", sx: { mr: 1 } }), _jsxs(Typography, { variant: "body2", color: "text.secondary", children: [formatPercentage(analytics.viewRate), " View Rate"] })] })] }) }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "subtitle2", color: "text.secondary", gutterBottom: true, children: "Acceptance Rate" }), _jsx(Typography, { variant: "h4", children: formatPercentage(analytics.acceptanceRate) }), _jsxs(Box, { display: "flex", alignItems: "center", mt: 1, children: [_jsx(AcceptIcon, { fontSize: "small", color: "success", sx: { mr: 1 } }), _jsxs(Typography, { variant: "body2", color: "text.secondary", children: [analytics.totalAcceptances, " Accepted"] })] })] }) }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "subtitle2", color: "text.secondary", gutterBottom: true, children: "Rejection Rate" }), _jsx(Typography, { variant: "h4", children: formatPercentage(analytics.rejectionRate) }), _jsxs(Box, { display: "flex", alignItems: "center", mt: 1, children: [_jsx(RejectIcon, { fontSize: "small", color: "error", sx: { mr: 1 } }), _jsxs(Typography, { variant: "body2", color: "text.secondary", children: [analytics.totalRejections, " Rejected"] })] })] }) }) })] }), _jsx(Paper, { sx: { mb: 3 }, children: _jsxs(Tabs, { value: activeTab, onChange: handleTabChange, indicatorColor: "primary", textColor: "primary", variant: "fullWidth", children: [_jsx(Tab, { label: "Overview" }), _jsx(Tab, { label: "Proposals" }), _jsx(Tab, { label: "Engagement" })] }) }), activeTab === 0 && (_jsxs(Grid, { container: true, spacing: 3, children: [_jsx(Grid, { item: true, xs: 12, md: 8, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Views by Date" }), _jsx(Box, { height: 300, children: _jsx(ResponsiveContainer, { width: "100%", height: "100%", children: _jsxs(LineChart, { data: prepareViewsByDateChart(), margin: { top: 5, right: 30, left: 20, bottom: 5 }, children: [_jsx(CartesianGrid, { strokeDasharray: "3 3" }), _jsx(XAxis, { dataKey: "date" }), _jsx(YAxis, {}), _jsx(Tooltip, {}), _jsx(Legend, {}), _jsx(Line, { type: "monotone", dataKey: "views", stroke: theme.palette.primary.main, activeDot: { r: 8 } })] }) }) })] }) }) }), _jsx(Grid, { item: true, xs: 12, md: 4, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Proposals by Status" }), _jsx(Box, { height: 300, children: _jsx(ResponsiveContainer, { width: "100%", height: "100%", children: _jsxs(PieChart, { children: [_jsx(Pie, { data: prepareProposalsByStatusChart(), cx: "50%", cy: "50%", labelLine: false, label: ({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`, outerRadius: 80, fill: "#8884d8", dataKey: "count", children: prepareProposalsByStatusChart().map((entry, index) => (_jsx(Cell, { fill: COLORS[index % COLORS.length] }, `cell-${index}`))) }), _jsx(Tooltip, {})] }) }) })] }) }) })] })), activeTab === 1 && (_jsx(Grid, { container: true, spacing: 3, children: _jsx(Grid, { item: true, xs: 12, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2, children: [_jsx(Typography, { variant: "h6", children: "Top Proposals" }), _jsxs(FormControl, { size: "small", sx: { minWidth: 120 }, children: [_jsx(InputLabel, { id: "status-filter-label", children: "Status" }), _jsxs(Select, { labelId: "status-filter-label", value: filterStatus, label: "Status", onChange: (e) => setFilterStatus(e.target.value), children: [_jsx(MenuItem, { value: "all", children: "All" }), _jsx(MenuItem, { value: "draft", children: "Draft" }), _jsx(MenuItem, { value: "sent", children: "Sent" }), _jsx(MenuItem, { value: "viewed", children: "Viewed" }), _jsx(MenuItem, { value: "accepted", children: "Accepted" }), _jsx(MenuItem, { value: "rejected", children: "Rejected" })] })] })] }), _jsx(Box, { height: 400, children: _jsx(ResponsiveContainer, { width: "100%", height: "100%", children: _jsxs(BarChart, { data: prepareTopProposalsChart(), margin: { top: 5, right: 30, left: 20, bottom: 5 }, children: [_jsx(CartesianGrid, { strokeDasharray: "3 3" }), _jsx(XAxis, { dataKey: "name" }), _jsx(YAxis, {}), _jsx(Tooltip, {}), _jsx(Legend, {}), _jsx(Bar, { dataKey: "views", fill: theme.palette.primary.main, name: "Views" }), _jsx(Bar, { dataKey: "downloads", fill: theme.palette.secondary.main, name: "Downloads" })] }) }) })] }) }) }) })), activeTab === 2 && (_jsx(Grid, { container: true, spacing: 3, children: _jsx(Grid, { item: true, xs: 12, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Engagement Metrics" }), _jsxs(Grid, { container: true, spacing: 2, children: [_jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsxs(Box, { textAlign: "center", p: 2, children: [_jsx(Typography, { variant: "h4", color: "primary", children: formatPercentage(analytics.viewRate) }), _jsx(Typography, { variant: "body2", color: "text.secondary", children: "View Rate" })] }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsxs(Box, { textAlign: "center", p: 2, children: [_jsx(Typography, { variant: "h4", color: "secondary", children: analytics.totalDownloads }), _jsx(Typography, { variant: "body2", color: "text.secondary", children: "Downloads" })] }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsxs(Box, { textAlign: "center", p: 2, children: [_jsx(Typography, { variant: "h4", color: "success.main", children: formatPercentage(analytics.acceptanceRate) }), _jsx(Typography, { variant: "body2", color: "text.secondary", children: "Acceptance Rate" })] }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsxs(Box, { textAlign: "center", p: 2, children: [_jsx(Typography, { variant: "h4", color: "error.main", children: formatPercentage(analytics.rejectionRate) }), _jsx(Typography, { variant: "body2", color: "text.secondary", children: "Rejection Rate" })] }) })] })] }) }) }) }))] }));
};
export default ProposalAnalyticsDashboard;
