import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Box, Card, CardContent, CardHeader, Divider, Grid, Typography, Chip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, CircularProgress, Alert, Button, Select, MenuItem, FormControl, InputLabel, TextField, InputAdornment } from '@mui/material';
import { DateRange as DateRangeIcon, Refresh as RefreshIcon, Download as DownloadIcon } from '@mui/icons-material';
import { DateRangePicker } from '@mui/x-date-pickers-pro/DateRangePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { format, subDays, isAfter, isBefore } from 'date-fns';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>xi<PERSON>, <PERSON><PERSON>ianG<PERSON>, Tooltip as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { useProposals } from '../../hooks/useProposals';
/**
 * Proposal Analytics Dashboard Component
 *
 * This component provides analytics and insights for proposals.
 * It displays metrics, charts, and tables for proposal performance.
 */
const ProposalAnalyticsDashboard = ({ tenantId }) => {
    // State for proposals and analytics
    const [proposals, setProposals] = useState([]);
    const [analyticsEvents, setAnalyticsEvents] = useState([]);
    const [filteredProposals, setFilteredProposals] = useState([]);
    // State for filters
    const [dateRange, setDateRange] = useState([
        subDays(new Date(), 30),
        new Date()
    ]);
    const [statusFilter, setStatusFilter] = useState('all');
    const [searchQuery, setSearchQuery] = useState('');
    // UI state
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    // Get proposals from hook
    const { getProposals } = useProposals();
    // Load proposals
    useEffect(() => {
        const loadProposals = async () => {
            setLoading(true);
            try {
                const result = await getProposals({
                    limit: 100,
                    sortBy: 'createdAt',
                    sortOrder: 'desc'
                });
                setProposals(result.data);
                // Extract all analytics events
                const events = [];
                result.data.forEach((proposal) => {
                    if (proposal.analyticsEvents && proposal.analyticsEvents.length > 0) {
                        events.push(...proposal.analyticsEvents);
                    }
                });
                setAnalyticsEvents(events);
            }
            catch (err) {
                console.error('Error loading proposals:', err);
                setError('Failed to load proposals');
            }
            finally {
                setLoading(false);
            }
        };
        loadProposals();
    }, [getProposals]);
    // Apply filters
    useEffect(() => {
        let filtered = [...proposals];
        // Apply date range filter
        if (dateRange[0] && dateRange[1]) {
            filtered = filtered.filter(proposal => {
                if (!proposal.createdAt)
                    return false;
                const createdAt = new Date(proposal.createdAt);
                const startDate = dateRange[0];
                const endDate = dateRange[1];
                return isAfter(createdAt, startDate) && isBefore(createdAt, endDate);
            });
        }
        // Apply status filter
        if (statusFilter !== 'all') {
            filtered = filtered.filter(proposal => proposal.status === statusFilter);
        }
        // Apply search filter
        if (searchQuery) {
            const query = searchQuery.toLowerCase();
            filtered = filtered.filter(proposal => proposal.title.toLowerCase().includes(query) ||
                (proposal.description && proposal.description.toLowerCase().includes(query)));
        }
        setFilteredProposals(filtered);
    }, [proposals, dateRange, statusFilter, searchQuery]);
    // Calculate metrics
    const totalProposals = filteredProposals.length;
    const sentProposals = filteredProposals.filter(p => p.status === 'sent').length;
    const viewedProposals = filteredProposals.filter(p => p.status === 'viewed').length;
    const acceptedProposals = filteredProposals.filter(p => p.status === 'accepted').length;
    const rejectedProposals = filteredProposals.filter(p => p.status === 'rejected').length;
    // Calculate conversion rates
    const viewRate = sentProposals > 0 ? (viewedProposals / sentProposals) * 100 : 0;
    const acceptanceRate = viewedProposals > 0 ? (acceptedProposals / viewedProposals) * 100 : 0;
    // Calculate total value
    const totalValue = filteredProposals.reduce((sum, proposal) => {
        return sum + (proposal.pricing?.total || 0);
    }, 0);
    // Calculate average time to accept
    const avgTimeToAccept = (() => {
        const acceptedWithDates = filteredProposals.filter(p => p.status === 'accepted' && p.sentAt && p.acceptedAt);
        if (acceptedWithDates.length === 0)
            return 0;
        const totalHours = acceptedWithDates.reduce((sum, proposal) => {
            const sentAt = new Date(proposal.sentAt);
            const acceptedAt = new Date(proposal.acceptedAt);
            const diffMs = acceptedAt.getTime() - sentAt.getTime();
            const diffHours = diffMs / (1000 * 60 * 60);
            return sum + diffHours;
        }, 0);
        return totalHours / acceptedWithDates.length;
    })();
    // Prepare chart data
    const statusChartData = [
        { name: 'Draft', value: filteredProposals.filter(p => p.status === 'draft').length },
        { name: 'Sent', value: sentProposals },
        { name: 'Viewed', value: viewedProposals },
        { name: 'Accepted', value: acceptedProposals },
        { name: 'Rejected', value: rejectedProposals },
    ];
    const COLORS = ['#8884d8', '#83a6ed', '#8dd1e1', '#82ca9d', '#ff8042'];
    // Prepare timeline data
    const timelineData = (() => {
        if (dateRange[0] && dateRange[1]) {
            const startDate = dateRange[0];
            const endDate = dateRange[1];
            const diffDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
            // Group by week if more than 30 days
            if (diffDays > 30) {
                // Group by week logic
                return [];
            }
            else {
                // Group by day
                const data = [];
                for (let i = 0; i < diffDays; i++) {
                    const date = new Date(startDate);
                    date.setDate(date.getDate() + i);
                    const dateStr = format(date, 'yyyy-MM-dd');
                    const sent = filteredProposals.filter(p => p.sentAt && format(new Date(p.sentAt), 'yyyy-MM-dd') === dateStr).length;
                    const viewed = filteredProposals.filter(p => p.viewedAt && format(new Date(p.viewedAt), 'yyyy-MM-dd') === dateStr).length;
                    const accepted = filteredProposals.filter(p => p.acceptedAt && format(new Date(p.acceptedAt), 'yyyy-MM-dd') === dateStr).length;
                    data.push({
                        date: format(date, 'MMM dd'),
                        sent,
                        viewed,
                        accepted,
                    });
                }
                return data;
            }
        }
        return [];
    })();
    // Handle refresh
    const handleRefresh = async () => {
        setLoading(true);
        try {
            const result = await getProposals({
                limit: 100,
                sortBy: 'createdAt',
                sortOrder: 'desc'
            });
            setProposals(result.data);
            // Extract all analytics events
            const events = [];
            result.data.forEach((proposal) => {
                if (proposal.analyticsEvents && proposal.analyticsEvents.length > 0) {
                    events.push(...proposal.analyticsEvents);
                }
            });
            setAnalyticsEvents(events);
        }
        catch (err) {
            console.error('Error refreshing proposals:', err);
            setError('Failed to refresh proposals');
        }
        finally {
            setLoading(false);
        }
    };
    // Handle export
    const handleExport = () => {
        // Create CSV content
        const headers = ['Title', 'Status', 'Created At', 'Sent At', 'Viewed At', 'Accepted At', 'Rejected At', 'Value'];
        const rows = filteredProposals.map(p => [
            p.title,
            p.status,
            p.createdAt ? format(new Date(p.createdAt), 'yyyy-MM-dd HH:mm') : '',
            p.sentAt ? format(new Date(p.sentAt), 'yyyy-MM-dd HH:mm') : '',
            p.viewedAt ? format(new Date(p.viewedAt), 'yyyy-MM-dd HH:mm') : '',
            p.acceptedAt ? format(new Date(p.acceptedAt), 'yyyy-MM-dd HH:mm') : '',
            p.rejectedAt ? format(new Date(p.rejectedAt), 'yyyy-MM-dd HH:mm') : '',
            p.pricing?.total || 0,
        ]);
        const csvContent = [
            headers.join(','),
            ...rows.map(row => row.join(','))
        ].join('\n');
        // Create and download the file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `proposal-analytics-${format(new Date(), 'yyyy-MM-dd')}.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };
    // Render loading state
    if (loading && proposals.length === 0) {
        return (_jsx(Box, { display: "flex", justifyContent: "center", alignItems: "center", p: 4, children: _jsx(CircularProgress, {}) }));
    }
    return (_jsxs(Box, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 3, children: [_jsx(Typography, { variant: "h5", component: "h1", children: "Proposal Analytics" }), _jsxs(Box, { children: [_jsx(Button, { variant: "outlined", startIcon: _jsx(DownloadIcon, {}), onClick: handleExport, sx: { mr: 1 }, children: "Export" }), _jsx(Button, { variant: "outlined", startIcon: _jsx(RefreshIcon, {}), onClick: handleRefresh, disabled: loading, children: "Refresh" })] })] }), error && (_jsx(Alert, { severity: "error", sx: { mb: 3 }, children: error })), _jsx(Card, { sx: { mb: 3 }, children: _jsx(CardContent, { children: _jsxs(Grid, { container: true, spacing: 2, alignItems: "center", children: [_jsx(Grid, { item: true, xs: 12, md: 4, children: _jsx(LocalizationProvider, { dateAdapter: AdapterDateFns, children: _jsx(DateRangePicker, { value: dateRange, onChange: (newValue) => setDateRange(newValue), slotProps: {
                                            textField: {
                                                fullWidth: true,
                                                variant: 'outlined',
                                                InputProps: {
                                                    startAdornment: (_jsx(InputAdornment, { position: "start", children: _jsx(DateRangeIcon, {}) })),
                                                },
                                            },
                                        } }) }) }), _jsx(Grid, { item: true, xs: 12, md: 4, children: _jsxs(FormControl, { fullWidth: true, variant: "outlined", children: [_jsx(InputLabel, { children: "Status" }), _jsxs(Select, { value: statusFilter, onChange: (e) => setStatusFilter(e.target.value), label: "Status", children: [_jsx(MenuItem, { value: "all", children: "All Statuses" }), _jsx(MenuItem, { value: "draft", children: "Draft" }), _jsx(MenuItem, { value: "sent", children: "Sent" }), _jsx(MenuItem, { value: "viewed", children: "Viewed" }), _jsx(MenuItem, { value: "accepted", children: "Accepted" }), _jsx(MenuItem, { value: "rejected", children: "Rejected" })] })] }) }), _jsx(Grid, { item: true, xs: 12, md: 4, children: _jsx(TextField, { fullWidth: true, label: "Search", variant: "outlined", value: searchQuery, onChange: (e) => setSearchQuery(e.target.value), placeholder: "Search by title or description" }) })] }) }) }), _jsxs(Grid, { container: true, spacing: 3, sx: { mb: 3 }, children: [_jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "subtitle2", color: "text.secondary", gutterBottom: true, children: "Total Proposals" }), _jsx(Typography, { variant: "h4", children: totalProposals })] }) }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "subtitle2", color: "text.secondary", gutterBottom: true, children: "View Rate" }), _jsxs(Typography, { variant: "h4", children: [viewRate.toFixed(1), "%"] }), _jsxs(Typography, { variant: "caption", color: "text.secondary", children: [viewedProposals, " of ", sentProposals, " sent proposals viewed"] })] }) }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "subtitle2", color: "text.secondary", gutterBottom: true, children: "Acceptance Rate" }), _jsxs(Typography, { variant: "h4", children: [acceptanceRate.toFixed(1), "%"] }), _jsxs(Typography, { variant: "caption", color: "text.secondary", children: [acceptedProposals, " of ", viewedProposals, " viewed proposals accepted"] })] }) }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 3, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "subtitle2", color: "text.secondary", gutterBottom: true, children: "Total Value" }), _jsxs(Typography, { variant: "h4", children: ["$", totalValue.toLocaleString()] }), _jsxs(Typography, { variant: "caption", color: "text.secondary", children: ["From ", acceptedProposals, " accepted proposals"] })] }) }) })] }), _jsxs(Grid, { container: true, spacing: 3, sx: { mb: 3 }, children: [_jsx(Grid, { item: true, xs: 12, md: 8, children: _jsxs(Card, { children: [_jsx(CardHeader, { title: "Proposal Activity" }), _jsx(Divider, {}), _jsx(CardContent, { children: _jsx(Box, { height: 300, children: _jsx(ResponsiveContainer, { width: "100%", height: "100%", children: _jsxs(BarChart, { data: timelineData, margin: { top: 20, right: 30, left: 20, bottom: 5 }, children: [_jsx(CartesianGrid, { strokeDasharray: "3 3" }), _jsx(XAxis, { dataKey: "date" }), _jsx(YAxis, {}), _jsx(RechartsTooltip, {}), _jsx(Legend, {}), _jsx(Bar, { dataKey: "sent", name: "Sent", fill: "#8884d8" }), _jsx(Bar, { dataKey: "viewed", name: "Viewed", fill: "#82ca9d" }), _jsx(Bar, { dataKey: "accepted", name: "Accepted", fill: "#ffc658" })] }) }) }) })] }) }), _jsx(Grid, { item: true, xs: 12, md: 4, children: _jsxs(Card, { children: [_jsx(CardHeader, { title: "Proposal Status" }), _jsx(Divider, {}), _jsx(CardContent, { children: _jsx(Box, { height: 300, display: "flex", justifyContent: "center", alignItems: "center", children: _jsx(ResponsiveContainer, { width: "100%", height: "100%", children: _jsxs(PieChart, { children: [_jsx(Pie, { data: statusChartData, cx: "50%", cy: "50%", labelLine: false, outerRadius: 80, fill: "#8884d8", dataKey: "value", label: ({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`, children: statusChartData.map((entry, index) => (_jsx(Cell, { fill: COLORS[index % COLORS.length] }, `cell-${index}`))) }), _jsx(RechartsTooltip, {})] }) }) }) })] }) })] }), _jsxs(Card, { children: [_jsx(CardHeader, { title: "Recent Proposals" }), _jsx(Divider, {}), _jsx(TableContainer, { children: _jsxs(Table, { children: [_jsx(TableHead, { children: _jsxs(TableRow, { children: [_jsx(TableCell, { children: "Title" }), _jsx(TableCell, { children: "Status" }), _jsx(TableCell, { children: "Created" }), _jsx(TableCell, { children: "Sent" }), _jsx(TableCell, { children: "Views" }), _jsx(TableCell, { children: "Value" })] }) }), _jsxs(TableBody, { children: [filteredProposals.slice(0, 10).map((proposal) => (_jsxs(TableRow, { children: [_jsx(TableCell, { children: proposal.title }), _jsx(TableCell, { children: _jsx(Chip, { label: proposal.status?.toUpperCase() || 'DRAFT', color: proposal.status === 'accepted' ? 'success' :
                                                            proposal.status === 'rejected' ? 'error' :
                                                                proposal.status === 'sent' ? 'primary' :
                                                                    proposal.status === 'viewed' ? 'info' :
                                                                        'default', size: "small" }) }), _jsx(TableCell, { children: proposal.createdAt ? format(new Date(proposal.createdAt), 'MMM dd, yyyy') : '-' }), _jsx(TableCell, { children: proposal.sentAt ? format(new Date(proposal.sentAt), 'MMM dd, yyyy') : '-' }), _jsx(TableCell, { children: proposal.viewCount || 0 }), _jsxs(TableCell, { children: ["$", proposal.pricing?.total?.toLocaleString() || '0'] })] }, proposal._id?.toString() || `proposal-${Math.random()}`))), filteredProposals.length === 0 && (_jsx(TableRow, { children: _jsx(TableCell, { colSpan: 6, align: "center", children: "No proposals found matching the filters" }) }))] })] }) })] })] }));
};
export default ProposalAnalyticsDashboard;
