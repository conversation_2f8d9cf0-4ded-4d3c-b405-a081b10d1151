import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { Box, Button, TextField, Typography, FormControl, FormGroup, FormControlLabel, Checkbox, CircularProgress, Alert, Paper, Select, MenuItem, InputLabel, Grid, useMediaQuery, useTheme } from '@mui/material';
import { AutoAwesome as AIIcon, Refresh as RefreshIcon } from '@mui/icons-material';
/**
 * AI-powered proposal generator component using Material UI
 *
 * This component provides a responsive UI for generating proposals with AI.
 */
const ProposalAIGeneratorMUI = ({ opportunity, company, contacts, onGenerate, }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
    // State
    const [prompt, setPrompt] = useState('');
    const [model, setModel] = useState('claude-3-opus-20240229');
    const [isGenerating, setIsGenerating] = useState(false);
    const [error, setError] = useState(null);
    // Section toggles
    const [includeExecutiveSummary, setIncludeExecutiveSummary] = useState(true);
    const [includeSolution, setIncludeSolution] = useState(true);
    const [includeTimeline, setIncludeTimeline] = useState(true);
    const [includePricing, setIncludePricing] = useState(true);
    const [includeTeam, setIncludeTeam] = useState(false);
    const [includeTestimonials, setIncludeTestimonials] = useState(false);
    const [includeTerms, setIncludeTerms] = useState(true);
    // Generate a default prompt based on opportunity, company, and contacts
    const generateDefaultPrompt = () => {
        let defaultPrompt = 'Generate a professional business proposal';
        if (opportunity) {
            defaultPrompt += ` for the opportunity "${opportunity.name}"`;
            if (opportunity.description) {
                defaultPrompt += ` which involves ${opportunity.description}`;
            }
            if (opportunity.amount) {
                defaultPrompt += ` with an approximate value of $${opportunity.amount.toLocaleString()}`;
            }
        }
        if (company) {
            defaultPrompt += ` for ${company.name}`;
            if (company.industry) {
                defaultPrompt += ` in the ${company.industry} industry`;
            }
        }
        defaultPrompt += '. Include the following sections:';
        if (includeExecutiveSummary) {
            defaultPrompt += '\n- Executive Summary';
        }
        if (includeSolution) {
            defaultPrompt += '\n- Proposed Solution';
        }
        if (includeTimeline) {
            defaultPrompt += '\n- Project Timeline';
        }
        if (includePricing) {
            defaultPrompt += '\n- Pricing and Investment';
        }
        if (includeTeam) {
            defaultPrompt += '\n- Our Team';
        }
        if (includeTestimonials) {
            defaultPrompt += '\n- Client Testimonials';
        }
        if (includeTerms) {
            defaultPrompt += '\n- Terms and Conditions';
        }
        defaultPrompt += '\n\nMake the proposal persuasive, professional, and tailored to the client\'s needs.';
        return defaultPrompt;
    };
    // Handle prompt generation
    const handleGeneratePrompt = () => {
        setPrompt(generateDefaultPrompt());
    };
    // Handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!prompt) {
            setError('Please enter a prompt');
            return;
        }
        setIsGenerating(true);
        setError(null);
        try {
            // This would be replaced with an actual API call to generate content
            // For now, we'll just simulate a delay and return mock data
            await new Promise(resolve => setTimeout(resolve, 3000));
            // Mock generated content
            const generatedContent = {
                title: opportunity ? `Proposal: ${opportunity.name}` : 'Business Proposal',
                description: 'A comprehensive solution tailored to your specific needs and objectives.',
                sections: [
                    {
                        title: 'Executive Summary',
                        content: 'This proposal outlines our comprehensive solution designed to address your specific challenges and help you achieve your business objectives. Based on our understanding of your requirements, we have developed a tailored approach that leverages our expertise and proven methodologies to deliver exceptional results.',
                        type: 'text',
                        order: 0,
                    },
                    {
                        title: 'Proposed Solution',
                        content: 'Our solution combines innovative technology with industry best practices to provide a scalable, efficient, and effective approach to addressing your needs. We will implement a phased approach that minimizes disruption while maximizing value at each stage of the project.',
                        type: 'text',
                        order: 1,
                    },
                    {
                        title: 'Project Timeline',
                        content: 'The project will be executed in three phases over a 12-week period:\n\nPhase 1 (Weeks 1-4): Discovery and Planning\n- Requirements gathering and analysis\n- Solution design and architecture\n- Project plan finalization\n\nPhase 2 (Weeks 5-10): Implementation\n- Development and configuration\n- Integration with existing systems\n- Quality assurance and testing\n\nPhase 3 (Weeks 11-12): Deployment and Training\n- System deployment\n- User training and documentation\n- Post-implementation support',
                        type: 'timeline',
                        order: 2,
                    },
                ],
                pricing: {
                    items: [
                        {
                            name: 'Discovery and Planning',
                            description: 'Requirements gathering, solution design, and project planning',
                            quantity: 1,
                            unitPrice: 5000,
                            total: 5000,
                        },
                        {
                            name: 'Implementation',
                            description: 'Development, configuration, integration, and testing',
                            quantity: 1,
                            unitPrice: 15000,
                            total: 15000,
                        },
                        {
                            name: 'Deployment and Training',
                            description: 'System deployment, user training, and documentation',
                            quantity: 1,
                            unitPrice: 5000,
                            total: 5000,
                        },
                        {
                            name: 'Monthly Support',
                            description: 'Ongoing technical support and maintenance',
                            quantity: 12,
                            unitPrice: 1000,
                            total: 12000,
                        },
                    ],
                },
                terms: 'Payment Terms:\n- 50% due upon project initiation\n- 25% due upon completion of Phase 2\n- 25% due upon project completion\n\nProject Timeline:\n- Project will commence within 2 weeks of proposal acceptance\n- Estimated completion time is 12 weeks from project start date\n\nWarranty:\n- 90-day warranty on all deliverables\n- Support included for 12 months from project completion',
                aiPrompt: prompt,
                aiModel: model,
            };
            onGenerate(generatedContent);
        }
        catch (error) {
            console.error('Error generating proposal:', error);
            setError('Failed to generate proposal. Please try again.');
        }
        finally {
            setIsGenerating(false);
        }
    };
    return (_jsxs(Box, { component: "form", onSubmit: handleSubmit, sx: { width: '100%' }, children: [error && (_jsx(Alert, { severity: "error", sx: { mb: 3 }, children: error })), _jsxs(Grid, { container: true, spacing: { xs: 2, md: 3 }, children: [_jsxs(Grid, { item: true, xs: 12, md: 5, order: { xs: 1, md: 2 }, children: [_jsx(Typography, { variant: "subtitle2", gutterBottom: true, children: "Sections to Include" }), _jsx(Paper, { variant: "outlined", sx: { p: 2 }, children: _jsx(FormControl, { component: "fieldset", fullWidth: true, children: _jsx(FormGroup, { children: _jsxs(Grid, { container: true, spacing: 1, children: [_jsx(Grid, { item: true, xs: 12, sm: 6, md: 12, children: _jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: includeExecutiveSummary, onChange: (e) => setIncludeExecutiveSummary(e.target.checked), "aria-label": "Include executive summary" }), label: "Executive Summary" }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 12, children: _jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: includeSolution, onChange: (e) => setIncludeSolution(e.target.checked), "aria-label": "Include proposed solution" }), label: "Proposed Solution" }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 12, children: _jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: includeTimeline, onChange: (e) => setIncludeTimeline(e.target.checked), "aria-label": "Include project timeline" }), label: "Project Timeline" }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 12, children: _jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: includePricing, onChange: (e) => setIncludePricing(e.target.checked), "aria-label": "Include pricing" }), label: "Pricing" }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 12, children: _jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: includeTeam, onChange: (e) => setIncludeTeam(e.target.checked), "aria-label": "Include team members" }), label: "Team Members" }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 12, children: _jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: includeTestimonials, onChange: (e) => setIncludeTestimonials(e.target.checked), "aria-label": "Include client testimonials" }), label: "Client Testimonials" }) }), _jsx(Grid, { item: true, xs: 12, sm: 6, md: 12, children: _jsx(FormControlLabel, { control: _jsx(Checkbox, { checked: includeTerms, onChange: (e) => setIncludeTerms(e.target.checked), "aria-label": "Include terms and conditions" }), label: "Terms & Conditions" }) })] }) }) }) })] }), _jsxs(Grid, { item: true, xs: 12, md: 7, order: { xs: 2, md: 1 }, children: [_jsxs(FormControl, { fullWidth: true, margin: "normal", children: [_jsx(InputLabel, { id: "ai-model-label", children: "AI Model" }), _jsxs(Select, { labelId: "ai-model-label", value: model, onChange: (e) => setModel(e.target.value), label: "AI Model", "aria-label": "Select AI model", children: [_jsx(MenuItem, { value: "claude-3-opus-20240229", children: "Claude 3 Opus (Highest Quality)" }), _jsx(MenuItem, { value: "claude-3-sonnet-20240229", children: "Claude 3 Sonnet (Balanced)" }), _jsx(MenuItem, { value: "claude-3-haiku-20240307", children: "Claude 3 Haiku (Fastest)" })] })] }), _jsx(Box, { sx: { display: 'flex', justifyContent: 'flex-end', mt: 2, mb: 2 }, children: _jsx(Button, { variant: "outlined", startIcon: _jsx(RefreshIcon, {}), onClick: handleGeneratePrompt, "aria-label": "Generate default prompt", children: "Generate Default Prompt" }) }), _jsx(Typography, { variant: "subtitle2", gutterBottom: true, children: "AI Prompt" }), _jsx(TextField, { fullWidth: true, multiline: true, rows: isMobile ? 6 : 8, value: prompt, onChange: (e) => setPrompt(e.target.value), placeholder: "Describe what you want to include in your proposal. For example: 'Create a proposal for a web development project for a small e-commerce business. The project includes website design, development, and 6 months of maintenance. The budget is around $15,000.'", variant: "outlined", margin: "normal", required: true, inputProps: { 'aria-label': 'AI prompt' } }), (opportunity || company || contacts) && (_jsxs(Paper, { variant: "outlined", sx: { p: 2, mt: 2, bgcolor: 'background.paper' }, children: [_jsx(Typography, { variant: "subtitle2", gutterBottom: true, children: "Context Information" }), opportunity && (_jsxs(Box, { sx: { mb: 2 }, children: [_jsx(Typography, { variant: "body2", fontWeight: "medium", children: "Opportunity:" }), _jsxs(Typography, { variant: "body2", color: "text.secondary", children: [opportunity.name, opportunity.description && ` - ${opportunity.description}`, opportunity.amount && ` - $${opportunity.amount.toLocaleString()}`] })] })), company && (_jsxs(Box, { sx: { mb: 2 }, children: [_jsx(Typography, { variant: "body2", fontWeight: "medium", children: "Company:" }), _jsxs(Typography, { variant: "body2", color: "text.secondary", children: [company.name, company.industry && ` - ${company.industry}`] })] })), contacts && contacts.length > 0 && (_jsxs(Box, { children: [_jsx(Typography, { variant: "body2", fontWeight: "medium", children: "Contacts:" }), _jsx(Typography, { variant: "body2", color: "text.secondary", children: contacts.map(contact => contact.name).join(', ') })] }))] }))] })] }), _jsx(Box, { sx: {
                    display: 'flex',
                    justifyContent: 'flex-end',
                    mt: 4,
                    position: 'sticky',
                    bottom: { xs: 0, md: 'auto' },
                    backgroundColor: { xs: 'background.paper', md: 'transparent' },
                    padding: { xs: 2, md: 0 },
                    borderTop: { xs: `1px solid ${theme.palette.divider}`, md: 'none' },
                    zIndex: 10,
                    width: '100%'
                }, children: _jsx(Button, { type: "submit", variant: "contained", color: "primary", disabled: isGenerating || !prompt, startIcon: isGenerating ? _jsx(CircularProgress, { size: 20, color: "inherit" }) : _jsx(AIIcon, {}), fullWidth: isMobile, "aria-label": "Generate proposal", children: isGenerating ? 'Generating...' : 'Generate Proposal' }) })] }));
};
export default ProposalAIGeneratorMUI;
