import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, Button, Input, Textarea } from '@aizako/ui-kit';
const CreateProposalDialog = ({ isOpen, onClose, onSubmit, contactId, companyId, opportunityId, }) => {
    const [formData, setFormData] = useState({
        title: '',
        description: '',
        contactId,
        companyId,
        opportunityId,
    });
    const handleSubmit = (e) => {
        e.preventDefault();
        onSubmit(formData);
        onClose();
    };
    const handleInputChange = (field) => (e) => {
        setFormData(prev => ({
            ...prev,
            [field]: e.target.value,
        }));
    };
    return (_jsx(Dialog, { open: isOpen, onOpenChange: onClose, children: _jsxs(DialogContent, { children: [_jsx(DialogHeader, { children: _jsx(DialogTitle, { children: "Create New Proposal" }) }), _jsxs("form", { onSubmit: handleSubmit, className: "space-y-4", children: [_jsxs("div", { children: [_jsx("label", { htmlFor: "title", className: "block text-sm font-medium mb-1", children: "Proposal Title" }), _jsx(Input, { id: "title", value: formData.title, onChange: handleInputChange('title'), placeholder: "Enter proposal title", required: true })] }), _jsxs("div", { children: [_jsx("label", { htmlFor: "description", className: "block text-sm font-medium mb-1", children: "Description (Optional)" }), _jsx(Textarea, { id: "description", value: formData.description || '', onChange: handleInputChange('description'), placeholder: "Enter proposal description", rows: 3 })] }), _jsxs(DialogFooter, { children: [_jsx(Button, { type: "button", variant: "outline", onClick: onClose, children: "Cancel" }), _jsx(Button, { type: "submit", children: "Create Proposal" })] })] })] }) }));
};
export default CreateProposalDialog;
