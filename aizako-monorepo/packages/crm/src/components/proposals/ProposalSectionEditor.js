import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { Box, TextField, Select, MenuItem, FormControl, InputLabel, Typography, Button, IconButton, Tooltip, Collapse, Paper } from '@mui/material';
import { ExpandMore as ExpandMoreIcon, ExpandLess as ExpandLessIcon, AutoAwesome as AIIcon } from '@mui/icons-material';
import RichTextEditor from '../common/RichTextEditor';
import { ProposalService } from '../../services/proposal-service';
/**
 * Proposal Section Editor Component
 *
 * This component provides a UI for editing a proposal section.
 * It supports different section types and AI-powered content generation.
 */
const ProposalSectionEditor = ({ section, onChange }) => {
    const [expanded, setExpanded] = useState(true);
    const [aiPrompt, setAiPrompt] = useState('');
    const [aiDialogOpen, setAiDialogOpen] = useState(false);
    const [generating, setGenerating] = useState(false);
    const [error, setError] = useState(null);
    // Handle section field changes
    const handleChange = (field, value) => {
        onChange({ [field]: value });
    };
    // Toggle expanded state
    const toggleExpanded = () => {
        setExpanded(!expanded);
    };
    // Generate content with AI
    const generateWithAI = async () => {
        if (!aiPrompt) {
            setError('Please enter a prompt');
            return;
        }
        setGenerating(true);
        setError(null);
        try {
            const generatedSection = await ProposalService.generateProposalSectionWithAI(section.type, aiPrompt, 'claude-3-opus-20240229');
            onChange({
                content: generatedSection.content,
                aiGenerated: true
            });
            setAiDialogOpen(false);
            setAiPrompt('');
        }
        catch (err) {
            setError('Failed to generate content');
            console.error(err);
        }
        finally {
            setGenerating(false);
        }
    };
    return (_jsxs(Box, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 1, children: [_jsx(TextField, { label: "Section Title", value: section.title, onChange: (e) => handleChange('title', e.target.value), fullWidth: true, variant: "outlined", size: "small", sx: { mr: 2 } }), _jsxs(FormControl, { variant: "outlined", size: "small", sx: { minWidth: 120 }, children: [_jsx(InputLabel, { children: "Type" }), _jsxs(Select, { value: section.type, onChange: (e) => handleChange('type', e.target.value), label: "Type", children: [_jsx(MenuItem, { value: "text", children: "Text" }), _jsx(MenuItem, { value: "timeline", children: "Timeline" }), _jsx(MenuItem, { value: "team", children: "Team" }), _jsx(MenuItem, { value: "testimonials", children: "Testimonials" }), _jsx(MenuItem, { value: "images", children: "Images" }), _jsx(MenuItem, { value: "custom", children: "Custom" })] })] }), _jsx(IconButton, { onClick: toggleExpanded, size: "small", sx: { ml: 1 }, children: expanded ? _jsx(ExpandLessIcon, {}) : _jsx(ExpandMoreIcon, {}) })] }), _jsx(Collapse, { in: expanded, children: _jsxs(Box, { sx: { mt: 2 }, children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 1, children: [_jsx(Typography, { variant: "subtitle2", children: "Content" }), _jsx(Tooltip, { title: "Generate with AI", children: _jsx(Button, { startIcon: _jsx(AIIcon, {}), size: "small", variant: "outlined", color: "secondary", onClick: () => setAiDialogOpen(true), children: "AI Generate" }) })] }), _jsx(RichTextEditor, { value: section.content, onChange: (value) => handleChange('content', value), placeholder: "Enter section content...", minHeight: 200 }), _jsx(Collapse, { in: aiDialogOpen, children: _jsxs(Paper, { sx: { p: 2, mt: 2, bgcolor: 'background.paper' }, children: [_jsx(Typography, { variant: "subtitle2", gutterBottom: true, children: "Generate Content with AI" }), _jsx(TextField, { label: "Prompt", fullWidth: true, multiline: true, rows: 3, value: aiPrompt, onChange: (e) => setAiPrompt(e.target.value), placeholder: "Describe what you want to generate...", variant: "outlined", margin: "normal", error: !!error, helperText: error }), _jsxs(Box, { display: "flex", justifyContent: "flex-end", mt: 1, children: [_jsx(Button, { variant: "outlined", onClick: () => setAiDialogOpen(false), sx: { mr: 1 }, children: "Cancel" }), _jsx(Button, { variant: "contained", color: "secondary", onClick: generateWithAI, disabled: generating || !aiPrompt, children: generating ? 'Generating...' : 'Generate' })] })] }) })] }) })] }));
};
export default ProposalSectionEditor;
