import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import React, { useState, useEffect } from 'react';
import { Box, Typography, Button, Card, CardContent, Grid, Divider, Chip, CircularProgress, Alert, IconButton, Tooltip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Stepper, Step, StepLabel, StepContent, List, ListItem, ListItemText, ListItemAvatar, Avatar, ListItemSecondaryAction, useTheme, useMediaQuery, } from '@mui/material';
import { CheckCircle as ApproveIcon, Cancel as RejectIcon, Cancel as CancelIcon, Edit as EditIcon, Person as PersonIcon, Send as SendIcon, Visibility as ViewIcon, Refresh as RefreshIcon, Add as AddIcon, Delete as DeleteIcon, } from '@mui/icons-material';
import { format } from 'date-fns';
import { ProposalService } from '../../../services/proposal-service';
import { ProposalApprovalService } from '../../../services/proposal-approval-service';
import { useAuth } from '../../../hooks/useAuth';
/**
 * ProposalApprovalWorkflow Component
 *
 * This component displays and manages the approval workflow for a proposal.
 */
const ProposalApprovalWorkflow = ({ proposalId, tenantId, onViewProposal, onEditProposal, }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const { user } = useAuth();
    // State
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [proposal, setProposal] = useState(null);
    const [approvalWorkflow, setApprovalWorkflow] = useState(null);
    const [approvers, setApprovers] = useState([]);
    const [availableApprovers, setAvailableApprovers] = useState([]);
    const [commentDialogOpen, setCommentDialogOpen] = useState(false);
    const [comment, setComment] = useState('');
    const [addApproverDialogOpen, setAddApproverDialogOpen] = useState(false);
    const [selectedApprover, setSelectedApprover] = useState('');
    const [approvalLevel, setApprovalLevel] = useState(1);
    // Fetch proposal and approval workflow
    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);
                // Fetch proposal
                const proposalData = await ProposalService.getProposalById(proposalId, tenantId);
                setProposal(proposalData);
                // Fetch approval workflow
                const workflowData = await ProposalApprovalService.getApprovalWorkflow(proposalId, tenantId);
                setApprovalWorkflow(workflowData);
                // Fetch approvers
                const approversData = await ProposalApprovalService.getApprovers(tenantId);
                setAvailableApprovers(approversData);
                // Set current approvers
                if (workflowData?.approvers) {
                    setApprovers(workflowData.approvers);
                }
            }
            catch (err) {
                console.error('Error fetching data:', err);
                setError('Failed to load data. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchData();
    }, [proposalId, tenantId]);
    // Handle refresh
    const handleRefresh = () => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);
                // Fetch approval workflow
                const workflowData = await ProposalApprovalService.getApprovalWorkflow(proposalId, tenantId);
                setApprovalWorkflow(workflowData);
            }
            catch (err) {
                console.error('Error fetching data:', err);
                setError('Failed to load data. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchData();
    };
    // Handle approve
    const handleApprove = async () => {
        try {
            setLoading(true);
            setError(null);
            await ProposalApprovalService.approveProposal(proposalId, tenantId, {
                comment,
            });
            // Refresh data
            handleRefresh();
            // Close dialog
            setCommentDialogOpen(false);
            setComment('');
        }
        catch (err) {
            console.error('Error approving proposal:', err);
            setError('Failed to approve proposal. Please try again.');
            setLoading(false);
        }
    };
    // Handle reject
    const handleReject = async () => {
        try {
            setLoading(true);
            setError(null);
            await ProposalApprovalService.rejectProposal(proposalId, tenantId, {
                comment,
            });
            // Refresh data
            handleRefresh();
            // Close dialog
            setCommentDialogOpen(false);
            setComment('');
        }
        catch (err) {
            console.error('Error rejecting proposal:', err);
            setError('Failed to reject proposal. Please try again.');
            setLoading(false);
        }
    };
    // Handle add approver
    const handleAddApprover = async () => {
        try {
            setLoading(true);
            setError(null);
            await ProposalApprovalService.addApprover(proposalId, tenantId, {
                approverId: selectedApprover,
                level: approvalLevel,
            });
            // Refresh data
            handleRefresh();
            // Close dialog
            setAddApproverDialogOpen(false);
            setSelectedApprover('');
            setApprovalLevel(1);
        }
        catch (err) {
            console.error('Error adding approver:', err);
            setError('Failed to add approver. Please try again.');
            setLoading(false);
        }
    };
    // Handle remove approver
    const handleRemoveApprover = async (approverId) => {
        try {
            setLoading(true);
            setError(null);
            await ProposalApprovalService.removeApprover(proposalId, tenantId, {
                approverId,
            });
            // Refresh data
            handleRefresh();
        }
        catch (err) {
            console.error('Error removing approver:', err);
            setError('Failed to remove approver. Please try again.');
            setLoading(false);
        }
    };
    // Handle start approval process
    const handleStartApprovalProcess = async () => {
        try {
            setLoading(true);
            setError(null);
            await ProposalApprovalService.startApprovalProcess(proposalId, tenantId);
            // Refresh data
            handleRefresh();
        }
        catch (err) {
            console.error('Error starting approval process:', err);
            setError('Failed to start approval process. Please try again.');
            setLoading(false);
        }
    };
    // Handle cancel approval process
    const handleCancelApprovalProcess = async () => {
        try {
            setLoading(true);
            setError(null);
            await ProposalApprovalService.cancelApprovalProcess(proposalId, tenantId);
            // Refresh data
            handleRefresh();
        }
        catch (err) {
            console.error('Error canceling approval process:', err);
            setError('Failed to cancel approval process. Please try again.');
            setLoading(false);
        }
    };
    // Check if current user is an approver at the current level
    const isCurrentApprover = () => {
        if (!approvalWorkflow || !user)
            return false;
        const currentLevel = approvalWorkflow.currentLevel;
        const currentLevelApprovers = approvers.filter(approver => approver.level === currentLevel);
        return currentLevelApprovers.some(approver => approver.userId === user.id);
    };
    // Render loading state
    if (loading && !proposal) {
        return (_jsx(Box, { display: "flex", justifyContent: "center", alignItems: "center", height: "400px", children: _jsx(CircularProgress, {}) }));
    }
    // Render error state
    if (error) {
        return (_jsxs(Alert, { severity: "error", sx: { mb: 2 }, children: [error, _jsx(Button, { variant: "outlined", size: "small", onClick: handleRefresh, startIcon: _jsx(RefreshIcon, {}), sx: { ml: 2 }, children: "Retry" })] }));
    }
    return (_jsxs(Box, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 3, children: [_jsx(Typography, { variant: "h5", children: "Approval Workflow" }), _jsxs(Box, { children: [_jsx(Button, { variant: "outlined", startIcon: _jsx(RefreshIcon, {}), onClick: handleRefresh, sx: { mr: 1 }, children: "Refresh" }), _jsx(Button, { variant: "outlined", startIcon: _jsx(ViewIcon, {}), onClick: () => onViewProposal && onViewProposal(proposalId), sx: { mr: 1 }, children: "View Proposal" }), approvalWorkflow?.status === 'rejected' && (_jsx(Button, { variant: "outlined", startIcon: _jsx(EditIcon, {}), onClick: () => onEditProposal && onEditProposal(proposalId), sx: { mr: 1 }, children: "Edit Proposal" }))] })] }), proposal && (_jsx(Card, { sx: { mb: 3 }, children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: proposal.title }), _jsx(Typography, { variant: "body2", color: "text.secondary", gutterBottom: true, children: proposal.description }), _jsx(Divider, { sx: { my: 1 } }), _jsxs(Grid, { container: true, spacing: 2, children: [_jsxs(Grid, { item: true, xs: 12, sm: 6, md: 3, children: [_jsx(Typography, { variant: "body2", color: "text.secondary", children: "Status" }), _jsx(Chip, { label: proposal.status.toUpperCase(), color: proposal.status === 'accepted' ? 'success' :
                                                proposal.status === 'rejected' ? 'error' :
                                                    proposal.status === 'viewed' ? 'info' :
                                                        proposal.status === 'sent' ? 'primary' :
                                                            'default', size: "small" })] }), _jsxs(Grid, { item: true, xs: 12, sm: 6, md: 3, children: [_jsx(Typography, { variant: "body2", color: "text.secondary", children: "Created By" }), _jsx(Typography, { variant: "body2", children: proposal.createdBy?.name || 'Unknown' })] }), _jsxs(Grid, { item: true, xs: 12, sm: 6, md: 3, children: [_jsx(Typography, { variant: "body2", color: "text.secondary", children: "Created At" }), _jsx(Typography, { variant: "body2", children: format(new Date(proposal.createdAt), 'PPP') })] }), _jsxs(Grid, { item: true, xs: 12, sm: 6, md: 3, children: [_jsx(Typography, { variant: "body2", color: "text.secondary", children: "Last Updated" }), _jsx(Typography, { variant: "body2", children: format(new Date(proposal.updatedAt), 'PPP') })] })] })] }) })), !approvalWorkflow ? (_jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "No Approval Workflow" }), _jsx(Typography, { variant: "body2", color: "text.secondary", gutterBottom: true, children: "This proposal doesn't have an approval workflow yet. You can set up approvers and start the approval process." }), _jsxs(Box, { mt: 2, children: [_jsx(Button, { variant: "contained", startIcon: _jsx(AddIcon, {}), onClick: () => setAddApproverDialogOpen(true), sx: { mr: 1 }, children: "Add Approver" }), _jsx(Button, { variant: "contained", startIcon: _jsx(SendIcon, {}), onClick: handleStartApprovalProcess, disabled: approvers.length === 0, children: "Start Approval Process" })] })] }) })) : (_jsxs(Box, { children: [_jsx(Card, { sx: { mb: 3 }, children: _jsxs(CardContent, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2, children: [_jsx(Typography, { variant: "h6", children: "Approval Status" }), _jsx(Chip, { label: approvalWorkflow.status.toUpperCase(), color: approvalWorkflow.status === 'approved' ? 'success' :
                                                approvalWorkflow.status === 'rejected' ? 'error' :
                                                    approvalWorkflow.status === 'in_progress' ? 'primary' :
                                                        'default' })] }), _jsx(Stepper, { activeStep: approvalWorkflow.currentLevel - 1, orientation: isMobile ? "vertical" : "horizontal", children: Array.from({ length: approvalWorkflow.totalLevels }, (_, i) => i + 1).map((level) => {
                                        const levelApprovers = approvers.filter(approver => approver.level === level);
                                        const isCompleted = level < approvalWorkflow.currentLevel;
                                        const isCurrent = level === approvalWorkflow.currentLevel;
                                        return (_jsxs(Step, { completed: isCompleted, children: [_jsxs(StepLabel, { children: ["Level ", level] }), isMobile && (_jsxs(StepContent, { children: [_jsxs(Typography, { variant: "body2", children: [levelApprovers.length, " Approver", levelApprovers.length !== 1 ? 's' : ''] }), isCompleted && (_jsxs(Typography, { variant: "body2", color: "success.main", children: ["Approved on ", format(new Date(approvalWorkflow.levelCompletedAt[level - 1]), 'PPP')] }))] }))] }, level));
                                    }) }), approvalWorkflow.status === 'in_progress' && (_jsxs(Box, { mt: 3, children: [_jsxs(Typography, { variant: "subtitle1", gutterBottom: true, children: ["Current Level: ", approvalWorkflow.currentLevel] }), _jsxs(Typography, { variant: "body2", color: "text.secondary", gutterBottom: true, children: ["Waiting for approval from level ", approvalWorkflow.currentLevel, " approvers."] }), isCurrentApprover() && (_jsxs(Box, { mt: 2, children: [_jsx(Button, { variant: "contained", color: "success", startIcon: _jsx(ApproveIcon, {}), onClick: () => setCommentDialogOpen(true), sx: { mr: 1 }, children: "Approve" }), _jsx(Button, { variant: "contained", color: "error", startIcon: _jsx(RejectIcon, {}), onClick: () => setCommentDialogOpen(true), children: "Reject" })] }))] })), approvalWorkflow.status === 'approved' && (_jsx(Alert, { severity: "success", sx: { mt: 3 }, children: "This proposal has been approved by all required approvers." })), approvalWorkflow.status === 'rejected' && (_jsx(Alert, { severity: "error", sx: { mt: 3 }, children: "This proposal has been rejected. Please review the comments and make necessary changes." })), approvalWorkflow.status === 'in_progress' && (_jsx(Box, { mt: 2, display: "flex", justifyContent: "flex-end", children: _jsx(Button, { variant: "outlined", color: "error", startIcon: _jsx(CancelIcon, {}), onClick: handleCancelApprovalProcess, children: "Cancel Approval Process" }) }))] }) }), _jsx(Card, { sx: { mb: 3 }, children: _jsxs(CardContent, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2, children: [_jsx(Typography, { variant: "h6", children: "Approvers" }), approvalWorkflow.status !== 'approved' && approvalWorkflow.status !== 'rejected' && (_jsx(Button, { variant: "outlined", startIcon: _jsx(AddIcon, {}), onClick: () => setAddApproverDialogOpen(true), children: "Add Approver" }))] }), approvers.length === 0 ? (_jsx(Typography, { variant: "body2", color: "text.secondary", children: "No approvers assigned yet." })) : (_jsx(List, { children: Array.from({ length: approvalWorkflow.totalLevels }, (_, i) => i + 1).map((level) => {
                                        const levelApprovers = approvers.filter(approver => approver.level === level);
                                        return levelApprovers.length > 0 ? (_jsxs(React.Fragment, { children: [_jsxs(Typography, { variant: "subtitle2", gutterBottom: true, children: ["Level ", level] }), levelApprovers.map((approver) => (_jsxs(ListItem, { children: [_jsx(ListItemAvatar, { children: _jsx(Avatar, { children: _jsx(PersonIcon, {}) }) }), _jsx(ListItemText, { primary: approver.name, secondary: approver.email }), _jsxs(ListItemSecondaryAction, { children: [approver.status === 'approved' && (_jsx(Tooltip, { title: "Approved", children: _jsx(ApproveIcon, { color: "success" }) })), approver.status === 'rejected' && (_jsx(Tooltip, { title: "Rejected", children: _jsx(RejectIcon, { color: "error" }) })), approver.status === 'pending' && (_jsx(Tooltip, { title: "Pending", children: _jsx(CircularProgress, { size: 20 }) })), approvalWorkflow.status !== 'approved' && approvalWorkflow.status !== 'rejected' && (_jsx(Tooltip, { title: "Remove Approver", children: _jsx(IconButton, { edge: "end", onClick: () => handleRemoveApprover(approver.userId), children: _jsx(DeleteIcon, {}) }) }))] })] }, approver.userId)))] }, level)) : null;
                                    }) }))] }) }), _jsx(Card, { children: _jsxs(CardContent, { children: [_jsx(Typography, { variant: "h6", gutterBottom: true, children: "Approval History" }), approvalWorkflow.history?.length === 0 ? (_jsx(Typography, { variant: "body2", color: "text.secondary", children: "No approval history yet." })) : (_jsx(List, { children: approvalWorkflow.history?.map((event, index) => (_jsxs(ListItem, { divider: index < approvalWorkflow.history.length - 1, children: [_jsx(ListItemAvatar, { children: _jsxs(Avatar, { sx: {
                                                        bgcolor: event.action === 'approved' ? 'success.main' :
                                                            event.action === 'rejected' ? 'error.main' :
                                                                event.action === 'started' ? 'primary.main' :
                                                                    event.action === 'canceled' ? 'warning.main' :
                                                                        'grey.500'
                                                    }, children: [event.action === 'approved' && _jsx(ApproveIcon, {}), event.action === 'rejected' && _jsx(RejectIcon, {}), event.action === 'started' && _jsx(SendIcon, {}), event.action === 'canceled' && _jsx(CancelIcon, {}), event.action === 'added_approver' && _jsx(PersonIcon, {}), event.action === 'removed_approver' && _jsx(DeleteIcon, {})] }) }), _jsx(ListItemText, { primary: _jsxs(Box, { children: [_jsxs(Typography, { variant: "subtitle2", children: [event.action === 'approved' && 'Approved', event.action === 'rejected' && 'Rejected', event.action === 'started' && 'Started Approval Process', event.action === 'canceled' && 'Canceled Approval Process', event.action === 'added_approver' && 'Added Approver', event.action === 'removed_approver' && 'Removed Approver'] }), _jsxs(Typography, { variant: "body2", color: "text.secondary", children: [event.user?.name || 'System', " \u2022 ", format(new Date(event.timestamp), 'PPp')] })] }), secondary: event.comment && (_jsxs(Typography, { variant: "body2", color: "text.secondary", sx: { mt: 1 }, children: ["\"", event.comment, "\""] })) })] }, index))) }))] }) })] })), _jsxs(Dialog, { open: commentDialogOpen, onClose: () => setCommentDialogOpen(false), maxWidth: "sm", fullWidth: true, children: [_jsx(DialogTitle, { children: "Add Comment" }), _jsx(DialogContent, { children: _jsx(TextField, { label: "Comment", value: comment, onChange: (e) => setComment(e.target.value), fullWidth: true, multiline: true, rows: 4, margin: "normal", placeholder: "Add your comments or feedback here..." }) }), _jsxs(DialogActions, { children: [_jsx(Button, { onClick: () => setCommentDialogOpen(false), children: "Cancel" }), _jsx(Button, { variant: "contained", color: "error", onClick: handleReject, children: "Reject" }), _jsx(Button, { variant: "contained", color: "success", onClick: handleApprove, children: "Approve" })] })] }), _jsxs(Dialog, { open: addApproverDialogOpen, onClose: () => setAddApproverDialogOpen(false), maxWidth: "sm", fullWidth: true, children: [_jsx(DialogTitle, { children: "Add Approver" }), _jsxs(DialogContent, { children: [_jsxs(FormControl, { fullWidth: true, margin: "normal", children: [_jsx(InputLabel, { children: "Approver" }), _jsx(Select, { value: selectedApprover, onChange: (e) => setSelectedApprover(e.target.value), label: "Approver", children: availableApprovers.map((approver) => (_jsxs(MenuItem, { value: approver.id, children: [approver.name, " (", approver.email, ")"] }, approver.id))) })] }), _jsxs(FormControl, { fullWidth: true, margin: "normal", children: [_jsx(InputLabel, { children: "Approval Level" }), _jsx(Select, { value: approvalLevel, onChange: (e) => setApprovalLevel(Number(e.target.value)), label: "Approval Level", children: Array.from({ length: 5 }, (_, i) => i + 1).map((level) => (_jsxs(MenuItem, { value: level, children: ["Level ", level] }, level))) })] })] }), _jsxs(DialogActions, { children: [_jsx(Button, { onClick: () => setAddApproverDialogOpen(false), children: "Cancel" }), _jsx(Button, { variant: "contained", onClick: handleAddApprover, disabled: !selectedApprover, children: "Add Approver" })] })] })] }));
};
export default ProposalApprovalWorkflow;
