import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Box, Typography, Button, Card, CardContent, Grid, Divider, Chip, CircularProgress, Alert, IconButton, Tooltip, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, Dialog, DialogTitle, DialogContent, DialogActions, useTheme, useMediaQuery, Tabs, Tab, } from '@mui/material';
import { CompareArrows as CompareIcon, Add as AddIcon, Delete as DeleteIcon, Refresh as RefreshIcon, Download as DownloadIcon, CheckCircle as CheckCircleIcon, Cancel as CancelIcon, Visibility as ViewIcon, Timeline as TimelineIcon, AttachMoney as MoneyIcon, Description as DescriptionIcon, } from '@mui/icons-material';
import { format } from 'date-fns';
import { ProposalService } from '../../../services/proposal-service';
import { formatCurrency } from '../../../utils/formatters';
import DiffViewer from 'react-diff-viewer';
/**
 * ProposalComparisonView Component
 *
 * This component displays a comparison view of multiple proposals.
 */
const ProposalComparisonView = ({ tenantId, initialProposalIds = [], onViewProposal, }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));
    // State
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [proposals, setProposals] = useState([]);
    const [selectedProposalIds, setSelectedProposalIds] = useState(initialProposalIds);
    const [selectedProposals, setSelectedProposals] = useState([]);
    const [availableProposals, setAvailableProposals] = useState([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [addDialogOpen, setAddDialogOpen] = useState(false);
    const [diffDialogOpen, setDiffDialogOpen] = useState(false);
    const [diffSection, setDiffSection] = useState(null);
    const [activeTab, setActiveTab] = useState(0);
    // Fetch proposals
    useEffect(() => {
        const fetchProposals = async () => {
            try {
                setLoading(true);
                setError(null);
                // Fetch all proposals
                const response = await ProposalService.getProposals({
                    limit: 100,
                    page: 1,
                }, tenantId);
                setAvailableProposals(response.proposals);
                // Fetch selected proposals
                if (selectedProposalIds.length > 0) {
                    const selectedProposalsData = await Promise.all(selectedProposalIds.map(id => ProposalService.getProposalById(id, tenantId)));
                    setSelectedProposals(selectedProposalsData);
                }
            }
            catch (err) {
                console.error('Error fetching proposals:', err);
                setError('Failed to load proposals. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchProposals();
    }, [tenantId, selectedProposalIds]);
    // Handle refresh
    const handleRefresh = () => {
        const fetchProposals = async () => {
            try {
                setLoading(true);
                setError(null);
                // Fetch all proposals
                const response = await ProposalService.getProposals({
                    limit: 100,
                    page: 1,
                }, tenantId);
                setAvailableProposals(response.proposals);
                // Fetch selected proposals
                if (selectedProposalIds.length > 0) {
                    const selectedProposalsData = await Promise.all(selectedProposalIds.map(id => ProposalService.getProposalById(id, tenantId)));
                    setSelectedProposals(selectedProposalsData);
                }
            }
            catch (err) {
                console.error('Error fetching proposals:', err);
                setError('Failed to load proposals. Please try again.');
            }
            finally {
                setLoading(false);
            }
        };
        fetchProposals();
    };
    // Handle add proposal
    const handleAddProposal = (proposalId) => {
        if (selectedProposalIds.includes(proposalId))
            return;
        setSelectedProposalIds([...selectedProposalIds, proposalId]);
        setAddDialogOpen(false);
    };
    // Handle remove proposal
    const handleRemoveProposal = (proposalId) => {
        setSelectedProposalIds(selectedProposalIds.filter(id => id !== proposalId));
    };
    // Handle tab change
    const handleTabChange = (event, newValue) => {
        setActiveTab(newValue);
    };
    // Handle view diff
    const handleViewDiff = (sectionTitle, leftContent, rightContent) => {
        setDiffSection({
            title: sectionTitle,
            left: leftContent,
            right: rightContent,
        });
        setDiffDialogOpen(true);
    };
    // Filter available proposals
    const filteredAvailableProposals = availableProposals.filter(proposal => {
        if (selectedProposalIds.includes(proposal._id))
            return false;
        if (searchQuery) {
            const query = searchQuery.toLowerCase();
            return (proposal.title.toLowerCase().includes(query) ||
                proposal.description?.toLowerCase().includes(query) ||
                proposal.status.toLowerCase().includes(query));
        }
        return true;
    });
    // Render loading state
    if (loading && selectedProposals.length === 0) {
        return (_jsx(Box, { display: "flex", justifyContent: "center", alignItems: "center", height: "400px", children: _jsx(CircularProgress, {}) }));
    }
    // Render error state
    if (error) {
        return (_jsxs(Alert, { severity: "error", sx: { mb: 2 }, children: [error, _jsx(Button, { variant: "outlined", size: "small", onClick: handleRefresh, startIcon: _jsx(RefreshIcon, {}), sx: { ml: 2 }, children: "Retry" })] }));
    }
    return (_jsxs(Box, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", mb: 3, children: [_jsx(Typography, { variant: "h4", children: "Proposal Comparison" }), _jsxs(Box, { children: [_jsx(Button, { variant: "outlined", startIcon: _jsx(RefreshIcon, {}), onClick: handleRefresh, sx: { mr: 1 }, children: "Refresh" }), _jsx(Button, { variant: "contained", startIcon: _jsx(AddIcon, {}), onClick: () => setAddDialogOpen(true), children: "Add Proposal" })] })] }), selectedProposals.length === 0 ? (_jsx(Alert, { severity: "info", children: "No proposals selected for comparison. Click \"Add Proposal\" to select proposals to compare." })) : (_jsxs(Box, { children: [_jsx(Grid, { container: true, spacing: 3, mb: 3, children: selectedProposals.map((proposal) => (_jsx(Grid, { item: true, xs: 12, sm: 6, md: 4, children: _jsx(Card, { children: _jsxs(CardContent, { children: [_jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "flex-start", children: [_jsx(Typography, { variant: "h6", noWrap: true, sx: { maxWidth: '200px' }, children: proposal.title }), _jsx(IconButton, { size: "small", color: "error", onClick: () => handleRemoveProposal(proposal._id), children: _jsx(DeleteIcon, {}) })] }), _jsx(Typography, { variant: "body2", color: "text.secondary", gutterBottom: true, children: proposal.description }), _jsx(Divider, { sx: { my: 1 } }), _jsxs(Box, { display: "flex", flexWrap: "wrap", gap: 0.5, mb: 1, children: [_jsx(Chip, { label: proposal.status.toUpperCase(), color: proposal.status === 'accepted' ? 'success' :
                                                        proposal.status === 'rejected' ? 'error' :
                                                            proposal.status === 'viewed' ? 'info' :
                                                                proposal.status === 'sent' ? 'primary' :
                                                                    'default', size: "small" }), proposal.sections?.length > 0 && (_jsx(Chip, { label: `${proposal.sections.length} Sections`, size: "small" }))] }), _jsxs(Typography, { variant: "caption", display: "block", color: "text.secondary", children: ["Created ", format(new Date(proposal.createdAt), 'PPP')] })] }) }) }, proposal._id))) }), _jsx(Paper, { sx: { mb: 3 }, children: _jsxs(Tabs, { value: activeTab, onChange: handleTabChange, indicatorColor: "primary", textColor: "primary", variant: isMobile ? "scrollable" : "fullWidth", scrollButtons: isMobile ? "auto" : undefined, children: [_jsx(Tab, { label: "Overview", icon: _jsx(DescriptionIcon, {}) }), _jsx(Tab, { label: "Content", icon: _jsx(CompareIcon, {}) }), _jsx(Tab, { label: "Pricing", icon: _jsx(MoneyIcon, {}) }), _jsx(Tab, { label: "Timeline", icon: _jsx(TimelineIcon, {}) })] }) }), activeTab === 0 && (_jsx(TableContainer, { component: Paper, children: _jsxs(Table, { children: [_jsx(TableHead, { children: _jsxs(TableRow, { children: [_jsx(TableCell, { children: "Feature" }), selectedProposals.map((proposal) => (_jsx(TableCell, { children: proposal.title }, proposal._id)))] }) }), _jsxs(TableBody, { children: [_jsxs(TableRow, { children: [_jsx(TableCell, { children: "Status" }), selectedProposals.map((proposal) => (_jsx(TableCell, { children: _jsx(Chip, { label: proposal.status.toUpperCase(), color: proposal.status === 'accepted' ? 'success' :
                                                            proposal.status === 'rejected' ? 'error' :
                                                                proposal.status === 'viewed' ? 'info' :
                                                                    proposal.status === 'sent' ? 'primary' :
                                                                        'default', size: "small" }) }, proposal._id)))] }), _jsxs(TableRow, { children: [_jsx(TableCell, { children: "Created" }), selectedProposals.map((proposal) => (_jsx(TableCell, { children: format(new Date(proposal.createdAt), 'PPP') }, proposal._id)))] }), _jsxs(TableRow, { children: [_jsx(TableCell, { children: "Last Updated" }), selectedProposals.map((proposal) => (_jsx(TableCell, { children: format(new Date(proposal.updatedAt), 'PPP') }, proposal._id)))] }), _jsxs(TableRow, { children: [_jsx(TableCell, { children: "Sections" }), selectedProposals.map((proposal) => (_jsx(TableCell, { children: proposal.sections?.length || 0 }, proposal._id)))] }), _jsxs(TableRow, { children: [_jsx(TableCell, { children: "Total Price" }), selectedProposals.map((proposal) => (_jsx(TableCell, { children: proposal.pricing?.total ? formatCurrency(proposal.pricing.total) : 'N/A' }, proposal._id)))] }), _jsxs(TableRow, { children: [_jsx(TableCell, { children: "AI Generated" }), selectedProposals.map((proposal) => (_jsx(TableCell, { children: proposal.aiGenerated ? (_jsx(CheckCircleIcon, { color: "success" })) : (_jsx(CancelIcon, { color: "error" })) }, proposal._id)))] }), _jsxs(TableRow, { children: [_jsx(TableCell, { children: "Actions" }), selectedProposals.map((proposal) => (_jsxs(TableCell, { children: [_jsx(Tooltip, { title: "View Proposal", children: _jsx(IconButton, { size: "small", onClick: () => onViewProposal && onViewProposal(proposal._id), children: _jsx(ViewIcon, {}) }) }), _jsx(Tooltip, { title: "Download", children: _jsx(IconButton, { size: "small", children: _jsx(DownloadIcon, {}) }) })] }, proposal._id)))] })] })] }) })), activeTab === 1 && (_jsx(TableContainer, { component: Paper, children: _jsxs(Table, { children: [_jsx(TableHead, { children: _jsxs(TableRow, { children: [_jsx(TableCell, { children: "Section" }), selectedProposals.map((proposal) => (_jsx(TableCell, { children: proposal.title }, proposal._id))), _jsx(TableCell, { children: "Compare" })] }) }), _jsx(TableBody, { children: Array.from(new Set(selectedProposals.flatMap(proposal => proposal.sections?.map((section) => section.title) || []))).map((sectionTitle) => (_jsxs(TableRow, { children: [_jsx(TableCell, { children: sectionTitle }), selectedProposals.map((proposal) => {
                                                const section = proposal.sections?.find((s) => s.title === sectionTitle);
                                                return (_jsx(TableCell, { children: section ? (_jsxs(Box, { children: [_jsxs(Typography, { variant: "body2", noWrap: true, sx: { maxWidth: '200px' }, children: [section.content.substring(0, 50), "..."] }), _jsx(Typography, { variant: "caption", color: "text.secondary", children: section.type })] })) : (_jsx(Typography, { variant: "body2", color: "text.secondary", children: "Not included" })) }, proposal._id));
                                            }), _jsx(TableCell, { children: selectedProposals.length === 2 && (_jsx(Tooltip, { title: "Compare Content", children: _jsx(IconButton, { size: "small", onClick: () => {
                                                            const leftProposal = selectedProposals[0];
                                                            const rightProposal = selectedProposals[1];
                                                            const leftSection = leftProposal.sections?.find((s) => s.title === sectionTitle);
                                                            const rightSection = rightProposal.sections?.find((s) => s.title === sectionTitle);
                                                            handleViewDiff(sectionTitle, leftSection?.content || '', rightSection?.content || '');
                                                        }, children: _jsx(CompareIcon, {}) }) })) })] }, sectionTitle))) })] }) })), activeTab === 2 && (_jsx(TableContainer, { component: Paper, children: _jsxs(Table, { children: [_jsx(TableHead, { children: _jsxs(TableRow, { children: [_jsx(TableCell, { children: "Item" }), selectedProposals.map((proposal) => (_jsx(TableCell, { children: proposal.title }, proposal._id)))] }) }), _jsxs(TableBody, { children: [Array.from(new Set(selectedProposals.flatMap(proposal => proposal.pricing?.items?.map((item) => item.name) || []))).map((itemName) => (_jsxs(TableRow, { children: [_jsx(TableCell, { children: itemName }), selectedProposals.map((proposal) => {
                                                    const item = proposal.pricing?.items?.find((i) => i.name === itemName);
                                                    return (_jsx(TableCell, { children: item ? (_jsxs(Box, { children: [_jsxs(Typography, { variant: "body2", children: [formatCurrency(item.unitPrice), " x ", item.quantity] }), _jsx(Typography, { variant: "body1", fontWeight: "bold", children: formatCurrency(item.total) })] })) : (_jsx(Typography, { variant: "body2", color: "text.secondary", children: "Not included" })) }, proposal._id));
                                                })] }, itemName))), _jsxs(TableRow, { children: [_jsx(TableCell, { sx: { fontWeight: 'bold' }, children: "Subtotal" }), selectedProposals.map((proposal) => (_jsx(TableCell, { sx: { fontWeight: 'bold' }, children: proposal.pricing?.subtotal ? formatCurrency(proposal.pricing.subtotal) : 'N/A' }, proposal._id)))] }), _jsxs(TableRow, { children: [_jsx(TableCell, { sx: { fontWeight: 'bold' }, children: "Total" }), selectedProposals.map((proposal) => (_jsx(TableCell, { sx: { fontWeight: 'bold' }, children: proposal.pricing?.total ? formatCurrency(proposal.pricing.total) : 'N/A' }, proposal._id)))] })] })] }) })), activeTab === 3 && (_jsx(TableContainer, { component: Paper, children: _jsxs(Table, { children: [_jsx(TableHead, { children: _jsxs(TableRow, { children: [_jsx(TableCell, { children: "Timeline" }), selectedProposals.map((proposal) => (_jsx(TableCell, { children: proposal.title }, proposal._id)))] }) }), _jsxs(TableBody, { children: [Array.from(new Set(selectedProposals.flatMap(proposal => proposal.timeline?.milestones?.map((milestone) => milestone.name) || []))).map((milestoneName) => (_jsxs(TableRow, { children: [_jsx(TableCell, { children: milestoneName }), selectedProposals.map((proposal) => {
                                                    const milestone = proposal.timeline?.milestones?.find((m) => m.name === milestoneName);
                                                    return (_jsx(TableCell, { children: milestone ? (_jsxs(Box, { children: [_jsxs(Typography, { variant: "body2", children: [milestone.duration, " ", milestone.durationUnit] }), _jsx(Typography, { variant: "caption", color: "text.secondary", children: milestone.description })] })) : (_jsx(Typography, { variant: "body2", color: "text.secondary", children: "Not included" })) }, proposal._id));
                                                })] }, milestoneName))), _jsxs(TableRow, { children: [_jsx(TableCell, { sx: { fontWeight: 'bold' }, children: "Total Duration" }), selectedProposals.map((proposal) => (_jsx(TableCell, { sx: { fontWeight: 'bold' }, children: proposal.timeline?.totalDuration ?
                                                        `${proposal.timeline.totalDuration} ${proposal.timeline.durationUnit}` :
                                                        'N/A' }, proposal._id)))] })] })] }) }))] })), _jsxs(Dialog, { open: addDialogOpen, onClose: () => setAddDialogOpen(false), maxWidth: "md", fullWidth: true, children: [_jsx(DialogTitle, { children: "Add Proposal to Comparison" }), _jsxs(DialogContent, { children: [_jsx(TextField, { fullWidth: true, placeholder: "Search proposals...", value: searchQuery, onChange: (e) => setSearchQuery(e.target.value), margin: "normal" }), _jsx(TableContainer, { component: Paper, sx: { mt: 2 }, children: _jsxs(Table, { children: [_jsx(TableHead, { children: _jsxs(TableRow, { children: [_jsx(TableCell, { children: "Title" }), _jsx(TableCell, { children: "Status" }), _jsx(TableCell, { children: "Created" }), _jsx(TableCell, { children: "Actions" })] }) }), _jsx(TableBody, { children: filteredAvailableProposals.map((proposal) => (_jsxs(TableRow, { children: [_jsx(TableCell, { children: proposal.title }), _jsx(TableCell, { children: _jsx(Chip, { label: proposal.status.toUpperCase(), color: proposal.status === 'accepted' ? 'success' :
                                                                proposal.status === 'rejected' ? 'error' :
                                                                    proposal.status === 'viewed' ? 'info' :
                                                                        proposal.status === 'sent' ? 'primary' :
                                                                            'default', size: "small" }) }), _jsx(TableCell, { children: format(new Date(proposal.createdAt), 'PPP') }), _jsx(TableCell, { children: _jsx(Button, { variant: "contained", size: "small", onClick: () => handleAddProposal(proposal._id), children: "Add" }) })] }, proposal._id))) })] }) })] }), _jsx(DialogActions, { children: _jsx(Button, { onClick: () => setAddDialogOpen(false), children: "Cancel" }) })] }), _jsxs(Dialog, { open: diffDialogOpen, onClose: () => setDiffDialogOpen(false), maxWidth: "lg", fullWidth: true, children: [_jsxs(DialogTitle, { children: ["Compare Section: ", diffSection?.title] }), _jsx(DialogContent, { children: diffSection && (_jsx(DiffViewer, { oldValue: diffSection.left, newValue: diffSection.right, splitView: true, leftTitle: selectedProposals[0]?.title || 'Left', rightTitle: selectedProposals[1]?.title || 'Right', useDarkTheme: theme.palette.mode === 'dark' })) }), _jsx(DialogActions, { children: _jsx(Button, { onClick: () => setDiffDialogOpen(false), children: "Close" }) })] })] }));
};
export default ProposalComparisonView;
