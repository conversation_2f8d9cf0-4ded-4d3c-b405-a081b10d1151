import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Button, Card, Input, Select, Textarea, Tabs, TabsContent, TabsList, TabsTrigger } from '@aizako/ui-kit';
import { useAuth, useTenant } from '@aizako/core-lib';
/**
 * Proposal form component
 */
export const ProposalForm = ({ proposal, opportunityId, companyId, contactIds, onSubmit, isLoading = false, }) => {
    const { user } = useAuth();
    const { tenant } = useTenant();
    const [formData, setFormData] = useState({
        title: '',
        description: '',
        opportunityId: opportunityId,
        companyId: companyId,
        contactIds: contactIds,
        sections: [],
        pricing: {
            currency: 'USD',
            items: [],
            subtotal: 0,
            total: 0,
        },
        terms: '',
        notes: '',
        owner: user?.id, // Type assertion for ObjectId compatibility
        createdBy: user?.id, // Type assertion for ObjectId compatibility
        tags: [],
        customFields: {},
        publicAccessEnabled: true,
        emailEnabled: true,
        downloadEnabled: true,
        downloadFormats: ['pdf', 'docx'],
        aiGenerated: false,
        ...proposal,
    });
    const [activeTab, setActiveTab] = useState('details');
    const [aiPrompt, setAiPrompt] = useState('');
    const [isGenerating, setIsGenerating] = useState(false);
    // Calculate pricing totals when items change
    useEffect(() => {
        if (formData.pricing?.items) {
            const subtotal = formData.pricing.items.reduce((sum, item) => sum + item.total, 0);
            const discount = formData.pricing.discount || 0;
            const tax = formData.pricing.tax || 0;
            const total = subtotal - discount + tax;
            setFormData(prev => ({
                ...prev,
                pricing: {
                    ...prev.pricing,
                    subtotal,
                    total,
                },
            }));
        }
    }, [formData.pricing?.items, formData.pricing?.discount, formData.pricing?.tax]);
    // Handle input change
    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value,
        }));
    };
    // Handle section change
    const handleSectionChange = (index, field, value) => {
        setFormData(prev => {
            const sections = [...(prev.sections || [])];
            sections[index] = {
                ...sections[index],
                [field]: value,
            };
            return {
                ...prev,
                sections,
            };
        });
    };
    // Add a new section
    const addSection = () => {
        const newSection = {
            id: crypto.randomUUID(),
            title: 'New Section',
            content: '',
            order: formData.sections?.length || 0,
            type: 'text',
            isVisible: true,
        };
        setFormData(prev => ({
            ...prev,
            sections: [...(prev.sections || []), newSection],
        }));
    };
    // Remove a section
    const removeSection = (index) => {
        setFormData(prev => {
            const sections = [...(prev.sections || [])];
            sections.splice(index, 1);
            // Update order of remaining sections
            sections.forEach((section, i) => {
                section.order = i;
            });
            return {
                ...prev,
                sections,
            };
        });
    };
    // Handle pricing item change
    const handlePricingItemChange = (index, field, value) => {
        setFormData(prev => {
            const items = [...(prev.pricing?.items || [])];
            items[index] = {
                ...items[index],
                [field]: value,
            };
            // Recalculate total for this item
            if (field === 'quantity' || field === 'unitPrice' || field === 'discount') {
                const quantity = Number(items[index].quantity);
                const unitPrice = Number(items[index].unitPrice);
                const discount = Number(items[index].discount || 0);
                const tax = Number(items[index].tax || 0);
                const subtotal = quantity * unitPrice;
                const total = subtotal - discount + tax;
                items[index].total = total;
            }
            return {
                ...prev,
                pricing: {
                    ...(prev.pricing || { currency: 'USD', subtotal: 0, total: 0 }),
                    items,
                },
            };
        });
    };
    // Add a new pricing item
    const addPricingItem = () => {
        const newItem = {
            id: crypto.randomUUID(),
            name: 'New Item',
            description: '',
            quantity: 1,
            unitPrice: 0,
            total: 0,
        };
        setFormData(prev => ({
            ...prev,
            pricing: {
                ...(prev.pricing || { currency: 'USD', subtotal: 0, total: 0 }),
                items: [...(prev.pricing?.items || []), newItem],
            },
        }));
    };
    // Remove a pricing item
    const removePricingItem = (index) => {
        setFormData(prev => {
            const items = [...(prev.pricing?.items || [])];
            items.splice(index, 1);
            return {
                ...prev,
                pricing: {
                    ...(prev.pricing || { currency: 'USD', subtotal: 0, total: 0 }),
                    items,
                },
            };
        });
    };
    // Generate proposal with AI
    const generateWithAI = async () => {
        if (!aiPrompt)
            return;
        setIsGenerating(true);
        try {
            // This would be replaced with an actual API call to generate content
            // For now, we'll just simulate a delay
            await new Promise(resolve => setTimeout(resolve, 2000));
            // Simulate AI-generated content
            const aiGeneratedProposal = {
                title: `Proposal for ${formData.companyId}`,
                description: 'AI-generated proposal based on your requirements',
                sections: [
                    {
                        id: crypto.randomUUID(),
                        title: 'Executive Summary',
                        content: `This proposal addresses the needs outlined in our discussions. We propose a comprehensive solution that will help you achieve your business objectives.`,
                        order: 0,
                        type: 'text',
                        isVisible: true,
                        aiGenerated: true,
                    },
                    {
                        id: crypto.randomUUID(),
                        title: 'Proposed Solution',
                        content: `Based on your requirements, we recommend the following solution...`,
                        order: 1,
                        type: 'text',
                        isVisible: true,
                        aiGenerated: true,
                    },
                    {
                        id: crypto.randomUUID(),
                        title: 'Timeline',
                        content: `The project will be completed in the following phases...`,
                        order: 2,
                        type: 'text',
                        isVisible: true,
                        aiGenerated: true,
                    },
                ],
                pricing: {
                    currency: 'USD',
                    items: [
                        {
                            id: crypto.randomUUID(),
                            name: 'Implementation',
                            description: 'Initial setup and configuration',
                            quantity: 1,
                            unitPrice: 5000,
                            total: 5000,
                        },
                        {
                            id: crypto.randomUUID(),
                            name: 'Monthly Subscription',
                            description: 'Ongoing service and support',
                            quantity: 12,
                            unitPrice: 1000,
                            total: 12000,
                        },
                    ],
                    subtotal: 17000,
                    total: 17000,
                },
                terms: 'Payment terms: 50% upfront, 50% upon completion',
                aiGenerated: true,
                aiPrompt,
            };
            setFormData(prev => ({
                ...prev,
                ...aiGeneratedProposal,
            }));
        }
        catch (error) {
            console.error('Error generating proposal with AI:', error);
        }
        finally {
            setIsGenerating(false);
        }
    };
    // Handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();
        await onSubmit(formData);
    };
    return (_jsxs("form", { onSubmit: handleSubmit, className: "space-y-6", children: [_jsxs(Tabs, { value: activeTab, onValueChange: setActiveTab, children: [_jsxs(TabsList, { children: [_jsx(TabsTrigger, { value: "details", children: "Details" }), _jsx(TabsTrigger, { value: "sections", children: "Sections" }), _jsx(TabsTrigger, { value: "pricing", children: "Pricing" }), _jsx(TabsTrigger, { value: "settings", children: "Settings" }), _jsx(TabsTrigger, { value: "ai", children: "AI Generator" })] }), _jsxs(TabsContent, { value: "details", className: "space-y-4 mt-4", children: [_jsxs("div", { children: [_jsx("label", { htmlFor: "title", className: "block text-sm font-medium", children: "Title" }), _jsx(Input, { id: "title", name: "title", value: formData.title, onChange: handleChange, required: true })] }), _jsxs("div", { children: [_jsx("label", { htmlFor: "description", className: "block text-sm font-medium", children: "Description" }), _jsx(Textarea, { id: "description", name: "description", value: formData.description, onChange: handleChange, rows: 3 })] }), _jsxs("div", { children: [_jsx("label", { htmlFor: "terms", className: "block text-sm font-medium", children: "Terms & Conditions" }), _jsx(Textarea, { id: "terms", name: "terms", value: formData.terms, onChange: handleChange, rows: 5 })] }), _jsxs("div", { children: [_jsx("label", { htmlFor: "notes", className: "block text-sm font-medium", children: "Internal Notes" }), _jsx(Textarea, { id: "notes", name: "notes", value: formData.notes, onChange: handleChange, rows: 3 })] })] }), _jsxs(TabsContent, { value: "sections", className: "space-y-4 mt-4", children: [_jsx(Button, { type: "button", onClick: addSection, variant: "outline", children: "Add Section" }), formData.sections?.map((section, index) => (_jsxs(Card, { className: "p-4", children: [_jsxs("div", { className: "flex justify-between items-center mb-2", children: [_jsxs("h3", { className: "text-lg font-medium", children: ["Section ", index + 1] }), _jsx(Button, { type: "button", onClick: () => removeSection(index), variant: "destructive", size: "sm", children: "Remove" })] }), _jsxs("div", { className: "space-y-4", children: [_jsxs("div", { children: [_jsx("label", { htmlFor: `section-${index}-title`, className: "block text-sm font-medium", children: "Title" }), _jsx(Input, { id: `section-${index}-title`, value: section.title, onChange: (e) => handleSectionChange(index, 'title', e.target.value), required: true })] }), _jsxs("div", { children: [_jsx("label", { htmlFor: `section-${index}-type`, className: "block text-sm font-medium", children: "Type" }), _jsxs(Select, { id: `section-${index}-type`, value: section.type, onChange: (e) => handleSectionChange(index, 'type', e.target.value), children: [_jsx("option", { value: "text", children: "Text" }), _jsx("option", { value: "pricing", children: "Pricing" }), _jsx("option", { value: "timeline", children: "Timeline" }), _jsx("option", { value: "team", children: "Team" }), _jsx("option", { value: "testimonials", children: "Testimonials" }), _jsx("option", { value: "images", children: "Images" }), _jsx("option", { value: "custom", children: "Custom" })] })] }), _jsxs("div", { children: [_jsx("label", { htmlFor: `section-${index}-content`, className: "block text-sm font-medium", children: "Content" }), _jsx(Textarea, { id: `section-${index}-content`, value: section.content, onChange: (e) => handleSectionChange(index, 'content', e.target.value), rows: 5, required: true })] }), _jsxs("div", { className: "flex items-center", children: [_jsx("input", { type: "checkbox", id: `section-${index}-visible`, checked: section.isVisible, onChange: (e) => handleSectionChange(index, 'isVisible', e.target.checked), className: "mr-2" }), _jsx("label", { htmlFor: `section-${index}-visible`, className: "text-sm", children: "Visible in proposal" })] })] })] }, section.id)))] }), _jsxs(TabsContent, { value: "pricing", className: "space-y-4 mt-4", children: [_jsxs("div", { children: [_jsx("label", { htmlFor: "currency", className: "block text-sm font-medium", children: "Currency" }), _jsxs(Select, { id: "currency", value: formData.pricing?.currency || 'USD', onChange: (e) => setFormData(prev => ({
                                            ...prev,
                                            pricing: {
                                                ...(prev.pricing || { items: [], subtotal: 0, total: 0 }),
                                                currency: e.target.value,
                                            },
                                        })), children: [_jsx("option", { value: "USD", children: "USD ($)" }), _jsx("option", { value: "EUR", children: "EUR (\u20AC)" }), _jsx("option", { value: "GBP", children: "GBP (\u00A3)" }), _jsx("option", { value: "CAD", children: "CAD ($)" }), _jsx("option", { value: "AUD", children: "AUD ($)" })] })] }), _jsx(Button, { type: "button", onClick: addPricingItem, variant: "outline", children: "Add Item" }), formData.pricing?.items?.map((item, index) => (_jsxs(Card, { className: "p-4", children: [_jsxs("div", { className: "flex justify-between items-center mb-2", children: [_jsxs("h3", { className: "text-lg font-medium", children: ["Item ", index + 1] }), _jsx(Button, { type: "button", onClick: () => removePricingItem(index), variant: "destructive", size: "sm", children: "Remove" })] }), _jsxs("div", { className: "space-y-4", children: [_jsxs("div", { children: [_jsx("label", { htmlFor: `item-${index}-name`, className: "block text-sm font-medium", children: "Name" }), _jsx(Input, { id: `item-${index}-name`, value: item.name, onChange: (e) => handlePricingItemChange(index, 'name', e.target.value), required: true })] }), _jsxs("div", { children: [_jsx("label", { htmlFor: `item-${index}-description`, className: "block text-sm font-medium", children: "Description" }), _jsx(Textarea, { id: `item-${index}-description`, value: item.description || '', onChange: (e) => handlePricingItemChange(index, 'description', e.target.value), rows: 2 })] }), _jsxs("div", { className: "grid grid-cols-2 gap-4", children: [_jsxs("div", { children: [_jsx("label", { htmlFor: `item-${index}-quantity`, className: "block text-sm font-medium", children: "Quantity" }), _jsx(Input, { id: `item-${index}-quantity`, type: "number", min: "0", step: "1", value: item.quantity, onChange: (e) => handlePricingItemChange(index, 'quantity', Number(e.target.value)), required: true })] }), _jsxs("div", { children: [_jsx("label", { htmlFor: `item-${index}-unitPrice`, className: "block text-sm font-medium", children: "Unit Price" }), _jsx(Input, { id: `item-${index}-unitPrice`, type: "number", min: "0", step: "0.01", value: item.unitPrice, onChange: (e) => handlePricingItemChange(index, 'unitPrice', Number(e.target.value)), required: true })] })] }), _jsxs("div", { className: "grid grid-cols-2 gap-4", children: [_jsxs("div", { children: [_jsx("label", { htmlFor: `item-${index}-discount`, className: "block text-sm font-medium", children: "Discount" }), _jsx(Input, { id: `item-${index}-discount`, type: "number", min: "0", step: "0.01", value: item.discount || 0, onChange: (e) => handlePricingItemChange(index, 'discount', Number(e.target.value)) })] }), _jsxs("div", { children: [_jsx("label", { htmlFor: `item-${index}-tax`, className: "block text-sm font-medium", children: "Tax" }), _jsx(Input, { id: `item-${index}-tax`, type: "number", min: "0", step: "0.01", value: item.tax || 0, onChange: (e) => handlePricingItemChange(index, 'tax', Number(e.target.value)) })] })] }), _jsxs("div", { children: [_jsx("label", { htmlFor: `item-${index}-total`, className: "block text-sm font-medium", children: "Total" }), _jsx(Input, { id: `item-${index}-total`, type: "number", value: item.total, readOnly: true, disabled: true })] }), _jsxs("div", { className: "flex items-center", children: [_jsx("input", { type: "checkbox", id: `item-${index}-optional`, checked: item.isOptional || false, onChange: (e) => handlePricingItemChange(index, 'isOptional', e.target.checked), className: "mr-2" }), _jsx("label", { htmlFor: `item-${index}-optional`, className: "text-sm", children: "Optional item" })] })] })] }, item.id))), _jsxs("div", { className: "mt-4 space-y-2", children: [_jsxs("div", { className: "flex justify-between", children: [_jsx("span", { children: "Subtotal:" }), _jsxs("span", { children: [formData.pricing?.currency, " ", formData.pricing?.subtotal?.toFixed(2)] })] }), _jsxs("div", { children: [_jsx("label", { htmlFor: "discount", className: "block text-sm font-medium", children: "Discount" }), _jsx(Input, { id: "discount", type: "number", min: "0", step: "0.01", value: formData.pricing?.discount || 0, onChange: (e) => setFormData(prev => ({
                                                    ...prev,
                                                    pricing: {
                                                        ...(prev.pricing || { items: [], subtotal: 0, total: 0, currency: 'USD' }),
                                                        discount: Number(e.target.value),
                                                    },
                                                })) })] }), _jsxs("div", { children: [_jsx("label", { htmlFor: "tax", className: "block text-sm font-medium", children: "Tax" }), _jsx(Input, { id: "tax", type: "number", min: "0", step: "0.01", value: formData.pricing?.tax || 0, onChange: (e) => setFormData(prev => ({
                                                    ...prev,
                                                    pricing: {
                                                        ...(prev.pricing || { items: [], subtotal: 0, total: 0, currency: 'USD' }),
                                                        tax: Number(e.target.value),
                                                    },
                                                })) })] }), _jsxs("div", { className: "flex justify-between font-bold", children: [_jsx("span", { children: "Total:" }), _jsxs("span", { children: [formData.pricing?.currency, " ", formData.pricing?.total?.toFixed(2)] })] })] })] }), _jsxs(TabsContent, { value: "settings", className: "space-y-4 mt-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx("h3", { className: "text-lg font-medium", children: "Sharing Options" }), _jsxs("div", { className: "flex items-center", children: [_jsx("input", { type: "checkbox", id: "publicAccessEnabled", checked: formData.publicAccessEnabled, onChange: (e) => setFormData(prev => ({ ...prev, publicAccessEnabled: e.target.checked })), className: "mr-2" }), _jsx("label", { htmlFor: "publicAccessEnabled", className: "text-sm", children: "Enable public access via link" })] }), _jsxs("div", { className: "flex items-center", children: [_jsx("input", { type: "checkbox", id: "emailEnabled", checked: formData.emailEnabled, onChange: (e) => setFormData(prev => ({ ...prev, emailEnabled: e.target.checked })), className: "mr-2" }), _jsx("label", { htmlFor: "emailEnabled", className: "text-sm", children: "Enable email sharing" })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx("h3", { className: "text-lg font-medium", children: "Download Options" }), _jsxs("div", { className: "flex items-center", children: [_jsx("input", { type: "checkbox", id: "downloadEnabled", checked: formData.downloadEnabled, onChange: (e) => setFormData(prev => ({ ...prev, downloadEnabled: e.target.checked })), className: "mr-2" }), _jsx("label", { htmlFor: "downloadEnabled", className: "text-sm", children: "Enable downloads" })] }), formData.downloadEnabled && (_jsxs("div", { className: "ml-6 space-y-2", children: [_jsxs("div", { className: "flex items-center", children: [_jsx("input", { type: "checkbox", id: "downloadPdf", checked: formData.downloadFormats?.includes('pdf'), onChange: (e) => {
                                                            const formats = [...(formData.downloadFormats || [])];
                                                            if (e.target.checked) {
                                                                if (!formats.includes('pdf'))
                                                                    formats.push('pdf');
                                                            }
                                                            else {
                                                                const index = formats.indexOf('pdf');
                                                                if (index !== -1)
                                                                    formats.splice(index, 1);
                                                            }
                                                            setFormData(prev => ({ ...prev, downloadFormats: formats }));
                                                        }, className: "mr-2" }), _jsx("label", { htmlFor: "downloadPdf", className: "text-sm", children: "PDF" })] }), _jsxs("div", { className: "flex items-center", children: [_jsx("input", { type: "checkbox", id: "downloadDocx", checked: formData.downloadFormats?.includes('docx'), onChange: (e) => {
                                                            const formats = [...(formData.downloadFormats || [])];
                                                            if (e.target.checked) {
                                                                if (!formats.includes('docx'))
                                                                    formats.push('docx');
                                                            }
                                                            else {
                                                                const index = formats.indexOf('docx');
                                                                if (index !== -1)
                                                                    formats.splice(index, 1);
                                                            }
                                                            setFormData(prev => ({ ...prev, downloadFormats: formats }));
                                                        }, className: "mr-2" }), _jsx("label", { htmlFor: "downloadDocx", className: "text-sm", children: "DOCX" })] }), _jsxs("div", { className: "flex items-center", children: [_jsx("input", { type: "checkbox", id: "downloadMd", checked: formData.downloadFormats?.includes('md'), onChange: (e) => {
                                                            const formats = [...(formData.downloadFormats || [])];
                                                            if (e.target.checked) {
                                                                if (!formats.includes('md'))
                                                                    formats.push('md');
                                                            }
                                                            else {
                                                                const index = formats.indexOf('md');
                                                                if (index !== -1)
                                                                    formats.splice(index, 1);
                                                            }
                                                            setFormData(prev => ({ ...prev, downloadFormats: formats }));
                                                        }, className: "mr-2" }), _jsx("label", { htmlFor: "downloadMd", className: "text-sm", children: "Markdown" })] })] }))] })] }), _jsxs(TabsContent, { value: "ai", className: "space-y-4 mt-4", children: [_jsxs("div", { children: [_jsx("label", { htmlFor: "aiPrompt", className: "block text-sm font-medium", children: "AI Prompt" }), _jsx(Textarea, { id: "aiPrompt", value: aiPrompt, onChange: (e) => setAiPrompt(e.target.value), placeholder: "Describe the proposal you want to generate...", rows: 5 })] }), _jsx(Button, { type: "button", onClick: generateWithAI, disabled: isGenerating || !aiPrompt, children: isGenerating ? 'Generating...' : 'Generate Proposal' }), formData.aiGenerated && (_jsx("div", { className: "mt-4 p-4 bg-green-50 border border-green-200 rounded-md", children: _jsx("p", { className: "text-green-700", children: "This proposal was generated with AI based on your prompt. You can edit any part of it as needed." }) }))] })] }), _jsx("div", { className: "flex justify-end space-x-2", children: _jsx(Button, { type: "submit", disabled: isLoading, children: isLoading ? 'Saving...' : 'Save Proposal' }) })] }));
};
export default ProposalForm;
