import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState } from 'react';
import { Button, Card, Select, Checkbox, RadioGroup, RadioGroupItem, Label } from '@aizako/ui-kit';
/**
 * Proposal download options component
 */
export const ProposalDownloadOptions = ({ proposal, onDownload, isLoading = false, }) => {
    const [format, setFormat] = useState('pdf');
    const [includeHeader, setIncludeHeader] = useState(true);
    const [includeFooter, setIncludeFooter] = useState(true);
    const [includeBranding, setIncludeBranding] = useState(true);
    const [includePageNumbers, setIncludePageNumbers] = useState(true);
    const [colorScheme, setColorScheme] = useState('default');
    const [paperSize, setPaperSize] = useState('a4');
    // Handle download
    const handleDownload = async () => {
        await onDownload(format, {
            includeHeader,
            includeFooter,
            includeBranding,
            includePageNumbers,
            colorScheme,
            paperSize,
        });
    };
    // Check if format is available
    const isFormatAvailable = (formatToCheck) => {
        if (!proposal.downloadFormats)
            return false;
        return proposal.downloadFormats.includes(formatToCheck);
    };
    return (_jsxs(Card, { className: "p-4", children: [_jsx("h2", { className: "text-xl font-semibold mb-4", children: "Download Options" }), _jsxs("div", { className: "space-y-6", children: [_jsxs("div", { children: [_jsx("label", { htmlFor: "format", className: "block text-sm font-medium mb-1", children: "Format" }), _jsxs(Select, { id: "format", value: format, onChange: (e) => setFormat(e.target.value), className: "w-full", children: [_jsxs("option", { value: "pdf", disabled: !isFormatAvailable('pdf'), children: ["PDF ", !isFormatAvailable('pdf') && '(Not Available)'] }), _jsxs("option", { value: "docx", disabled: !isFormatAvailable('docx'), children: ["DOCX ", !isFormatAvailable('docx') && '(Not Available)'] }), _jsxs("option", { value: "md", disabled: !isFormatAvailable('md'), children: ["Markdown ", !isFormatAvailable('md') && '(Not Available)'] })] })] }), format === 'pdf' && (_jsxs(_Fragment, { children: [_jsxs("div", { children: [_jsx("h3", { className: "text-md font-medium mb-2", children: "Layout Options" }), _jsxs("div", { className: "space-y-2", children: [_jsxs("div", { className: "flex items-center", children: [_jsx(Checkbox, { id: "includeHeader", checked: includeHeader, onChange: (e) => setIncludeHeader(e.target.checked) }), _jsx("label", { htmlFor: "includeHeader", className: "ml-2 text-sm", children: "Include header with logo" })] }), _jsxs("div", { className: "flex items-center", children: [_jsx(Checkbox, { id: "includeFooter", checked: includeFooter, onChange: (e) => setIncludeFooter(e.target.checked) }), _jsx("label", { htmlFor: "includeFooter", className: "ml-2 text-sm", children: "Include footer with contact information" })] }), _jsxs("div", { className: "flex items-center", children: [_jsx(Checkbox, { id: "includeBranding", checked: includeBranding, onChange: (e) => setIncludeBranding(e.target.checked) }), _jsx("label", { htmlFor: "includeBranding", className: "ml-2 text-sm", children: "Include company branding and colors" })] }), _jsxs("div", { className: "flex items-center", children: [_jsx(Checkbox, { id: "includePageNumbers", checked: includePageNumbers, onChange: (e) => setIncludePageNumbers(e.target.checked) }), _jsx("label", { htmlFor: "includePageNumbers", className: "ml-2 text-sm", children: "Include page numbers" })] })] })] }), _jsxs("div", { children: [_jsx("h3", { className: "text-md font-medium mb-2", children: "Color Scheme" }), _jsxs(RadioGroup, { value: colorScheme, onValueChange: setColorScheme, children: [_jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(RadioGroupItem, { value: "default", id: "colorDefault" }), _jsx(Label, { htmlFor: "colorDefault", children: "Default" })] }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(RadioGroupItem, { value: "professional", id: "colorProfessional" }), _jsx(Label, { htmlFor: "colorProfessional", children: "Professional (Blue)" })] }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(RadioGroupItem, { value: "creative", id: "colorCreative" }), _jsx(Label, { htmlFor: "colorCreative", children: "Creative (Purple)" })] }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(RadioGroupItem, { value: "modern", id: "colorModern" }), _jsx(Label, { htmlFor: "colorModern", children: "Modern (Teal)" })] }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(RadioGroupItem, { value: "classic", id: "colorClassic" }), _jsx(Label, { htmlFor: "colorClassic", children: "Classic (Gray)" })] })] })] }), _jsxs("div", { children: [_jsx("h3", { className: "text-md font-medium mb-2", children: "Paper Size" }), _jsxs(RadioGroup, { value: paperSize, onValueChange: setPaperSize, children: [_jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(RadioGroupItem, { value: "a4", id: "sizeA4" }), _jsx(Label, { htmlFor: "sizeA4", children: "A4" })] }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(RadioGroupItem, { value: "letter", id: "sizeLetter" }), _jsx(Label, { htmlFor: "sizeLetter", children: "US Letter" })] }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(RadioGroupItem, { value: "legal", id: "sizeLegal" }), _jsx(Label, { htmlFor: "sizeLegal", children: "US Legal" })] })] })] })] })), format === 'docx' && (_jsx("div", { className: "p-3 bg-blue-50 border border-blue-200 rounded-md", children: _jsx("p", { className: "text-sm text-blue-700", children: "The DOCX format will include editable content that can be modified in Microsoft Word or other compatible word processors. Basic styling and formatting will be preserved." }) })), format === 'md' && (_jsx("div", { className: "p-3 bg-blue-50 border border-blue-200 rounded-md", children: _jsx("p", { className: "text-sm text-blue-700", children: "The Markdown format provides plain text content with simple formatting that can be used in various Markdown-compatible applications and platforms." }) })), _jsx("div", { className: "flex justify-end", children: _jsx(Button, { onClick: handleDownload, disabled: isLoading, children: isLoading ? 'Preparing Download...' : `Download as ${format.toUpperCase()}` }) })] })] }));
};
export default ProposalDownloadOptions;
