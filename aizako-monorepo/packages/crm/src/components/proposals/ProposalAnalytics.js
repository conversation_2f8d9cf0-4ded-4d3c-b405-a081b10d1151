import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Card } from '@aizako/ui-kit';
import { formatDate } from '../../utils/formatters';
/**
 * Proposal analytics component
 */
export const ProposalAnalytics = ({ proposal, }) => {
    // Group events by date
    const eventsByDate = proposal.analyticsEvents?.reduce((acc, event) => {
        const date = new Date(event.timestamp).toLocaleDateString();
        if (!acc[date]) {
            acc[date] = [];
        }
        acc[date].push(event);
        return acc;
    }, {}) || {};
    // Calculate total view time
    const totalViewTime = proposal.analyticsEvents?.reduce((total, event) => {
        return total + (event.duration || 0);
    }, 0) || 0;
    // Calculate average view time
    const viewEvents = proposal.analyticsEvents?.filter(event => event.eventType === 'view') || [];
    const averageViewTime = viewEvents.length > 0
        ? (viewEvents.reduce((total, event) => total + (event.duration || 0), 0) / viewEvents.length)
        : 0;
    // Calculate section views
    const sectionViews = proposal.analyticsEvents?.filter(event => event.eventType === 'section_view') || [];
    const sectionViewCounts = sectionViews.reduce((acc, event) => {
        const sectionId = event.sectionId;
        if (sectionId) {
            if (!acc[sectionId]) {
                acc[sectionId] = 0;
            }
            acc[sectionId]++;
        }
        return acc;
    }, {});
    // Get section names
    const sectionNames = proposal.sections.reduce((acc, section) => {
        acc[section.id] = section.title;
        return acc;
    }, {});
    // Calculate device breakdown
    const deviceCounts = proposal.analyticsEvents?.reduce((acc, event) => {
        const device = event.device || 'Unknown';
        if (!acc[device]) {
            acc[device] = 0;
        }
        acc[device]++;
        return acc;
    }, {}) || {};
    // Calculate browser breakdown
    const browserCounts = proposal.analyticsEvents?.reduce((acc, event) => {
        let browser = 'Unknown';
        if (event.userAgent) {
            if (event.userAgent.includes('Chrome'))
                browser = 'Chrome';
            else if (event.userAgent.includes('Firefox'))
                browser = 'Firefox';
            else if (event.userAgent.includes('Safari'))
                browser = 'Safari';
            else if (event.userAgent.includes('Edge'))
                browser = 'Edge';
            else if (event.userAgent.includes('MSIE') || event.userAgent.includes('Trident'))
                browser = 'Internet Explorer';
        }
        if (!acc[browser]) {
            acc[browser] = 0;
        }
        acc[browser]++;
        return acc;
    }, {}) || {};
    return (_jsx("div", { className: "space-y-6", children: _jsxs(Card, { className: "p-4", children: [_jsx("h2", { className: "text-xl font-semibold mb-4", children: "Proposal Analytics" }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6", children: [_jsxs("div", { className: "bg-white p-4 rounded-lg border border-gray-200 shadow-sm", children: [_jsx("h3", { className: "text-sm font-medium text-gray-500", children: "Total Views" }), _jsx("p", { className: "text-2xl font-bold", children: proposal.viewCount || 0 })] }), _jsxs("div", { className: "bg-white p-4 rounded-lg border border-gray-200 shadow-sm", children: [_jsx("h3", { className: "text-sm font-medium text-gray-500", children: "Total View Time" }), _jsxs("p", { className: "text-2xl font-bold", children: [Math.round(totalViewTime / 60), " min"] })] }), _jsxs("div", { className: "bg-white p-4 rounded-lg border border-gray-200 shadow-sm", children: [_jsx("h3", { className: "text-sm font-medium text-gray-500", children: "Avg. View Time" }), _jsxs("p", { className: "text-2xl font-bold", children: [Math.round(averageViewTime), " sec"] })] }), _jsxs("div", { className: "bg-white p-4 rounded-lg border border-gray-200 shadow-sm", children: [_jsx("h3", { className: "text-sm font-medium text-gray-500", children: "Status" }), _jsx("p", { className: "text-2xl font-bold capitalize", children: proposal.status })] })] }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6", children: [_jsxs("div", { children: [_jsx("h3", { className: "text-lg font-medium mb-3", children: "View Timeline" }), Object.keys(eventsByDate).length > 0 ? (_jsx("div", { className: "space-y-4", children: Object.entries(eventsByDate).map(([date, events]) => (_jsxs("div", { className: "border-b border-gray-200 pb-3", children: [_jsx("h4", { className: "text-sm font-medium text-gray-700 mb-2", children: date }), _jsx("div", { className: "space-y-2", children: events.map((event, index) => (_jsxs("div", { className: "flex items-start", children: [_jsx("div", { className: "mr-2 mt-1", children: _jsx("div", { className: "h-2 w-2 rounded-full bg-blue-500" }) }), _jsxs("div", { children: [_jsxs("p", { className: "text-sm", children: [_jsx("span", { className: "font-medium capitalize", children: event.eventType.replace('_', ' ') }), ' - ', new Date(event.timestamp).toLocaleTimeString()] }), event.duration && (_jsxs("p", { className: "text-xs text-gray-500", children: ["Duration: ", event.duration, " seconds"] })), event.device && (_jsxs("p", { className: "text-xs text-gray-500", children: ["Device: ", event.device] })), event.location && (_jsxs("p", { className: "text-xs text-gray-500", children: ["Location: ", event.location] }))] })] }, index))) })] }, date))) })) : (_jsx("p", { className: "text-gray-500 italic", children: "No view data available" }))] }), _jsxs("div", { children: [_jsx("h3", { className: "text-lg font-medium mb-3", children: "Section Engagement" }), Object.keys(sectionViewCounts).length > 0 ? (_jsx("div", { className: "space-y-3", children: Object.entries(sectionViewCounts).map(([sectionId, count]) => (_jsx("div", { className: "flex items-center", children: _jsxs("div", { className: "w-full", children: [_jsxs("div", { className: "flex justify-between mb-1", children: [_jsx("span", { className: "text-sm font-medium", children: sectionNames[sectionId] || `Section ${sectionId}` }), _jsxs("span", { className: "text-sm text-gray-500", children: [count, " views"] })] }), _jsx("div", { className: "w-full bg-gray-200 rounded-full h-2", children: _jsx("div", { className: "bg-blue-600 h-2 rounded-full", style: { width: `${Math.min(100, (count / Math.max(...Object.values(sectionViewCounts))) * 100)}%` } }) })] }) }, sectionId))) })) : (_jsx("p", { className: "text-gray-500 italic", children: "No section engagement data available" })), _jsx("h3", { className: "text-lg font-medium mt-6 mb-3", children: "Device Breakdown" }), Object.keys(deviceCounts).length > 0 ? (_jsx("div", { className: "space-y-3", children: Object.entries(deviceCounts).map(([device, count]) => (_jsx("div", { className: "flex items-center", children: _jsxs("div", { className: "w-full", children: [_jsxs("div", { className: "flex justify-between mb-1", children: [_jsx("span", { className: "text-sm font-medium", children: device }), _jsxs("span", { className: "text-sm text-gray-500", children: [count, " events"] })] }), _jsx("div", { className: "w-full bg-gray-200 rounded-full h-2", children: _jsx("div", { className: "bg-green-600 h-2 rounded-full", style: { width: `${Math.min(100, (count / Math.max(...Object.values(deviceCounts))) * 100)}%` } }) })] }) }, device))) })) : (_jsx("p", { className: "text-gray-500 italic", children: "No device data available" })), _jsx("h3", { className: "text-lg font-medium mt-6 mb-3", children: "Browser Breakdown" }), Object.keys(browserCounts).length > 0 ? (_jsx("div", { className: "space-y-3", children: Object.entries(browserCounts).map(([browser, count]) => (_jsx("div", { className: "flex items-center", children: _jsxs("div", { className: "w-full", children: [_jsxs("div", { className: "flex justify-between mb-1", children: [_jsx("span", { className: "text-sm font-medium", children: browser }), _jsxs("span", { className: "text-sm text-gray-500", children: [count, " events"] })] }), _jsx("div", { className: "w-full bg-gray-200 rounded-full h-2", children: _jsx("div", { className: "bg-purple-600 h-2 rounded-full", style: { width: `${Math.min(100, (count / Math.max(...Object.values(browserCounts))) * 100)}%` } }) })] }) }, browser))) })) : (_jsx("p", { className: "text-gray-500 italic", children: "No browser data available" }))] })] }), _jsxs("div", { className: "mt-6", children: [_jsx("h3", { className: "text-lg font-medium mb-3", children: "Proposal History" }), _jsxs("div", { className: "space-y-3", children: [_jsxs("div", { className: "flex items-start", children: [_jsx("div", { className: "mr-2 mt-1", children: _jsx("div", { className: "h-3 w-3 rounded-full bg-blue-500" }) }), _jsxs("div", { children: [_jsxs("p", { className: "text-sm", children: [_jsx("span", { className: "font-medium", children: "Created" }), ' - ', formatDate(proposal.createdAt)] }), proposal.createdBy && (_jsxs("p", { className: "text-xs text-gray-500", children: ["By: ", proposal.createdBy.toString()] }))] })] }), proposal.sentAt && (_jsxs("div", { className: "flex items-start", children: [_jsx("div", { className: "mr-2 mt-1", children: _jsx("div", { className: "h-3 w-3 rounded-full bg-green-500" }) }), _jsxs("div", { children: [_jsxs("p", { className: "text-sm", children: [_jsx("span", { className: "font-medium", children: "Sent" }), ' - ', formatDate(proposal.sentAt)] }), proposal.sentBy && (_jsxs("p", { className: "text-xs text-gray-500", children: ["By: ", proposal.sentBy.toString()] }))] })] })), proposal.viewedAt && (_jsxs("div", { className: "flex items-start", children: [_jsx("div", { className: "mr-2 mt-1", children: _jsx("div", { className: "h-3 w-3 rounded-full bg-purple-500" }) }), _jsx("div", { children: _jsxs("p", { className: "text-sm", children: [_jsx("span", { className: "font-medium", children: "First Viewed" }), ' - ', formatDate(proposal.viewedAt)] }) })] })), proposal.acceptedAt && (_jsxs("div", { className: "flex items-start", children: [_jsx("div", { className: "mr-2 mt-1", children: _jsx("div", { className: "h-3 w-3 rounded-full bg-green-600" }) }), _jsxs("div", { children: [_jsxs("p", { className: "text-sm", children: [_jsx("span", { className: "font-medium", children: "Accepted" }), ' - ', formatDate(proposal.acceptedAt)] }), proposal.acceptedBy && (_jsxs("p", { className: "text-xs text-gray-500", children: ["By: ", proposal.acceptedBy.toString()] }))] })] })), proposal.rejectedAt && (_jsxs("div", { className: "flex items-start", children: [_jsx("div", { className: "mr-2 mt-1", children: _jsx("div", { className: "h-3 w-3 rounded-full bg-red-500" }) }), _jsxs("div", { children: [_jsxs("p", { className: "text-sm", children: [_jsx("span", { className: "font-medium", children: "Rejected" }), ' - ', formatDate(proposal.rejectedAt)] }), proposal.rejectedBy && (_jsxs("p", { className: "text-xs text-gray-500", children: ["By: ", proposal.rejectedBy.toString()] })), proposal.rejectionReason && (_jsxs("p", { className: "text-xs text-gray-500", children: ["Reason: ", proposal.rejectionReason] }))] })] }))] })] })] }) }));
};
export default ProposalAnalytics;
