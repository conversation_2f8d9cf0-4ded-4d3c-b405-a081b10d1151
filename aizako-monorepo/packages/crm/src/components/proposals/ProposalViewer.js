import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@aizako/ui-kit';
import { formatCurrency } from '../../utils/formatters';
/**
 * Proposal viewer component
 */
export const ProposalViewer = ({ proposal, isPublic = false, onAccept, onReject, onDownload, }) => {
    const [activeTab, setActiveTab] = useState('proposal');
    // Filter sections to only show visible ones
    const visibleSections = proposal.sections.filter(section => section.isVisible);
    // Format date
    const formatDate = (date) => {
        if (!date)
            return '';
        return new Date(date).toLocaleDateString();
    };
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex justify-between items-start", children: [_jsxs("div", { children: [_jsx("h1", { className: "text-2xl font-bold", children: proposal.title }), proposal.description && (_jsx("p", { className: "text-gray-600 mt-2", children: proposal.description }))] }), !isPublic && (_jsx("div", { className: "flex space-x-2", children: proposal.downloadEnabled && proposal.downloadFormats && proposal.downloadFormats.length > 0 && (_jsxs("div", { className: "relative inline-block", children: [_jsx(Button, { variant: "outline", children: "Download" }), _jsx("div", { className: "absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 hidden group-hover:block", children: _jsxs("div", { className: "py-1", children: [proposal.downloadFormats.includes('pdf') && (_jsx("button", { onClick: () => onDownload && onDownload('pdf'), className: "block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100", children: "Download as PDF" })), proposal.downloadFormats.includes('docx') && (_jsx("button", { onClick: () => onDownload && onDownload('docx'), className: "block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100", children: "Download as DOCX" })), proposal.downloadFormats.includes('md') && (_jsx("button", { onClick: () => onDownload && onDownload('md'), className: "block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100", children: "Download as Markdown" }))] }) })] })) }))] }), isPublic && (_jsxs("div", { className: "flex justify-between items-center", children: [_jsxs("div", { className: "text-sm text-gray-500", children: [_jsxs("span", { children: ["Created: ", formatDate(proposal.createdAt)] }), proposal.expiresAt && (_jsxs("span", { className: "ml-4", children: ["Expires: ", formatDate(proposal.expiresAt)] }))] }), _jsxs("div", { className: "flex space-x-2", children: [proposal.status === 'sent' && (_jsxs(_Fragment, { children: [_jsx(Button, { onClick: onAccept, variant: "success", children: "Accept Proposal" }), _jsx(Button, { onClick: onReject, variant: "destructive", children: "Decline" })] })), proposal.downloadEnabled && proposal.downloadFormats && proposal.downloadFormats.length > 0 && (_jsxs("div", { className: "relative inline-block group", children: [_jsx(Button, { variant: "outline", children: "Download" }), _jsx("div", { className: "absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 hidden group-hover:block", children: _jsxs("div", { className: "py-1", children: [proposal.downloadFormats.includes('pdf') && (_jsx("button", { onClick: () => onDownload && onDownload('pdf'), className: "block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100", children: "Download as PDF" })), proposal.downloadFormats.includes('docx') && (_jsx("button", { onClick: () => onDownload && onDownload('docx'), className: "block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100", children: "Download as DOCX" })), proposal.downloadFormats.includes('md') && (_jsx("button", { onClick: () => onDownload && onDownload('md'), className: "block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100", children: "Download as Markdown" }))] }) })] }))] })] })), !isPublic && (_jsxs(Tabs, { value: activeTab, onValueChange: setActiveTab, children: [_jsxs(TabsList, { children: [_jsx(TabsTrigger, { value: "proposal", children: "Proposal" }), _jsx(TabsTrigger, { value: "details", children: "Details" }), _jsx(TabsTrigger, { value: "analytics", children: "Analytics" })] }), _jsx(TabsContent, { value: "proposal", className: "mt-4", children: _jsx(ProposalContent, { proposal: proposal, visibleSections: visibleSections }) }), _jsx(TabsContent, { value: "details", className: "mt-4", children: _jsxs(Card, { className: "p-4", children: [_jsx("h2", { className: "text-xl font-semibold mb-4", children: "Proposal Details" }), _jsxs("div", { className: "grid grid-cols-2 gap-4", children: [_jsxs("div", { children: [_jsx("h3", { className: "text-sm font-medium text-gray-500", children: "Status" }), _jsx("p", { children: proposal.status })] }), _jsxs("div", { children: [_jsx("h3", { className: "text-sm font-medium text-gray-500", children: "Created" }), _jsx("p", { children: formatDate(proposal.createdAt) })] }), proposal.sentAt && (_jsxs("div", { children: [_jsx("h3", { className: "text-sm font-medium text-gray-500", children: "Sent" }), _jsx("p", { children: formatDate(proposal.sentAt) })] })), proposal.viewedAt && (_jsxs("div", { children: [_jsx("h3", { className: "text-sm font-medium text-gray-500", children: "First Viewed" }), _jsx("p", { children: formatDate(proposal.viewedAt) })] })), proposal.lastViewedAt && (_jsxs("div", { children: [_jsx("h3", { className: "text-sm font-medium text-gray-500", children: "Last Viewed" }), _jsx("p", { children: formatDate(proposal.lastViewedAt) })] })), proposal.expiresAt && (_jsxs("div", { children: [_jsx("h3", { className: "text-sm font-medium text-gray-500", children: "Expires" }), _jsx("p", { children: formatDate(proposal.expiresAt) })] })), proposal.acceptedAt && (_jsxs("div", { children: [_jsx("h3", { className: "text-sm font-medium text-gray-500", children: "Accepted" }), _jsx("p", { children: formatDate(proposal.acceptedAt) })] })), proposal.rejectedAt && (_jsxs("div", { children: [_jsx("h3", { className: "text-sm font-medium text-gray-500", children: "Rejected" }), _jsx("p", { children: formatDate(proposal.rejectedAt) })] })), _jsxs("div", { children: [_jsx("h3", { className: "text-sm font-medium text-gray-500", children: "View Count" }), _jsx("p", { children: proposal.viewCount })] }), _jsxs("div", { children: [_jsx("h3", { className: "text-sm font-medium text-gray-500", children: "Public Access" }), _jsx("p", { children: proposal.publicAccessEnabled ? 'Enabled' : 'Disabled' })] }), _jsxs("div", { children: [_jsx("h3", { className: "text-sm font-medium text-gray-500", children: "Downloads" }), _jsx("p", { children: proposal.downloadEnabled ? 'Enabled' : 'Disabled' })] }), proposal.downloadEnabled && proposal.downloadFormats && (_jsxs("div", { children: [_jsx("h3", { className: "text-sm font-medium text-gray-500", children: "Download Formats" }), _jsx("p", { children: proposal.downloadFormats.join(', ') })] })), proposal.aiGenerated && (_jsxs("div", { className: "col-span-2", children: [_jsx("h3", { className: "text-sm font-medium text-gray-500", children: "AI Generated" }), _jsx("p", { children: "Yes" })] })), proposal.notes && (_jsxs("div", { className: "col-span-2", children: [_jsx("h3", { className: "text-sm font-medium text-gray-500", children: "Internal Notes" }), _jsx("p", { children: proposal.notes })] }))] })] }) }), _jsx(TabsContent, { value: "analytics", className: "mt-4", children: _jsxs(Card, { className: "p-4", children: [_jsx("h2", { className: "text-xl font-semibold mb-4", children: "Proposal Analytics" }), _jsxs("div", { className: "space-y-4", children: [_jsxs("div", { children: [_jsx("h3", { className: "text-lg font-medium", children: "View Summary" }), _jsxs("p", { children: ["Total Views: ", proposal.viewCount] }), proposal.viewedAt && (_jsxs("p", { children: ["First Viewed: ", formatDate(proposal.viewedAt)] })), proposal.lastViewedAt && (_jsxs("p", { children: ["Last Viewed: ", formatDate(proposal.lastViewedAt)] }))] }), proposal.analyticsEvents && proposal.analyticsEvents.length > 0 && (_jsxs("div", { children: [_jsx("h3", { className: "text-lg font-medium", children: "Event Timeline" }), _jsx("div", { className: "space-y-2 mt-2", children: proposal.analyticsEvents.map((event, index) => (_jsxs("div", { className: "flex items-start", children: [_jsx("div", { className: "mr-2 mt-1", children: _jsx("div", { className: "h-2 w-2 rounded-full bg-blue-500" }) }), _jsxs("div", { children: [_jsxs("p", { className: "text-sm", children: [_jsx("span", { className: "font-medium", children: event.eventType }), ' - ', new Date(event.timestamp).toLocaleString()] }), event.duration && (_jsxs("p", { className: "text-xs text-gray-500", children: ["Duration: ", event.duration, " seconds"] })), event.userAgent && (_jsx("p", { className: "text-xs text-gray-500", children: event.userAgent }))] })] }, index))) })] }))] })] }) })] })), isPublic && (_jsx(ProposalContent, { proposal: proposal, visibleSections: visibleSections }))] }));
};
/**
 * Proposal content component
 */
const ProposalContent = ({ proposal, visibleSections, }) => {
    return (_jsxs("div", { className: "space-y-8", children: [visibleSections.map((section) => (_jsxs("div", { className: "space-y-4", children: [_jsx("h2", { className: "text-xl font-semibold", children: section.title }), section.type === 'text' && (_jsx("div", { className: "prose max-w-none", children: section.content.split('\n').map((paragraph, i) => (_jsx("p", { children: paragraph }, i))) })), section.type === 'pricing' && (_jsx("div", { children: _jsx("p", { children: section.content }) })), section.type === 'timeline' && (_jsx("div", { children: _jsx("p", { children: section.content }) })), section.type === 'team' && (_jsx("div", { children: _jsx("p", { children: section.content }) })), section.type === 'testimonials' && (_jsx("div", { children: _jsx("p", { children: section.content }) })), section.type === 'images' && (_jsx("div", { children: _jsx("p", { children: section.content }) })), section.type === 'custom' && (_jsx("div", { children: _jsx("p", { children: section.content }) }))] }, section.id))), proposal.pricing && proposal.pricing.items && proposal.pricing.items.length > 0 && (_jsxs("div", { className: "space-y-4", children: [_jsx("h2", { className: "text-xl font-semibold", children: "Pricing" }), _jsxs("table", { className: "min-w-full divide-y divide-gray-200", children: [_jsx("thead", { className: "bg-gray-50", children: _jsxs("tr", { children: [_jsx("th", { scope: "col", className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Item" }), _jsx("th", { scope: "col", className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Quantity" }), _jsx("th", { scope: "col", className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Unit Price" }), _jsx("th", { scope: "col", className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Total" })] }) }), _jsx("tbody", { className: "bg-white divide-y divide-gray-200", children: proposal.pricing.items.map((item) => (_jsxs("tr", { children: [_jsxs("td", { className: "px-6 py-4 whitespace-nowrap", children: [_jsx("div", { className: "text-sm font-medium text-gray-900", children: item.name }), item.description && (_jsx("div", { className: "text-sm text-gray-500", children: item.description })), item.isOptional && (_jsx("div", { className: "text-xs text-gray-500 italic", children: "Optional" }))] }), _jsx("td", { className: "px-6 py-4 whitespace-nowrap text-sm text-gray-500", children: item.quantity }), _jsx("td", { className: "px-6 py-4 whitespace-nowrap text-sm text-gray-500", children: formatCurrency(item.unitPrice, proposal.pricing?.currency || 'USD') }), _jsx("td", { className: "px-6 py-4 whitespace-nowrap text-sm text-gray-900", children: formatCurrency(item.total, proposal.pricing?.currency || 'USD') })] }, item.id))) }), _jsxs("tfoot", { children: [_jsxs("tr", { children: [_jsx("td", { colSpan: 3, className: "px-6 py-4 text-sm font-medium text-gray-900 text-right", children: "Subtotal" }), _jsx("td", { className: "px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900", children: formatCurrency(proposal.pricing?.subtotal || 0, proposal.pricing?.currency || 'USD') })] }), proposal.pricing?.discount && proposal.pricing.discount > 0 && (_jsxs("tr", { children: [_jsx("td", { colSpan: 3, className: "px-6 py-4 text-sm font-medium text-gray-900 text-right", children: "Discount" }), _jsx("td", { className: "px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900", children: formatCurrency(proposal.pricing.discount, proposal.pricing?.currency || 'USD') })] })), proposal.pricing?.tax && proposal.pricing.tax > 0 && (_jsxs("tr", { children: [_jsx("td", { colSpan: 3, className: "px-6 py-4 text-sm font-medium text-gray-900 text-right", children: "Tax" }), _jsx("td", { className: "px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900", children: formatCurrency(proposal.pricing.tax, proposal.pricing?.currency || 'USD') })] })), _jsxs("tr", { children: [_jsx("td", { colSpan: 3, className: "px-6 py-4 text-base font-bold text-gray-900 text-right", children: "Total" }), _jsx("td", { className: "px-6 py-4 whitespace-nowrap text-base font-bold text-gray-900", children: formatCurrency(proposal.pricing?.total || 0, proposal.pricing?.currency || 'USD') })] })] })] })] })), proposal.terms && (_jsxs("div", { className: "space-y-4", children: [_jsx("h2", { className: "text-xl font-semibold", children: "Terms & Conditions" }), _jsx("div", { className: "prose max-w-none", children: proposal.terms.split('\n').map((paragraph, i) => (_jsx("p", { children: paragraph }, i))) })] }))] }));
};
export default ProposalViewer;
