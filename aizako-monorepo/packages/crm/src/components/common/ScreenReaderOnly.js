import { jsx as _jsx } from "react/jsx-runtime";
import React from 'react';
import { Box } from '@mui/material';
/**
 * ScreenReaderOnly Component
 *
 * This component renders content that is only visible to screen readers.
 * It can be used to provide additional context or instructions for screen reader users.
 */
const ScreenReaderOnly = ({ children, focusOnMount = false, focusable = false, id, ariaLive, ariaAtomic, ariaRelevant, ...boxProps }) => {
    const ref = React.useRef(null);
    React.useEffect(() => {
        if (focusOnMount && ref.current) {
            ref.current.focus();
        }
    }, [focusOnMount]);
    return (_jsx(Box, { ref: ref, sx: {
            position: 'absolute',
            width: '1px',
            height: '1px',
            padding: 0,
            margin: '-1px',
            overflow: 'hidden',
            clip: 'rect(0, 0, 0, 0)',
            whiteSpace: 'nowrap',
            borderWidth: 0,
        }, tabIndex: focusable ? 0 : undefined, id: id, "aria-live": ariaLive, "aria-atomic": ariaAtomic, "aria-relevant": ariaRelevant, ...boxProps, children: children }));
};
/**
 * Announce a message to screen readers
 * @param message Message to announce
 * @param assertive Whether to announce assertively (interrupting other announcements)
 */
export const announceToScreenReader = (message, assertive = false) => {
    // Create a temporary element for the announcement
    const element = document.createElement('div');
    element.setAttribute('aria-live', assertive ? 'assertive' : 'polite');
    element.setAttribute('aria-atomic', 'true');
    element.setAttribute('style', 'position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border-width: 0;');
    // Add to the DOM
    document.body.appendChild(element);
    // Set the message after a small delay to ensure it's announced
    setTimeout(() => {
        element.textContent = message;
        // Remove the element after it's been announced
        setTimeout(() => {
            document.body.removeChild(element);
        }, 1000);
    }, 100);
};
/**
 * Live Region Component
 *
 * This component creates a live region for screen reader announcements.
 */
export const LiveRegion = ({ id, children, assertive = false }) => {
    return (_jsx(ScreenReaderOnly, { id: id, ariaLive: assertive ? 'assertive' : 'polite', ariaAtomic: true, children: children }));
};
export default ScreenReaderOnly;
