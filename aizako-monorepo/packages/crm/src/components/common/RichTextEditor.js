import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState, useEffect, useRef } from 'react';
import { Box, Toolbar, Divider, IconButton, Tooltip, ToggleButton, ToggleButtonGroup, Menu, MenuItem, Typography, TextField, Dialog, DialogTitle, DialogContent, DialogActions, Button } from '@mui/material';
import { FormatBold as BoldIcon, FormatItalic as ItalicIcon, FormatUnderlined as UnderlineIcon, FormatStrikethrough as StrikethroughIcon, FormatListBulleted as BulletListIcon, FormatListNumbered as NumberedListIcon, FormatQuote as QuoteIcon, Code as CodeIcon, Link as LinkIcon, Image as ImageIcon, FormatAlignLeft as AlignLeftIcon, FormatAlignCenter as AlignCenterIcon, FormatAlignRight as AlignRightIcon, FormatAlignJustify as AlignJustifyIcon, FormatColorText as TextColorIcon, FormatColorFill as BackgroundColorIcon, Title as HeadingIcon, FormatClear as ClearFormattingIcon, Undo as UndoIcon, Redo as RedoIcon } from '@mui/icons-material';
import { Editor, EditorState, RichUtils, convertFromHTML, ContentState, Modifier, AtomicBlockUtils } from 'draft-js';
import { stateToHTML } from 'draft-js-export-html';
import 'draft-js/dist/Draft.css';
/**
 * Rich Text Editor Component
 *
 * This component provides a WYSIWYG editor for rich text content.
 * It supports formatting, lists, links, images, and more.
 */
const RichTextEditor = ({ value, onChange, placeholder = 'Enter content...', minHeight = 200, maxHeight = 500, readOnly = false }) => {
    // Editor state
    const [editorState, setEditorState] = useState(() => {
        if (value) {
            const blocksFromHTML = convertFromHTML(value);
            const contentState = ContentState.createFromBlockArray(blocksFromHTML.contentBlocks, blocksFromHTML.entityMap);
            return EditorState.createWithContent(contentState);
        }
        return EditorState.createEmpty();
    });
    // UI state
    const [linkDialogOpen, setLinkDialogOpen] = useState(false);
    const [linkUrl, setLinkUrl] = useState('');
    const [linkText, setLinkText] = useState('');
    const [imageDialogOpen, setImageDialogOpen] = useState(false);
    const [imageUrl, setImageUrl] = useState('');
    const [imageAlt, setImageAlt] = useState('');
    const [colorMenuAnchor, setColorMenuAnchor] = useState(null);
    const [colorType, setColorType] = useState('text');
    const [headingMenuAnchor, setHeadingMenuAnchor] = useState(null);
    // Editor ref
    const editorRef = useRef(null);
    // Update HTML value when editor state changes
    useEffect(() => {
        const contentState = editorState.getCurrentContent();
        const html = stateToHTML(contentState);
        if (html !== value) {
            onChange(html);
        }
    }, [editorState, onChange, value]);
    // Update editor state when value changes externally
    useEffect(() => {
        if (!value) {
            setEditorState(EditorState.createEmpty());
            return;
        }
        const currentContent = editorState.getCurrentContent();
        const currentHtml = stateToHTML(currentContent);
        if (value !== currentHtml) {
            const blocksFromHTML = convertFromHTML(value);
            const contentState = ContentState.createFromBlockArray(blocksFromHTML.contentBlocks, blocksFromHTML.entityMap);
            setEditorState(EditorState.createWithContent(contentState));
        }
    }, [value]);
    // Handle editor state change
    const handleEditorChange = (state) => {
        setEditorState(state);
    };
    // Focus the editor
    const focusEditor = () => {
        if (editorRef.current) {
            editorRef.current.focus();
        }
    };
    // Toggle inline style
    const toggleInlineStyle = (style) => {
        handleEditorChange(RichUtils.toggleInlineStyle(editorState, style));
    };
    // Toggle block type
    const toggleBlockType = (blockType) => {
        handleEditorChange(RichUtils.toggleBlockType(editorState, blockType));
    };
    // Handle key command
    const handleKeyCommand = (command, editorState) => {
        const newState = RichUtils.handleKeyCommand(editorState, command);
        if (newState) {
            handleEditorChange(newState);
            return 'handled';
        }
        return 'not-handled';
    };
    // Add link
    const addLink = () => {
        const selection = editorState.getSelection();
        const contentState = editorState.getCurrentContent();
        const contentStateWithEntity = contentState.createEntity('LINK', 'MUTABLE', { url: linkUrl });
        const entityKey = contentStateWithEntity.getLastCreatedEntityKey();
        let nextEditorState;
        if (selection.isCollapsed()) {
            // If no text is selected, insert the link text
            const textWithEntity = Modifier.insertText(contentState, selection, linkText, undefined, entityKey);
            nextEditorState = EditorState.push(editorState, textWithEntity, 'insert-characters');
        }
        else {
            // If text is selected, apply the link to the selection
            nextEditorState = EditorState.push(editorState, contentStateWithEntity, 'apply-entity');
            nextEditorState = RichUtils.toggleLink(nextEditorState, nextEditorState.getSelection(), entityKey);
        }
        handleEditorChange(nextEditorState);
        setLinkDialogOpen(false);
        setLinkUrl('');
        setLinkText('');
    };
    // Add image
    const addImage = () => {
        const contentState = editorState.getCurrentContent();
        const contentStateWithEntity = contentState.createEntity('IMAGE', 'IMMUTABLE', { src: imageUrl, alt: imageAlt });
        const entityKey = contentStateWithEntity.getLastCreatedEntityKey();
        const newEditorState = EditorState.set(editorState, { currentContent: contentStateWithEntity });
        handleEditorChange(AtomicBlockUtils.insertAtomicBlock(newEditorState, entityKey, ' '));
        setImageDialogOpen(false);
        setImageUrl('');
        setImageAlt('');
    };
    // Apply text color
    const applyColor = (color) => {
        const style = colorType === 'text' ? `color-${color}` : `bgcolor-${color}`;
        toggleInlineStyle(style);
        setColorMenuAnchor(null);
    };
    // Apply heading
    const applyHeading = (level) => {
        toggleBlockType(level);
        setHeadingMenuAnchor(null);
    };
    // Clear formatting
    const clearFormatting = () => {
        const selection = editorState.getSelection();
        const contentState = editorState.getCurrentContent();
        const styles = editorState.getCurrentInlineStyle();
        // Remove all inline styles
        let nextContentState = contentState;
        styles.forEach(style => {
            if (style) {
                nextContentState = Modifier.removeInlineStyle(nextContentState, selection, style);
            }
        });
        // Convert block type to unstyled
        const blockType = RichUtils.getCurrentBlockType(editorState);
        if (blockType !== 'unstyled') {
            nextContentState = Modifier.setBlockType(nextContentState, selection, 'unstyled');
        }
        // Update editor state
        const nextEditorState = EditorState.push(editorState, nextContentState, 'change-block-type');
        handleEditorChange(nextEditorState);
    };
    // Undo
    const handleUndo = () => {
        handleEditorChange(EditorState.undo(editorState));
    };
    // Redo
    const handleRedo = () => {
        handleEditorChange(EditorState.redo(editorState));
    };
    // Get current inline style
    const currentInlineStyle = editorState.getCurrentInlineStyle();
    // Get current block type
    const currentBlockType = RichUtils.getCurrentBlockType(editorState);
    // Color options
    const colors = [
        { name: 'Red', value: '#f44336' },
        { name: 'Pink', value: '#e91e63' },
        { name: 'Purple', value: '#9c27b0' },
        { name: 'Deep Purple', value: '#673ab7' },
        { name: 'Indigo', value: '#3f51b5' },
        { name: 'Blue', value: '#2196f3' },
        { name: 'Light Blue', value: '#03a9f4' },
        { name: 'Cyan', value: '#00bcd4' },
        { name: 'Teal', value: '#009688' },
        { name: 'Green', value: '#4caf50' },
        { name: 'Light Green', value: '#8bc34a' },
        { name: 'Lime', value: '#cddc39' },
        { name: 'Yellow', value: '#ffeb3b' },
        { name: 'Amber', value: '#ffc107' },
        { name: 'Orange', value: '#ff9800' },
        { name: 'Deep Orange', value: '#ff5722' },
        { name: 'Brown', value: '#795548' },
        { name: 'Grey', value: '#9e9e9e' },
        { name: 'Blue Grey', value: '#607d8b' },
        { name: 'Black', value: '#000000' }
    ];
    return (_jsxs(Box, { sx: { border: '1px solid #ddd', borderRadius: 1, overflow: 'hidden' }, children: [!readOnly && (_jsxs(_Fragment, { children: [_jsxs(Toolbar, { variant: "dense", sx: { bgcolor: 'background.paper' }, children: [_jsxs(ToggleButtonGroup, { size: "small", sx: { mr: 1 }, children: [_jsx(ToggleButton, { value: "bold", selected: currentInlineStyle.has('BOLD'), onClick: () => toggleInlineStyle('BOLD'), children: _jsx(Tooltip, { title: "Bold", children: _jsx(BoldIcon, { fontSize: "small" }) }) }), _jsx(ToggleButton, { value: "italic", selected: currentInlineStyle.has('ITALIC'), onClick: () => toggleInlineStyle('ITALIC'), children: _jsx(Tooltip, { title: "Italic", children: _jsx(ItalicIcon, { fontSize: "small" }) }) }), _jsx(ToggleButton, { value: "underline", selected: currentInlineStyle.has('UNDERLINE'), onClick: () => toggleInlineStyle('UNDERLINE'), children: _jsx(Tooltip, { title: "Underline", children: _jsx(UnderlineIcon, { fontSize: "small" }) }) }), _jsx(ToggleButton, { value: "strikethrough", selected: currentInlineStyle.has('STRIKETHROUGH'), onClick: () => toggleInlineStyle('STRIKETHROUGH'), children: _jsx(Tooltip, { title: "Strikethrough", children: _jsx(StrikethroughIcon, { fontSize: "small" }) }) })] }), _jsx(Divider, { orientation: "vertical", flexItem: true, sx: { mx: 0.5 } }), _jsxs(ToggleButtonGroup, { size: "small", sx: { mr: 1 }, children: [_jsx(ToggleButton, { value: "unordered-list", selected: currentBlockType === 'unordered-list-item', onClick: () => toggleBlockType('unordered-list-item'), children: _jsx(Tooltip, { title: "Bullet List", children: _jsx(BulletListIcon, { fontSize: "small" }) }) }), _jsx(ToggleButton, { value: "ordered-list", selected: currentBlockType === 'ordered-list-item', onClick: () => toggleBlockType('ordered-list-item'), children: _jsx(Tooltip, { title: "Numbered List", children: _jsx(NumberedListIcon, { fontSize: "small" }) }) })] }), _jsx(Divider, { orientation: "vertical", flexItem: true, sx: { mx: 0.5 } }), _jsxs(ToggleButtonGroup, { size: "small", sx: { mr: 1 }, children: [_jsx(ToggleButton, { value: "left", selected: currentBlockType === 'left-align', onClick: () => toggleBlockType('left-align'), children: _jsx(Tooltip, { title: "Align Left", children: _jsx(AlignLeftIcon, { fontSize: "small" }) }) }), _jsx(ToggleButton, { value: "center", selected: currentBlockType === 'center-align', onClick: () => toggleBlockType('center-align'), children: _jsx(Tooltip, { title: "Align Center", children: _jsx(AlignCenterIcon, { fontSize: "small" }) }) }), _jsx(ToggleButton, { value: "right", selected: currentBlockType === 'right-align', onClick: () => toggleBlockType('right-align'), children: _jsx(Tooltip, { title: "Align Right", children: _jsx(AlignRightIcon, { fontSize: "small" }) }) }), _jsx(ToggleButton, { value: "justify", selected: currentBlockType === 'justify-align', onClick: () => toggleBlockType('justify-align'), children: _jsx(Tooltip, { title: "Justify", children: _jsx(AlignJustifyIcon, { fontSize: "small" }) }) })] }), _jsx(Divider, { orientation: "vertical", flexItem: true, sx: { mx: 0.5 } }), _jsxs(ToggleButtonGroup, { size: "small", sx: { mr: 1 }, children: [_jsx(ToggleButton, { value: "blockquote", selected: currentBlockType === 'blockquote', onClick: () => toggleBlockType('blockquote'), children: _jsx(Tooltip, { title: "Quote", children: _jsx(QuoteIcon, { fontSize: "small" }) }) }), _jsx(ToggleButton, { value: "code-block", selected: currentBlockType === 'code-block', onClick: () => toggleBlockType('code-block'), children: _jsx(Tooltip, { title: "Code Block", children: _jsx(CodeIcon, { fontSize: "small" }) }) })] }), _jsx(Divider, { orientation: "vertical", flexItem: true, sx: { mx: 0.5 } }), _jsx(Tooltip, { title: "Headings", children: _jsx(IconButton, { size: "small", onClick: (e) => setHeadingMenuAnchor(e.currentTarget), children: _jsx(HeadingIcon, { fontSize: "small" }) }) }), _jsx(Tooltip, { title: "Text Color", children: _jsx(IconButton, { size: "small", onClick: (e) => {
                                        setColorMenuAnchor(e.currentTarget);
                                        setColorType('text');
                                    }, children: _jsx(TextColorIcon, { fontSize: "small" }) }) }), _jsx(Tooltip, { title: "Background Color", children: _jsx(IconButton, { size: "small", onClick: (e) => {
                                        setColorMenuAnchor(e.currentTarget);
                                        setColorType('background');
                                    }, children: _jsx(BackgroundColorIcon, { fontSize: "small" }) }) }), _jsx(Divider, { orientation: "vertical", flexItem: true, sx: { mx: 0.5 } }), _jsx(Tooltip, { title: "Insert Link", children: _jsx(IconButton, { size: "small", onClick: () => setLinkDialogOpen(true), children: _jsx(LinkIcon, { fontSize: "small" }) }) }), _jsx(Tooltip, { title: "Insert Image", children: _jsx(IconButton, { size: "small", onClick: () => setImageDialogOpen(true), children: _jsx(ImageIcon, { fontSize: "small" }) }) }), _jsx(Divider, { orientation: "vertical", flexItem: true, sx: { mx: 0.5 } }), _jsx(Tooltip, { title: "Clear Formatting", children: _jsx(IconButton, { size: "small", onClick: clearFormatting, children: _jsx(ClearFormattingIcon, { fontSize: "small" }) }) }), _jsx(Box, { sx: { flexGrow: 1 } }), _jsx(Tooltip, { title: "Undo", children: _jsx(IconButton, { size: "small", onClick: handleUndo, disabled: editorState.getUndoStack().size === 0, children: _jsx(UndoIcon, { fontSize: "small" }) }) }), _jsx(Tooltip, { title: "Redo", children: _jsx(IconButton, { size: "small", onClick: handleRedo, disabled: editorState.getRedoStack().size === 0, children: _jsx(RedoIcon, { fontSize: "small" }) }) })] }), _jsx(Divider, {})] })), _jsx(Box, { sx: {
                    p: 2,
                    minHeight,
                    maxHeight: maxHeight || 'none',
                    overflow: 'auto',
                    bgcolor: 'background.paper',
                    '& .DraftEditor-root': {
                        height: '100%',
                    },
                    '& .public-DraftEditorPlaceholder-root': {
                        color: 'text.disabled',
                    },
                }, onClick: focusEditor, children: _jsx(Editor, { ref: editorRef, editorState: editorState, onChange: handleEditorChange, handleKeyCommand: handleKeyCommand, placeholder: placeholder, readOnly: readOnly }) }), _jsxs(Dialog, { open: linkDialogOpen, onClose: () => setLinkDialogOpen(false), maxWidth: "xs", fullWidth: true, children: [_jsx(DialogTitle, { children: "Insert Link" }), _jsxs(DialogContent, { children: [_jsx(TextField, { label: "URL", value: linkUrl, onChange: (e) => setLinkUrl(e.target.value), fullWidth: true, margin: "normal", variant: "outlined", placeholder: "https://example.com" }), _jsx(TextField, { label: "Text", value: linkText, onChange: (e) => setLinkText(e.target.value), fullWidth: true, margin: "normal", variant: "outlined", placeholder: "Link text", helperText: "Leave empty to use the selected text" })] }), _jsxs(DialogActions, { children: [_jsx(Button, { onClick: () => setLinkDialogOpen(false), children: "Cancel" }), _jsx(Button, { onClick: addLink, variant: "contained", disabled: !linkUrl, children: "Insert" })] })] }), _jsxs(Dialog, { open: imageDialogOpen, onClose: () => setImageDialogOpen(false), maxWidth: "xs", fullWidth: true, children: [_jsx(DialogTitle, { children: "Insert Image" }), _jsxs(DialogContent, { children: [_jsx(TextField, { label: "Image URL", value: imageUrl, onChange: (e) => setImageUrl(e.target.value), fullWidth: true, margin: "normal", variant: "outlined", placeholder: "https://example.com/image.jpg" }), _jsx(TextField, { label: "Alt Text", value: imageAlt, onChange: (e) => setImageAlt(e.target.value), fullWidth: true, margin: "normal", variant: "outlined", placeholder: "Image description" })] }), _jsxs(DialogActions, { children: [_jsx(Button, { onClick: () => setImageDialogOpen(false), children: "Cancel" }), _jsx(Button, { onClick: addImage, variant: "contained", disabled: !imageUrl, children: "Insert" })] })] }), _jsx(Menu, { anchorEl: colorMenuAnchor, open: Boolean(colorMenuAnchor), onClose: () => setColorMenuAnchor(null), children: _jsxs(Box, { sx: { p: 1, width: 220 }, children: [_jsx(Typography, { variant: "subtitle2", gutterBottom: true, children: colorType === 'text' ? 'Text Color' : 'Background Color' }), _jsx(Box, { sx: { display: 'flex', flexWrap: 'wrap', gap: 0.5 }, children: colors.map((color) => (_jsx(Tooltip, { title: color.name, children: _jsx(Box, { sx: {
                                        width: 24,
                                        height: 24,
                                        bgcolor: color.value,
                                        borderRadius: 0.5,
                                        cursor: 'pointer',
                                        border: '1px solid #ddd',
                                        '&:hover': {
                                            opacity: 0.8,
                                        },
                                    }, onClick: () => applyColor(color.value.replace('#', '')) }) }, color.value))) })] }) }), _jsxs(Menu, { anchorEl: headingMenuAnchor, open: Boolean(headingMenuAnchor), onClose: () => setHeadingMenuAnchor(null), children: [_jsx(MenuItem, { onClick: () => applyHeading('header-one'), children: _jsx(Typography, { variant: "h6", children: "Heading 1" }) }), _jsx(MenuItem, { onClick: () => applyHeading('header-two'), children: _jsx(Typography, { variant: "h6", sx: { fontSize: '1.25rem' }, children: "Heading 2" }) }), _jsx(MenuItem, { onClick: () => applyHeading('header-three'), children: _jsx(Typography, { variant: "h6", sx: { fontSize: '1.1rem' }, children: "Heading 3" }) }), _jsx(MenuItem, { onClick: () => applyHeading('header-four'), children: _jsx(Typography, { variant: "h6", sx: { fontSize: '1rem' }, children: "Heading 4" }) }), _jsx(MenuItem, { onClick: () => applyHeading('header-five'), children: _jsx(Typography, { variant: "h6", sx: { fontSize: '0.9rem' }, children: "Heading 5" }) }), _jsx(MenuItem, { onClick: () => applyHeading('header-six'), children: _jsx(Typography, { variant: "h6", sx: { fontSize: '0.8rem' }, children: "Heading 6" }) }), _jsx(Divider, {}), _jsx(MenuItem, { onClick: () => applyHeading('unstyled'), children: _jsx(Typography, { variant: "body1", children: "Normal Text" }) })] })] }));
};
export default RichTextEditor;
