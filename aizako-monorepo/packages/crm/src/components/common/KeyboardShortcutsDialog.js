import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, List, ListItem, ListItemText, Divider, Box, Chip, useTheme, useMediaQuery } from '@mui/material';
import { formatKeyCombination } from '../../utils/keyboard-shortcuts';
/**
 * Keyboard Shortcuts Dialog Component
 *
 * This component displays a dialog with keyboard shortcuts and their descriptions.
 */
const KeyboardShortcutsDialog = ({ open, onClose, shortcuts }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    // Group shortcuts by category
    const categories = {
        'General': shortcuts.filter(s => s.description.includes('Save') ||
            s.description.includes('Preview') ||
            s.description.includes('Close')),
        'Editing': shortcuts.filter(s => s.description.includes('section') ||
            s.description.includes('content')),
        'Actions': shortcuts.filter(s => s.description.includes('Download') ||
            s.description.includes('Send')),
        'Other': shortcuts.filter(s => !s.description.includes('Save') &&
            !s.description.includes('Preview') &&
            !s.description.includes('Close') &&
            !s.description.includes('section') &&
            !s.description.includes('content') &&
            !s.description.includes('Download') &&
            !s.description.includes('Send'))
    };
    return (_jsxs(Dialog, { open: open, onClose: onClose, maxWidth: "sm", fullWidth: true, "aria-labelledby": "keyboard-shortcuts-dialog-title", children: [_jsx(DialogTitle, { id: "keyboard-shortcuts-dialog-title", children: "Keyboard Shortcuts" }), _jsxs(DialogContent, { dividers: true, children: [Object.entries(categories).map(([category, categoryShortcuts]) => (categoryShortcuts.length > 0 && (_jsxs(Box, { sx: { mb: 3 }, children: [_jsx(Typography, { variant: "subtitle1", gutterBottom: true, children: category }), _jsx(Divider, { sx: { mb: 1 } }), _jsx(List, { dense: true, disablePadding: true, children: categoryShortcuts.map((shortcut, index) => (_jsx(ListItem, { sx: { py: 0.5 }, children: _jsx(ListItemText, { primary: shortcut.description, secondary: _jsx(Chip, { label: formatKeyCombination(shortcut.key), size: "small", variant: "outlined", sx: {
                                                fontFamily: 'monospace',
                                                fontWeight: 'bold',
                                                mt: 0.5
                                            } }), primaryTypographyProps: {
                                            variant: 'body2'
                                        }, sx: {
                                            display: 'flex',
                                            flexDirection: isMobile ? 'column' : 'row',
                                            justifyContent: 'space-between',
                                            alignItems: isMobile ? 'flex-start' : 'center'
                                        } }) }, index))) })] }, category)))), _jsx(Box, { sx: { mt: 2 }, children: _jsx(Typography, { variant: "body2", color: "text.secondary", children: "Note: On Mac, use \u2318 (Command) instead of Ctrl for most shortcuts." }) })] }), _jsx(DialogActions, { children: _jsx(Button, { onClick: onClose, color: "primary", children: "Close" }) })] }));
};
export default KeyboardShortcutsDialog;
