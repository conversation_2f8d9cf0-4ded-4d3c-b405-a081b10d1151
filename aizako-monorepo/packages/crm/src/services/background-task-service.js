import mongoose from 'mongoose';
import { EventEmitter } from 'events';
/**
 * Background task status
 */
export var TaskStatus;
(function (TaskStatus) {
    TaskStatus["PENDING"] = "pending";
    TaskStatus["RUNNING"] = "running";
    TaskStatus["COMPLETED"] = "completed";
    TaskStatus["FAILED"] = "failed";
    TaskStatus["CANCELLED"] = "cancelled";
})(TaskStatus || (TaskStatus = {}));
/**
 * Background task schema
 */
const BackgroundTaskSchema = new mongoose.Schema({
    tenantId: { type: String, required: true, index: true },
    userId: { type: String, index: true },
    taskType: { type: String, required: true, index: true },
    status: {
        type: String,
        enum: Object.values(TaskStatus),
        default: TaskStatus.PENDING,
        index: true,
    },
    progress: { type: Number, default: 0, min: 0, max: 100 },
    params: { type: Object, required: true },
    result: { type: mongoose.Schema.Types.Mixed },
    error: { type: String },
    startedAt: { type: Date },
    completedAt: { type: Date },
}, {
    timestamps: true,
});
// Create indexes
BackgroundTaskSchema.index({ tenantId: 1, taskType: 1, status: 1 });
BackgroundTaskSchema.index({ createdAt: 1 });
BackgroundTaskSchema.index({ status: 1, taskType: 1 });
// Create the model
export const BackgroundTask = mongoose.models.BackgroundTask ||
    mongoose.model('BackgroundTask', BackgroundTaskSchema);
/**
 * Background Task Service
 *
 * This service provides background task processing capabilities.
 */
export class BackgroundTaskService {
    constructor() {
        Object.defineProperty(this, "taskHandlers", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: new Map()
        });
        Object.defineProperty(this, "eventEmitter", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: new EventEmitter()
        });
        Object.defineProperty(this, "isProcessing", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: false
        });
        Object.defineProperty(this, "processingInterval", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: null
        });
    }
    /**
     * Get the singleton instance
     * @returns BackgroundTaskService instance
     */
    static getInstance() {
        if (!BackgroundTaskService.instance) {
            BackgroundTaskService.instance = new BackgroundTaskService();
        }
        return BackgroundTaskService.instance;
    }
    /**
     * Register a task handler
     * @param taskType Task type
     * @param handler Task handler function
     */
    registerTaskHandler(taskType, handler) {
        this.taskHandlers.set(taskType, handler);
        console.log(`Registered handler for task type: ${taskType}`);
    }
    /**
     * Start the background task processor
     * @param intervalMs Processing interval in milliseconds (default: 5000)
     */
    start(intervalMs = 5000) {
        if (this.processingInterval) {
            return;
        }
        console.log('Starting background task processor');
        this.processingInterval = setInterval(() => this.processTasks(), intervalMs);
        // Process tasks immediately
        this.processTasks();
    }
    /**
     * Stop the background task processor
     */
    stop() {
        if (this.processingInterval) {
            clearInterval(this.processingInterval);
            this.processingInterval = null;
            console.log('Stopped background task processor');
        }
    }
    /**
     * Create a new background task
     * @param taskType Task type
     * @param params Task parameters
     * @param tenantId Tenant ID
     * @param userId User ID (optional)
     * @returns Created task
     */
    async createTask(taskType, params, tenantId, userId) {
        const task = await BackgroundTask.create({
            tenantId,
            userId,
            taskType,
            status: TaskStatus.PENDING,
            progress: 0,
            params,
        });
        // Emit task created event
        this.eventEmitter.emit('taskCreated', task);
        return task;
    }
    /**
     * Get a task by ID
     * @param taskId Task ID
     * @returns Task or null if not found
     */
    async getTask(taskId) {
        return await BackgroundTask.findById(taskId);
    }
    /**
     * Get tasks by tenant ID and status
     * @param tenantId Tenant ID
     * @param status Task status (optional)
     * @param taskType Task type (optional)
     * @param limit Maximum number of tasks to return (default: 100)
     * @returns Array of tasks
     */
    async getTasks(tenantId, status, taskType, limit = 100) {
        const query = { tenantId };
        if (status) {
            query.status = status;
        }
        if (taskType) {
            query.taskType = taskType;
        }
        return await BackgroundTask.find(query)
            .sort({ createdAt: -1 })
            .limit(limit);
    }
    /**
     * Cancel a task
     * @param taskId Task ID
     * @param tenantId Tenant ID
     * @returns True if successful
     */
    async cancelTask(taskId, tenantId) {
        const task = await BackgroundTask.findOne({
            _id: new mongoose.Types.ObjectId(taskId),
            tenantId,
        });
        if (!task || task.status !== TaskStatus.PENDING) {
            return false;
        }
        task.status = TaskStatus.CANCELLED;
        await task.save();
        return true;
    }
    /**
     * Process pending tasks
     * @private
     */
    async processTasks() {
        if (this.isProcessing) {
            return;
        }
        this.isProcessing = true;
        try {
            // Find pending tasks
            const pendingTasks = await BackgroundTask.find({
                status: TaskStatus.PENDING,
            }).sort({ createdAt: 1 }).limit(10);
            if (pendingTasks.length === 0) {
                this.isProcessing = false;
                return;
            }
            console.log(`Processing ${pendingTasks.length} pending tasks`);
            // Process each task
            for (const task of pendingTasks) {
                await this.processTask(task);
            }
        }
        catch (error) {
            console.error('Error processing tasks:', error);
        }
        finally {
            this.isProcessing = false;
        }
    }
    /**
     * Process a single task
     * @param task Task to process
     * @private
     */
    async processTask(task) {
        const handler = this.taskHandlers.get(task.taskType);
        if (!handler) {
            console.error(`No handler registered for task type: ${task.taskType}`);
            task.status = TaskStatus.FAILED;
            task.error = `No handler registered for task type: ${task.taskType}`;
            await task.save();
            return;
        }
        // Update task status to running
        task.status = TaskStatus.RUNNING;
        task.startedAt = new Date();
        await task.save();
        try {
            // Create progress update function
            const updateProgress = async (progress) => {
                task.progress = Math.min(Math.max(progress, 0), 100);
                await task.save();
            };
            // Execute the task handler
            const result = await handler(task, updateProgress);
            // Update task status to completed
            task.status = TaskStatus.COMPLETED;
            task.progress = 100;
            task.result = result;
            task.completedAt = new Date();
            await task.save();
            // Emit task completed event
            this.eventEmitter.emit('taskCompleted', task);
        }
        catch (error) {
            console.error(`Error processing task ${task._id}:`, error);
            // Update task status to failed
            task.status = TaskStatus.FAILED;
            task.error = error instanceof Error ? error.message : String(error);
            await task.save();
            // Emit task failed event
            this.eventEmitter.emit('taskFailed', task);
        }
    }
    /**
     * Subscribe to task events
     * @param event Event name ('taskCreated', 'taskCompleted', 'taskFailed')
     * @param listener Event listener
     */
    on(event, listener) {
        this.eventEmitter.on(event, listener);
    }
    /**
     * Unsubscribe from task events
     * @param event Event name
     * @param listener Event listener
     */
    off(event, listener) {
        this.eventEmitter.off(event, listener);
    }
}
export default BackgroundTaskService;
