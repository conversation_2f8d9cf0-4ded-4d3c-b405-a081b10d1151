import axios from 'axios';
import { API_BASE_URL } from '../config';
import { getAuthToken } from '../utils/auth';
/**
 * Invoice Service
 *
 * This service provides methods for managing invoices.
 */
export class InvoiceService {
    /**
     * Get invoices by proposal
     * @param proposalId Proposal ID
     * @param tenantId Tenant ID
     * @returns Invoices
     */
    static async getInvoicesByProposal(proposalId, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.get(`${API_BASE_URL}/api/invoicing/invoices/proposal/${proposalId}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error getting invoices by proposal:', error);
            throw error;
        }
    }
    /**
     * Get invoice by ID
     * @param invoiceId Invoice ID
     * @param tenantId Tenant ID
     * @returns Invoice
     */
    static async getInvoiceById(invoiceId, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.get(`${API_BASE_URL}/api/invoicing/invoices/${invoiceId}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error getting invoice:', error);
            throw error;
        }
    }
    /**
     * Create invoice
     * @param invoiceData Invoice data
     * @param tenantId Tenant ID
     * @returns Created invoice
     */
    static async createInvoice(invoiceData, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.post(`${API_BASE_URL}/api/invoicing/invoices`, invoiceData, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error creating invoice:', error);
            throw error;
        }
    }
    /**
     * Update invoice
     * @param invoiceId Invoice ID
     * @param invoiceData Invoice data
     * @param tenantId Tenant ID
     * @returns Updated invoice
     */
    static async updateInvoice(invoiceId, invoiceData, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.put(`${API_BASE_URL}/api/invoicing/invoices/${invoiceId}`, invoiceData, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error updating invoice:', error);
            throw error;
        }
    }
    /**
     * Delete invoice
     * @param invoiceId Invoice ID
     * @param tenantId Tenant ID
     * @returns Success status
     */
    static async deleteInvoice(invoiceId, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.delete(`${API_BASE_URL}/api/invoicing/invoices/${invoiceId}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error deleting invoice:', error);
            throw error;
        }
    }
    /**
     * Send invoice
     * @param invoiceId Invoice ID
     * @param emailData Email data
     * @param tenantId Tenant ID
     * @returns Success status
     */
    static async sendInvoice(invoiceId, emailData, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.post(`${API_BASE_URL}/api/invoicing/invoices/${invoiceId}/send`, emailData, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error sending invoice:', error);
            throw error;
        }
    }
    /**
     * Mark invoice as paid
     * @param invoiceId Invoice ID
     * @param paymentData Payment data
     * @param tenantId Tenant ID
     * @returns Updated invoice
     */
    static async markAsPaid(invoiceId, paymentData, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.post(`${API_BASE_URL}/api/invoicing/invoices/${invoiceId}/mark-paid`, paymentData, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error marking invoice as paid:', error);
            throw error;
        }
    }
    /**
     * Get invoice analytics
     * @param invoiceId Invoice ID
     * @param tenantId Tenant ID
     * @returns Invoice analytics
     */
    static async getInvoiceAnalytics(invoiceId, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.get(`${API_BASE_URL}/api/invoicing/invoices/${invoiceId}/analytics`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error getting invoice analytics:', error);
            throw error;
        }
    }
    /**
     * Get invoice public URL
     * @param invoiceId Invoice ID
     * @param tenantId Tenant ID
     * @returns Public URL
     */
    static async getInvoicePublicUrl(invoiceId, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.get(`${API_BASE_URL}/api/invoicing/invoices/${invoiceId}/public-url`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data.url;
        }
        catch (error) {
            console.error('Error getting invoice public URL:', error);
            throw error;
        }
    }
    /**
     * Download invoice
     * @param invoiceId Invoice ID
     * @param format Format (pdf, csv)
     * @param tenantId Tenant ID
     * @returns Download URL
     */
    static async downloadInvoice(invoiceId, format, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.get(`${API_BASE_URL}/api/invoicing/invoices/${invoiceId}/download`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
                params: {
                    format,
                },
            });
            return response.data.url;
        }
        catch (error) {
            console.error('Error downloading invoice:', error);
            throw error;
        }
    }
    /**
     * Get invoice payment history
     * @param invoiceId Invoice ID
     * @param tenantId Tenant ID
     * @returns Payment history
     */
    static async getInvoicePaymentHistory(invoiceId, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.get(`${API_BASE_URL}/api/invoicing/invoices/${invoiceId}/payment-history`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error getting invoice payment history:', error);
            throw error;
        }
    }
}
export default InvoiceService;
