import mongoose from 'mongoose';
import { Proposal } from '../models/proposal';
import { ProposalAnalyticsEvent } from '../models/proposal-analytics-event';
/**
 * Proposal Analytics Service
 *
 * This service provides analytics for proposals.
 */
export class ProposalAnalyticsService {
    /**
     * Record a proposal view
     * @param proposalId Proposal ID
     * @param data View data
     * @returns Updated proposal
     */
    static async recordView(proposalId, data) {
        try {
            // Create analytics event
            const event = await ProposalAnalyticsEvent.create({
                proposalId: new mongoose.Types.ObjectId(proposalId),
                eventType: 'view',
                data,
                timestamp: new Date(data.timestamp),
            });
            // Update proposal
            const proposal = await Proposal.findByIdAndUpdate(proposalId, {
                $inc: { viewCount: 1 },
                $set: {
                    lastViewedAt: new Date(data.timestamp),
                    status: 'viewed'
                },
                $push: { analyticsEvents: event._id },
                $setOnInsert: { viewedAt: new Date(data.timestamp) }
            }, { new: true, upsert: false });
            return proposal;
        }
        catch (error) {
            console.error('Error recording proposal view:', error);
            throw error;
        }
    }
    /**
     * Record a proposal download
     * @param proposalId Proposal ID
     * @param data Download data
     * @returns Updated proposal
     */
    static async recordDownload(proposalId, data) {
        try {
            // Create analytics event
            const event = await ProposalAnalyticsEvent.create({
                proposalId: new mongoose.Types.ObjectId(proposalId),
                eventType: 'download',
                data,
                timestamp: new Date(data.timestamp),
            });
            // Update proposal
            const proposal = await Proposal.findByIdAndUpdate(proposalId, {
                $push: { analyticsEvents: event._id },
            }, { new: true });
            return proposal;
        }
        catch (error) {
            console.error('Error recording proposal download:', error);
            throw error;
        }
    }
    /**
     * Record a proposal acceptance
     * @param proposalId Proposal ID
     * @param data Acceptance data
     * @returns Updated proposal
     */
    static async recordAcceptance(proposalId, data) {
        try {
            // Create analytics event
            const event = await ProposalAnalyticsEvent.create({
                proposalId: new mongoose.Types.ObjectId(proposalId),
                eventType: 'accept',
                data,
                timestamp: new Date(data.timestamp),
            });
            // Update proposal
            const proposal = await Proposal.findByIdAndUpdate(proposalId, {
                $set: {
                    status: 'accepted',
                    acceptedAt: new Date(data.timestamp),
                    acceptedBy: data.acceptedBy,
                },
                $push: { analyticsEvents: event._id },
            }, { new: true });
            return proposal;
        }
        catch (error) {
            console.error('Error recording proposal acceptance:', error);
            throw error;
        }
    }
    /**
     * Record a proposal rejection
     * @param proposalId Proposal ID
     * @param data Rejection data
     * @returns Updated proposal
     */
    static async recordRejection(proposalId, data) {
        try {
            // Create analytics event
            const event = await ProposalAnalyticsEvent.create({
                proposalId: new mongoose.Types.ObjectId(proposalId),
                eventType: 'reject',
                data,
                timestamp: new Date(data.timestamp),
            });
            // Update proposal
            const proposal = await Proposal.findByIdAndUpdate(proposalId, {
                $set: {
                    status: 'rejected',
                    rejectedAt: new Date(data.timestamp),
                    rejectedBy: data.rejectedBy,
                    rejectionReason: data.rejectionReason,
                },
                $push: { analyticsEvents: event._id },
            }, { new: true });
            return proposal;
        }
        catch (error) {
            console.error('Error recording proposal rejection:', error);
            throw error;
        }
    }
    /**
     * Get proposal analytics
     * @param proposalId Proposal ID
     * @returns Proposal analytics
     */
    static async getProposalAnalytics(proposalId) {
        try {
            // Get all analytics events for the proposal
            const events = await ProposalAnalyticsEvent.find({
                proposalId: new mongoose.Types.ObjectId(proposalId),
            }).sort({ timestamp: 1 });
            // Calculate analytics
            const views = events.filter(event => event.eventType === 'view').length;
            const downloads = events.filter(event => event.eventType === 'download').length;
            // Calculate unique views based on IP address
            const uniqueIPs = new Set(events
                .filter(event => event.eventType === 'view' && event.data?.ipAddress)
                .map(event => event.data.ipAddress));
            const uniqueViews = uniqueIPs.size;
            // Calculate downloads by format
            const downloadsByFormat = {};
            events
                .filter(event => event.eventType === 'download')
                .forEach(event => {
                const format = event.data?.format || 'unknown';
                downloadsByFormat[format] = (downloadsByFormat[format] || 0) + 1;
            });
            // Calculate views by date
            const viewsByDate = {};
            events
                .filter(event => event.eventType === 'view')
                .forEach(event => {
                const date = event.timestamp.toISOString().split('T')[0];
                viewsByDate[date] = (viewsByDate[date] || 0) + 1;
            });
            // Calculate views by hour
            const viewsByHour = {};
            events
                .filter(event => event.eventType === 'view')
                .forEach(event => {
                const hour = event.timestamp.getHours().toString().padStart(2, '0');
                viewsByHour[hour] = (viewsByHour[hour] || 0) + 1;
            });
            // Calculate views by location
            const viewsByLocation = {};
            events
                .filter(event => event.eventType === 'view' && event.data?.location)
                .forEach(event => {
                const location = event.data.location;
                viewsByLocation[location] = (viewsByLocation[location] || 0) + 1;
            });
            // Calculate views by referrer
            const viewsByReferrer = {};
            events
                .filter(event => event.eventType === 'view' && event.data?.referrer)
                .forEach(event => {
                const referrer = event.data.referrer;
                viewsByReferrer[referrer] = (viewsByReferrer[referrer] || 0) + 1;
            });
            // Calculate views by device
            const viewsByDevice = {};
            events
                .filter(event => event.eventType === 'view' && event.data?.userAgent)
                .forEach(event => {
                const userAgent = event.data.userAgent;
                const device = this.getDeviceFromUserAgent(userAgent);
                viewsByDevice[device] = (viewsByDevice[device] || 0) + 1;
            });
            // Calculate average view duration
            let totalDuration = 0;
            let durationCount = 0;
            // Group view events by IP address and calculate duration between consecutive views
            const viewsByIP = {};
            events
                .filter(event => event.eventType === 'view' && event.data?.ipAddress)
                .forEach(event => {
                const ip = event.data.ipAddress;
                if (!viewsByIP[ip]) {
                    viewsByIP[ip] = [];
                }
                viewsByIP[ip].push(event.timestamp);
            });
            // Calculate duration between consecutive views
            Object.values(viewsByIP).forEach(timestamps => {
                if (timestamps.length < 2)
                    return;
                for (let i = 1; i < timestamps.length; i++) {
                    const duration = timestamps[i].getTime() - timestamps[i - 1].getTime();
                    // Only count durations less than 30 minutes (1,800,000 ms)
                    if (duration < 1800000) {
                        totalDuration += duration;
                        durationCount++;
                    }
                }
            });
            const averageViewDuration = durationCount > 0 ? totalDuration / durationCount : 0;
            // Format events for response
            const formattedEvents = events.map(event => ({
                eventType: event.eventType,
                timestamp: event.timestamp,
                data: event.data,
            }));
            return {
                views,
                uniqueViews,
                downloads,
                downloadsByFormat,
                viewsByDate,
                viewsByHour,
                viewsByLocation,
                viewsByReferrer,
                viewsByDevice,
                averageViewDuration,
                events: formattedEvents,
            };
        }
        catch (error) {
            console.error('Error getting proposal analytics:', error);
            throw error;
        }
    }
    /**
     * Get tenant analytics
     * @param tenantId Tenant ID
     * @param startDate Start date
     * @param endDate End date
     * @returns Tenant analytics
     */
    static async getTenantAnalytics(tenantId, startDate, endDate) {
        try {
            // Get all proposals for the tenant
            const proposals = await Proposal.find({
                tenantId,
                createdAt: { $gte: startDate, $lte: endDate },
            });
            // Get all analytics events for the tenant's proposals
            const proposalIds = proposals.map(proposal => proposal._id);
            const events = await ProposalAnalyticsEvent.find({
                proposalId: { $in: proposalIds },
                timestamp: { $gte: startDate, $lte: endDate },
            });
            // Calculate analytics
            const totalProposals = proposals.length;
            const totalViews = events.filter(event => event.eventType === 'view').length;
            const totalDownloads = events.filter(event => event.eventType === 'download').length;
            const totalAcceptances = events.filter(event => event.eventType === 'accept').length;
            const totalRejections = events.filter(event => event.eventType === 'reject').length;
            // Calculate rates
            const sentProposals = proposals.filter(proposal => proposal.status === 'sent' ||
                proposal.status === 'viewed' ||
                proposal.status === 'accepted' ||
                proposal.status === 'rejected').length;
            const viewedProposals = proposals.filter(proposal => proposal.status === 'viewed' ||
                proposal.status === 'accepted' ||
                proposal.status === 'rejected').length;
            const viewRate = sentProposals > 0 ? viewedProposals / sentProposals : 0;
            const acceptanceRate = viewedProposals > 0 ? totalAcceptances / viewedProposals : 0;
            const rejectionRate = viewedProposals > 0 ? totalRejections / viewedProposals : 0;
            // Calculate proposals by status
            const proposalsByStatus = {};
            proposals.forEach(proposal => {
                const status = proposal.status || 'draft';
                proposalsByStatus[status] = (proposalsByStatus[status] || 0) + 1;
            });
            // Calculate views by date
            const viewsByDate = {};
            events
                .filter(event => event.eventType === 'view')
                .forEach(event => {
                const date = event.timestamp.toISOString().split('T')[0];
                viewsByDate[date] = (viewsByDate[date] || 0) + 1;
            });
            // Calculate acceptances by date
            const acceptancesByDate = {};
            events
                .filter(event => event.eventType === 'accept')
                .forEach(event => {
                const date = event.timestamp.toISOString().split('T')[0];
                acceptancesByDate[date] = (acceptancesByDate[date] || 0) + 1;
            });
            // Calculate top proposals
            const proposalViews = {};
            const proposalDownloads = {};
            events.forEach(event => {
                const proposalId = event.proposalId.toString();
                if (event.eventType === 'view') {
                    proposalViews[proposalId] = (proposalViews[proposalId] || 0) + 1;
                }
                else if (event.eventType === 'download') {
                    proposalDownloads[proposalId] = (proposalDownloads[proposalId] || 0) + 1;
                }
            });
            const topProposals = proposals
                .map((proposal) => ({
                proposalId: proposal._id.toString(),
                title: proposal.title,
                views: proposalViews[proposal._id.toString()] || 0,
                downloads: proposalDownloads[proposal._id.toString()] || 0,
                status: proposal.status || 'draft',
            }))
                .sort((a, b) => b.views - a.views)
                .slice(0, 10);
            return {
                totalProposals,
                totalViews,
                totalDownloads,
                totalAcceptances,
                totalRejections,
                viewRate,
                acceptanceRate,
                rejectionRate,
                proposalsByStatus,
                viewsByDate,
                acceptancesByDate,
                topProposals,
            };
        }
        catch (error) {
            console.error('Error getting tenant analytics:', error);
            throw error;
        }
    }
    /**
     * Get device from user agent
     * @param userAgent User agent string
     * @returns Device type
     * @private
     */
    static getDeviceFromUserAgent(userAgent) {
        if (!userAgent)
            return 'unknown';
        if (/iPhone|iPad|iPod/i.test(userAgent)) {
            return 'iOS';
        }
        else if (/Android/i.test(userAgent)) {
            return 'Android';
        }
        else if (/Windows Phone/i.test(userAgent)) {
            return 'Windows Phone';
        }
        else if (/Windows/i.test(userAgent)) {
            return 'Windows';
        }
        else if (/Macintosh/i.test(userAgent)) {
            return 'Mac';
        }
        else if (/Linux/i.test(userAgent)) {
            return 'Linux';
        }
        else {
            return 'Other';
        }
    }
}
export default ProposalAnalyticsService;
