// Email template service for CRM module
// TODO: Implement proper email template service
import { EmailTemplate } from '../models/email-template';
export class EmailTemplateService {
    async createEmailTemplate(data) {
        try {
            const template = new EmailTemplate({
                ...data,
                isActive: data.isActive !== undefined ? data.isActive : true,
                createdAt: new Date(),
                updatedAt: new Date(),
            });
            return await template.save();
        }
        catch (error) {
            console.error('Error creating email template:', error);
            throw new Error('Failed to create email template');
        }
    }
    async getEmailTemplate(id, tenantId) {
        try {
            return await EmailTemplate.findOne({ _id: id, tenantId })
                .populate('userId', 'name email');
        }
        catch (error) {
            console.error('Error getting email template:', error);
            throw new Error('Failed to get email template');
        }
    }
    async updateEmailTemplate(data) {
        try {
            const { id, ...updateData } = data;
            return await EmailTemplate.findOneAndUpdate({ _id: id, tenantId: data.tenantId }, { ...updateData, updatedAt: new Date() }, { new: true }).populate('userId', 'name email');
        }
        catch (error) {
            console.error('Error updating email template:', error);
            throw new Error('Failed to update email template');
        }
    }
    async deleteEmailTemplate(id, tenantId) {
        try {
            const result = await EmailTemplate.deleteOne({ _id: id, tenantId });
            return result.deletedCount > 0;
        }
        catch (error) {
            console.error('Error deleting email template:', error);
            throw new Error('Failed to delete email template');
        }
    }
    async getEmailTemplates(tenantId, filters = {}, page = 1, limit = 20) {
        try {
            const query = { tenantId };
            if (filters.type)
                query.type = filters.type;
            if (filters.isActive !== undefined)
                query.isActive = filters.isActive;
            if (filters.search) {
                query.$or = [
                    { name: { $regex: filters.search, $options: 'i' } },
                    { subject: { $regex: filters.search, $options: 'i' } },
                    { content: { $regex: filters.search, $options: 'i' } },
                ];
            }
            const skip = (page - 1) * limit;
            const [templates, total] = await Promise.all([
                EmailTemplate.find(query)
                    .sort({ createdAt: -1 })
                    .skip(skip)
                    .limit(limit)
                    .populate('userId', 'name email'),
                EmailTemplate.countDocuments(query),
            ]);
            return {
                templates,
                total,
                page,
                totalPages: Math.ceil(total / limit),
            };
        }
        catch (error) {
            console.error('Error getting email templates:', error);
            throw new Error('Failed to get email templates');
        }
    }
    async getEmailTemplatesByType(tenantId, type) {
        try {
            return await EmailTemplate.find({
                tenantId,
                type,
                isActive: true
            }).sort({ name: 1 });
        }
        catch (error) {
            console.error('Error getting email templates by type:', error);
            throw new Error('Failed to get email templates by type');
        }
    }
    async duplicateEmailTemplate(id, tenantId, newName) {
        try {
            const originalTemplate = await EmailTemplate.findOne({ _id: id, tenantId });
            if (!originalTemplate) {
                throw new Error('Template not found');
            }
            const duplicatedTemplate = new EmailTemplate({
                name: newName,
                subject: originalTemplate.subject,
                content: originalTemplate.content,
                category: originalTemplate.category,
                isActive: false, // Start as inactive
                variables: originalTemplate.variables,
                tenantId: originalTemplate.tenantId,
                owner: originalTemplate.owner,
                createdBy: originalTemplate.createdBy,
                createdAt: new Date(),
                updatedAt: new Date(),
            });
            return await duplicatedTemplate.save();
        }
        catch (error) {
            console.error('Error duplicating email template:', error);
            throw new Error('Failed to duplicate email template');
        }
    }
    async renderEmailTemplate(id, tenantId, variables = {}) {
        try {
            const template = await EmailTemplate.findOne({ _id: id, tenantId });
            if (!template) {
                throw new Error('Template not found');
            }
            if (!template.isActive) {
                throw new Error('Template is not active');
            }
            // Simple variable replacement (in a real implementation, use a proper template engine)
            let renderedSubject = template.subject;
            let renderedContent = template.content;
            Object.entries(variables).forEach(([key, value]) => {
                const placeholder = `{{${key}}}`;
                renderedSubject = renderedSubject.replace(new RegExp(placeholder, 'g'), String(value));
                renderedContent = renderedContent.replace(new RegExp(placeholder, 'g'), String(value));
            });
            return {
                subject: renderedSubject,
                content: renderedContent,
            };
        }
        catch (error) {
            console.error('Error rendering email template:', error);
            throw new Error('Failed to render email template');
        }
    }
    async getTemplateVariables(id, tenantId) {
        try {
            const template = await EmailTemplate.findOne({ _id: id, tenantId });
            if (!template) {
                throw new Error('Template not found');
            }
            // Extract variables from template content and subject
            const variableRegex = /\{\{(\w+)\}\}/g;
            const variables = new Set();
            let match;
            while ((match = variableRegex.exec(template.subject)) !== null) {
                variables.add(match[1]);
            }
            while ((match = variableRegex.exec(template.content)) !== null) {
                variables.add(match[1]);
            }
            return Array.from(variables);
        }
        catch (error) {
            console.error('Error getting template variables:', error);
            throw new Error('Failed to get template variables');
        }
    }
    async activateEmailTemplate(id, tenantId) {
        try {
            return await EmailTemplate.findOneAndUpdate({ _id: id, tenantId }, {
                isActive: true,
                updatedAt: new Date(),
            }, { new: true });
        }
        catch (error) {
            console.error('Error activating email template:', error);
            throw new Error('Failed to activate email template');
        }
    }
    async deactivateEmailTemplate(id, tenantId) {
        try {
            return await EmailTemplate.findOneAndUpdate({ _id: id, tenantId }, {
                isActive: false,
                updatedAt: new Date(),
            }, { new: true });
        }
        catch (error) {
            console.error('Error deactivating email template:', error);
            throw new Error('Failed to deactivate email template');
        }
    }
}
export const emailTemplateService = new EmailTemplateService();
