import { Company } from '../models/company';
import mongoose from 'mongoose';
/**
 * Company service
 */
export class CompanyService {
    /**
     * Create a new company
     * @param companyData Company data
     * @param tenantId Tenant ID
     * @returns Created company
     */
    static async createCompany(companyData, tenantId) {
        try {
            const company = new Company({
                ...companyData,
                tenantId,
            });
            return await company.save();
        }
        catch (error) {
            console.error('Error creating company:', error);
            throw error;
        }
    }
    /**
     * Get company by ID
     * @param id Company ID
     * @param tenantId Tenant ID
     * @returns Company or null if not found
     */
    static async getCompanyById(id, tenantId) {
        try {
            return await Company.findOne({ _id: id, tenantId });
        }
        catch (error) {
            console.error('Error getting company by ID:', error);
            throw error;
        }
    }
    /**
     * Get companies with pagination
     * @param params Pagination parameters
     * @param tenantId Tenant ID
     * @returns Paginated companies
     */
    static async getCompanies(params, tenantId) {
        try {
            const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc', ...filters } = params;
            const query = Company.find({ tenantId });
            // Apply filters
            Object.entries(filters).forEach(([key, value]) => {
                if (value !== undefined && value !== null) {
                    query.where(key).equals(value);
                }
            });
            // Get total count
            const total = await Company.countDocuments(query.getQuery());
            // Apply pagination and sorting
            const companies = await query
                .sort({ [sortBy]: sortOrder === 'asc' ? 1 : -1 })
                .skip((page - 1) * limit)
                .limit(limit)
                .exec();
            return { companies, total };
        }
        catch (error) {
            console.error('Error getting companies:', error);
            throw error;
        }
    }
    /**
     * Get companies with advanced filtering, sorting, and pagination
     * @param options Filter options
     * @param sortOptions Sort options
     * @param paginationOptions Pagination options
     * @param tenantId Tenant ID
     * @returns Companies and total count
     */
    static async getCompaniesAdvanced(options = {}, sortOptions = { field: 'createdAt', direction: 'desc' }, paginationOptions = { page: 1, limit: 20 }, tenantId) {
        try {
            const filter = { tenantId };
            // Apply search filter
            if (options.search) {
                filter.$or = [
                    { name: { $regex: options.search, $options: 'i' } },
                    { industry: { $regex: options.search, $options: 'i' } },
                    { 'location.city': { $regex: options.search, $options: 'i' } },
                    { 'location.country': { $regex: options.search, $options: 'i' } },
                ];
            }
            // Apply status filter
            if (options.status) {
                filter.status = Array.isArray(options.status)
                    ? { $in: options.status }
                    : options.status;
            }
            // Apply industry filter
            if (options.industry) {
                filter.industry = Array.isArray(options.industry)
                    ? { $in: options.industry }
                    : options.industry;
            }
            // Apply tags filter
            if (options.tags) {
                filter.tags = Array.isArray(options.tags)
                    ? { $all: options.tags }
                    : { $all: [options.tags] };
            }
            // Apply owner filter
            if (options.owner) {
                filter.owner = new mongoose.Types.ObjectId(options.owner);
            }
            // Apply date filters
            if (options.createdAfter || options.createdBefore) {
                filter.createdAt = {};
                if (options.createdAfter) {
                    filter.createdAt.$gte = options.createdAfter;
                }
                if (options.createdBefore) {
                    filter.createdAt.$lte = options.createdBefore;
                }
            }
            // Apply score filters
            if (options.scoreMin !== undefined || options.scoreMax !== undefined) {
                filter['score.current'] = {};
                if (options.scoreMin !== undefined) {
                    filter['score.current'].$gte = options.scoreMin;
                }
                if (options.scoreMax !== undefined) {
                    filter['score.current'].$lte = options.scoreMax;
                }
            }
            // Apply custom fields filters
            if (options.customFields) {
                for (const [key, value] of Object.entries(options.customFields)) {
                    filter[`customFields.${key}`] = value;
                }
            }
            // Calculate skip value for pagination
            const skip = (paginationOptions.page - 1) * paginationOptions.limit;
            // Create sort object
            const sort = {};
            sort[sortOptions.field] = sortOptions.direction === 'asc' ? 1 : -1;
            // Execute query with pagination
            const [companies, total] = await Promise.all([
                Company.find(filter)
                    .sort(sort)
                    .skip(skip)
                    .limit(paginationOptions.limit),
                Company.countDocuments(filter),
            ]);
            return { companies, total };
        }
        catch (error) {
            console.error('Error getting companies with advanced filtering:', error);
            throw error;
        }
    }
    /**
     * Update company
     * @param id Company ID
     * @param companyData Company data to update
     * @param tenantId Tenant ID
     * @returns Updated company or null if not found
     */
    static async updateCompany(id, companyData, tenantId) {
        try {
            return await Company.findOneAndUpdate({ _id: id, tenantId }, { $set: companyData }, { new: true });
        }
        catch (error) {
            console.error('Error updating company:', error);
            throw error;
        }
    }
    /**
     * Delete company
     * @param id Company ID
     * @param tenantId Tenant ID
     * @returns True if deleted, false if not found
     */
    static async deleteCompany(id, tenantId) {
        try {
            const result = await Company.deleteOne({ _id: id, tenantId });
            return result.deletedCount > 0;
        }
        catch (error) {
            console.error('Error deleting company:', error);
            throw error;
        }
    }
    /**
     * Add an interaction to a company
     * @param companyId Company ID
     * @param interaction Interaction data
     * @param tenantId Tenant ID
     * @returns Updated company
     */
    static async addInteraction(companyId, interaction, tenantId) {
        try {
            return await Company.findOneAndUpdate({ _id: companyId, tenantId }, {
                $push: { interactions: interaction }
            }, { new: true });
        }
        catch (error) {
            console.error('Error adding interaction to company:', error);
            throw error;
        }
    }
    /**
     * Update company score
     * @param companyId Company ID
     * @param score Score data
     * @param tenantId Tenant ID
     * @returns Updated company
     */
    static async updateScore(companyId, score, tenantId) {
        try {
            return await Company.findOneAndUpdate({ _id: companyId, tenantId }, { $set: { score } }, { new: true });
        }
        catch (error) {
            console.error('Error updating company score:', error);
            throw error;
        }
    }
    /**
     * Update company insights
     * @param companyId Company ID
     * @param insights Insights data
     * @param tenantId Tenant ID
     * @returns Updated company
     */
    static async updateInsights(companyId, insights, tenantId) {
        try {
            return await Company.findOneAndUpdate({ _id: companyId, tenantId }, { $set: { insights } }, { new: true });
        }
        catch (error) {
            console.error('Error updating company insights:', error);
            throw error;
        }
    }
}
export default CompanyService;
