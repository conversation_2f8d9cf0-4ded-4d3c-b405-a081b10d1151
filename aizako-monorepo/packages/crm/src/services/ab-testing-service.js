import mongoose from 'mongoose';
import { Proposal } from '../models/proposal';
import { ProposalAnalyticsEvent } from '../models/proposal-analytics-event';
import { ABTest } from '../models/ab-test';
import { v4 as uuidv4 } from 'uuid';
/**
 * A/B Testing Service
 *
 * This service provides A/B testing capabilities for proposals.
 */
export class ABTestingService {
    /**
     * Create a new A/B test
     * @param options Test options
     * @param tenantId Tenant ID
     * @returns Created A/B test
     */
    static async createTest(options, tenantId) {
        try {
            // Get the base proposal
            const baseProposal = await Proposal.findOne({
                _id: new mongoose.Types.ObjectId(options.baseProposalId),
                tenantId,
            });
            if (!baseProposal) {
                throw new Error(`Base proposal not found: ${options.baseProposalId}`);
            }
            // Create variant proposals
            const variantProposals = [];
            for (const variant of options.variants) {
                // Clone the base proposal
                const variantProposal = new Proposal({
                    ...baseProposal.toObject(),
                    _id: new mongoose.Types.ObjectId(),
                    title: `${baseProposal.title} - ${variant.name}`,
                    description: variant.description || baseProposal.description,
                    sections: [...baseProposal.sections],
                    publicToken: uuidv4(),
                    publicUrl: `/proposals/public/${uuidv4()}`,
                    viewCount: 0,
                    analyticsEvents: [],
                    createdAt: new Date(),
                    updatedAt: new Date(),
                });
                // Apply changes to sections
                for (const change of variant.changes) {
                    const sectionIndex = variantProposal.sections.findIndex(section => section.id === change.sectionId);
                    if (sectionIndex !== -1) {
                        variantProposal.sections[sectionIndex].content = change.content;
                    }
                }
                // Save the variant proposal
                await variantProposal.save();
                // Add to variant proposals
                variantProposals.push({
                    variantId: variant.name,
                    proposalId: variantProposal._id,
                });
            }
            // Calculate traffic split if not provided
            const trafficSplit = options.trafficSplit ||
                Array(options.variants.length + 1).fill(1 / (options.variants.length + 1));
            // Create the A/B test
            const abTest = await ABTest.create({
                tenantId,
                name: options.name,
                description: options.description,
                baseProposalId: new mongoose.Types.ObjectId(options.baseProposalId),
                variants: options.variants.map((variant, index) => ({
                    name: variant.name,
                    description: variant.description,
                    proposalId: variantProposals[index].proposalId,
                    trafficPercentage: trafficSplit[index + 1] * 100,
                })),
                baseTrafficPercentage: trafficSplit[0] * 100,
                status: 'active',
                startDate: options.startDate || new Date(),
                endDate: options.endDate,
                createdAt: new Date(),
                updatedAt: new Date(),
            });
            return abTest;
        }
        catch (error) {
            console.error('Error creating A/B test:', error);
            throw error;
        }
    }
    /**
     * Get an A/B test
     * @param testId Test ID
     * @param tenantId Tenant ID
     * @returns A/B test or null if not found
     */
    static async getTest(testId, tenantId) {
        try {
            return await ABTest.findOne({
                _id: new mongoose.Types.ObjectId(testId),
                tenantId,
            });
        }
        catch (error) {
            console.error('Error getting A/B test:', error);
            throw error;
        }
    }
    /**
     * Get all A/B tests for a tenant
     * @param tenantId Tenant ID
     * @param status Test status (optional)
     * @returns Array of A/B tests
     */
    static async getTests(tenantId, status) {
        try {
            const query = { tenantId };
            if (status) {
                query.status = status;
            }
            return await ABTest.find(query).sort({ createdAt: -1 });
        }
        catch (error) {
            console.error('Error getting A/B tests:', error);
            throw error;
        }
    }
    /**
     * Update an A/B test
     * @param testId Test ID
     * @param updates Test updates
     * @param tenantId Tenant ID
     * @returns Updated A/B test or null if not found
     */
    static async updateTest(testId, updates, tenantId) {
        try {
            // Get the test
            const test = await ABTest.findOne({
                _id: new mongoose.Types.ObjectId(testId),
                tenantId,
            });
            if (!test) {
                return null;
            }
            // Update basic properties
            if (updates.name)
                test.name = updates.name;
            if (updates.description)
                test.description = updates.description;
            if (updates.status)
                test.status = updates.status;
            if (updates.baseTrafficPercentage)
                test.baseTrafficPercentage = updates.baseTrafficPercentage;
            if (updates.endDate)
                test.endDate = updates.endDate;
            // Update variants
            if (updates.variants) {
                for (const variantUpdate of updates.variants) {
                    const variant = test.variants.find(v => v.name === variantUpdate.name);
                    if (variant) {
                        variant.trafficPercentage = variantUpdate.trafficPercentage;
                    }
                }
            }
            // Update timestamp
            test.updatedAt = new Date();
            // Save and return
            await test.save();
            return test;
        }
        catch (error) {
            console.error('Error updating A/B test:', error);
            throw error;
        }
    }
    /**
     * Delete an A/B test
     * @param testId Test ID
     * @param tenantId Tenant ID
     * @param deleteVariants Whether to delete variant proposals
     * @returns True if successful
     */
    static async deleteTest(testId, tenantId, deleteVariants = false) {
        try {
            // Get the test
            const test = await ABTest.findOne({
                _id: new mongoose.Types.ObjectId(testId),
                tenantId,
            });
            if (!test) {
                return false;
            }
            // Delete variant proposals if requested
            if (deleteVariants) {
                for (const variant of test.variants) {
                    await Proposal.deleteOne({
                        _id: variant.proposalId,
                        tenantId,
                    });
                }
            }
            // Delete the test
            await ABTest.deleteOne({
                _id: new mongoose.Types.ObjectId(testId),
                tenantId,
            });
            return true;
        }
        catch (error) {
            console.error('Error deleting A/B test:', error);
            throw error;
        }
    }
    /**
     * Get A/B test results
     * @param testId Test ID
     * @param tenantId Tenant ID
     * @returns Test results
     */
    static async getTestResults(testId, tenantId) {
        try {
            // Get the test
            const test = await ABTest.findOne({
                _id: new mongoose.Types.ObjectId(testId),
                tenantId,
            });
            if (!test) {
                throw new Error(`A/B test not found: ${testId}`);
            }
            // Get analytics for base proposal
            const baseProposalEvents = await ProposalAnalyticsEvent.find({
                proposalId: test.baseProposalId,
            });
            const baseProposalViews = baseProposalEvents.filter(event => event.eventType === 'view').length;
            const baseProposalUniqueViews = new Set(baseProposalEvents
                .filter(event => event.eventType === 'view' && event.data?.ipAddress)
                .map(event => event.data.ipAddress)).size;
            const baseProposalAcceptances = baseProposalEvents.filter(event => event.eventType === 'accept').length;
            const baseProposalRejections = baseProposalEvents.filter(event => event.eventType === 'reject').length;
            // Calculate base proposal rates
            const baseProposalViewRate = baseProposalViews > 0 ? baseProposalUniqueViews / baseProposalViews : 0;
            const baseProposalAcceptanceRate = baseProposalUniqueViews > 0 ? baseProposalAcceptances / baseProposalUniqueViews : 0;
            const baseProposalRejectionRate = baseProposalUniqueViews > 0 ? baseProposalRejections / baseProposalUniqueViews : 0;
            // Get analytics for variant proposals
            const variantResults = await Promise.all(test.variants.map(async (variant) => {
                const variantEvents = await ProposalAnalyticsEvent.find({
                    proposalId: variant.proposalId,
                });
                const views = variantEvents.filter(event => event.eventType === 'view').length;
                const uniqueViews = new Set(variantEvents
                    .filter(event => event.eventType === 'view' && event.data?.ipAddress)
                    .map(event => event.data.ipAddress)).size;
                const acceptances = variantEvents.filter(event => event.eventType === 'accept').length;
                const rejections = variantEvents.filter(event => event.eventType === 'reject').length;
                // Calculate rates
                const viewRate = views > 0 ? uniqueViews / views : 0;
                const acceptanceRate = uniqueViews > 0 ? acceptances / uniqueViews : 0;
                const rejectionRate = uniqueViews > 0 ? rejections / uniqueViews : 0;
                // Calculate improvement over base proposal
                const improvement = baseProposalAcceptanceRate > 0
                    ? (acceptanceRate - baseProposalAcceptanceRate) / baseProposalAcceptanceRate * 100
                    : 0;
                return {
                    name: variant.name,
                    proposalId: variant.proposalId.toString(),
                    views,
                    uniqueViews,
                    viewRate,
                    acceptances,
                    acceptanceRate,
                    rejections,
                    rejectionRate,
                    improvement,
                };
            }));
            // Determine the winner
            let winner = null;
            let bestAcceptanceRate = baseProposalAcceptanceRate;
            let confidence = 0;
            for (const variant of variantResults) {
                if (variant.acceptanceRate > bestAcceptanceRate && variant.uniqueViews >= 10) {
                    bestAcceptanceRate = variant.acceptanceRate;
                    winner = variant.name;
                    // Calculate confidence using a simple heuristic
                    // In a real implementation, you would use a statistical test
                    const sampleSize = Math.min(variant.uniqueViews, baseProposalUniqueViews);
                    confidence = Math.min(sampleSize / 100, 0.95) * 100;
                }
            }
            return {
                testId: String(test._id),
                name: test.name,
                baseProposal: {
                    proposalId: test.baseProposalId.toString(),
                    views: baseProposalViews,
                    uniqueViews: baseProposalUniqueViews,
                    viewRate: baseProposalViewRate,
                    acceptances: baseProposalAcceptances,
                    acceptanceRate: baseProposalAcceptanceRate,
                    rejections: baseProposalRejections,
                    rejectionRate: baseProposalRejectionRate,
                },
                variants: variantResults,
                winner,
                confidence,
            };
        }
        catch (error) {
            console.error('Error getting A/B test results:', error);
            throw error;
        }
    }
    /**
     * Get a proposal for an A/B test
     * @param testId Test ID
     * @param tenantId Tenant ID
     * @returns Proposal ID
     */
    static async getTestProposal(testId, tenantId) {
        try {
            // Get the test
            const test = await ABTest.findOne({
                _id: new mongoose.Types.ObjectId(testId),
                tenantId,
            });
            if (!test) {
                throw new Error(`A/B test not found: ${testId}`);
            }
            // If the test is not active, return the base proposal
            if (test.status !== 'active') {
                return test.baseProposalId.toString();
            }
            // Calculate the random assignment
            const random = Math.random() * 100;
            let cumulativePercentage = test.baseTrafficPercentage;
            // If the random number is less than the base traffic percentage, return the base proposal
            if (random < cumulativePercentage) {
                return test.baseProposalId.toString();
            }
            // Otherwise, find the variant
            for (const variant of test.variants) {
                cumulativePercentage += variant.trafficPercentage;
                if (random < cumulativePercentage) {
                    return variant.proposalId.toString();
                }
            }
            // If we get here, return the base proposal
            return test.baseProposalId.toString();
        }
        catch (error) {
            console.error('Error getting A/B test proposal:', error);
            throw error;
        }
    }
}
export default ABTestingService;
