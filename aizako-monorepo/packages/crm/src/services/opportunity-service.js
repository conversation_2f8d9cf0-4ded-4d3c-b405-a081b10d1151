import { Opportunity } from '../models/opportunity';
import mongoose from 'mongoose';
/**
 * Opportunity service
 */
export class OpportunityService {
    /**
     * Create a new opportunity
     * @param opportunityData Opportunity data
     * @param tenantId Tenant ID
     * @returns Created opportunity
     */
    static async createOpportunity(opportunityData, tenantId) {
        try {
            const opportunity = new Opportunity({
                ...opportunityData,
                tenantId,
            });
            return await opportunity.save();
        }
        catch (error) {
            console.error('Error creating opportunity:', error);
            throw error;
        }
    }
    /**
     * Get opportunity by ID
     * @param id Opportunity ID
     * @param tenantId Tenant ID
     * @returns Opportunity or null if not found
     */
    static async getOpportunityById(id, tenantId) {
        try {
            return await Opportunity.findOne({ _id: id, tenantId });
        }
        catch (error) {
            console.error('Error getting opportunity by ID:', error);
            throw error;
        }
    }
    /**
     * Get opportunities with pagination
     * @param params Pagination parameters
     * @param tenantId Tenant ID
     * @returns Paginated opportunities
     */
    static async getOpportunities(params, tenantId) {
        try {
            const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc', ...filters } = params;
            const query = Opportunity.find({ tenantId });
            // Apply filters
            Object.entries(filters).forEach(([key, value]) => {
                if (value !== undefined && value !== null) {
                    query.where(key).equals(value);
                }
            });
            // Get total count
            const total = await Opportunity.countDocuments(query.getQuery());
            // Apply pagination and sorting
            const opportunities = await query
                .sort({ [sortBy]: sortOrder === 'asc' ? 1 : -1 })
                .skip((page - 1) * limit)
                .limit(limit)
                .exec();
            return { opportunities, total };
        }
        catch (error) {
            console.error('Error getting opportunities:', error);
            throw error;
        }
    }
    /**
     * Get opportunities with advanced filtering, sorting, and pagination
     * @param options Filter options
     * @param sortOptions Sort options
     * @param paginationOptions Pagination options
     * @param tenantId Tenant ID
     * @returns Opportunities and total count
     */
    static async getOpportunitiesAdvanced(options = {}, sortOptions = { field: 'createdAt', direction: 'desc' }, paginationOptions = { page: 1, limit: 20 }, tenantId) {
        try {
            const filter = { tenantId };
            // Apply search filter
            if (options.search) {
                filter.$or = [
                    { name: { $regex: options.search, $options: 'i' } },
                    { description: { $regex: options.search, $options: 'i' } },
                ];
            }
            // Apply stage filter
            if (options.stage) {
                filter.stage = Array.isArray(options.stage)
                    ? { $in: options.stage }
                    : options.stage;
            }
            // Apply company filter
            if (options.companyId) {
                filter.companyId = new mongoose.Types.ObjectId(options.companyId);
            }
            // Apply contacts filter
            if (options.contactIds) {
                if (Array.isArray(options.contactIds)) {
                    filter.contacts = { $in: options.contactIds.map(id => new mongoose.Types.ObjectId(id)) };
                }
                else {
                    filter.contacts = new mongoose.Types.ObjectId(options.contactIds);
                }
            }
            // Apply owner filter
            if (options.owner) {
                filter.owner = new mongoose.Types.ObjectId(options.owner);
            }
            // Apply tags filter
            if (options.tags) {
                filter.tags = Array.isArray(options.tags)
                    ? { $all: options.tags }
                    : { $all: [options.tags] };
            }
            // Apply close date filters
            if (options.closeDateAfter || options.closeDateBefore) {
                filter.closeDate = {};
                if (options.closeDateAfter) {
                    filter.closeDate.$gte = options.closeDateAfter;
                }
                if (options.closeDateBefore) {
                    filter.closeDate.$lte = options.closeDateBefore;
                }
            }
            // Apply amount filters
            if (options.amountMin !== undefined || options.amountMax !== undefined) {
                filter.amount = {};
                if (options.amountMin !== undefined) {
                    filter.amount.$gte = options.amountMin;
                }
                if (options.amountMax !== undefined) {
                    filter.amount.$lte = options.amountMax;
                }
            }
            // Apply probability filter
            if (options.probability !== undefined) {
                filter.probability = { $gte: options.probability };
            }
            // Apply forecast category filter
            if (options.forecastCategory) {
                filter.forecastCategory = options.forecastCategory;
            }
            // Apply created date filters
            if (options.createdAfter || options.createdBefore) {
                filter.createdAt = {};
                if (options.createdAfter) {
                    filter.createdAt.$gte = options.createdAfter;
                }
                if (options.createdBefore) {
                    filter.createdAt.$lte = options.createdBefore;
                }
            }
            // Apply score filters
            if (options.scoreMin !== undefined || options.scoreMax !== undefined) {
                filter['score.winProbability'] = {};
                if (options.scoreMin !== undefined) {
                    filter['score.winProbability'].$gte = options.scoreMin;
                }
                if (options.scoreMax !== undefined) {
                    filter['score.winProbability'].$lte = options.scoreMax;
                }
            }
            // Apply custom fields filters
            if (options.customFields) {
                for (const [key, value] of Object.entries(options.customFields)) {
                    filter[`customFields.${key}`] = value;
                }
            }
            // Calculate skip value for pagination
            const skip = (paginationOptions.page - 1) * paginationOptions.limit;
            // Create sort object
            const sort = {};
            sort[sortOptions.field] = sortOptions.direction === 'asc' ? 1 : -1;
            // Execute query with pagination
            const [opportunities, total] = await Promise.all([
                Opportunity.find(filter)
                    .sort(sort)
                    .skip(skip)
                    .limit(paginationOptions.limit),
                Opportunity.countDocuments(filter),
            ]);
            return { opportunities, total };
        }
        catch (error) {
            console.error('Error getting opportunities with advanced filtering:', error);
            throw error;
        }
    }
    /**
     * Update opportunity
     * @param id Opportunity ID
     * @param opportunityData Opportunity data to update
     * @param tenantId Tenant ID
     * @returns Updated opportunity or null if not found
     */
    static async updateOpportunity(id, opportunityData, tenantId) {
        try {
            return await Opportunity.findOneAndUpdate({ _id: id, tenantId }, { $set: opportunityData }, { new: true });
        }
        catch (error) {
            console.error('Error updating opportunity:', error);
            throw error;
        }
    }
    /**
     * Delete opportunity
     * @param id Opportunity ID
     * @param tenantId Tenant ID
     * @returns True if deleted, false if not found
     */
    static async deleteOpportunity(id, tenantId) {
        try {
            const result = await Opportunity.deleteOne({ _id: id, tenantId });
            return result.deletedCount > 0;
        }
        catch (error) {
            console.error('Error deleting opportunity:', error);
            throw error;
        }
    }
    /**
     * Update opportunity stage
     * @param id Opportunity ID
     * @param stage New stage
     * @param userId User ID making the change
     * @param reason Reason for stage change
     * @param tenantId Tenant ID
     * @returns Updated opportunity
     */
    static async updateStage(id, stage, userId, tenantId, reason) {
        try {
            const opportunity = await Opportunity.findOne({ _id: id, tenantId });
            if (!opportunity) {
                return null;
            }
            const now = new Date();
            const lastStageChange = opportunity.stageHistory?.find(history => history.toStage === opportunity.stage);
            const daysInStage = lastStageChange
                ? Math.floor((now.getTime() - lastStageChange.timestamp.getTime()) / (1000 * 60 * 60 * 24))
                : 0;
            const stageTransition = {
                fromStage: opportunity.stage,
                toStage: stage,
                timestamp: now,
                daysInStage,
                reason,
                userId: new mongoose.Types.ObjectId(userId),
            };
            return await Opportunity.findOneAndUpdate({ _id: id, tenantId }, {
                $set: { stage },
                $push: { stageHistory: stageTransition },
            }, { new: true });
        }
        catch (error) {
            console.error('Error updating opportunity stage:', error);
            throw error;
        }
    }
    /**
     * Add an interaction to an opportunity
     * @param opportunityId Opportunity ID
     * @param interaction Interaction data
     * @param tenantId Tenant ID
     * @returns Updated opportunity
     */
    static async addInteraction(opportunityId, interaction, tenantId) {
        try {
            return await Opportunity.findOneAndUpdate({ _id: opportunityId, tenantId }, {
                $push: { interactions: interaction }
            }, { new: true });
        }
        catch (error) {
            console.error('Error adding interaction to opportunity:', error);
            throw error;
        }
    }
    /**
     * Add an objection to an opportunity
     * @param opportunityId Opportunity ID
     * @param objection Objection data
     * @param tenantId Tenant ID
     * @returns Updated opportunity
     */
    static async addObjection(opportunityId, objection, tenantId) {
        try {
            return await Opportunity.findOneAndUpdate({ _id: opportunityId, tenantId }, {
                $push: { objections: objection }
            }, { new: true });
        }
        catch (error) {
            console.error('Error adding objection to opportunity:', error);
            throw error;
        }
    }
    /**
     * Update opportunity score
     * @param opportunityId Opportunity ID
     * @param score Score data
     * @param tenantId Tenant ID
     * @returns Updated opportunity
     */
    static async updateScore(opportunityId, score, tenantId) {
        try {
            return await Opportunity.findOneAndUpdate({ _id: opportunityId, tenantId }, { $set: { score } }, { new: true });
        }
        catch (error) {
            console.error('Error updating opportunity score:', error);
            throw error;
        }
    }
    /**
     * Update opportunity insights
     * @param opportunityId Opportunity ID
     * @param insights Insights data
     * @param tenantId Tenant ID
     * @returns Updated opportunity
     */
    static async updateInsights(opportunityId, insights, tenantId) {
        try {
            return await Opportunity.findOneAndUpdate({ _id: opportunityId, tenantId }, { $set: { insights } }, { new: true });
        }
        catch (error) {
            console.error('Error updating opportunity insights:', error);
            throw error;
        }
    }
}
export default OpportunityService;
