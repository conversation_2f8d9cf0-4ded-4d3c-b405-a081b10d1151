import axios from 'axios';
import { API_BASE_URL } from '../config';
import { getAuthToken } from '../utils/auth';
/**
 * Proposal Approval Service
 *
 * This service provides methods for managing proposal approval workflows.
 */
export class ProposalApprovalService {
    /**
     * Get approval workflow for a proposal
     * @param proposalId Proposal ID
     * @param tenantId Tenant ID
     * @returns Approval workflow
     */
    static async getApprovalWorkflow(proposalId, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.get(`${API_BASE_URL}/api/crm/proposal-approvals/${proposalId}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error getting approval workflow:', error);
            throw error;
        }
    }
    /**
     * Get approvers for a tenant
     * @param tenantId Tenant ID
     * @returns Approvers
     */
    static async getApprovers(tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.get(`${API_BASE_URL}/api/crm/proposal-approvals/approvers`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error getting approvers:', error);
            throw error;
        }
    }
    /**
     * Add approver to a proposal
     * @param proposalId Proposal ID
     * @param tenantId Tenant ID
     * @param data Approver data
     * @returns Updated approval workflow
     */
    static async addApprover(proposalId, tenantId, data) {
        try {
            const token = await getAuthToken();
            const response = await axios.post(`${API_BASE_URL}/api/crm/proposal-approvals/${proposalId}/approvers`, data, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error adding approver:', error);
            throw error;
        }
    }
    /**
     * Remove approver from a proposal
     * @param proposalId Proposal ID
     * @param tenantId Tenant ID
     * @param data Approver data
     * @returns Updated approval workflow
     */
    static async removeApprover(proposalId, tenantId, data) {
        try {
            const token = await getAuthToken();
            const response = await axios.delete(`${API_BASE_URL}/api/crm/proposal-approvals/${proposalId}/approvers/${data.approverId}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error removing approver:', error);
            throw error;
        }
    }
    /**
     * Start approval process
     * @param proposalId Proposal ID
     * @param tenantId Tenant ID
     * @returns Updated approval workflow
     */
    static async startApprovalProcess(proposalId, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.post(`${API_BASE_URL}/api/crm/proposal-approvals/${proposalId}/start`, {}, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error starting approval process:', error);
            throw error;
        }
    }
    /**
     * Cancel approval process
     * @param proposalId Proposal ID
     * @param tenantId Tenant ID
     * @returns Updated approval workflow
     */
    static async cancelApprovalProcess(proposalId, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.post(`${API_BASE_URL}/api/crm/proposal-approvals/${proposalId}/cancel`, {}, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error canceling approval process:', error);
            throw error;
        }
    }
    /**
     * Approve proposal
     * @param proposalId Proposal ID
     * @param tenantId Tenant ID
     * @param data Approval data
     * @returns Updated approval workflow
     */
    static async approveProposal(proposalId, tenantId, data) {
        try {
            const token = await getAuthToken();
            const response = await axios.post(`${API_BASE_URL}/api/crm/proposal-approvals/${proposalId}/approve`, data, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error approving proposal:', error);
            throw error;
        }
    }
    /**
     * Reject proposal
     * @param proposalId Proposal ID
     * @param tenantId Tenant ID
     * @param data Rejection data
     * @returns Updated approval workflow
     */
    static async rejectProposal(proposalId, tenantId, data) {
        try {
            const token = await getAuthToken();
            const response = await axios.post(`${API_BASE_URL}/api/crm/proposal-approvals/${proposalId}/reject`, data, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error rejecting proposal:', error);
            throw error;
        }
    }
    /**
     * Get approval history
     * @param proposalId Proposal ID
     * @param tenantId Tenant ID
     * @returns Approval history
     */
    static async getApprovalHistory(proposalId, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.get(`${API_BASE_URL}/api/crm/proposal-approvals/${proposalId}/history`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error getting approval history:', error);
            throw error;
        }
    }
    /**
     * Get pending approvals for current user
     * @param tenantId Tenant ID
     * @returns Pending approvals
     */
    static async getPendingApprovals(tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.get(`${API_BASE_URL}/api/crm/proposal-approvals/pending`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error getting pending approvals:', error);
            throw error;
        }
    }
    /**
     * Get approval settings
     * @param tenantId Tenant ID
     * @returns Approval settings
     */
    static async getApprovalSettings(tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.get(`${API_BASE_URL}/api/crm/proposal-approvals/settings`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error getting approval settings:', error);
            throw error;
        }
    }
    /**
     * Update approval settings
     * @param tenantId Tenant ID
     * @param data Settings data
     * @returns Updated approval settings
     */
    static async updateApprovalSettings(tenantId, data) {
        try {
            const token = await getAuthToken();
            const response = await axios.put(`${API_BASE_URL}/api/crm/proposal-approvals/settings`, data, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error updating approval settings:', error);
            throw error;
        }
    }
}
export default ProposalApprovalService;
