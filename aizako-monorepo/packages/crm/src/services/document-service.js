// Document service for CRM module
// TODO: Implement proper document service
import { Document } from '../models/document';
export class DocumentService {
    async createDocument(data) {
        try {
            const document = new Document({
                ...data,
                createdAt: new Date(),
                updatedAt: new Date(),
            });
            return await document.save();
        }
        catch (error) {
            console.error('Error creating document:', error);
            throw new Error('Failed to create document');
        }
    }
    async getDocument(id, tenantId) {
        try {
            return await Document.findOne({ _id: id, tenantId })
                .populate('contactId', 'name email')
                .populate('companyId', 'name')
                .populate('opportunityId', 'name')
                .populate('proposalId', 'title')
                .populate('userId', 'name email');
        }
        catch (error) {
            console.error('Error getting document:', error);
            throw new Error('Failed to get document');
        }
    }
    async updateDocument(data) {
        try {
            const { id, ...updateData } = data;
            return await Document.findOneAndUpdate({ _id: id, tenantId: data.tenantId }, { ...updateData, updatedAt: new Date() }, { new: true }).populate('contactId', 'name email')
                .populate('companyId', 'name')
                .populate('opportunityId', 'name')
                .populate('proposalId', 'title')
                .populate('userId', 'name email');
        }
        catch (error) {
            console.error('Error updating document:', error);
            throw new Error('Failed to update document');
        }
    }
    async deleteDocument(id, tenantId) {
        try {
            const result = await Document.deleteOne({ _id: id, tenantId });
            return result.deletedCount > 0;
        }
        catch (error) {
            console.error('Error deleting document:', error);
            throw new Error('Failed to delete document');
        }
    }
    async getDocuments(tenantId, filters = {}, page = 1, limit = 20) {
        try {
            const query = { tenantId };
            if (filters.type)
                query.type = filters.type;
            if (filters.contactId)
                query.contactId = filters.contactId;
            if (filters.companyId)
                query.companyId = filters.companyId;
            if (filters.opportunityId)
                query.opportunityId = filters.opportunityId;
            if (filters.proposalId)
                query.proposalId = filters.proposalId;
            if (filters.userId)
                query.userId = filters.userId;
            if (filters.isPublic !== undefined)
                query.isPublic = filters.isPublic;
            if (filters.tags && filters.tags.length > 0) {
                query.tags = { $in: filters.tags };
            }
            if (filters.search) {
                query.$or = [
                    { name: { $regex: filters.search, $options: 'i' } },
                    { content: { $regex: filters.search, $options: 'i' } },
                    { tags: { $in: [new RegExp(filters.search, 'i')] } },
                ];
            }
            const skip = (page - 1) * limit;
            const [documents, total] = await Promise.all([
                Document.find(query)
                    .sort({ createdAt: -1 })
                    .skip(skip)
                    .limit(limit)
                    .populate('contactId', 'name email')
                    .populate('companyId', 'name')
                    .populate('opportunityId', 'name')
                    .populate('proposalId', 'title')
                    .populate('userId', 'name email'),
                Document.countDocuments(query),
            ]);
            return {
                documents,
                total,
                page,
                totalPages: Math.ceil(total / limit),
            };
        }
        catch (error) {
            console.error('Error getting documents:', error);
            throw new Error('Failed to get documents');
        }
    }
    async getDocumentsByEntity(tenantId, entityType, entityId) {
        try {
            const query = { tenantId };
            switch (entityType) {
                case 'contact':
                    query.contactId = entityId;
                    break;
                case 'company':
                    query.companyId = entityId;
                    break;
                case 'opportunity':
                    query.opportunityId = entityId;
                    break;
                case 'proposal':
                    query.proposalId = entityId;
                    break;
                default:
                    throw new Error('Invalid entity type');
            }
            return await Document.find(query)
                .sort({ createdAt: -1 })
                .populate('userId', 'name email');
        }
        catch (error) {
            console.error('Error getting documents by entity:', error);
            throw new Error('Failed to get documents by entity');
        }
    }
    async getDocumentStats(tenantId, userId) {
        try {
            const query = { tenantId };
            if (userId)
                query.userId = userId;
            const documents = await Document.find(query);
            const stats = {
                total: documents.length,
                totalSize: 0,
                byType: {},
                recentCount: 0,
            };
            const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
            documents.forEach(doc => {
                stats.totalSize += doc.fileSize || 0;
                if (!stats.byType[doc.fileType]) {
                    stats.byType[doc.fileType] = 0;
                }
                stats.byType[doc.fileType]++;
                if (doc.createdAt && doc.createdAt > oneWeekAgo) {
                    stats.recentCount++;
                }
            });
            return stats;
        }
        catch (error) {
            console.error('Error getting document stats:', error);
            throw new Error('Failed to get document stats');
        }
    }
    async addTagToDocument(id, tenantId, tag) {
        try {
            return await Document.findOneAndUpdate({ _id: id, tenantId }, {
                $addToSet: { tags: tag },
                updatedAt: new Date(),
            }, { new: true });
        }
        catch (error) {
            console.error('Error adding tag to document:', error);
            throw new Error('Failed to add tag to document');
        }
    }
    async removeTagFromDocument(id, tenantId, tag) {
        try {
            return await Document.findOneAndUpdate({ _id: id, tenantId }, {
                $pull: { tags: tag },
                updatedAt: new Date(),
            }, { new: true });
        }
        catch (error) {
            console.error('Error removing tag from document:', error);
            throw new Error('Failed to remove tag from document');
        }
    }
}
export const documentService = new DocumentService();
