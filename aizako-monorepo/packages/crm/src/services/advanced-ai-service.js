import { AIService } from './ai-service';
import { ProposalService } from './proposal-service';
import { OpportunityService } from './opportunity-service';
import { CompanyService } from './company-service';
import { ContactService } from './contact-service';
import { v4 as uuidv4 } from 'uuid';
/**
 * Advanced AI Service
 *
 * This service provides advanced AI capabilities for proposal generation.
 */
export class AdvancedAIService {
    /**
     * Generate a proposal with context-aware AI
     * @param options Generation options
     * @param tenantId Tenant ID
     * @returns Generated proposal
     */
    static async generateContextAwareProposal(options, tenantId) {
        try {
            // Gather context information
            const context = await this.gatherContext(options, tenantId);
            // Generate enhanced prompt
            const enhancedPrompt = await this.generateEnhancedPrompt(options.prompt, context, options);
            // Generate proposal with enhanced prompt
            const proposalData = await AIService.generateProposal({
                prompt: enhancedPrompt,
                model: options.model,
                includeSections: {
                    executiveSummary: options.includeSections?.executiveSummary ?? true,
                    solution: options.includeSections?.solution ?? true,
                    timeline: options.includeSections?.timeline ?? true,
                    pricing: options.includeSections?.pricing ?? true,
                    team: options.includeSections?.team ?? false,
                    testimonials: options.includeSections?.testimonials ?? false,
                    terms: options.includeSections?.terms ?? true,
                },
                opportunityId: options.opportunityId,
                companyId: options.companyId,
                contactIds: options.contactIds,
            });
            // Create the proposal
            return await ProposalService.createProposal({
                ...proposalData,
                status: 'draft',
                aiGenerated: true,
                aiPrompt: options.prompt,
                aiModel: options.model,
                opportunityId: options.opportunityId,
                companyId: options.companyId,
                contactIds: options.contactIds,
            }, tenantId);
        }
        catch (error) {
            console.error('Error generating context-aware proposal:', error);
            throw error;
        }
    }
    /**
     * Generate a proposal section with context-aware AI
     * @param options Generation options
     * @param tenantId Tenant ID
     * @returns Generated section
     */
    static async generateContextAwareSection(options, tenantId) {
        try {
            // Gather context information
            const context = await this.gatherContext(options, tenantId);
            // If proposalId is provided, add proposal context
            if (options.proposalId) {
                const proposal = await ProposalService.getProposalById(options.proposalId, tenantId);
                if (proposal) {
                    context.proposal = {
                        title: proposal.title,
                        description: proposal.description,
                        sections: proposal.sections.map(section => ({
                            title: section.title,
                            type: section.type,
                        })),
                        pricing: proposal.pricing,
                    };
                }
            }
            // Generate enhanced prompt
            const enhancedPrompt = await this.generateEnhancedPrompt(options.prompt, context, options);
            // Generate section with enhanced prompt
            const sectionData = await AIService.generateProposalSection(options.sectionType, enhancedPrompt, options.model, context);
            // Add additional metadata
            return {
                ...sectionData,
                id: uuidv4(),
                order: 0,
                isVisible: true,
                aiGenerated: true,
                type: sectionData.type,
            };
        }
        catch (error) {
            console.error('Error generating context-aware section:', error);
            throw error;
        }
    }
    /**
     * Generate a competitive analysis section
     * @param options Generation options
     * @param tenantId Tenant ID
     * @returns Generated section
     */
    static async generateCompetitiveAnalysis(options, tenantId) {
        try {
            let context = {};
            // Get company information if provided
            if (options.companyId) {
                const company = await CompanyService.getCompanyById(options.companyId, tenantId);
                if (company) {
                    context.company = {
                        name: company.name,
                        industry: company.industry,
                        size: company.size,
                        website: company.website,
                    };
                }
            }
            // Add industry if provided
            if (options.industry) {
                context.industry = options.industry;
            }
            // Add competitors if provided
            if (options.competitors && options.competitors.length > 0) {
                context.competitors = options.competitors;
            }
            // Generate prompt for competitive analysis
            const prompt = `Generate a detailed competitive analysis section for a proposal. 
      ${context.company ? `The client company is ${context.company.name} in the ${context.company.industry || 'unknown'} industry.` : ''}
      ${context.industry ? `The industry is ${context.industry}.` : ''}
      ${context.competitors ? `The main competitors are: ${context.competitors.join(', ')}.` : ''}
      
      Include the following in the analysis:
      1. Overview of the competitive landscape
      2. Strengths and weaknesses of key competitors
      3. Opportunities for differentiation
      4. Recommended positioning strategy
      
      ${options.customInstructions || ''}`;
            // Generate section
            const sectionData = await AIService.generateProposalSection('text', prompt, options.model, context);
            // Add additional metadata
            return {
                ...sectionData,
                title: 'Competitive Analysis',
                id: uuidv4(),
                order: 0,
                isVisible: true,
                aiGenerated: true,
                type: sectionData.type,
            };
        }
        catch (error) {
            console.error('Error generating competitive analysis:', error);
            throw error;
        }
    }
    /**
     * Generate a pricing recommendation
     * @param options Generation options
     * @param tenantId Tenant ID
     * @returns Generated pricing recommendation
     */
    static async generatePricingRecommendation(options, tenantId) {
        try {
            // Gather context
            const context = await this.gatherContext(options, tenantId);
            // Generate prompt for pricing recommendation
            const prompt = `Generate a pricing recommendation for a proposal.
      ${context.opportunity ? `The opportunity is: ${context.opportunity.name} - ${context.opportunity.description || ''}` : ''}
      ${context.company ? `The client company is ${context.company.name} in the ${context.company.industry || 'unknown'} industry.` : ''}
      ${options.industry ? `The industry is ${options.industry}.` : ''}
      ${options.projectScope ? `The project scope is: ${options.projectScope}` : ''}
      
      Provide a detailed pricing structure with line items, quantities, unit prices, and totals.
      Include recommendations for discounts or bundling if appropriate.
      
      ${options.customInstructions || ''}`;
            // Generate pricing recommendation
            const response = await AIService.generateText(prompt, options.model);
            // Parse the response to extract pricing information
            // This is a simplified implementation - in a real system, you would use a more robust parsing approach
            const pricingItems = [];
            let currency = 'USD';
            let subtotal = 0;
            let discount = 0;
            let tax = 0;
            let total = 0;
            // Extract pricing information from the response
            // This is a placeholder - in a real implementation, you would parse the AI response
            return {
                recommendation: response,
                suggestedPricing: {
                    items: pricingItems,
                    currency,
                    subtotal,
                    discount,
                    tax,
                    total,
                },
            };
        }
        catch (error) {
            console.error('Error generating pricing recommendation:', error);
            throw error;
        }
    }
    /**
     * Gather context information for AI generation
     * @param options Generation options
     * @param tenantId Tenant ID
     * @returns Context information
     * @private
     */
    static async gatherContext(options, tenantId) {
        const context = {};
        // Get opportunity information if provided
        if (options.opportunityId) {
            const opportunity = await OpportunityService.getOpportunityById(options.opportunityId, tenantId);
            if (opportunity) {
                context.opportunity = {
                    name: opportunity.name,
                    description: opportunity.description,
                    stage: opportunity.stage,
                    amount: opportunity.amount,
                    closeDate: opportunity.closeDate,
                };
            }
        }
        // Get company information if provided
        if (options.companyId) {
            const company = await CompanyService.getCompanyById(options.companyId, tenantId);
            if (company) {
                context.company = {
                    name: company.name,
                    industry: company.industry,
                    size: company.size,
                    website: company.website,
                    description: company.description,
                };
            }
        }
        // Get contact information if provided
        if (options.contactIds && options.contactIds.length > 0) {
            const contacts = await Promise.all(options.contactIds.map(id => ContactService.getContactById(id, tenantId)));
            context.contacts = contacts
                .filter(contact => contact !== null)
                .map(contact => ({
                name: contact.name,
                title: contact.title,
                email: contact.email,
                phone: contact.phone,
            }));
        }
        // Add additional context based on options
        if (options.competitorAnalysis) {
            // This would be implemented in a real system
            context.competitorAnalysis = 'Competitor analysis would be included here';
        }
        if (options.marketResearch) {
            // This would be implemented in a real system
            context.marketResearch = 'Market research would be included here';
        }
        if (options.industryTrends) {
            // This would be implemented in a real system
            context.industryTrends = 'Industry trends would be included here';
        }
        if (options.previousProposals) {
            // Get previous proposals for the company
            if (options.companyId) {
                const previousProposals = await ProposalService.getProposalsByCompany(options.companyId, tenantId);
                if (previousProposals && previousProposals.length > 0) {
                    context.previousProposals = previousProposals.map(proposal => ({
                        title: proposal.title,
                        status: proposal.status,
                        createdAt: proposal.createdAt,
                    }));
                }
            }
        }
        if (options.similarDeals) {
            // This would be implemented in a real system
            context.similarDeals = 'Similar deals would be included here';
        }
        return context;
    }
    /**
     * Generate an enhanced prompt with context information
     * @param basePrompt Base prompt
     * @param context Context information
     * @param options Generation options
     * @returns Enhanced prompt
     * @private
     */
    static async generateEnhancedPrompt(basePrompt, context, options) {
        let enhancedPrompt = basePrompt;
        // Add context information to the prompt
        if (context.opportunity) {
            enhancedPrompt += `\n\nOpportunity Information:
- Name: ${context.opportunity.name}
- Description: ${context.opportunity.description || 'N/A'}
- Stage: ${context.opportunity.stage || 'N/A'}
- Amount: ${context.opportunity.amount ? `$${context.opportunity.amount.toLocaleString()}` : 'N/A'}
- Close Date: ${context.opportunity.closeDate ? new Date(context.opportunity.closeDate).toLocaleDateString() : 'N/A'}`;
        }
        if (context.company) {
            enhancedPrompt += `\n\nCompany Information:
- Name: ${context.company.name}
- Industry: ${context.company.industry || 'N/A'}
- Size: ${context.company.size || 'N/A'}
- Website: ${context.company.website || 'N/A'}
- Description: ${context.company.description || 'N/A'}`;
        }
        if (context.contacts && context.contacts.length > 0) {
            enhancedPrompt += `\n\nContact Information:`;
            for (const contact of context.contacts) {
                enhancedPrompt += `
- Name: ${contact.name}
  Title: ${contact.title || 'N/A'}
  Email: ${contact.email || 'N/A'}
  Phone: ${contact.phone || 'N/A'}`;
            }
        }
        // Add custom instructions if provided
        if (options.customInstructions) {
            enhancedPrompt += `\n\nAdditional Instructions:\n${options.customInstructions}`;
        }
        return enhancedPrompt;
    }
}
export default AdvancedAIService;
