import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
/**
 * AI Service
 *
 * This service provides methods for interacting with AI models.
 */
export class AIService {
    /**
     * Set the API key
     * @param apiKey Claude API key
     */
    static setApiKey(apiKey) {
        this.apiKey = apiKey;
    }
    /**
     * Generate text with <PERSON>
     * @param prompt Prompt to send to <PERSON>
     * @param model Claude model to use
     * @param options Additional options
     * @returns Generated text
     */
    static async generateText(prompt, model = 'claude-3-opus-20240229', options = {}) {
        try {
            const { temperature = 0.7, maxTokens = 4000, system } = options;
            const response = await axios.post(this.apiUrl, {
                model,
                messages: [
                    {
                        role: 'user',
                        content: prompt,
                    },
                ],
                temperature,
                max_tokens: maxTokens,
                ...(system && { system }),
            }, {
                headers: {
                    'Content-Type': 'application/json',
                    'x-api-key': this.apiKey,
                    'anthropic-version': '2023-06-01',
                },
            });
            // Extract the generated text from the response
            const content = response.data.content;
            let generatedText = '';
            for (const item of content) {
                if (item.type === 'text' && item.text) {
                    generatedText += item.text;
                }
            }
            return generatedText;
        }
        catch (error) {
            console.error('Error generating text with Claude:', error);
            throw new Error('Failed to generate text with Claude');
        }
    }
    /**
     * Generate a proposal with Claude
     * @param options Proposal generation options
     * @returns Generated proposal data
     */
    static async generateProposal(options) {
        try {
            const { prompt, model, includeSections, opportunityId, companyId, contactIds } = options;
            // Create a system prompt that instructs Claude on the proposal format
            const systemPrompt = `You are an expert business proposal writer. Your task is to generate a professional business proposal based on the user's requirements. 
      
The proposal should include the following sections:
${includeSections.executiveSummary ? '- Executive Summary\n' : ''}${includeSections.solution ? '- Proposed Solution\n' : ''}${includeSections.timeline ? '- Project Timeline\n' : ''}${includeSections.pricing ? '- Pricing\n' : ''}${includeSections.team ? '- Team Members\n' : ''}${includeSections.testimonials ? '- Client Testimonials\n' : ''}${includeSections.terms ? '- Terms & Conditions\n' : ''}

Your response should be in JSON format with the following structure:
{
  "title": "Proposal title",
  "description": "Brief description of the proposal",
  "sections": [
    {
      "title": "Section title",
      "content": "Section content",
      "type": "text",
      "order": 0
    },
    ...
  ],
  "pricing": {
    "items": [
      {
        "name": "Item name",
        "description": "Item description",
        "quantity": 1,
        "unitPrice": 100,
        "total": 100
      },
      ...
    ]
  },
  "terms": "Terms and conditions text"
}

Make the proposal persuasive, professional, and tailored to the client's needs. Use clear, concise language and focus on the value proposition.`;
            // Generate the proposal with Claude
            const generatedText = await this.generateText(prompt, model, {
                system: systemPrompt,
                temperature: 0.7,
                maxTokens: 4000,
            });
            // Parse the JSON response
            const jsonMatch = generatedText.match(/```json\n([\s\S]*?)\n```/) ||
                generatedText.match(/```\n([\s\S]*?)\n```/) ||
                generatedText.match(/{[\s\S]*}/);
            let proposalData;
            if (jsonMatch) {
                try {
                    proposalData = JSON.parse(jsonMatch[0].replace(/```json\n|```\n|```/g, ''));
                }
                catch (e) {
                    // If parsing fails, try to extract the JSON object directly
                    const directJsonMatch = generatedText.match(/{[\s\S]*}/);
                    if (directJsonMatch) {
                        proposalData = JSON.parse(directJsonMatch[0]);
                    }
                    else {
                        throw new Error('Failed to parse JSON response from Claude');
                    }
                }
            }
            else {
                throw new Error('Failed to extract JSON from Claude response');
            }
            // Add additional metadata
            proposalData.aiGenerated = true;
            proposalData.aiPrompt = prompt;
            proposalData.aiModel = model;
            // Add opportunity, company, and contact IDs if provided
            if (opportunityId) {
                proposalData.opportunityId = opportunityId;
            }
            if (companyId) {
                proposalData.companyId = companyId;
            }
            if (contactIds && contactIds.length > 0) {
                proposalData.contactIds = contactIds;
            }
            // Add IDs to sections and pricing items
            if (proposalData.sections) {
                proposalData.sections = proposalData.sections.map((section, index) => ({
                    ...section,
                    id: uuidv4(),
                    order: index,
                    isVisible: true,
                    aiGenerated: true,
                }));
            }
            if (proposalData.pricing && proposalData.pricing.items) {
                proposalData.pricing.items = proposalData.pricing.items.map(item => ({
                    ...item,
                    id: uuidv4(),
                }));
                // Calculate subtotal and total
                const subtotal = proposalData.pricing.items.reduce((sum, item) => sum + item.total, 0);
                proposalData.pricing.subtotal = subtotal;
                proposalData.pricing.total = subtotal;
            }
            return proposalData;
        }
        catch (error) {
            console.error('Error generating proposal with Claude:', error);
            throw new Error('Failed to generate proposal with Claude');
        }
    }
    /**
     * Generate a proposal section with Claude
     * @param sectionType Type of section to generate
     * @param prompt Prompt to send to Claude
     * @param model Claude model to use
     * @param context Additional context
     * @returns Generated section
     */
    static async generateProposalSection(sectionType, prompt, model = 'claude-3-opus-20240229', context = {}) {
        try {
            // Create a system prompt that instructs Claude on the section format
            const systemPrompt = `You are an expert business proposal writer. Your task is to generate a professional ${sectionType} section for a business proposal based on the user's requirements.
      
Your response should be in JSON format with the following structure:
{
  "title": "Section title",
  "content": "Section content",
  "type": "${sectionType}"
}

Make the section persuasive, professional, and tailored to the client's needs. Use clear, concise language and focus on the value proposition.`;
            // Generate the section with Claude
            const generatedText = await this.generateText(prompt, model, {
                system: systemPrompt,
                temperature: 0.7,
                maxTokens: 2000,
            });
            // Parse the JSON response
            const jsonMatch = generatedText.match(/```json\n([\s\S]*?)\n```/) ||
                generatedText.match(/```\n([\s\S]*?)\n```/) ||
                generatedText.match(/{[\s\S]*}/);
            let sectionData;
            if (jsonMatch) {
                try {
                    sectionData = JSON.parse(jsonMatch[0].replace(/```json\n|```\n|```/g, ''));
                }
                catch (e) {
                    // If parsing fails, try to extract the JSON object directly
                    const directJsonMatch = generatedText.match(/{[\s\S]*}/);
                    if (directJsonMatch) {
                        sectionData = JSON.parse(directJsonMatch[0]);
                    }
                    else {
                        throw new Error('Failed to parse JSON response from Claude');
                    }
                }
            }
            else {
                throw new Error('Failed to extract JSON from Claude response');
            }
            return sectionData;
        }
        catch (error) {
            console.error('Error generating proposal section with Claude:', error);
            throw new Error('Failed to generate proposal section with Claude');
        }
    }
}
Object.defineProperty(AIService, "apiKey", {
    enumerable: true,
    configurable: true,
    writable: true,
    value: process.env.CLAUDE_API_KEY || ''
});
Object.defineProperty(AIService, "apiUrl", {
    enumerable: true,
    configurable: true,
    writable: true,
    value: 'https://api.anthropic.com/v1/messages'
});
