import axios from 'axios';
import { API_BASE_URL } from '../config';
import { getAuthToken } from '../utils/auth';
/**
 * Task Service
 *
 * This service provides methods for managing tasks.
 */
export class TaskService {
    /**
     * Get tasks by proposal
     * @param proposalId Proposal ID
     * @param tenantId Tenant ID
     * @returns Tasks
     */
    static async getTasksByProposal(proposalId, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.get(`${API_BASE_URL}/api/tasks/proposal/${proposalId}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error getting tasks by proposal:', error);
            throw error;
        }
    }
    /**
     * Get task by ID
     * @param taskId Task ID
     * @param tenantId Tenant ID
     * @returns Task
     */
    static async getTaskById(taskId, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.get(`${API_BASE_URL}/api/tasks/${taskId}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error getting task:', error);
            throw error;
        }
    }
    /**
     * Create task
     * @param taskData Task data
     * @param tenantId Tenant ID
     * @returns Created task
     */
    static async createTask(taskData, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.post(`${API_BASE_URL}/api/tasks`, taskData, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error creating task:', error);
            throw error;
        }
    }
    /**
     * Update task
     * @param taskId Task ID
     * @param taskData Task data
     * @param tenantId Tenant ID
     * @returns Updated task
     */
    static async updateTask(taskId, taskData, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.put(`${API_BASE_URL}/api/tasks/${taskId}`, taskData, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error updating task:', error);
            throw error;
        }
    }
    /**
     * Delete task
     * @param taskId Task ID
     * @param tenantId Tenant ID
     * @returns Success status
     */
    static async deleteTask(taskId, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.delete(`${API_BASE_URL}/api/tasks/${taskId}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error deleting task:', error);
            throw error;
        }
    }
    /**
     * Get team members
     * @param tenantId Tenant ID
     * @returns Team members
     */
    static async getTeamMembers(tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.get(`${API_BASE_URL}/api/users/team-members`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error getting team members:', error);
            throw error;
        }
    }
    /**
     * Get task comments
     * @param taskId Task ID
     * @param tenantId Tenant ID
     * @returns Task comments
     */
    static async getTaskComments(taskId, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.get(`${API_BASE_URL}/api/tasks/${taskId}/comments`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error getting task comments:', error);
            throw error;
        }
    }
    /**
     * Add task comment
     * @param taskId Task ID
     * @param commentData Comment data
     * @param tenantId Tenant ID
     * @returns Created comment
     */
    static async addTaskComment(taskId, commentData, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.post(`${API_BASE_URL}/api/tasks/${taskId}/comments`, commentData, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error adding task comment:', error);
            throw error;
        }
    }
    /**
     * Delete task comment
     * @param taskId Task ID
     * @param commentId Comment ID
     * @param tenantId Tenant ID
     * @returns Success status
     */
    static async deleteTaskComment(taskId, commentId, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.delete(`${API_BASE_URL}/api/tasks/${taskId}/comments/${commentId}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error deleting task comment:', error);
            throw error;
        }
    }
    /**
     * Update task progress
     * @param taskId Task ID
     * @param progress Progress percentage (0-100)
     * @param tenantId Tenant ID
     * @returns Updated task
     */
    static async updateTaskProgress(taskId, progress, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.put(`${API_BASE_URL}/api/tasks/${taskId}/progress`, { progress }, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error updating task progress:', error);
            throw error;
        }
    }
    /**
     * Get task activity
     * @param taskId Task ID
     * @param tenantId Tenant ID
     * @returns Task activity
     */
    static async getTaskActivity(taskId, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.get(`${API_BASE_URL}/api/tasks/${taskId}/activity`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error getting task activity:', error);
            throw error;
        }
    }
    /**
     * Get task settings
     * @param tenantId Tenant ID
     * @returns Task settings
     */
    static async getTaskSettings(tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.get(`${API_BASE_URL}/api/tasks/settings`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error getting task settings:', error);
            throw error;
        }
    }
    /**
     * Update task settings
     * @param settingsData Settings data
     * @param tenantId Tenant ID
     * @returns Updated settings
     */
    static async updateTaskSettings(settingsData, tenantId) {
        try {
            const token = await getAuthToken();
            const response = await axios.put(`${API_BASE_URL}/api/tasks/settings`, settingsData, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                },
            });
            return response.data;
        }
        catch (error) {
            console.error('Error updating task settings:', error);
            throw error;
        }
    }
}
export default TaskService;
