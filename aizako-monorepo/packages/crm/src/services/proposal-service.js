import { Proposal } from '../models/proposal';
import { DocumentGenerationService } from './document-generation-service';
import { AIService } from './ai-service';
import mongoose from 'mongoose';
import crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';
/**
 * Proposal service
 */
export class ProposalService {
    /**
     * Create a new proposal
     * @param proposalData Proposal data
     * @param tenantId Tenant ID
     * @returns Created proposal
     */
    static async createProposal(proposalData, tenantId) {
        try {
            // Generate a public token for sharing
            const publicToken = crypto.randomBytes(32).toString('hex');
            const proposal = new Proposal({
                ...proposalData,
                tenantId,
                publicToken,
                publicUrl: `/proposals/public/${publicToken}`,
                viewCount: 0,
                status: 'draft',
            });
            const savedProposal = await proposal.save();
            return savedProposal.toObject();
        }
        catch (error) {
            console.error('Error creating proposal:', error);
            throw error;
        }
    }
    /**
     * Get proposal by ID
     * @param id Proposal ID
     * @param tenantId Tenant ID
     * @returns Proposal or null if not found
     */
    static async getProposalById(id, tenantId) {
        try {
            return await Proposal.findOne({ _id: id, tenantId });
        }
        catch (error) {
            console.error('Error getting proposal by ID:', error);
            throw error;
        }
    }
    /**
     * Get proposal by public token
     * @param token Public token
     * @returns Proposal or null if not found
     */
    static async getProposalByToken(token) {
        try {
            return await Proposal.findOne({
                publicToken: token,
                publicAccessEnabled: true,
                status: { $nin: ['draft', 'expired'] }
            });
        }
        catch (error) {
            console.error('Error getting proposal by token:', error);
            throw error;
        }
    }
    /**
     * Get proposals with pagination
     * @param params Pagination parameters
     * @param tenantId Tenant ID
     * @returns Paginated proposals
     */
    static async getProposals(params, tenantId) {
        try {
            const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc', ...filters } = params;
            const query = Proposal.find({ tenantId });
            // Apply filters
            Object.entries(filters).forEach(([key, value]) => {
                if (value !== undefined && value !== null) {
                    query.where(key).equals(value);
                }
            });
            // Get total count
            const total = await Proposal.countDocuments(query.getQuery());
            // Apply pagination and sorting
            const proposals = await query
                .sort({ [sortBy]: sortOrder === 'asc' ? 1 : -1 })
                .skip((page - 1) * limit)
                .limit(limit)
                .exec();
            return {
                proposals: proposals.map(p => p.toObject()),
                total
            };
        }
        catch (error) {
            console.error('Error getting proposals:', error);
            throw error;
        }
    }
    /**
     * Get proposals with advanced filtering, sorting, and pagination
     * @param options Filter options
     * @param sortOptions Sort options
     * @param paginationOptions Pagination options
     * @param tenantId Tenant ID
     * @returns Proposals and total count
     */
    static async getProposalsAdvanced(options = {}, sortOptions = { field: 'createdAt', direction: 'desc' }, paginationOptions = { page: 1, limit: 20 }, tenantId) {
        try {
            const filter = { tenantId };
            // Apply search filter
            if (options.search) {
                filter.$or = [
                    { title: { $regex: options.search, $options: 'i' } },
                    { description: { $regex: options.search, $options: 'i' } },
                ];
            }
            // Apply status filter
            if (options.status) {
                filter.status = Array.isArray(options.status)
                    ? { $in: options.status }
                    : options.status;
            }
            // Apply opportunity filter
            if (options.opportunityId) {
                filter.opportunityId = new mongoose.Types.ObjectId(options.opportunityId);
            }
            // Apply company filter
            if (options.companyId) {
                filter.companyId = new mongoose.Types.ObjectId(options.companyId);
            }
            // Apply contacts filter
            if (options.contactIds) {
                if (Array.isArray(options.contactIds)) {
                    filter.contactIds = { $in: options.contactIds.map(id => new mongoose.Types.ObjectId(id)) };
                }
                else {
                    filter.contactIds = new mongoose.Types.ObjectId(options.contactIds);
                }
            }
            // Apply owner filter
            if (options.owner) {
                filter.owner = new mongoose.Types.ObjectId(options.owner);
            }
            // Apply tags filter
            if (options.tags) {
                filter.tags = Array.isArray(options.tags)
                    ? { $all: options.tags }
                    : { $all: [options.tags] };
            }
            // Apply created date filters
            if (options.createdAfter || options.createdBefore) {
                filter.createdAt = {};
                if (options.createdAfter) {
                    filter.createdAt.$gte = options.createdAfter;
                }
                if (options.createdBefore) {
                    filter.createdAt.$lte = options.createdBefore;
                }
            }
            // Apply sent date filters
            if (options.sentAfter || options.sentBefore) {
                filter.sentAt = {};
                if (options.sentAfter) {
                    filter.sentAt.$gte = options.sentAfter;
                }
                if (options.sentBefore) {
                    filter.sentAt.$lte = options.sentBefore;
                }
            }
            // Apply viewed date filters
            if (options.viewedAfter || options.viewedBefore) {
                filter.viewedAt = {};
                if (options.viewedAfter) {
                    filter.viewedAt.$gte = options.viewedAfter;
                }
                if (options.viewedBefore) {
                    filter.viewedAt.$lte = options.viewedBefore;
                }
            }
            // Apply expires date filters
            if (options.expiresAfter || options.expiresBefore) {
                filter.expiresAt = {};
                if (options.expiresAfter) {
                    filter.expiresAt.$gte = options.expiresAfter;
                }
                if (options.expiresBefore) {
                    filter.expiresAt.$lte = options.expiresBefore;
                }
            }
            // Apply AI generated filter
            if (options.aiGenerated !== undefined) {
                filter.aiGenerated = options.aiGenerated;
            }
            // Apply custom fields filters
            if (options.customFields) {
                for (const [key, value] of Object.entries(options.customFields)) {
                    filter[`customFields.${key}`] = value;
                }
            }
            // Calculate skip value for pagination
            const skip = (paginationOptions.page - 1) * paginationOptions.limit;
            // Create sort object
            const sort = {};
            sort[sortOptions.field] = sortOptions.direction === 'asc' ? 1 : -1;
            // Execute query with pagination
            const [proposals, total] = await Promise.all([
                Proposal.find(filter)
                    .sort(sort)
                    .skip(skip)
                    .limit(paginationOptions.limit),
                Proposal.countDocuments(filter),
            ]);
            return {
                proposals: proposals.map(p => p.toObject()),
                total
            };
        }
        catch (error) {
            console.error('Error getting proposals with advanced filtering:', error);
            throw error;
        }
    }
    /**
     * Update proposal
     * @param id Proposal ID
     * @param proposalData Proposal data to update
     * @param tenantId Tenant ID
     * @param userId User ID (optional, for version history)
     * @param comment Comment for version history (optional)
     * @returns Updated proposal or null if not found
     */
    static async updateProposal(id, proposalData, tenantId, userId, comment) {
        try {
            // Get the current proposal for version history
            const currentProposal = await Proposal.findOne({
                _id: id,
                tenantId
            });
            if (!currentProposal) {
                return null;
            }
            // Update the proposal
            const updatedProposal = await Proposal.findOneAndUpdate({ _id: id, tenantId }, { $set: proposalData }, { new: true });
            // Create version history if userId is provided
            if (updatedProposal && userId) {
                try {
                    const VersionHistoryService = require('./version-history-service').default;
                    await VersionHistoryService.createVersion(updatedProposal, currentProposal.toObject(), userId, comment);
                }
                catch (versionError) {
                    console.error('Error creating version history:', versionError);
                    // Continue even if version history fails
                }
            }
            return updatedProposal ? updatedProposal.toObject() : null;
        }
        catch (error) {
            console.error('Error updating proposal:', error);
            throw error;
        }
    }
    /**
     * Delete proposal
     * @param id Proposal ID
     * @param tenantId Tenant ID
     * @returns True if deleted, false if not found
     */
    static async deleteProposal(id, tenantId) {
        try {
            const result = await Proposal.deleteOne({ _id: id, tenantId });
            return result.deletedCount > 0;
        }
        catch (error) {
            console.error('Error deleting proposal:', error);
            throw error;
        }
    }
    /**
     * Send proposal
     * @param id Proposal ID
     * @param userId User ID sending the proposal
     * @param tenantId Tenant ID
     * @param options Send options
     * @returns Updated proposal
     */
    static async sendProposal(id, userId, tenantId, options) {
        try {
            // Get the proposal
            const proposal = await this.getProposalById(id, tenantId);
            if (!proposal) {
                throw new Error('Proposal not found');
            }
            // Update the proposal status
            const updatedProposal = await Proposal.findOneAndUpdate({ _id: id, tenantId }, {
                $set: {
                    status: 'sent',
                    sentAt: new Date(),
                    sentBy: new mongoose.Types.ObjectId(userId),
                    ...(options.expiresAt && { expiresAt: options.expiresAt }),
                },
            }, { new: true });
            // TODO: Implement email sending logic
            // This would involve integrating with an email service like Resend
            // For now, we'll just return the updated proposal
            return updatedProposal ? updatedProposal.toObject() : null;
        }
        catch (error) {
            console.error('Error sending proposal:', error);
            throw error;
        }
    }
    /**
     * Record proposal view
     * @param id Proposal ID
     * @param analyticsEvent Analytics event data
     * @returns Updated proposal
     */
    static async recordView(id, analyticsEvent) {
        try {
            return await Proposal.findOneAndUpdate({ _id: id }, {
                $inc: { viewCount: 1 },
                $set: {
                    status: 'viewed',
                    viewedAt: new Date(),
                    lastViewedAt: new Date()
                },
                $push: { analyticsEvents: analyticsEvent },
            }, { new: true });
        }
        catch (error) {
            console.error('Error recording proposal view:', error);
            throw error;
        }
    }
    /**
     * Accept proposal
     * @param id Proposal ID
     * @param userId User ID accepting the proposal
     * @param tenantId Tenant ID
     * @returns Updated proposal
     */
    static async acceptProposal(id, userId, tenantId) {
        try {
            return await Proposal.findOneAndUpdate({ _id: id, tenantId }, {
                $set: {
                    status: 'accepted',
                    acceptedAt: new Date(),
                    acceptedBy: new mongoose.Types.ObjectId(userId),
                },
            }, { new: true });
        }
        catch (error) {
            console.error('Error accepting proposal:', error);
            throw error;
        }
    }
    /**
     * Reject proposal
     * @param id Proposal ID
     * @param userId User ID rejecting the proposal
     * @param reason Rejection reason
     * @param tenantId Tenant ID
     * @returns Updated proposal
     */
    static async rejectProposal(id, userId, reason, tenantId) {
        try {
            return await Proposal.findOneAndUpdate({ _id: id, tenantId }, {
                $set: {
                    status: 'rejected',
                    rejectedAt: new Date(),
                    rejectedBy: new mongoose.Types.ObjectId(userId),
                    rejectionReason: reason,
                },
            }, { new: true });
        }
        catch (error) {
            console.error('Error rejecting proposal:', error);
            throw error;
        }
    }
    /**
     * Generate proposal document
     * @param proposal Proposal data
     * @param options Document generation options
     * @param skipCache Whether to skip the cache
     * @returns Generated document
     */
    static async generateProposalDocument(proposal, options, skipCache = false) {
        try {
            return await DocumentGenerationService.generateDocument(proposal, options, proposal.tenantId, skipCache);
        }
        catch (error) {
            console.error('Error generating proposal document:', error);
            throw error;
        }
    }
    /**
     * Generate proposal with AI
     * @param options AI generation options
     * @param tenantId Tenant ID
     * @returns Generated proposal
     */
    static async generateProposalWithAI(options, tenantId) {
        try {
            // Generate proposal data with AI
            const proposalData = await AIService.generateProposal(options);
            // Create the proposal
            return await this.createProposal({
                ...proposalData,
                status: 'draft',
                aiGenerated: true,
                aiPrompt: options.prompt,
                aiModel: options.model,
            }, tenantId);
        }
        catch (error) {
            console.error('Error generating proposal with AI:', error);
            throw error;
        }
    }
    /**
     * Generate proposal section with AI
     * @param sectionType Section type
     * @param prompt Prompt
     * @param model Model
     * @param context Additional context
     * @returns Generated section
     */
    static async generateProposalSectionWithAI(sectionType, prompt, model, context = {}) {
        try {
            // Generate section with AI
            const sectionData = await AIService.generateProposalSection(sectionType, prompt, model, context);
            // Add additional metadata
            return {
                ...sectionData,
                id: uuidv4(),
                order: 0,
                isVisible: true,
                aiGenerated: true,
            };
        }
        catch (error) {
            console.error('Error generating proposal section with AI:', error);
            throw error;
        }
    }
    /**
     * Get tenant analytics
     * @param tenantId Tenant ID
     * @param startDate Start date for analytics
     * @param endDate End date for analytics
     * @returns Analytics data
     */
    static async getTenantAnalytics(tenantId, startDate, endDate) {
        try {
            // TODO: Implement proper analytics aggregation
            // For now, return mock data
            return {
                totalProposals: 0,
                totalViews: 0,
                totalDownloads: 0,
                totalAcceptances: 0,
                totalRejections: 0,
                viewRate: 0,
                acceptanceRate: 0,
                rejectionRate: 0,
                proposalsByStatus: {},
                viewsByDate: {},
                topProposals: [],
            };
        }
        catch (error) {
            console.error('Error getting tenant analytics:', error);
            throw error;
        }
    }
    /**
     * Get proposal analytics
     * @param proposalId Proposal ID
     * @param tenantId Tenant ID
     * @returns Analytics data
     */
    static async getProposalAnalytics(proposalId, tenantId) {
        try {
            // TODO: Implement proper analytics aggregation
            // For now, return mock data
            return {
                totalViews: 0,
                totalDownloads: 0,
                totalShares: 0,
                viewsByDate: {},
                deviceBreakdown: {},
                locationBreakdown: {},
                sectionViews: {},
                averageViewTime: 0,
                events: [],
            };
        }
        catch (error) {
            console.error('Error getting proposal analytics:', error);
            throw error;
        }
    }
    /**
     * Get proposals by contact
     * @param contactId Contact ID
     * @param tenantId Tenant ID
     * @returns Proposals
     */
    static async getProposalsByContact(contactId, tenantId) {
        try {
            // TODO: Implement proper contact-based proposal fetching
            return [];
        }
        catch (error) {
            console.error('Error getting proposals by contact:', error);
            throw error;
        }
    }
    /**
     * Get proposals by company
     * @param companyId Company ID
     * @param tenantId Tenant ID
     * @returns Proposals
     */
    static async getProposalsByCompany(companyId, tenantId) {
        try {
            // TODO: Implement proper company-based proposal fetching
            return [];
        }
        catch (error) {
            console.error('Error getting proposals by company:', error);
            throw error;
        }
    }
    /**
     * Get proposals by opportunity
     * @param opportunityId Opportunity ID
     * @param tenantId Tenant ID
     * @returns Proposals
     */
    static async getProposalsByOpportunity(opportunityId, tenantId) {
        try {
            const proposals = await Proposal.find({ opportunityId, tenantId }).sort({ createdAt: -1 });
            return proposals.map(p => p.toObject());
        }
        catch (error) {
            console.error('Error getting proposals by opportunity:', error);
            throw error;
        }
    }
}
export default ProposalService;
