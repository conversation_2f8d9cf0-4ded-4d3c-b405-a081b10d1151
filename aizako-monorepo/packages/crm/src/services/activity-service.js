// Activity service for CRM module
// TODO: Implement proper activity service
import { Activity } from '../models/activity';
export class ActivityService {
    async createActivity(data) {
        try {
            const activity = new Activity({
                ...data,
                createdAt: new Date(),
                updatedAt: new Date(),
            });
            return await activity.save();
        }
        catch (error) {
            console.error('Error creating activity:', error);
            throw new Error('Failed to create activity');
        }
    }
    async getActivity(id, tenantId) {
        try {
            return await Activity.findOne({ _id: id, tenantId });
        }
        catch (error) {
            console.error('Error getting activity:', error);
            throw new Error('Failed to get activity');
        }
    }
    async updateActivity(data) {
        try {
            const { id, ...updateData } = data;
            return await Activity.findOneAndUpdate({ _id: id, tenantId: data.tenantId }, { ...updateData, updatedAt: new Date() }, { new: true });
        }
        catch (error) {
            console.error('Error updating activity:', error);
            throw new Error('Failed to update activity');
        }
    }
    async deleteActivity(id, tenantId) {
        try {
            const result = await Activity.deleteOne({ _id: id, tenantId });
            return result.deletedCount > 0;
        }
        catch (error) {
            console.error('Error deleting activity:', error);
            throw new Error('Failed to delete activity');
        }
    }
    async getActivities(tenantId, filters = {}, page = 1, limit = 20) {
        try {
            const query = { tenantId };
            if (filters.type)
                query.type = filters.type;
            if (filters.status)
                query.status = filters.status;
            if (filters.priority)
                query.priority = filters.priority;
            if (filters.contactId)
                query.contactId = filters.contactId;
            if (filters.companyId)
                query.companyId = filters.companyId;
            if (filters.opportunityId)
                query.opportunityId = filters.opportunityId;
            if (filters.userId)
                query.userId = filters.userId;
            if (filters.dateFrom || filters.dateTo) {
                query.dueDate = {};
                if (filters.dateFrom)
                    query.dueDate.$gte = filters.dateFrom;
                if (filters.dateTo)
                    query.dueDate.$lte = filters.dateTo;
            }
            const skip = (page - 1) * limit;
            const [activities, total] = await Promise.all([
                Activity.find(query)
                    .sort({ dueDate: 1, createdAt: -1 })
                    .skip(skip)
                    .limit(limit)
                    .populate('contactId', 'name email')
                    .populate('companyId', 'name')
                    .populate('opportunityId', 'name')
                    .populate('userId', 'name email'),
                Activity.countDocuments(query),
            ]);
            return {
                activities,
                total,
                page,
                totalPages: Math.ceil(total / limit),
            };
        }
        catch (error) {
            console.error('Error getting activities:', error);
            throw new Error('Failed to get activities');
        }
    }
    async getUpcomingActivities(tenantId, userId, days = 7) {
        try {
            const query = {
                tenantId,
                status: { $in: ['pending', 'in_progress'] },
                dueDate: {
                    $gte: new Date(),
                    $lte: new Date(Date.now() + days * 24 * 60 * 60 * 1000),
                },
            };
            if (userId)
                query.userId = userId;
            return await Activity.find(query)
                .sort({ dueDate: 1 })
                .populate('contactId', 'name email')
                .populate('companyId', 'name')
                .populate('opportunityId', 'name');
        }
        catch (error) {
            console.error('Error getting upcoming activities:', error);
            throw new Error('Failed to get upcoming activities');
        }
    }
    async getOverdueActivities(tenantId, userId) {
        try {
            const query = {
                tenantId,
                status: { $in: ['pending', 'in_progress'] },
                dueDate: { $lt: new Date() },
            };
            if (userId)
                query.userId = userId;
            return await Activity.find(query)
                .sort({ dueDate: 1 })
                .populate('contactId', 'name email')
                .populate('companyId', 'name')
                .populate('opportunityId', 'name');
        }
        catch (error) {
            console.error('Error getting overdue activities:', error);
            throw new Error('Failed to get overdue activities');
        }
    }
    async markActivityComplete(id, tenantId) {
        try {
            return await Activity.findOneAndUpdate({ _id: id, tenantId }, {
                status: 'completed',
                completedAt: new Date(),
                updatedAt: new Date(),
            }, { new: true });
        }
        catch (error) {
            console.error('Error marking activity complete:', error);
            throw new Error('Failed to mark activity complete');
        }
    }
}
export const activityService = new ActivityService();
