import { AIService } from './ai-service';
import { ProposalService } from './proposal-service';
import { v4 as uuidv4 } from 'uuid';
/**
 * Task type constants
 */
export const TASK_TYPES = {
    GENERATE_PROPOSAL: 'generate_proposal',
    GENERATE_PROPOSAL_SECTION: 'generate_proposal_section',
};
/**
 * <PERSON><PERSON> for generating a proposal with AI
 * @param task Background task
 * @param updateProgress Progress update function
 * @returns Generated proposal
 */
export const generateProposalHandler = async (task, updateProgress) => {
    const { prompt, model, includeSections, opportunityId, companyId, contactIds } = task.params;
    // Update progress to indicate we're starting
    await updateProgress(10);
    try {
        // Generate the proposal content with AI
        const generatedContent = await AIService.generateProposal({
            prompt,
            model,
            includeSections,
            opportunityId,
            companyId,
            contactIds,
        });
        // Update progress to indicate content generation is complete
        await updateProgress(50);
        // Create the proposal in the database
        const proposal = await ProposalService.createProposal({
            title: generatedContent.title,
            description: generatedContent.description,
            sections: generatedContent.sections,
            pricing: generatedContent.pricing,
            terms: generatedContent.terms,
            status: 'draft',
            aiGenerated: true,
            aiPrompt: prompt,
            aiModel: model,
            opportunityId,
            companyId,
            contactIds,
        }, task.tenantId);
        // Update progress to indicate proposal creation is complete
        await updateProgress(90);
        // Return the created proposal
        return {
            proposalId: proposal._id?.toString() || '',
            title: proposal.title,
        };
    }
    catch (error) {
        console.error('Error generating proposal with AI:', error);
        throw error;
    }
};
/**
 * Handler for generating a proposal section with AI
 * @param task Background task
 * @param updateProgress Progress update function
 * @returns Generated section
 */
export const generateProposalSectionHandler = async (task, updateProgress) => {
    const { proposalId, sectionType, prompt, model, context } = task.params;
    // Update progress to indicate we're starting
    await updateProgress(10);
    try {
        // Generate the section content with AI
        const generatedSection = await AIService.generateProposalSection(sectionType, prompt, model, context || {});
        // Update progress to indicate content generation is complete
        await updateProgress(50);
        // If proposalId is provided, add the section to the proposal
        if (proposalId) {
            const proposal = await ProposalService.getProposalById(proposalId, task.tenantId);
            if (!proposal) {
                throw new Error(`Proposal not found: ${proposalId}`);
            }
            // Create a new section
            const newSection = {
                id: uuidv4(),
                title: generatedSection.title,
                content: generatedSection.content,
                type: sectionType,
                order: proposal.sections.length,
                isVisible: true,
                aiGenerated: true,
            };
            // Add the section to the proposal
            proposal.sections.push(newSection);
            // Update the proposal
            await ProposalService.updateProposal(proposalId, { sections: proposal.sections }, task.tenantId);
            // Update progress to indicate section addition is complete
            await updateProgress(90);
            // Return the updated proposal and new section
            return {
                proposalId,
                section: newSection,
            };
        }
        else {
            // Just return the generated section
            const section = {
                id: uuidv4(),
                title: generatedSection.title,
                content: generatedSection.content,
                type: sectionType,
                order: 0,
                isVisible: true,
                aiGenerated: true,
            };
            // Update progress to indicate completion
            await updateProgress(90);
            return { section };
        }
    }
    catch (error) {
        console.error('Error generating proposal section with AI:', error);
        throw error;
    }
};
/**
 * Register all AI task handlers
 * @param taskService Background task service
 */
export const registerAITaskHandlers = (taskService) => {
    taskService.registerTaskHandler(TASK_TYPES.GENERATE_PROPOSAL, generateProposalHandler);
    taskService.registerTaskHandler(TASK_TYPES.GENERATE_PROPOSAL_SECTION, generateProposalSectionHandler);
};
