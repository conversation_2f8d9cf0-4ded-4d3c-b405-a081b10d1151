import { Contact } from '../models/contact';
import mongoose from 'mongoose';
/**
 * Contact service
 */
export class ContactService {
    /**
     * Create a new contact
     * @param contactData Contact data
     * @param tenantId Tenant ID
     * @returns Created contact
     */
    static async createContact(contactData, tenantId) {
        try {
            const contact = new Contact({
                ...contactData,
                tenantId,
            });
            return await contact.save();
        }
        catch (error) {
            console.error('Error creating contact:', error);
            throw error;
        }
    }
    /**
     * Get contact by ID
     * @param id Contact ID
     * @param tenantId Tenant ID
     * @returns Contact or null if not found
     */
    static async getContactById(id, tenantId) {
        try {
            return await Contact.findOne({ _id: id, tenantId });
        }
        catch (error) {
            console.error('Error getting contact by ID:', error);
            throw error;
        }
    }
    /**
     * Get contacts with pagination
     * @param params Pagination parameters
     * @param tenantId Tenant ID
     * @returns Paginated contacts
     */
    static async getContacts(params, tenantId) {
        try {
            const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc', ...filters } = params;
            const query = Contact.find({ tenantId });
            // Apply filters
            Object.entries(filters).forEach(([key, value]) => {
                if (value !== undefined && value !== null) {
                    query.where(key).equals(value);
                }
            });
            // Get total count
            const total = await Contact.countDocuments(query.getQuery());
            // Apply pagination and sorting
            const contacts = await query
                .sort({ [sortBy]: sortOrder === 'asc' ? 1 : -1 })
                .skip((page - 1) * limit)
                .limit(limit)
                .exec();
            return { contacts, total };
        }
        catch (error) {
            console.error('Error getting contacts:', error);
            throw error;
        }
    }
    /**
     * Get contacts with advanced filtering, sorting, and pagination
     * @param options Filter options
     * @param sortOptions Sort options
     * @param paginationOptions Pagination options
     * @param tenantId Tenant ID
     * @returns Contacts and total count
     */
    static async getContactsAdvanced(options = {}, sortOptions = { field: 'createdAt', direction: 'desc' }, paginationOptions = { page: 1, limit: 20 }, tenantId) {
        try {
            const filter = { tenantId };
            // Apply search filter
            if (options.search) {
                filter.$or = [
                    { firstName: { $regex: options.search, $options: 'i' } },
                    { lastName: { $regex: options.search, $options: 'i' } },
                    { email: { $regex: options.search, $options: 'i' } },
                ];
            }
            // Apply status filter
            if (options.status) {
                filter.status = Array.isArray(options.status)
                    ? { $in: options.status }
                    : options.status;
            }
            // Apply tags filter
            if (options.tags) {
                filter.tags = Array.isArray(options.tags)
                    ? { $all: options.tags }
                    : { $all: [options.tags] };
            }
            // Apply owner filter
            if (options.owner) {
                filter.owner = new mongoose.Types.ObjectId(options.owner);
            }
            // Apply company filter
            if (options.companyId) {
                filter.companyId = new mongoose.Types.ObjectId(options.companyId);
            }
            // Apply date filters
            if (options.createdAfter || options.createdBefore) {
                filter.createdAt = {};
                if (options.createdAfter) {
                    filter.createdAt.$gte = options.createdAfter;
                }
                if (options.createdBefore) {
                    filter.createdAt.$lte = options.createdBefore;
                }
            }
            // Apply last contacted filters
            if (options.lastContactedAfter || options.lastContactedBefore) {
                filter.lastContactedAt = {};
                if (options.lastContactedAfter) {
                    filter.lastContactedAt.$gte = options.lastContactedAfter;
                }
                if (options.lastContactedBefore) {
                    filter.lastContactedAt.$lte = options.lastContactedBefore;
                }
            }
            // Apply score filters
            if (options.scoreMin !== undefined || options.scoreMax !== undefined) {
                filter['score.current'] = {};
                if (options.scoreMin !== undefined) {
                    filter['score.current'].$gte = options.scoreMin;
                }
                if (options.scoreMax !== undefined) {
                    filter['score.current'].$lte = options.scoreMax;
                }
            }
            // Apply custom fields filters
            if (options.customFields) {
                for (const [key, value] of Object.entries(options.customFields)) {
                    filter[`customFields.${key}`] = value;
                }
            }
            // Calculate skip value for pagination
            const skip = (paginationOptions.page - 1) * paginationOptions.limit;
            // Create sort object
            const sort = {};
            sort[sortOptions.field] = sortOptions.direction === 'asc' ? 1 : -1;
            // Execute query with pagination
            const [contacts, total] = await Promise.all([
                Contact.find(filter)
                    .sort(sort)
                    .skip(skip)
                    .limit(paginationOptions.limit),
                Contact.countDocuments(filter),
            ]);
            return { contacts, total };
        }
        catch (error) {
            console.error('Error getting contacts with advanced filtering:', error);
            throw error;
        }
    }
    /**
     * Update contact
     * @param id Contact ID
     * @param contactData Contact data to update
     * @param tenantId Tenant ID
     * @returns Updated contact or null if not found
     */
    static async updateContact(id, contactData, tenantId) {
        try {
            return await Contact.findOneAndUpdate({ _id: id, tenantId }, { $set: contactData }, { new: true });
        }
        catch (error) {
            console.error('Error updating contact:', error);
            throw error;
        }
    }
    /**
     * Delete contact
     * @param id Contact ID
     * @param tenantId Tenant ID
     * @returns True if deleted, false if not found
     */
    static async deleteContact(id, tenantId) {
        try {
            const result = await Contact.deleteOne({ _id: id, tenantId });
            return result.deletedCount > 0;
        }
        catch (error) {
            console.error('Error deleting contact:', error);
            throw error;
        }
    }
    /**
     * Add an interaction to a contact
     * @param contactId Contact ID
     * @param interaction Interaction data
     * @param tenantId Tenant ID
     * @returns Updated contact
     */
    static async addInteraction(contactId, interaction, tenantId) {
        try {
            return await Contact.findOneAndUpdate({ _id: contactId, tenantId }, {
                $push: { interactions: interaction },
                $set: { lastContactedAt: new Date() },
            }, { new: true });
        }
        catch (error) {
            console.error('Error adding interaction to contact:', error);
            throw error;
        }
    }
    /**
     * Update contact score
     * @param contactId Contact ID
     * @param score Score data
     * @param tenantId Tenant ID
     * @returns Updated contact
     */
    static async updateScore(contactId, score, tenantId) {
        try {
            return await Contact.findOneAndUpdate({ _id: contactId, tenantId }, { $set: { score } }, { new: true });
        }
        catch (error) {
            console.error('Error updating contact score:', error);
            throw error;
        }
    }
    /**
     * Update contact persona
     * @param contactId Contact ID
     * @param persona Persona data
     * @param tenantId Tenant ID
     * @returns Updated contact
     */
    static async updatePersona(contactId, persona, tenantId) {
        try {
            return await Contact.findOneAndUpdate({ _id: contactId, tenantId }, { $set: { persona } }, { new: true });
        }
        catch (error) {
            console.error('Error updating contact persona:', error);
            throw error;
        }
    }
    /**
     * Get contacts by company
     * @param companyId Company ID
     * @param tenantId Tenant ID
     * @returns Contacts
     */
    static async getContactsByCompany(companyId, tenantId) {
        try {
            return await Contact.find({
                companyId: new mongoose.Types.ObjectId(companyId),
                tenantId,
            });
        }
        catch (error) {
            console.error('Error getting contacts by company:', error);
            throw error;
        }
    }
}
