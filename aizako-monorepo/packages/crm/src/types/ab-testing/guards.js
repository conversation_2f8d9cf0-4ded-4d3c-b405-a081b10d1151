/**
 * Type guards for A/B testing types
 */
/**
 * Type guard for A/B test
 * @param obj Object to check
 * @returns True if the object is an A/B test
 */
export function isABTest(obj) {
    return (obj &&
        typeof obj === 'object' &&
        typeof obj.name === 'string' &&
        typeof obj.baseProposalId === 'object' &&
        Array.isArray(obj.variants) &&
        obj.variants.every((variant) => typeof variant === 'object' &&
            typeof variant.name === 'string' &&
            typeof variant.proposalId === 'object' &&
            typeof variant.trafficPercentage === 'number') &&
        typeof obj.baseTrafficPercentage === 'number' &&
        typeof obj.status === 'string' &&
        ['active', 'paused', 'completed'].includes(obj.status) &&
        obj.startDate instanceof Date);
}
/**
 * Type guard for array of A/B tests
 * @param obj Object to check
 * @returns True if the object is an array of A/B tests
 */
export function isABTestArray(obj) {
    return Array.isArray(obj) && obj.every(isABTest);
}
