// Note: Type guards moved to guards.ts
/**
 * Type guard for A/B test results
 * @param obj Object to check
 * @returns True if the object is A/B test results
 */
export function isABTestResults(obj) {
    return (obj &&
        typeof obj === 'object' &&
        typeof obj.testId === 'string' &&
        typeof obj.name === 'string' &&
        typeof obj.baseProposal === 'object' &&
        Array.isArray(obj.variants) &&
        (obj.winner === null || typeof obj.winner === 'string') &&
        typeof obj.confidence === 'number');
}
// Note: isABTestArray moved to guards.ts
