/**
 * Type guards for proposal types
 */
/**
 * Type guard for proposal analytics event
 * @param obj Object to check
 * @returns True if the object is a proposal analytics event
 */
export function isProposalAnalyticsEvent(obj) {
    return (obj &&
        typeof obj === 'object' &&
        typeof obj.eventType === 'string' &&
        obj.timestamp instanceof Date);
}
/**
 * Type guard for proposal section
 * @param obj Object to check
 * @returns True if the object is a proposal section
 */
export function isProposalSection(obj) {
    return (obj &&
        typeof obj === 'object' &&
        typeof obj.id === 'string' &&
        typeof obj.title === 'string' &&
        typeof obj.content === 'string' &&
        typeof obj.order === 'number' &&
        typeof obj.type === 'string' &&
        ['text', 'pricing', 'timeline', 'team', 'testimonials', 'images', 'custom'].includes(obj.type) &&
        typeof obj.isVisible === 'boolean');
}
/**
 * Type guard for proposal pricing item
 * @param obj Object to check
 * @returns True if the object is a proposal pricing item
 */
export function isProposalPricingItem(obj) {
    return (obj &&
        typeof obj === 'object' &&
        typeof obj.id === 'string' &&
        typeof obj.name === 'string' &&
        typeof obj.quantity === 'number' &&
        typeof obj.unitPrice === 'number' &&
        typeof obj.total === 'number');
}
/**
 * Type guard for proposal pricing
 * @param obj Object to check
 * @returns True if the object is a proposal pricing
 */
export function isProposalPricing(obj) {
    return (obj &&
        typeof obj === 'object' &&
        typeof obj.currency === 'string' &&
        Array.isArray(obj.items) &&
        obj.items.every((item) => isProposalPricingItem(item)) &&
        typeof obj.subtotal === 'number' &&
        typeof obj.total === 'number');
}
// Note: A/B test guards moved to ab-testing/guards.ts
/**
 * Type guard for array of proposal sections
 * @param obj Object to check
 * @returns True if the object is an array of proposal sections
 */
export function isProposalSectionArray(obj) {
    return Array.isArray(obj) && obj.every(isProposalSection);
}
/**
 * Type guard for array of proposal pricing items
 * @param obj Object to check
 * @returns True if the object is an array of proposal pricing items
 */
export function isProposalPricingItemArray(obj) {
    return Array.isArray(obj) && obj.every(isProposalPricingItem);
}
// Note: A/B test array guard moved to ab-testing/guards.ts
/**
 * Type guard for array of proposal analytics events
 * @param obj Object to check
 * @returns True if the object is an array of proposal analytics events
 */
export function isProposalAnalyticsEventArray(obj) {
    return Array.isArray(obj) && obj.every(isProposalAnalyticsEvent);
}
