/**
 * Type guard for proposal
 * @param obj Object to check
 * @returns True if the object is a proposal
 */
export function isProposal(obj) {
    return (obj &&
        typeof obj === 'object' &&
        typeof obj.title === 'string' &&
        Array.isArray(obj.sections) &&
        typeof obj.status === 'string' &&
        ['draft', 'sent', 'viewed', 'accepted', 'rejected', 'expired'].includes(obj.status));
}
// Note: Type guards moved to guards.ts
