/**
 * Create pagination result
 * @param data Data array
 * @param total Total count
 * @param page Current page
 * @param limit Items per page
 * @returns Pagination result
 */
export function createPaginationResult(data, total, page, limit) {
    const totalPages = Math.ceil(total / limit);
    return {
        data,
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
    };
}
