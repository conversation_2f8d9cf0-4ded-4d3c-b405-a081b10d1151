import { z } from 'zod';
import { ObjectId } from 'mongodb';
/**
 * Zod schema for MongoDB ObjectId
 * Shared across all modules to avoid duplication
 */
export const ObjectIdSchema = z.custom((val) => {
    try {
        if (typeof val === 'string') {
            return ObjectId.isValid(val);
        }
        return val instanceof ObjectId;
    }
    catch {
        return false;
    }
}, 'Invalid ObjectId');
/**
 * Common pagination schema
 */
export const PaginationSchema = z.object({
    page: z.number().int().min(1).default(1),
    limit: z.number().int().min(1).max(100).default(10),
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).default('desc'),
});
/**
 * Common response metadata schema
 */
export const ResponseMetadataSchema = z.object({
    total: z.number().int().min(0),
    page: z.number().int().min(1),
    limit: z.number().int().min(1),
    totalPages: z.number().int().min(0),
    hasNextPage: z.boolean(),
    hasPrevPage: z.boolean(),
});
