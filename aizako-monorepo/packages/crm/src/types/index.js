/**
 * CRM Types Module
 *
 * This module exports all types, schemas, and type guards used in the CRM module.
 * It provides a centralized location for all type definitions to ensure consistency
 * across the codebase.
 */
// Export common schemas and types
export * from './common-schemas';
export * from './pagination';
export * from './api';
// Export entity types
export * from './proposals';
export * from './ab-testing';
