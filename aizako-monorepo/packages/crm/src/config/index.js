// Configuration for CRM module
// TODO: Implement proper configuration management
// Default configuration
const defaultConfig = {
    database: {
        url: process.env.MONGODB_URL || 'mongodb://localhost:27017',
        name: process.env.MONGODB_DB_NAME || 'aizako_crm',
    },
    auth: {
        jwtSecret: process.env.JWT_SECRET || 'your-secret-key',
        tokenExpiry: process.env.JWT_EXPIRY || '24h',
    },
    email: {
        provider: process.env.EMAIL_PROVIDER || 'resend',
        apiKey: process.env.EMAIL_API_KEY || '',
        fromAddress: process.env.EMAIL_FROM_ADDRESS || '<EMAIL>',
    },
    calendar: {
        provider: process.env.CALENDAR_PROVIDER || 'google',
        clientId: process.env.CALENDAR_CLIENT_ID || '',
        clientSecret: process.env.CALENDAR_CLIENT_SECRET || '',
    },
    ai: {
        provider: process.env.AI_PROVIDER || 'anthropic',
        apiKey: process.env.AI_API_KEY || '',
        model: process.env.AI_MODEL || 'claude-3-sonnet',
    },
    storage: {
        provider: process.env.STORAGE_PROVIDER || 's3',
        bucket: process.env.STORAGE_BUCKET || 'aizako-crm-files',
        region: process.env.STORAGE_REGION || 'us-east-1',
    },
};
export const config = defaultConfig;
export default config;
// API configuration
export const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000/api';
