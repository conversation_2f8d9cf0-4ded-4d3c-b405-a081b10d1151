import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
// Set up MongoDB memory server
export const setupTestDB = async () => {
    // Create a MongoDB memory server
    const mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    // Connect to the in-memory database
    await mongoose.connect(mongoUri);
    // Store the server instance for later cleanup
    global.mongoServer = mongoServer;
};
// Clean up MongoDB memory server
export const teardownTestDB = async () => {
    // Disconnect from the database
    await mongoose.disconnect();
    // Stop the MongoDB memory server
    if (global.mongoServer) {
        await global.mongoServer.stop();
    }
};
// Clear all collections between tests
export const clearDatabase = async () => {
    const collections = mongoose.connection.collections;
    for (const key in collections) {
        const collection = collections[key];
        await collection.deleteMany({});
    }
};
// Mock the tenant access middleware
export const mockTenantAccessMiddleware = (req, res, next) => {
    req.tenantId = 'test-tenant-id';
    req.userId = 'test-user-id';
    next();
};
// Mock the rate limiter middleware
export const mockRateLimiterMiddleware = (req, res, next) => {
    next();
};
// Mock the validation middleware
export const mockValidationMiddleware = (schema) => (req, res, next) => {
    next();
};
// Mock the error handler middleware
export const mockErrorHandlerMiddleware = (err, req, res, next) => {
    res.status(err.statusCode || 500).json({
        message: err.message || 'Internal Server Error',
    });
};
// Mock the not found handler middleware
export const mockNotFoundHandlerMiddleware = (req, res) => {
    res.status(404).json({
        message: 'Not Found',
    });
};
// Mock the authentication middleware
export const mockAuthMiddleware = (req, res, next) => {
    req.user = {
        id: 'test-user-id',
        email: '<EMAIL>',
        role: 'admin',
    };
    next();
};
// Mock the authorization middleware
export const mockAuthorizationMiddleware = (roles) => (req, res, next) => {
    next();
};
// Mock the logging middleware
export const mockLoggingMiddleware = (req, res, next) => {
    next();
};
// Mock the request ID middleware
export const mockRequestIdMiddleware = (req, res, next) => {
    req.id = 'test-request-id';
    next();
};
// Mock the CORS middleware
export const mockCorsMiddleware = (req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    next();
};
// Mock the compression middleware
export const mockCompressionMiddleware = (req, res, next) => {
    next();
};
// Mock the body parser middleware
export const mockBodyParserMiddleware = (req, res, next) => {
    next();
};
// Mock the helmet middleware
export const mockHelmetMiddleware = (req, res, next) => {
    next();
};
// Mock the express-validator middleware
export const mockExpressValidatorMiddleware = (req, res, next) => {
    req.validationErrors = () => null;
    next();
};
// Mock the multer middleware
export const mockMulterMiddleware = (req, res, next) => {
    req.file = {
        fieldname: 'file',
        originalname: 'test.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        buffer: Buffer.from('test'),
        size: 4,
    };
    next();
};
// Mock the passport middleware
export const mockPassportMiddleware = (req, res, next) => {
    req.isAuthenticated = () => true;
    req.user = {
        id: 'test-user-id',
        email: '<EMAIL>',
        role: 'admin',
    };
    next();
};
// Mock the session middleware
export const mockSessionMiddleware = (req, res, next) => {
    req.session = {
        id: 'test-session-id',
        destroy: (callback) => callback(),
    };
    next();
};
// Mock the flash middleware
export const mockFlashMiddleware = (req, res, next) => {
    req.flash = (type, message) => { };
    next();
};
// Mock the cookie parser middleware
export const mockCookieParserMiddleware = (req, res, next) => {
    req.cookies = {
        token: 'test-token',
    };
    next();
};
// Mock the JWT middleware
export const mockJwtMiddleware = (req, res, next) => {
    req.user = {
        id: 'test-user-id',
        email: '<EMAIL>',
        role: 'admin',
    };
    next();
};
// Mock the GraphQL middleware
export const mockGraphQLMiddleware = (req, res, next) => {
    next();
};
// Mock the WebSocket middleware
export const mockWebSocketMiddleware = (req, res, next) => {
    next();
};
// Mock the Redis middleware
export const mockRedisMiddleware = (req, res, next) => {
    next();
};
// Mock the cache middleware
export const mockCacheMiddleware = (req, res, next) => {
    next();
};
// Mock the rate limiter middleware
export const mockRateLimiter = (req, res, next) => {
    next();
};
// Mock the validation middleware
export const mockValidation = (schema) => (req, res, next) => {
    next();
};
// Mock the error handler middleware
export const mockErrorHandler = (err, req, res, next) => {
    res.status(err.statusCode || 500).json({
        message: err.message || 'Internal Server Error',
    });
};
// Mock the not found handler middleware
export const mockNotFoundHandler = (req, res) => {
    res.status(404).json({
        message: 'Not Found',
    });
};
