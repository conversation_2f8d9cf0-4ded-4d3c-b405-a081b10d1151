import { Router } from 'express';
import { tenantAccessMiddleware } from '@aizako/core-lib';
import { validateRequest } from '../middleware/validation';
import { ProposalAnalyticsService } from '../../services/proposal-analytics-service';
import { ProposalService } from '../../services/proposal-service';
import { z } from 'zod';
import { ProposalViewEventSchema, ProposalDownloadEventSchema, ProposalAcceptanceEventSchema, ProposalRejectionEventSchema } from '../../types/proposals/analytics';
// Create router
const router = Router();
/**
 * @route GET /api/crm/proposals/:id/analytics
 * @desc Get analytics for a proposal
 * @access Private
 */
router.get('/:id/analytics', tenantAccessMiddleware, validateRequest(z.object({
    params: z.object({
        id: z.string(),
    }),
})), async (req, res) => {
    try {
        const { id } = req.params;
        const tenantId = req.tenantId;
        if (!tenantId) {
            return res.status(400).json({ message: 'Tenant ID is required' });
        }
        // Check if the proposal exists and belongs to the tenant
        const proposal = await ProposalService.getProposalById(id, tenantId);
        if (!proposal) {
            return res.status(404).json({ message: 'Proposal not found' });
        }
        // Get analytics
        const analytics = await ProposalAnalyticsService.getProposalAnalytics(id);
        return res.json(analytics);
    }
    catch (error) {
        console.error('Error getting proposal analytics:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route POST /api/crm/proposals/:id/analytics/view
 * @desc Record a proposal view
 * @access Public
 */
router.post('/:id/analytics/view', validateRequest(z.object({
    params: z.object({
        id: z.string(),
    }),
    body: ProposalViewEventSchema,
})), async (req, res) => {
    try {
        const { id } = req.params;
        const viewData = req.body;
        // Record view
        const proposal = await ProposalAnalyticsService.recordView(id, viewData);
        if (!proposal) {
            return res.status(404).json({ message: 'Proposal not found' });
        }
        return res.json({ message: 'View recorded successfully' });
    }
    catch (error) {
        console.error('Error recording proposal view:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route POST /api/crm/proposals/:id/analytics/download
 * @desc Record a proposal download
 * @access Public
 */
router.post('/:id/analytics/download', validateRequest(z.object({
    params: z.object({
        id: z.string(),
    }),
    body: ProposalDownloadEventSchema,
})), async (req, res) => {
    try {
        const { id } = req.params;
        const downloadData = req.body;
        // Record download
        const proposal = await ProposalAnalyticsService.recordDownload(id, downloadData);
        if (!proposal) {
            return res.status(404).json({ message: 'Proposal not found' });
        }
        return res.json({ message: 'Download recorded successfully' });
    }
    catch (error) {
        console.error('Error recording proposal download:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route POST /api/crm/proposals/:id/analytics/accept
 * @desc Record a proposal acceptance
 * @access Public
 */
router.post('/:id/analytics/accept', validateRequest(z.object({
    params: z.object({
        id: z.string(),
    }),
    body: ProposalAcceptanceEventSchema,
})), async (req, res) => {
    try {
        const { id } = req.params;
        const acceptanceData = req.body;
        // Record acceptance
        const proposal = await ProposalAnalyticsService.recordAcceptance(id, acceptanceData);
        if (!proposal) {
            return res.status(404).json({ message: 'Proposal not found' });
        }
        return res.json({ message: 'Acceptance recorded successfully' });
    }
    catch (error) {
        console.error('Error recording proposal acceptance:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route POST /api/crm/proposals/:id/analytics/reject
 * @desc Record a proposal rejection
 * @access Public
 */
router.post('/:id/analytics/reject', validateRequest(z.object({
    params: z.object({
        id: z.string(),
    }),
    body: ProposalRejectionEventSchema,
})), async (req, res) => {
    try {
        const { id } = req.params;
        const rejectionData = req.body;
        // Record rejection
        const proposal = await ProposalAnalyticsService.recordRejection(id, rejectionData);
        if (!proposal) {
            return res.status(404).json({ message: 'Proposal not found' });
        }
        return res.json({ message: 'Rejection recorded successfully' });
    }
    catch (error) {
        console.error('Error recording proposal rejection:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route GET /api/crm/proposals/analytics/tenant
 * @desc Get analytics for a tenant
 * @access Private
 */
router.get('/analytics/tenant', tenantAccessMiddleware, validateRequest(z.object({
    query: z.object({
        startDate: z.string().datetime().optional(),
        endDate: z.string().datetime().optional(),
    }),
})), async (req, res) => {
    try {
        const tenantId = req.tenantId;
        const { startDate, endDate } = req.query;
        if (!tenantId) {
            return res.status(400).json({ message: 'Tenant ID is required' });
        }
        // Set default date range if not provided
        const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
        const end = endDate ? new Date(endDate) : new Date();
        // Get analytics
        const analytics = await ProposalAnalyticsService.getTenantAnalytics(tenantId, start, end);
        return res.json(analytics);
    }
    catch (error) {
        console.error('Error getting tenant analytics:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
export default router;
