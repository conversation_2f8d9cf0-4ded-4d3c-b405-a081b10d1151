import { Router } from 'express';
import { tenantAccessMiddleware } from '@aizako/core-lib';
import { validateRequest } from '../middleware/validation';
import { VersionHistoryService } from '../../services/version-history-service';
import { ProposalService } from '../../services/proposal-service';
import { z } from 'zod';
const router = Router();
/**
 * @route GET /api/crm/proposals/:proposalId/versions
 * @desc Get versions for a proposal
 * @access Private
 */
router.get('/:proposalId/versions', tenantAccessMiddleware, validateRequest(z.object({
    params: z.object({
        proposalId: z.string(),
    }),
    query: z.object({
        limit: z.string().optional().transform(val => val ? parseInt(val, 10) : 10),
        skip: z.string().optional().transform(val => val ? parseInt(val, 10) : 0),
    }),
})), async (req, res) => {
    try {
        const { proposalId } = req.params;
        const { limit, skip } = req.query;
        const tenantId = req.tenantId;
        if (!tenantId) {
            return res.status(400).json({ message: 'Tenant ID is required' });
        }
        // Check if the proposal exists and belongs to the tenant
        const proposal = await ProposalService.getProposalById(proposalId, tenantId);
        if (!proposal) {
            return res.status(404).json({ message: 'Proposal not found' });
        }
        // Get versions
        const versions = await VersionHistoryService.getVersions(proposalId, tenantId, parseInt(limit, 10) || 10, parseInt(skip, 10) || 0);
        return res.json(versions);
    }
    catch (error) {
        console.error('Error getting proposal versions:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route GET /api/crm/proposals/:proposalId/versions/:versionNumber
 * @desc Get a specific version of a proposal
 * @access Private
 */
router.get('/:proposalId/versions/:versionNumber', tenantAccessMiddleware, validateRequest(z.object({
    params: z.object({
        proposalId: z.string(),
        versionNumber: z.string().transform(val => parseInt(val, 10)),
    }),
})), async (req, res) => {
    try {
        const { proposalId, versionNumber } = req.params;
        const tenantId = req.tenantId;
        if (!tenantId) {
            return res.status(400).json({ message: 'Tenant ID is required' });
        }
        // Check if the proposal exists and belongs to the tenant
        const proposal = await ProposalService.getProposalById(proposalId, tenantId);
        if (!proposal) {
            return res.status(404).json({ message: 'Proposal not found' });
        }
        // Get the version
        const version = await VersionHistoryService.getVersion(proposalId, parseInt(versionNumber, 10), tenantId);
        if (!version) {
            return res.status(404).json({ message: 'Version not found' });
        }
        return res.json(version);
    }
    catch (error) {
        console.error('Error getting proposal version:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route POST /api/crm/proposals/:proposalId/versions/:versionNumber/restore
 * @desc Restore a proposal to a specific version
 * @access Private
 */
router.post('/:proposalId/versions/:versionNumber/restore', tenantAccessMiddleware, validateRequest(z.object({
    params: z.object({
        proposalId: z.string(),
        versionNumber: z.string().transform(val => parseInt(val, 10)),
    }),
    body: z.object({
        comment: z.string().optional(),
    }),
})), async (req, res) => {
    try {
        const { proposalId, versionNumber } = req.params;
        const { comment } = req.body;
        const tenantId = req.tenantId;
        const userId = req.userId;
        if (!tenantId) {
            return res.status(400).json({ message: 'Tenant ID is required' });
        }
        if (!userId) {
            return res.status(400).json({ message: 'User ID is required' });
        }
        // Check if the proposal exists and belongs to the tenant
        const proposal = await ProposalService.getProposalById(proposalId, tenantId);
        if (!proposal) {
            return res.status(404).json({ message: 'Proposal not found' });
        }
        // Get the version
        const version = await VersionHistoryService.getVersion(proposalId, parseInt(versionNumber, 10), tenantId);
        if (!version) {
            return res.status(404).json({ message: 'Version not found' });
        }
        // Update the proposal with the version snapshot
        const restoredProposal = await ProposalService.updateProposal(proposalId, version.snapshot, tenantId, userId, `Restored from version ${versionNumber}${comment ? `: ${comment}` : ''}`);
        return res.json({
            message: `Proposal restored to version ${versionNumber}`,
            proposal: restoredProposal,
        });
    }
    catch (error) {
        console.error('Error restoring proposal version:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
export default router;
