import { Router } from 'express';
import { ContactService } from '../../services/contact-service';
import { tenantAccessMiddleware } from '@aizako/core-lib';
import { createPaginationResult } from '../../types/pagination';
const router = Router();
/**
 * @route GET /api/crm/contacts
 * @desc Get all contacts with pagination
 * @access Private
 */
router.get('/', tenantAccessMiddleware, async (req, res) => {
    try {
        const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc', ...filters } = req.query;
        const tenantId = req.tenantId;
        if (!tenantId) {
            return res.status(400).json({ message: 'Tenant ID is required' });
        }
        const { contacts, total } = await ContactService.getContacts({
            page: Number(page),
            limit: Number(limit),
            sortBy: sortBy,
            sortOrder: sortOrder,
            ...filters,
        }, tenantId);
        const result = createPaginationResult(contacts, total, Number(page), Number(limit));
        return res.json(result);
    }
    catch (error) {
        console.error('Error getting contacts:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route GET /api/crm/contacts/:id
 * @desc Get contact by ID
 * @access Private
 */
router.get('/:id', tenantAccessMiddleware, async (req, res) => {
    try {
        const { id } = req.params;
        const tenantId = req.tenantId;
        if (!tenantId) {
            return res.status(400).json({ message: 'Tenant ID is required' });
        }
        const contact = await ContactService.getContactById(id, tenantId);
        if (!contact) {
            return res.status(404).json({ message: 'Contact not found' });
        }
        return res.json(contact);
    }
    catch (error) {
        console.error('Error getting contact by ID:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route POST /api/crm/contacts
 * @desc Create a new contact
 * @access Private
 */
router.post('/', tenantAccessMiddleware, async (req, res) => {
    try {
        const contactData = req.body;
        const tenantId = req.tenantId;
        if (!tenantId) {
            return res.status(400).json({ message: 'Tenant ID is required' });
        }
        const contact = await ContactService.createContact(contactData, tenantId);
        return res.status(201).json(contact);
    }
    catch (error) {
        console.error('Error creating contact:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route PUT /api/crm/contacts/:id
 * @desc Update a contact
 * @access Private
 */
router.put('/:id', tenantAccessMiddleware, async (req, res) => {
    try {
        const { id } = req.params;
        const contactData = req.body;
        const tenantId = req.tenantId;
        if (!tenantId) {
            return res.status(400).json({ message: 'Tenant ID is required' });
        }
        const contact = await ContactService.updateContact(id, contactData, tenantId);
        if (!contact) {
            return res.status(404).json({ message: 'Contact not found' });
        }
        return res.json(contact);
    }
    catch (error) {
        console.error('Error updating contact:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route DELETE /api/crm/contacts/:id
 * @desc Delete a contact
 * @access Private
 */
router.delete('/:id', tenantAccessMiddleware, async (req, res) => {
    try {
        const { id } = req.params;
        const tenantId = req.tenantId;
        if (!tenantId) {
            return res.status(400).json({ message: 'Tenant ID is required' });
        }
        const deleted = await ContactService.deleteContact(id, tenantId);
        if (!deleted) {
            return res.status(404).json({ message: 'Contact not found' });
        }
        return res.json({ message: 'Contact deleted successfully' });
    }
    catch (error) {
        console.error('Error deleting contact:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route POST /api/crm/contacts/:id/interactions
 * @desc Add an interaction to a contact
 * @access Private
 */
router.post('/:id/interactions', tenantAccessMiddleware, async (req, res) => {
    try {
        const { id } = req.params;
        const interactionData = req.body;
        const tenantId = req.tenantId;
        if (!tenantId) {
            return res.status(400).json({ message: 'Tenant ID is required' });
        }
        const contact = await ContactService.addInteraction(id, interactionData, tenantId);
        if (!contact) {
            return res.status(404).json({ message: 'Contact not found' });
        }
        return res.json(contact);
    }
    catch (error) {
        console.error('Error adding interaction to contact:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route POST /api/crm/contacts/:id/score
 * @desc Update a contact's score
 * @access Private
 */
router.post('/:id/score', tenantAccessMiddleware, async (req, res) => {
    try {
        const { id } = req.params;
        const scoreData = req.body;
        const tenantId = req.tenantId;
        if (!tenantId) {
            return res.status(400).json({ message: 'Tenant ID is required' });
        }
        const contact = await ContactService.updateScore(id, scoreData, tenantId);
        if (!contact) {
            return res.status(404).json({ message: 'Contact not found' });
        }
        return res.json(contact);
    }
    catch (error) {
        console.error('Error updating contact score:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route POST /api/crm/contacts/:id/persona
 * @desc Update a contact's persona
 * @access Private
 */
router.post('/:id/persona', tenantAccessMiddleware, async (req, res) => {
    try {
        const { id } = req.params;
        const personaData = req.body;
        const tenantId = req.tenantId;
        if (!tenantId) {
            return res.status(400).json({ message: 'Tenant ID is required' });
        }
        const contact = await ContactService.updatePersona(id, personaData, tenantId);
        if (!contact) {
            return res.status(404).json({ message: 'Contact not found' });
        }
        return res.json(contact);
    }
    catch (error) {
        console.error('Error updating contact persona:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
export default router;
