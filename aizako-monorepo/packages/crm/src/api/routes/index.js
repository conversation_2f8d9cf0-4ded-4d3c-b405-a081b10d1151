import { Router } from 'express';
import contactsRouter from './contacts';
import companiesRouter from './companies';
import opportunitiesRouter from './opportunities';
import proposalsRouter from './proposals';
import proposalAiRouter from './proposal-ai';
const router = Router();
// Mount routes
router.use('/contacts', contactsRouter);
router.use('/companies', companiesRouter);
router.use('/opportunities', opportunitiesRouter);
router.use('/proposals', proposalsRouter);
router.use('/proposals/ai', proposalAiRouter);
export default router;
