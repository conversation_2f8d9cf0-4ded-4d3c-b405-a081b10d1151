import { Router } from 'express';
import { tenantAccessMiddleware } from '@aizako/core-lib';
import { validateRequest } from '../middleware/validation';
import { createTenantRateLimiter } from '../middleware/rate-limiter';
import { ProposalAIGenerationOptionsSchema } from '../../types/proposals/schemas';
import { BackgroundTaskService } from '../../services/background-task-service';
import { TASK_TYPES } from '../../services/ai-task-handlers';
import { z } from 'zod';
import proposalAdvancedAiRoutes from './proposal-advanced-ai';
// Get the background task service instance
const taskService = BackgroundTaskService.getInstance();
const router = Router();
// Use advanced AI routes
router.use('/advanced', proposalAdvancedAiRoutes);
// Create rate limiter for AI endpoints
const aiRateLimiter = createTenantRateLimiter({
    points: 10, // 10 requests per minute
    duration: 60,
    keyPrefix: 'proposal_ai',
});
/**
 * @route POST /api/crm/proposals/ai/generate
 * @desc Generate a complete proposal with AI
 * @access Private
 */
router.post('/generate', tenantAccessMiddleware, aiRateLimiter, validateRequest(ProposalAIGenerationOptionsSchema), async (req, res) => {
    try {
        const options = req.body;
        const tenantId = req.tenantId;
        const userId = req.userId;
        if (!tenantId) {
            return res.status(400).json({ message: 'Tenant ID is required' });
        }
        // Create a background task for generating the proposal
        const task = await taskService.createTask(TASK_TYPES.GENERATE_PROPOSAL, options, tenantId, userId);
        return res.status(202).json({
            message: 'Proposal generation started',
            taskId: task._id.toString(),
            status: task.status,
            estimatedTimeSeconds: 30, // Estimated time in seconds
        });
    }
    catch (error) {
        console.error('Error starting proposal generation task:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route POST /api/crm/proposals/ai/generate-section
 * @desc Generate a single proposal section with AI
 * @access Private
 */
router.post('/generate-section', tenantAccessMiddleware, aiRateLimiter, validateRequest(z.object({
    sectionType: z.string(),
    prompt: z.string(),
    model: z.string(),
    proposalId: z.string().optional(),
    opportunityId: z.string().optional(),
    companyId: z.string().optional(),
    contactIds: z.array(z.string()).optional(),
})), async (req, res) => {
    try {
        const { sectionType, prompt, model, opportunityId, companyId, contactIds, proposalId, } = req.body;
        const tenantId = req.tenantId;
        const userId = req.userId;
        if (!tenantId) {
            return res.status(400).json({ message: 'Tenant ID is required' });
        }
        // Create a background task for generating the section
        const task = await taskService.createTask(TASK_TYPES.GENERATE_PROPOSAL_SECTION, {
            sectionType,
            prompt,
            model,
            proposalId,
            context: {
                opportunityId,
                companyId,
                contactIds,
            },
        }, tenantId, userId);
        return res.status(202).json({
            message: 'Section generation started',
            taskId: task._id.toString(),
            status: task.status,
            estimatedTimeSeconds: 15, // Estimated time in seconds
        });
    }
    catch (error) {
        console.error('Error starting section generation task:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route GET /api/crm/proposals/ai/tasks/:taskId
 * @desc Get the status of a background task
 * @access Private
 */
router.get('/tasks/:taskId', tenantAccessMiddleware, async (req, res) => {
    try {
        const { taskId } = req.params;
        const tenantId = req.tenantId;
        if (!tenantId) {
            return res.status(400).json({ message: 'Tenant ID is required' });
        }
        // Get the task
        const task = await taskService.getTask(taskId);
        if (!task) {
            return res.status(404).json({ message: 'Task not found' });
        }
        // Check if the task belongs to the tenant
        if (task.tenantId !== tenantId) {
            return res.status(403).json({ message: 'Unauthorized' });
        }
        // Return the task status
        return res.json({
            taskId: task._id.toString(),
            status: task.status,
            progress: task.progress,
            result: task.result,
            error: task.error,
            createdAt: task.createdAt,
            startedAt: task.startedAt,
            completedAt: task.completedAt,
        });
    }
    catch (error) {
        console.error('Error getting task status:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route DELETE /api/crm/proposals/ai/tasks/:taskId
 * @desc Cancel a background task
 * @access Private
 */
router.delete('/tasks/:taskId', tenantAccessMiddleware, async (req, res) => {
    try {
        const { taskId } = req.params;
        const tenantId = req.tenantId;
        if (!tenantId) {
            return res.status(400).json({ message: 'Tenant ID is required' });
        }
        // Cancel the task
        const success = await taskService.cancelTask(taskId, tenantId);
        if (!success) {
            return res.status(400).json({ message: 'Task could not be cancelled' });
        }
        return res.json({ message: 'Task cancelled successfully' });
    }
    catch (error) {
        console.error('Error cancelling task:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
export default router;
