import { Router } from 'express';
import { tenantAccessMiddleware } from '@aizako/core-lib';
import { validateRequest } from '../middleware/validation';
import { createTenantRateLimiter } from '../middleware/rate-limiter';
import { AdvancedAIService } from '../../services/advanced-ai-service';
import { BackgroundTaskService } from '../../services/background-task-service';
import { z } from 'zod';
// Create router
const router = Router();
// Get the background task service instance
const taskService = BackgroundTaskService.getInstance();
// Define task types
const TASK_TYPES = {
    GENERATE_CONTEXT_AWARE_PROPOSAL: 'generate_context_aware_proposal',
    GENERATE_CONTEXT_AWARE_SECTION: 'generate_context_aware_section',
    GENERATE_COMPETITIVE_ANALYSIS: 'generate_competitive_analysis',
    GENERATE_PRICING_RECOMMENDATION: 'generate_pricing_recommendation',
};
// Register task handlers
taskService.registerTaskHandler(TASK_TYPES.GENERATE_CONTEXT_AWARE_PROPOSAL, async (task, updateProgress) => {
    await updateProgress(10);
    const result = await AdvancedAIService.generateContextAwareProposal(task.params, task.tenantId);
    await updateProgress(100);
    return {
        proposalId: result._id?.toString() || '',
        title: result.title,
    };
});
taskService.registerTaskHandler(TASK_TYPES.GENERATE_CONTEXT_AWARE_SECTION, async (task, updateProgress) => {
    await updateProgress(10);
    const result = await AdvancedAIService.generateContextAwareSection(task.params, task.tenantId);
    await updateProgress(100);
    return {
        section: result,
    };
});
taskService.registerTaskHandler(TASK_TYPES.GENERATE_COMPETITIVE_ANALYSIS, async (task, updateProgress) => {
    await updateProgress(10);
    const result = await AdvancedAIService.generateCompetitiveAnalysis(task.params, task.tenantId);
    await updateProgress(100);
    return {
        section: result,
    };
});
taskService.registerTaskHandler(TASK_TYPES.GENERATE_PRICING_RECOMMENDATION, async (task, updateProgress) => {
    await updateProgress(10);
    const result = await AdvancedAIService.generatePricingRecommendation(task.params, task.tenantId);
    await updateProgress(100);
    return result;
});
/**
 * @route POST /api/crm/proposals/advanced-ai/context-aware
 * @desc Generate a context-aware proposal
 * @access Private
 */
router.post('/context-aware', tenantAccessMiddleware, createTenantRateLimiter({
    duration: 60, // 1 minute
    points: 5, // 5 requests per minute
}), validateRequest(z.object({
    prompt: z.string(),
    model: z.string(),
    includeSections: z.record(z.boolean()),
    opportunityId: z.string().optional(),
    companyId: z.string().optional(),
    contactIds: z.array(z.string()).optional(),
    competitorAnalysis: z.boolean().optional(),
    marketResearch: z.boolean().optional(),
    industryTrends: z.boolean().optional(),
    previousProposals: z.boolean().optional(),
    similarDeals: z.boolean().optional(),
    customInstructions: z.string().optional(),
})), async (req, res) => {
    try {
        const options = req.body;
        const tenantId = req.tenantId;
        const userId = req.userId;
        if (!tenantId) {
            return res.status(400).json({ message: 'Tenant ID is required' });
        }
        // Create a background task
        const task = await taskService.createTask(TASK_TYPES.GENERATE_CONTEXT_AWARE_PROPOSAL, options, tenantId, userId);
        return res.status(202).json({
            message: 'Context-aware proposal generation started',
            taskId: task._id.toString(),
            status: task.status,
            estimatedTimeSeconds: 60, // Estimated time in seconds
        });
    }
    catch (error) {
        console.error('Error starting context-aware proposal generation:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route POST /api/crm/proposals/advanced-ai/context-aware-section
 * @desc Generate a context-aware section
 * @access Private
 */
router.post('/context-aware-section', tenantAccessMiddleware, createTenantRateLimiter({
    duration: 60, // 1 minute
    points: 10, // 10 requests per minute
}), validateRequest(z.object({
    sectionType: z.string(),
    prompt: z.string(),
    model: z.string(),
    proposalId: z.string().optional(),
    opportunityId: z.string().optional(),
    companyId: z.string().optional(),
    contactIds: z.array(z.string()).optional(),
    competitorAnalysis: z.boolean().optional(),
    marketResearch: z.boolean().optional(),
    industryTrends: z.boolean().optional(),
    previousProposals: z.boolean().optional(),
    similarDeals: z.boolean().optional(),
    customInstructions: z.string().optional(),
})), async (req, res) => {
    try {
        const options = req.body;
        const tenantId = req.tenantId;
        const userId = req.userId;
        if (!tenantId) {
            return res.status(400).json({ message: 'Tenant ID is required' });
        }
        // Create a background task
        const task = await taskService.createTask(TASK_TYPES.GENERATE_CONTEXT_AWARE_SECTION, options, tenantId, userId);
        return res.status(202).json({
            message: 'Context-aware section generation started',
            taskId: task._id.toString(),
            status: task.status,
            estimatedTimeSeconds: 30, // Estimated time in seconds
        });
    }
    catch (error) {
        console.error('Error starting context-aware section generation:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route POST /api/crm/proposals/advanced-ai/competitive-analysis
 * @desc Generate a competitive analysis section
 * @access Private
 */
router.post('/competitive-analysis', tenantAccessMiddleware, createTenantRateLimiter({
    duration: 60, // 1 minute
    points: 5, // 5 requests per minute
}), validateRequest(z.object({
    companyId: z.string().optional(),
    industry: z.string().optional(),
    competitors: z.array(z.string()).optional(),
    model: z.string(),
    customInstructions: z.string().optional(),
})), async (req, res) => {
    try {
        const options = req.body;
        const tenantId = req.tenantId;
        const userId = req.userId;
        if (!tenantId) {
            return res.status(400).json({ message: 'Tenant ID is required' });
        }
        // Create a background task
        const task = await taskService.createTask(TASK_TYPES.GENERATE_COMPETITIVE_ANALYSIS, options, tenantId, userId);
        return res.status(202).json({
            message: 'Competitive analysis generation started',
            taskId: task._id.toString(),
            status: task.status,
            estimatedTimeSeconds: 45, // Estimated time in seconds
        });
    }
    catch (error) {
        console.error('Error starting competitive analysis generation:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route POST /api/crm/proposals/advanced-ai/pricing-recommendation
 * @desc Generate a pricing recommendation
 * @access Private
 */
router.post('/pricing-recommendation', tenantAccessMiddleware, createTenantRateLimiter({
    duration: 60, // 1 minute
    points: 5, // 5 requests per minute
}), validateRequest(z.object({
    opportunityId: z.string().optional(),
    companyId: z.string().optional(),
    industry: z.string().optional(),
    projectScope: z.string().optional(),
    model: z.string(),
    customInstructions: z.string().optional(),
})), async (req, res) => {
    try {
        const options = req.body;
        const tenantId = req.tenantId;
        const userId = req.userId;
        if (!tenantId) {
            return res.status(400).json({ message: 'Tenant ID is required' });
        }
        // Create a background task
        const task = await taskService.createTask(TASK_TYPES.GENERATE_PRICING_RECOMMENDATION, options, tenantId, userId);
        return res.status(202).json({
            message: 'Pricing recommendation generation started',
            taskId: task._id.toString(),
            status: task.status,
            estimatedTimeSeconds: 30, // Estimated time in seconds
        });
    }
    catch (error) {
        console.error('Error starting pricing recommendation generation:', error);
        return res.status(500).json({ message: 'Server error' });
    }
});
export default router;
