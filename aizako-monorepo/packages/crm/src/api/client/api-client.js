import axios from 'axios';
/**
 * API error class
 */
export class ApiError extends Error {
    constructor(message, status, data) {
        super(message);
        Object.defineProperty(this, "status", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "data", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.name = 'ApiError';
        this.status = status;
        this.data = data;
    }
}
/**
 * API client class
 *
 * This class provides a wrapper around axios for making API requests.
 * It handles common tasks like setting headers, error handling, and response parsing.
 */
export class ApiClient {
    /**
     * Create a new API client
     * @param options API client options
     */
    constructor(options = {}) {
        Object.defineProperty(this, "client", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "tenantId", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: null
        });
        Object.defineProperty(this, "authToken", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: null
        });
        this.client = axios.create({
            baseURL: options.baseURL || '',
            timeout: options.timeout || 30000,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers,
            },
        });
        // Add request interceptor for auth and tenant headers
        this.client.interceptors.request.use((config) => {
            // Add tenant header if available
            if (this.tenantId) {
                config.headers['X-Tenant-ID'] = this.tenantId;
            }
            // Add auth header if available
            if (this.authToken) {
                config.headers['Authorization'] = `Bearer ${this.authToken}`;
            }
            return config;
        });
        // Add response interceptor for error handling
        this.client.interceptors.response.use((response) => response, (error) => {
            if (error.response) {
                // The request was made and the server responded with a status code
                // that falls out of the range of 2xx
                const status = error.response.status;
                const data = error.response.data;
                const message = data?.message || error.message;
                throw new ApiError(message, status, data);
            }
            else if (error.request) {
                // The request was made but no response was received
                throw new ApiError('No response received from server', 0);
            }
            else {
                // Something happened in setting up the request that triggered an Error
                throw new ApiError(error.message, 0);
            }
        });
    }
    /**
     * Set the tenant ID for all requests
     * @param tenantId Tenant ID
     */
    setTenantId(tenantId) {
        this.tenantId = tenantId;
    }
    /**
     * Set the auth token for all requests
     * @param token Auth token
     */
    setAuthToken(token) {
        this.authToken = token;
    }
    /**
     * Make a GET request
     * @param url URL to request
     * @param config Request config
     * @returns Response data
     */
    async get(url, config) {
        const response = await this.client.get(url, config);
        return response.data;
    }
    /**
     * Make a POST request
     * @param url URL to request
     * @param data Request data
     * @param config Request config
     * @returns Response data
     */
    async post(url, data, config) {
        const response = await this.client.post(url, data, config);
        return response.data;
    }
    /**
     * Make a PUT request
     * @param url URL to request
     * @param data Request data
     * @param config Request config
     * @returns Response data
     */
    async put(url, data, config) {
        const response = await this.client.put(url, data, config);
        return response.data;
    }
    /**
     * Make a PATCH request
     * @param url URL to request
     * @param data Request data
     * @param config Request config
     * @returns Response data
     */
    async patch(url, data, config) {
        const response = await this.client.patch(url, data, config);
        return response.data;
    }
    /**
     * Make a DELETE request
     * @param url URL to request
     * @param config Request config
     * @returns Response data
     */
    async delete(url, config) {
        const response = await this.client.delete(url, config);
        return response.data;
    }
    /**
     * Download a file
     * @param url URL to request
     * @param config Request config
     * @returns Blob data
     */
    async downloadFile(url, config) {
        const response = await this.client.get(url, {
            ...config,
            responseType: 'blob',
        });
        return response.data;
    }
}
/**
 * Create a new API client instance
 * @param options API client options
 * @returns API client instance
 */
export function createApiClient(options = {}) {
    return new ApiClient(options);
}
/**
 * Default API client instance
 */
export const apiClient = createApiClient();
