import { apiClient } from './api-client';
import { CRM_API_ROUTES } from '../../api';
/**
 * Proposals API client
 *
 * This module provides methods for interacting with the proposals API.
 */
export const proposalsApi = {
    /**
     * Get a list of proposals
     * @param params Query parameters
     * @returns Proposal list response
     */
    getProposals: async (params = {}) => {
        return apiClient.get(CRM_API_ROUTES.PROPOSALS, { params });
    },
    /**
     * Get a proposal by ID
     * @param id Proposal ID
     * @returns Proposal
     */
    getProposalById: async (id) => {
        return apiClient.get(`${CRM_API_ROUTES.PROPOSALS}/${id}`);
    },
    /**
     * Get a proposal by public token
     * @param token Public token
     * @returns Proposal
     */
    getProposalByToken: async (token) => {
        return apiClient.get(`${CRM_API_ROUTES.PROPOSALS}/public/${token}`);
    },
    /**
     * Create a new proposal
     * @param data Proposal data
     * @returns Created proposal
     */
    createProposal: async (data) => {
        return apiClient.post(CRM_API_ROUTES.PROPOSALS, data);
    },
    /**
     * Update a proposal
     * @param id Proposal ID
     * @param data Proposal data
     * @returns Updated proposal
     */
    updateProposal: async (id, data) => {
        return apiClient.put(`${CRM_API_ROUTES.PROPOSALS}/${id}`, data);
    },
    /**
     * Delete a proposal
     * @param id Proposal ID
     * @returns Success status
     */
    deleteProposal: async (id) => {
        return apiClient.delete(`${CRM_API_ROUTES.PROPOSALS}/${id}`);
    },
    /**
     * Send a proposal
     * @param id Proposal ID
     * @param options Send options
     * @returns Updated proposal
     */
    sendProposal: async (id, options) => {
        return apiClient.post(`${CRM_API_ROUTES.PROPOSALS}/${id}/send`, options);
    },
    /**
     * Accept a proposal
     * @param id Proposal ID
     * @returns Updated proposal
     */
    acceptProposal: async (id) => {
        return apiClient.post(`${CRM_API_ROUTES.PROPOSALS}/${id}/accept`, {});
    },
    /**
     * Reject a proposal
     * @param id Proposal ID
     * @param reason Rejection reason
     * @returns Updated proposal
     */
    rejectProposal: async (id, reason) => {
        return apiClient.post(`${CRM_API_ROUTES.PROPOSALS}/${id}/reject`, { reason });
    },
    /**
     * Record a view for a public proposal
     * @param token Public token
     * @param analyticsData Analytics data
     * @returns Success status
     */
    recordView: async (token, analyticsData = {}) => {
        return apiClient.post(`${CRM_API_ROUTES.PROPOSALS}/public/${token}/view`, analyticsData);
    },
    /**
     * Download a proposal
     * @param id Proposal ID
     * @param options Download options
     * @returns Blob data
     */
    downloadProposal: async (id, options) => {
        const { format, ...otherOptions } = options;
        return apiClient.downloadFile(`${CRM_API_ROUTES.PROPOSALS}/${id}/download?format=${format}`, {
            params: otherOptions,
        });
    },
    /**
     * Download a public proposal
     * @param token Public token
     * @param options Download options
     * @returns Blob data
     */
    downloadPublicProposal: async (token, options) => {
        const { format, ...otherOptions } = options;
        return apiClient.downloadFile(`${CRM_API_ROUTES.PROPOSALS}/public/${token}/download?format=${format}`, {
            params: otherOptions,
        });
    },
    /**
     * Generate a proposal with AI
     * @param options AI generation options
     * @returns Generated proposal data
     */
    generateProposal: async (options) => {
        return apiClient.post(`${CRM_API_ROUTES.PROPOSALS}/ai/generate`, options);
    },
    /**
     * Generate a proposal section with AI
     * @param sectionType Section type
     * @param prompt Prompt
     * @param model Model
     * @param context Additional context
     * @returns Generated section
     */
    generateProposalSection: async (sectionType, prompt, model, context = {}) => {
        return apiClient.post(`${CRM_API_ROUTES.PROPOSALS}/ai/generate-section`, {
            sectionType,
            prompt,
            model,
            ...context,
        });
    },
};
export default proposalsApi;
