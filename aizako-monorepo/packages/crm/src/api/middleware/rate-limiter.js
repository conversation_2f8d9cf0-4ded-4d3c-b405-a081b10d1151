import { RateLimiterMemory } from 'rate-limiter-flexible';
/**
 * Create a rate limiter middleware
 *
 * This middleware limits the number of requests a client can make in a given time period.
 * It can be used to prevent abuse and protect the API from DoS attacks.
 *
 * @param options Rate limiter options
 * @param keyGenerator Function to generate a key from the request
 * @returns Express middleware
 *
 * @example
 * ```typescript
 * // Limit to 100 requests per minute globally
 * app.use(createRateLimiter());
 *
 * // Limit to 10 requests per minute per IP
 * app.use(createRateLimiter({ points: 10 }, (req) => req.ip));
 *
 * // Limit to 5 requests per minute per tenant
 * app.use(createRateLimiter({ points: 5, keyPrefix: 'tenant' }, (req) => req.tenantId));
 * ```
 */
export const createRateLimiter = (options = {}, keyGenerator = () => 'global') => {
    const { points = 100, duration = 60, blockDuration = 0, keyPrefix = 'global', } = options;
    // Create a rate limiter instance
    const rateLimiter = new RateLimiterMemory({
        points,
        duration,
        blockDuration,
        keyPrefix,
    });
    // Return the middleware
    return async (req, res, next) => {
        try {
            // Generate a key for the request
            const key = keyGenerator(req);
            // Check if the request is allowed
            const rateLimiterRes = await rateLimiter.consume(key);
            // Set rate limit headers
            res.setHeader('X-RateLimit-Limit', points);
            res.setHeader('X-RateLimit-Remaining', rateLimiterRes.remainingPoints);
            res.setHeader('X-RateLimit-Reset', new Date(Date.now() + rateLimiterRes.msBeforeNext).toISOString());
            next();
        }
        catch (error) {
            // Rate limit exceeded
            if (error instanceof Error && 'remainingPoints' in error) {
                const rateLimiterRes = error;
                // Set rate limit headers
                res.setHeader('X-RateLimit-Limit', points);
                res.setHeader('X-RateLimit-Remaining', 0);
                res.setHeader('X-RateLimit-Reset', new Date(Date.now() + rateLimiterRes.msBeforeNext).toISOString());
                res.setHeader('Retry-After', Math.ceil(rateLimiterRes.msBeforeNext / 1000));
                // Send rate limit error response
                return res.status(429).json({
                    status: 'error',
                    message: 'Too many requests',
                    code: 'RATE_LIMIT_EXCEEDED',
                    retryAfter: Math.ceil(rateLimiterRes.msBeforeNext / 1000),
                });
            }
            // Pass other errors to the error handler
            next(error);
        }
    };
};
/**
 * Create a tenant-based rate limiter middleware
 *
 * This middleware limits the number of requests a tenant can make in a given time period.
 *
 * @param options Rate limiter options
 * @returns Express middleware
 *
 * @example
 * ```typescript
 * // Limit to 100 requests per minute per tenant
 * app.use(createTenantRateLimiter());
 *
 * // Limit to 10 requests per minute per tenant
 * app.use(createTenantRateLimiter({ points: 10 }));
 * ```
 */
export const createTenantRateLimiter = (options = {}) => {
    return createRateLimiter({ ...options, keyPrefix: 'tenant' }, (req) => req.tenantId || 'anonymous');
};
/**
 * Create an IP-based rate limiter middleware
 *
 * This middleware limits the number of requests an IP can make in a given time period.
 *
 * @param options Rate limiter options
 * @returns Express middleware
 *
 * @example
 * ```typescript
 * // Limit to 100 requests per minute per IP
 * app.use(createIpRateLimiter());
 *
 * // Limit to 10 requests per minute per IP
 * app.use(createIpRateLimiter({ points: 10 }));
 * ```
 */
export const createIpRateLimiter = (options = {}) => {
    return createRateLimiter({ ...options, keyPrefix: 'ip' }, (req) => req.ip || 'unknown');
};
export default createRateLimiter;
