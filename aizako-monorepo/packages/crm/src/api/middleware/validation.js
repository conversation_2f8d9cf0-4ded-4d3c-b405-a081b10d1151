import { ZodError } from 'zod';
/**
 * Validate request middleware
 *
 * This middleware validates the request data against a Zod schema.
 * It can validate data from the body, query, params, or headers.
 *
 * @param schema Zod schema to validate against
 * @param source Source of data to validate
 * @param options Validation options
 * @returns Express middleware
 *
 * @example
 * ```typescript
 * // Validate request body
 * router.post('/users', validateRequest(CreateUserSchema), (req, res) => {
 *   // req.body is now typed and validated
 * });
 *
 * // Validate request query
 * router.get('/users', validateRequest(GetUsersSchema, 'query'), (req, res) => {
 *   // req.query is now typed and validated
 * });
 * ```
 */
export const validateRequest = (schema, source = 'body', options = { stripUnknown: true }) => {
    return async (req, res, next) => {
        try {
            const data = req[source];
            // Parse and validate the data
            const parsed = await schema.parseAsync(data, {
                // Strip unknown properties if enabled
                ...(options.stripUnknown && {
                    unknownKeys: 'strip',
                    errorMap: (issue, ctx) => {
                        if (issue.code === 'unrecognized_keys') {
                            return { message: `Unrecognized key: ${issue.path.join('.')}` };
                        }
                        return { message: ctx.defaultError };
                    }
                })
            });
            // Replace the request data with the parsed data
            req[source] = parsed;
            next();
        }
        catch (error) {
            if (error instanceof ZodError) {
                // Format the validation errors
                const errors = error.errors.map((err) => ({
                    path: err.path.join('.'),
                    message: err.message,
                }));
                return res.status(400).json({
                    status: 'error',
                    message: 'Validation failed',
                    errors,
                });
            }
            // Pass other errors to the error handler
            next(error);
        }
    };
};
/**
 * Validate request with multiple schemas
 *
 * This middleware validates the request data against multiple Zod schemas.
 * It can validate data from the body, query, params, and headers.
 *
 * @param schemas Object mapping sources to schemas
 * @param options Validation options
 * @returns Express middleware
 *
 * @example
 * ```typescript
 * // Validate request body and query
 * router.post('/users', validateRequestMultiple({
 *   body: CreateUserSchema,
 *   query: PaginationSchema,
 * }), (req, res) => {
 *   // req.body and req.query are now typed and validated
 * });
 * ```
 */
export const validateRequestMultiple = (schemas, options = { stripUnknown: true }) => {
    return async (req, res, next) => {
        try {
            // Validate each source
            for (const [source, schema] of Object.entries(schemas)) {
                const data = req[source];
                // Parse and validate the data
                const parsed = await schema.parseAsync(data, {
                    // Strip unknown properties if enabled
                    ...(options.stripUnknown && {
                        unknownKeys: 'strip',
                        errorMap: (issue, ctx) => {
                            if (issue.code === 'unrecognized_keys') {
                                return { message: `Unrecognized key: ${issue.path.join('.')}` };
                            }
                            return { message: ctx.defaultError };
                        }
                    })
                });
                // Replace the request data with the parsed data
                req[source] = parsed;
            }
            next();
        }
        catch (error) {
            if (error instanceof ZodError) {
                // Format the validation errors
                const errors = error.errors.map((err) => ({
                    path: err.path.join('.'),
                    message: err.message,
                }));
                return res.status(400).json({
                    status: 'error',
                    message: 'Validation failed',
                    errors,
                });
            }
            // Pass other errors to the error handler
            next(error);
        }
    };
};
export default validateRequest;
