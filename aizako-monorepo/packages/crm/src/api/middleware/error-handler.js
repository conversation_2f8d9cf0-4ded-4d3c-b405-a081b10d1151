import { ZodError } from 'zod';
import { MongoError } from 'mongodb';
import mongoose from 'mongoose';
/**
 * Custom error class for API errors
 */
export class ApiError extends Error {
    constructor(message, statusCode, isOperational = true) {
        super(message);
        Object.defineProperty(this, "statusCode", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "isOperational", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        Error.captureStackTrace(this, this.constructor);
    }
}
/**
 * Not found error class
 */
export class NotFoundError extends ApiError {
    constructor(message = 'Resource not found') {
        super(message, 404);
    }
}
/**
 * Unauthorized error class
 */
export class UnauthorizedError extends ApiError {
    constructor(message = 'Unauthorized') {
        super(message, 401);
    }
}
/**
 * Forbidden error class
 */
export class ForbiddenError extends ApiError {
    constructor(message = 'Forbidden') {
        super(message, 403);
    }
}
/**
 * Bad request error class
 */
export class BadRequestError extends ApiError {
    constructor(message = 'Bad request') {
        super(message, 400);
    }
}
/**
 * Validation error class
 */
export class ValidationError extends ApiError {
    constructor(message = 'Validation failed', errors = []) {
        super(message, 400);
        Object.defineProperty(this, "errors", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.errors = errors;
    }
}
/**
 * Error handler middleware
 *
 * This middleware handles errors and sends appropriate responses.
 * It handles different types of errors and formats them consistently.
 *
 * @param err Error object
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
export const errorHandler = (err, req, res, next) => {
    // Log the error
    console.error('Error:', err);
    // Default error response
    let statusCode = 500;
    let message = 'Internal server error';
    let errors = [];
    let errorCode = 'INTERNAL_SERVER_ERROR';
    // Handle different error types
    if (err instanceof ApiError) {
        // Custom API error
        statusCode = err.statusCode;
        message = err.message;
        errorCode = err.constructor.name.replace('Error', '').toUpperCase();
        if (err instanceof ValidationError) {
            errors = err.errors;
            errorCode = 'VALIDATION_ERROR';
        }
    }
    else if (err instanceof ZodError) {
        // Zod validation error
        statusCode = 400;
        message = 'Validation failed';
        errorCode = 'VALIDATION_ERROR';
        errors = err.errors.map((e) => ({
            path: e.path.join('.'),
            message: e.message,
        }));
    }
    else if (err instanceof mongoose.Error.ValidationError) {
        // Mongoose validation error
        statusCode = 400;
        message = 'Validation failed';
        errorCode = 'VALIDATION_ERROR';
        errors = Object.values(err.errors).map((e) => ({
            path: e.path,
            message: e.message,
        }));
    }
    else if (err instanceof mongoose.Error.CastError) {
        // Mongoose cast error
        statusCode = 400;
        message = `Invalid ${err.path}: ${err.value}`;
        errorCode = 'INVALID_ID';
    }
    else if (err instanceof MongoError) {
        // MongoDB error
        statusCode = 500;
        message = 'Database error';
        errorCode = 'DATABASE_ERROR';
        // Handle duplicate key error
        if (err.code === 11000) {
            statusCode = 409;
            message = 'Duplicate key error';
            errorCode = 'DUPLICATE_KEY';
        }
    }
    else if (err.name === 'UnauthorizedError') {
        // JWT authentication error
        statusCode = 401;
        message = 'Unauthorized';
        errorCode = 'UNAUTHORIZED';
    }
    // Send the error response
    res.status(statusCode).json({
        status: 'error',
        message,
        code: errorCode,
        ...(errors.length > 0 && { errors }),
        ...(process.env.NODE_ENV === 'development' && { stack: err.stack }),
    });
};
/**
 * Not found middleware
 *
 * This middleware handles 404 errors for routes that don't exist.
 *
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
export const notFoundHandler = (req, res, next) => {
    const error = new NotFoundError(`Not found - ${req.originalUrl}`);
    next(error);
};
export default errorHandler;
