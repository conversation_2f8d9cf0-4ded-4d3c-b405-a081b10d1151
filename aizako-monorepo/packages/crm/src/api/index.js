import { Router } from 'express';
import { moduleMiddleware } from '@aizako/core-lib';
import routes from './routes/index';
/**
 * Create CRM API router
 * @returns Express router for CRM API
 */
export function createCRMApiRouter() {
    const router = Router();
    // Apply module middleware
    router.use(moduleMiddleware('crm'));
    // Mount routes
    router.use('/crm', routes);
    return router;
}
// Export routes
export { default as routes } from './routes/index';
// Export constants
export const CRM_API_BASE_PATH = '/api/crm';
// Export API routes
export const CRM_API_ROUTES = {
    CONTACTS: `${CRM_API_BASE_PATH}/contacts`,
    COMPANIES: `${CRM_API_BASE_PATH}/companies`,
    OPPORTUNITIES: `${CRM_API_BASE_PATH}/opportunities`,
    ACTIVITIES: `${CRM_API_BASE_PATH}/activities`,
    TASKS: `${CRM_API_BASE_PATH}/tasks`,
    DOCUMENTS: `${CRM_API_BASE_PATH}/documents`,
    PROPOSALS: `${CRM_API_BASE_PATH}/proposals`,
    EMAIL_TEMPLATES: `${CRM_API_BASE_PATH}/email-templates`,
    EMAIL_TRACKING: `${CRM_API_BASE_PATH}/email-tracking`,
    SEQUENCES: `${CRM_API_BASE_PATH}/sequences`,
    WORKFLOWS: `${CRM_API_BASE_PATH}/workflows`,
    TAGS: `${CRM_API_BASE_PATH}/tags`,
    TENANT_DOMAINS: `${CRM_API_BASE_PATH}/tenant-domains`,
};
