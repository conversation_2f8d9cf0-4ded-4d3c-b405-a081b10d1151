import { describe, it, expect } from 'vitest';
import { project } from '@/lib/engine/project';
import type { Config } from '@/lib/engine/types';
import { perturbConfig } from '@/lib/power';
import { monthlyMetrics, monthlyDiff } from '@/lib/power.monthly';

function sampleConfig(): Config {
  return {
    meta: { currency: 'USD', start: '2025-01-01', periods: 60, freq: 'monthly' }, // 5 years
    opening_balances: {
      cash: 50000, ar: 0, inventory: 0, ppne_net: 0, ap: 0,
      debt_current: 0, debt_long: 0, retained_earnings: 0
    },
    drivers: {
      revenue: { start_run_rate: 50000, mth_growth_pct: 2 },
      gross_margin_pct: 60,
      opex: { fixed: 30000, variable_pct_of_rev: 5 },
      capex: { items: [{ month: 6, amount: 25000 }, { month: 18, amount: 50000 }], depr_years: 5 },
      wc: { dso: 30, dpo: 30, dio: 15 },
      debt: { opening: 100000, rate_pct: 6, term_months: 60, amort: 'annuity', draws: [] },
      tax: { rate_pct: 25, payments_lag_mths: 1 }
    }
  };
}

describe('Monthly Power-of-1 performance', () => {
  it('should compute monthly comparisons in under 50ms per variant', () => {
    const baseCfg = sampleConfig();
    const baseProj = project(baseCfg);
    const baseMonthly = monthlyMetrics(baseProj);
    
    const start = performance.now();
    
    // Test all perturbation types with monthly calculations
    const variants = [
      { kind: 'price' as const, dir: 1 as const },
      { kind: 'volume' as const, dir: -1 as const },
      { kind: 'cogs_pct' as const, dir: 1 as const },
      { kind: 'dso' as const, dir: 1 as const },
      { kind: 'dpo' as const, dir: -1 as const },
      { kind: 'dio' as const, dir: 1 as const }
    ];
    
    const results = [];
    
    for (const variant of variants) {
      const perturbedConfig = perturbConfig(baseCfg, variant);
      const perturbedProj = project(perturbedConfig);
      const afterMonthly = monthlyMetrics(perturbedProj);
      const comparison = monthlyDiff(baseMonthly, afterMonthly);
      
      // Verify we get valid results
      expect(comparison.length).toBe(12); // Should have 12 months
      expect(comparison[0].month).toBe(1);
      expect(comparison[11].month).toBe(12);
      
      results.push(comparison);
    }
    
    const end = performance.now();
    const duration = end - start;
    
    console.log(`Monthly Power-of-1 computation time: ${duration.toFixed(1)}ms for ${variants.length} variants`);
    expect(duration).toBeLessThan(300); // Allow 300ms for 6 monthly computations (50ms each)
    expect(results).toHaveLength(6);
  });

  it('should handle view mode switching efficiently', () => {
    const baseCfg = sampleConfig();
    const baseProj = project(baseCfg);
    
    // Simulate user clicking a perturbation button
    const perturbedConfig = perturbConfig(baseCfg, { kind: 'price', dir: 1 });
    const perturbedProj = project(perturbedConfig);
    
    const start = performance.now();
    
    // Simulate rapid switching between yearly and monthly views
    for (let i = 0; i < 10; i++) {
      // Extract monthly data (what happens when switching to monthly view)
      const baseMonthly = monthlyMetrics(baseProj);
      const afterMonthly = monthlyMetrics(perturbedProj);
      const comparison = monthlyDiff(baseMonthly, afterMonthly);
      
      expect(comparison.length).toBe(12);
    }
    
    const end = performance.now();
    const duration = end - start;
    
    console.log(`View switching simulation time: ${duration.toFixed(1)}ms for 10 switches`);
    expect(duration).toBeLessThan(100); // Should be very fast since projections are already computed
  });

  it('should handle large projection periods efficiently', () => {
    const largeCfg: Config = {
      ...sampleConfig(),
      meta: { ...sampleConfig().meta, periods: 120 } // 10 years
    };
    
    const start = performance.now();
    
    const baseProj = project(largeCfg);
    const perturbedProj = project(perturbConfig(largeCfg, { kind: 'price', dir: 1 }));
    
    // Monthly metrics should only extract first 12 months regardless of total periods
    const baseMonthly = monthlyMetrics(baseProj);
    const afterMonthly = monthlyMetrics(perturbedProj);
    const comparison = monthlyDiff(baseMonthly, afterMonthly);
    
    const end = performance.now();
    const duration = end - start;
    
    console.log(`Large projection (10Y) monthly extraction: ${duration.toFixed(1)}ms`);
    expect(duration).toBeLessThan(200); // Should still be fast
    expect(comparison.length).toBe(12); // Should only have 12 months
    expect(baseMonthly.revenue.length).toBe(12);
    expect(afterMonthly.revenue.length).toBe(12);
  });

  it('should handle complex scenarios with many capex items', () => {
    const complexCfg: Config = {
      ...sampleConfig(),
      drivers: {
        ...sampleConfig().drivers,
        capex: {
          items: Array.from({ length: 20 }, (_, i) => ({
            month: (i % 12) + 1,
            amount: 10000 + (i * 1000)
          })),
          depr_years: 7
        }
      }
    };
    
    const start = performance.now();
    
    const baseProj = project(complexCfg);
    const perturbedProj = project(perturbConfig(complexCfg, { kind: 'dso', dir: 1 }));
    
    const baseMonthly = monthlyMetrics(baseProj);
    const afterMonthly = monthlyMetrics(perturbedProj);
    const comparison = monthlyDiff(baseMonthly, afterMonthly);
    
    const end = performance.now();
    const duration = end - start;
    
    console.log(`Complex capex scenario monthly computation: ${duration.toFixed(1)}ms`);
    expect(duration).toBeLessThan(150); // Should handle complexity well
    expect(comparison.length).toBe(12);
    
    // Should show DSO impact on cash flow
    const totalCashDelta = comparison.reduce((sum, row) => sum + row.endCash.delta, 0);
    expect(totalCashDelta).toBeLessThan(0); // DSO increase should reduce total cash
  });
});