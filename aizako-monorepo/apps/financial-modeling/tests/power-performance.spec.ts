import { describe, it, expect } from 'vitest';
import { project } from '@/lib/engine/project';
import type { Config } from '@/lib/engine/types';
import { perturbConfig, y1Metrics } from '@/lib/power';

function sampleConfig(): Config {
  return {
    meta: { currency: 'USD', start: '2025-01-01', periods: 60, freq: 'monthly' }, // 5 years
    opening_balances: {
      cash: 50000, ar: 0, inventory: 0, ppne_net: 0, ap: 0,
      debt_current: 0, debt_long: 0, retained_earnings: 0
    },
    drivers: {
      revenue: { start_run_rate: 50000, mth_growth_pct: 2 },
      gross_margin_pct: 60,
      opex: { fixed: 30000, variable_pct_of_rev: 5 },
      capex: { items: [{ month: 6, amount: 25000 }, { month: 18, amount: 50000 }], depr_years: 5 },
      wc: { dso: 30, dpo: 30, dio: 15 },
      debt: { opening: 100000, rate_pct: 6, term_months: 60, amort: 'annuity', draws: [] },
      tax: { rate_pct: 25, payments_lag_mths: 1 }
    }
  };
}

describe('Power-of-1 performance', () => {
  it('should compute perturbations in under 50ms', () => {
    const baseCfg = sampleConfig();
    
    const start = performance.now();
    
    // Test multiple perturbations rapidly
    const variants = [
      { kind: 'price' as const, dir: 1 as const },
      { kind: 'volume' as const, dir: -1 as const },
      { kind: 'cogs_pct' as const, dir: 1 as const },
      { kind: 'dso' as const, dir: 1 as const },
      { kind: 'dpo' as const, dir: -1 as const },
      { kind: 'dio' as const, dir: 1 as const }
    ];
    
    for (const variant of variants) {
      const perturbedConfig = perturbConfig(baseCfg, variant);
      const projection = project(perturbedConfig);
      const metrics = y1Metrics(projection);
      
      // Verify we get valid results
      expect(typeof metrics.revY1).toBe('number');
      expect(typeof metrics.niY1).toBe('number');
      expect(typeof metrics.cfoY1).toBe('number');
      expect(typeof metrics.endCashY1).toBe('number');
    }
    
    const end = performance.now();
    const duration = end - start;
    
    console.log(`Power-of-1 computation time: ${duration.toFixed(1)}ms for 6 variants`);
    expect(duration).toBeLessThan(300); // Allow 300ms for 6 computations (50ms each)
  });

  it('should handle complex scenarios efficiently', () => {
    const complexCfg: Config = {
      ...sampleConfig(),
      meta: { ...sampleConfig().meta, periods: 36 }, // 3 years
      drivers: {
        ...sampleConfig().drivers,
        capex: { 
          items: [
            { month: 1, amount: 10000 },
            { month: 6, amount: 25000 },
            { month: 12, amount: 15000 },
            { month: 18, amount: 30000 },
            { month: 24, amount: 20000 },
            { month: 30, amount: 40000 }
          ], 
          depr_years: 7 
        },
        debt: { 
          opening: 200000, 
          rate_pct: 7.5, 
          term_months: 84, 
          amort: 'annuity', 
          draws: [
            { month: 6, amount: 50000 },
            { month: 18, amount: 75000 }
          ]
        }
      }
    };
    
    const start = performance.now();
    
    // Test price perturbation on complex scenario
    const priceCfg = perturbConfig(complexCfg, { kind: 'price', dir: 1 });
    const projection = project(priceCfg);
    const metrics = y1Metrics(projection);
    
    const end = performance.now();
    const duration = end - start;
    
    console.log(`Complex scenario computation time: ${duration.toFixed(1)}ms`);
    expect(duration).toBeLessThan(50); // Should still be fast
    expect(metrics.revY1).toBeGreaterThan(0);
  });
});