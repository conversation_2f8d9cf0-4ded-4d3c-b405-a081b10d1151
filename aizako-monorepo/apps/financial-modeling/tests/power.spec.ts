import { describe, it, expect } from 'vitest';
import { project } from '@/lib/engine/project';
import type { Config } from '@/lib/engine/types';
import { perturbConfig, y1Metrics } from '@/lib/power';

function sampleConfig(): Config {
  return {
    meta: { currency: 'USD', start: '2025-01-01', periods: 12, freq: 'monthly' },
    opening_balances: {
      cash: 50000, ar: 0, inventory: 0, ppne_net: 0, ap: 0,
      debt_current: 0, debt_long: 0, retained_earnings: 0
    },
    drivers: {
      revenue: { start_run_rate: 50000, mth_growth_pct: 0 },
      gross_margin_pct: 60,
      opex: { fixed: 30000, variable_pct_of_rev: 5 },
      capex: { items: [], depr_years: 5 },
      wc: { dso: 30, dpo: 30, dio: 0 },
      debt: { opening: 0, rate_pct: 0, term_months: 0, amort: 'interest_only', draws: [] },
      tax: { rate_pct: 25, payments_lag_mths: 1 }
    }
  };
}

describe('Power-of-1 perturbations', () => {
  it('price +1% should raise Y1 Revenue/NI/CFO/EndCash', () => {
    const baseCfg = sampleConfig();
    const baseProj = project(baseCfg);
    const baseM = y1Metrics(baseProj);

    const pCfg = perturbConfig(baseCfg, { kind: 'price', dir: 1 });
    const pProj = project(pCfg);
    const after = y1Metrics(pProj);

    // Directional assertions
    expect(after.revY1).toBeGreaterThan(baseM.revY1);
    expect(after.niY1).toBeGreaterThan(baseM.niY1);
    expect(after.cfoY1).toBeGreaterThan(baseM.cfoY1);
    expect(after.endCashY1).toBeGreaterThan(baseM.endCashY1);

    // Verify projections are valid (have data)
    expect(baseProj.pnl.length).toBeGreaterThan(0);
    expect(pProj.pnl.length).toBeGreaterThan(0);
  });

  it('volume -1% should reduce Revenue/NI/CFO/EndCash', () => {
    const baseCfg = sampleConfig();
    const baseM = y1Metrics(project(baseCfg));

    const vCfg = perturbConfig(baseCfg, { kind: 'volume', dir: -1 });
    const after = y1Metrics(project(vCfg));

    expect(after.revY1).toBeLessThan(baseM.revY1);
    expect(after.niY1).toBeLessThan(baseM.niY1);
    expect(after.cfoY1).toBeLessThan(baseM.cfoY1);
    expect(after.endCashY1).toBeLessThan(baseM.endCashY1);
  });

  it('COGS% +1% (i.e., margin down) should reduce NI/CFO/EndCash', () => {
    const baseCfg = sampleConfig();
    const baseM = y1Metrics(project(baseCfg));

    const cCfg = perturbConfig(baseCfg, { kind: 'cogs_pct', dir: 1 });
    const after = y1Metrics(project(cCfg));

    expect(after.niY1).toBeLessThan(baseM.niY1);
    expect(after.cfoY1).toBeLessThan(baseM.cfoY1);
    expect(after.endCashY1).toBeLessThan(baseM.endCashY1);
    // Revenue (Y1) should be unchanged by margin tweak
    expect(after.revY1).toBeCloseTo(baseM.revY1, 6);
  });

  it('DSO +1 day should reduce CFO/EndCash; DPO +1 day should increase CFO/EndCash', () => {
    const baseCfg = sampleConfig();
    const baseM = y1Metrics(project(baseCfg));

    const dsoCfg = perturbConfig(baseCfg, { kind: 'dso', dir: 1 });
    const dsoM = y1Metrics(project(dsoCfg));
    expect(dsoM.cfoY1).toBeLessThan(baseM.cfoY1);
    expect(dsoM.endCashY1).toBeLessThan(baseM.endCashY1);

    const dpoCfg = perturbConfig(baseCfg, { kind: 'dpo', dir: 1 });
    const dpoM = y1Metrics(project(dpoCfg));
    expect(dpoM.cfoY1).toBeGreaterThan(baseM.cfoY1);
    expect(dpoM.endCashY1).toBeGreaterThan(baseM.endCashY1);
  });

  it('should handle edge cases safely', () => {
    const baseCfg = sampleConfig();
    
    // Test with zero gross margin
    const zeroMarginCfg = { ...baseCfg, drivers: { ...baseCfg.drivers, gross_margin_pct: 0 } };
    const cogsCfg = perturbConfig(zeroMarginCfg, { kind: 'cogs_pct', dir: 1 });
    expect(cogsCfg.drivers.gross_margin_pct).toBeLessThanOrEqual(0); // Should go slightly negative
    
    // Test DIO perturbation (should work even if DIO is 0)
    const dioCfg = perturbConfig(baseCfg, { kind: 'dio', dir: 1 });
    expect(dioCfg.drivers.wc.dio).toBe(1); // Should be 0 + 1 = 1
    
    // Test negative DSO is clamped to 0
    const negativeDSOCfg = { ...baseCfg, drivers: { ...baseCfg.drivers, wc: { ...baseCfg.drivers.wc, dso: 0 } } };
    const clampedDSOCfg = perturbConfig(negativeDSOCfg, { kind: 'dso', dir: -1 });
    expect(clampedDSOCfg.drivers.wc.dso).toBe(0); // Should be clamped at 0
  });

  it('should preserve config structure after perturbation', () => {
    const baseCfg = sampleConfig();
    const priceCfg = perturbConfig(baseCfg, { kind: 'price', dir: 1 });
    
    // Structure should be preserved
    expect(priceCfg).toHaveProperty('meta');
    expect(priceCfg).toHaveProperty('opening_balances');
    expect(priceCfg).toHaveProperty('drivers');
    expect(priceCfg.meta).toEqual(baseCfg.meta);
    expect(priceCfg.opening_balances).toEqual(baseCfg.opening_balances);
    
    // Only the perturbed field should change
    expect(priceCfg.drivers.revenue.start_run_rate).not.toBe(baseCfg.drivers.revenue.start_run_rate);
    expect(priceCfg.drivers.gross_margin_pct).toBe(baseCfg.drivers.gross_margin_pct);
  });

  it('should handle working capital bounds correctly', () => {
    const baseCfg = sampleConfig();
    
    // Test maximum DSO (365 days)
    const maxDSOCfg = { ...baseCfg, drivers: { ...baseCfg.drivers, wc: { ...baseCfg.drivers.wc, dso: 365 } } };
    const clampedMaxCfg = perturbConfig(maxDSOCfg, { kind: 'dso', dir: 1 });
    expect(clampedMaxCfg.drivers.wc.dso).toBe(365); // Should be clamped at 365
    
    // Test minimum DPO (0 days)
    const minDPOCfg = { ...baseCfg, drivers: { ...baseCfg.drivers, wc: { ...baseCfg.drivers.wc, dpo: 0 } } };
    const clampedMinCfg = perturbConfig(minDPOCfg, { kind: 'dpo', dir: -1 });
    expect(clampedMinCfg.drivers.wc.dpo).toBe(0); // Should be clamped at 0
  });

  it('should compute Y1 metrics correctly', () => {
    const baseCfg = sampleConfig();
    const proj = project(baseCfg);
    const metrics = y1Metrics(proj);
    
    expect(typeof metrics.revY1).toBe('number');
    expect(typeof metrics.niY1).toBe('number');
    expect(typeof metrics.cfoY1).toBe('number');
    expect(typeof metrics.endCashY1).toBe('number');
    
    // Basic sanity checks
    expect(metrics.revY1).toBeGreaterThan(0); // Should have positive revenue
    expect(typeof metrics.endCashY1).toBe('number'); // Should be a valid number (may be negative if burning cash)
  });
});