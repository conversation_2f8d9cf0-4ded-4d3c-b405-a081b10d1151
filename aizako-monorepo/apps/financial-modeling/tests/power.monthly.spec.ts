import { describe, it, expect } from 'vitest';
import { project } from '@/lib/engine/project';
import type { Config } from '@/lib/engine/types';
import { perturbConfig, y1Metrics } from '@/lib/power';
import { 
  monthlyMetrics, 
  monthlyDiff, 
  validateMonthlyMetrics,
  fmtMonthlyNumber,
  formatMonthlyDelta,
  getMonthlyMetricInfo 
} from '@/lib/power.monthly';

function sampleConfig(): Config {
  return {
    meta: { currency: 'USD', start: '2025-01-01', periods: 12, freq: 'monthly' },
    opening_balances: {
      cash: 50000, ar: 0, inventory: 0, ppne_net: 0, ap: 0,
      debt_current: 0, debt_long: 0, retained_earnings: 0
    },
    drivers: {
      revenue: { start_run_rate: 50000, mth_growth_pct: 2 },
      gross_margin_pct: 60,
      opex: { fixed: 30000, variable_pct_of_rev: 5 },
      capex: { items: [], depr_years: 5 },
      wc: { dso: 30, dpo: 30, dio: 0 },
      debt: { opening: 0, rate_pct: 0, term_months: 0, amort: 'interest_only', draws: [] },
      tax: { rate_pct: 25, payments_lag_mths: 1 }
    }
  };
}

describe('Monthly metrics extraction', () => {
  it('should extract monthly metrics from projection result', () => {
    const config = sampleConfig();
    const projection = project(config);
    const metrics = monthlyMetrics(projection);

    // Should have 12 months of data
    expect(metrics.revenue.length).toBe(12);
    expect(metrics.netInc.length).toBe(12);
    expect(metrics.cfo.length).toBe(12);
    expect(metrics.endCash.length).toBe(12);

    // All arrays should be numbers
    metrics.revenue.forEach(val => expect(typeof val).toBe('number'));
    metrics.netInc.forEach(val => expect(typeof val).toBe('number'));
    metrics.cfo.forEach(val => expect(typeof val).toBe('number'));
    metrics.endCash.forEach(val => expect(typeof val).toBe('number'));

    // Revenue should be positive (growing business)
    expect(metrics.revenue[0]).toBeGreaterThan(0);
    expect(metrics.revenue[11]).toBeGreaterThan(metrics.revenue[0]); // Growth
  });

  it('should handle projections with less than 12 months', () => {
    const config = { ...sampleConfig(), meta: { ...sampleConfig().meta, periods: 6 } };
    const projection = project(config);
    const metrics = monthlyMetrics(projection);

    // Should have 6 months of data
    expect(metrics.revenue.length).toBe(6);
    expect(metrics.netInc.length).toBe(6);
    expect(metrics.cfo.length).toBe(6);
    expect(metrics.endCash.length).toBe(6);
  });

  it('should validate monthly metrics consistency', () => {
    const config = sampleConfig();
    const projection = project(config);
    const metrics = monthlyMetrics(projection);

    expect(validateMonthlyMetrics(metrics)).toBe(true);

    // Test invalid metrics (different lengths)
    const invalidMetrics = {
      revenue: [1, 2, 3],
      netInc: [1, 2],
      cfo: [1, 2, 3],
      endCash: [1, 2, 3]
    };
    expect(validateMonthlyMetrics(invalidMetrics)).toBe(false);
  });
});

describe('Monthly comparison calculations', () => {
  it('should calculate monthly differences correctly', () => {
    const config = sampleConfig();
    const baseProj = project(config);
    const baseMetrics = monthlyMetrics(baseProj);

    // Apply price perturbation
    const perturbedConfig = perturbConfig(config, { kind: 'price', dir: 1 });
    const perturbedProj = project(perturbedConfig);
    const afterMetrics = monthlyMetrics(perturbedProj);

    const comparison = monthlyDiff(baseMetrics, afterMetrics);

    // Should have 12 comparison rows
    expect(comparison.length).toBe(12);

    // Each row should have proper structure
    comparison.forEach((row, index) => {
      expect(row.month).toBe(index + 1);
      expect(typeof row.revenue.base).toBe('number');
      expect(typeof row.revenue.after).toBe('number');
      expect(typeof row.revenue.delta).toBe('number');
      expect(row.revenue.delta).toBe(row.revenue.after - row.revenue.base);
      
      // Price increase should increase revenue
      expect(row.revenue.delta).toBeGreaterThan(0);
    });
  });

  it('should handle DSO perturbation effects on cash flow', () => {
    const config = sampleConfig();
    const baseProj = project(config);
    const baseMetrics = monthlyMetrics(baseProj);

    // Apply DSO +1 day perturbation (should reduce cash flow)
    const dsoPerturbedConfig = perturbConfig(config, { kind: 'dso', dir: 1 });
    const dsoProj = project(dsoPerturbedConfig);
    const dsoMetrics = monthlyMetrics(dsoProj);

    const dsoComparison = monthlyDiff(baseMetrics, dsoMetrics);

    // DSO increase should generally reduce CFO and end cash
    const totalCFODelta = dsoComparison.reduce((sum, row) => sum + row.cfo.delta, 0);
    const endCashDelta = dsoComparison[dsoComparison.length - 1].endCash.delta;

    expect(totalCFODelta).toBeLessThan(0); // Reduced cash flow
    expect(endCashDelta).toBeLessThan(0); // Reduced ending cash
  });

  it('should handle DPO perturbation effects on cash flow', () => {
    const config = sampleConfig();
    const baseProj = project(config);
    const baseMetrics = monthlyMetrics(baseProj);

    // Apply DPO +1 day perturbation (should increase cash flow)
    const dpoPerturbedConfig = perturbConfig(config, { kind: 'dpo', dir: 1 });
    const dpoProj = project(dpoPerturbedConfig);
    const dpoMetrics = monthlyMetrics(dpoProj);

    const dpoComparison = monthlyDiff(baseMetrics, dpoMetrics);

    // DPO increase should generally improve CFO and end cash
    const totalCFODelta = dpoComparison.reduce((sum, row) => sum + row.cfo.delta, 0);
    const endCashDelta = dpoComparison[dpoComparison.length - 1].endCash.delta;

    expect(totalCFODelta).toBeGreaterThan(0); // Improved cash flow
    expect(endCashDelta).toBeGreaterThan(0); // Improved ending cash
  });
});

describe('Monthly formatting utilities', () => {
  it('should format monthly numbers correctly', () => {
    expect(fmtMonthlyNumber(1234)).toBe('1,234');
    expect(fmtMonthlyNumber(1234567)).toBe('1,234,567');
    expect(fmtMonthlyNumber(0)).toBe('0');
    expect(fmtMonthlyNumber(-1234)).toBe('-1,234');
    expect(fmtMonthlyNumber(Infinity)).toBe('—');
    expect(fmtMonthlyNumber(NaN)).toBe('—');
  });

  it('should format monthly deltas with correct signs and colors', () => {
    const positiveDelta = formatMonthlyDelta(1500);
    expect(positiveDelta.formatted).toBe('+1,500');
    expect(positiveDelta.isPositive).toBe(true);
    expect(positiveDelta.isZero).toBe(false);

    const negativeDelta = formatMonthlyDelta(-2500);
    expect(negativeDelta.formatted).toBe('-2,500');
    expect(negativeDelta.isPositive).toBe(false);
    expect(negativeDelta.isZero).toBe(false);

    const zeroDelta = formatMonthlyDelta(0);
    expect(zeroDelta.formatted).toBe('0');
    expect(zeroDelta.isZero).toBe(true);

    // Near-zero should be treated as zero
    const nearZeroDelta = formatMonthlyDelta(0.001);
    expect(nearZeroDelta.isZero).toBe(true);
  });

  it('should provide metric information for tooltips', () => {
    const revenueInfo = getMonthlyMetricInfo('revenue');
    expect(revenueInfo.label).toBe('Revenue');
    expect(revenueInfo.shortLabel).toBe('Rev');
    expect(revenueInfo.tooltip).toContain('Monthly revenue');

    const cashInfo = getMonthlyMetricInfo('endCash');
    expect(cashInfo.label).toBe('End Cash');
    expect(cashInfo.shortLabel).toBe('Cash');
    expect(cashInfo.tooltip).toContain('Cash balance at end of month');
  });
});

describe('Monthly metrics edge cases', () => {
  it('should handle zero revenue scenarios', () => {
    const config = { ...sampleConfig() };
    config.drivers.revenue.start_run_rate = 0;
    
    const projection = project(config);
    const metrics = monthlyMetrics(projection);

    expect(metrics.revenue.every(val => val === 0)).toBe(true);
    expect(validateMonthlyMetrics(metrics)).toBe(true);
  });

  it('should handle high growth scenarios', () => {
    const config = { ...sampleConfig() };
    config.drivers.revenue.mth_growth_pct = 10; // 10% monthly growth
    
    const projection = project(config);
    const metrics = monthlyMetrics(projection);

    // Revenue should show compounding growth
    expect(metrics.revenue[11]).toBeGreaterThan(metrics.revenue[0] * 2); // Should more than double
    expect(validateMonthlyMetrics(metrics)).toBe(true);
  });

  it('should handle scenarios with seasonal effects via capex', () => {
    const config = { ...sampleConfig() };
    config.drivers.capex.items = [
      { month: 6, amount: 50000 }, // Big capex in month 6
      { month: 12, amount: 25000 } // Smaller capex in month 12
    ];
    
    const projection = project(config);
    const metrics = monthlyMetrics(projection);

    // Should have valid monthly metrics structure
    expect(validateMonthlyMetrics(metrics)).toBe(true);
    
    // Cash flow should show impact from capex (may vary based on timing and working capital effects)
    expect(metrics.endCash[11]).toBeLessThan(metrics.endCash[0] + 500000); // Shouldn't grow unreasonably
  });

  it('should maintain consistency between monthly and yearly totals', () => {
    const config = sampleConfig();
    const projection = project(config);
    const monthly = monthlyMetrics(projection);

    // Sum of monthly revenue should match Y1 calculation
    const monthlyRevenueSum = monthly.revenue.reduce((sum, val) => sum + val, 0);
    const monthlyNetIncSum = monthly.netInc.reduce((sum, val) => sum + val, 0);
    const monthlyCFOSum = monthly.cfo.reduce((sum, val) => sum + val, 0);

    // Compare with yearly totals
    const yearly = y1Metrics(projection);

    expect(monthlyRevenueSum).toBeCloseTo(yearly.revY1, 2);
    expect(monthlyNetIncSum).toBeCloseTo(yearly.niY1, 2);
    expect(monthlyCFOSum).toBeCloseTo(yearly.cfoY1, 2);
    expect(monthly.endCash[11]).toBeCloseTo(yearly.endCashY1, 2);
  });
});