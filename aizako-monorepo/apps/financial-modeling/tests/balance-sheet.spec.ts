import { describe, it, expect } from 'vitest';
import { project } from '../src/lib/engine/project';
import type { Config } from '../src/lib/engine/types';

// Helper function for testing balance calculations
interface PartialBalances {
  cash?: number;
  ar?: number;
  inventory?: number;
  ppne_net?: number;
  ap?: number;
  debt_current?: number;
  debt_long?: number;
  retained_earnings?: number;
}

function calculateBalancedOpenings(partialBalances: PartialBalances = {}) {
  const defaults = {
    cash: 50000,
    ar: 0,
    inventory: 0,
    ppne_net: 0,
    ap: 0,
    debt_current: 0,
    debt_long: 0,
    retained_earnings: undefined,
  };

  const balances = { ...defaults, ...partialBalances };

  if (balances.retained_earnings === undefined) {
    const total_assets = balances.cash + balances.ar + balances.inventory + balances.ppne_net;
    const total_liabilities = balances.ap + balances.debt_current + balances.debt_long;
    balances.retained_earnings = total_assets - total_liabilities;
  }

  return balances;
}

function createTestConfig(opening_balances: PartialBalances = {}): Config {
  return {
    meta: { currency: 'USD', start: '2025-01-01', periods: 12, freq: 'monthly' },
    opening_balances,
    drivers: {
      revenue: { start_run_rate: 50000, mth_growth_pct: 0 },
      gross_margin_pct: 60,
      opex: { fixed: 30000, variable_pct_of_rev: 5 },
      capex: { items: [], depr_years: 5 },
      wc: { dso: 30, dpo: 30, dio: 0 },
      debt: { opening: 0, rate_pct: 0, term_months: 0, amort: 'interest_only', draws: [] },
      tax: { rate_pct: 25, payments_lag_mths: 1 }
    }
  };
}

describe('Balance Sheet Identity Validation', () => {
  it('should work with balanced opening balances', () => {
    // Test with balanced balance sheet: Assets = Liabilities + Equity
    const config = createTestConfig({
      cash: 50000, ar: 0, inventory: 0, ppne_net: 0, ap: 0,
      debt_current: 0, debt_long: 0, retained_earnings: 50000  // Balances the 50k cash
    });
    const result = project(config);

    console.log('Balanced test:');
    console.log('Max residual:', result.checks.max_residual);
    console.log('Passes validation:', result.checks.passes_validation);

    // This should pass with properly balanced opening balances
    expect(result.checks.passes_validation).toBe(true);
    expect(result.checks.max_residual).toBeLessThan(0.01);
  });

  it('should handle auto-balancing when retained_earnings omitted', () => {
    // Test with exactly what the working power test uses, but with retained_earnings removed
    const config = createTestConfig({
      cash: 50000, ar: 0, inventory: 0, ppne_net: 0, ap: 0,
      debt_current: 0, debt_long: 0
      // retained_earnings intentionally omitted - should be auto-calculated
    });
    const result = project(config);

    console.log('Auto-balance test:');
    console.log('Max residual:', result.checks.max_residual);
    console.log('First BS retained earnings:', result.bs[0].retained_earnings);

    // Should pass validation with auto-calculated balances
    expect(result.checks.passes_validation).toBe(true);
    expect(result.checks.max_residual).toBeLessThan(0.01);
  });

  it('should handle partial opening balances (cash only)', () => {
    const config = createTestConfig({ cash: 100000 });
    const result = project(config);

    expect(result.checks.passes_validation).toBe(true);
    expect(result.checks.max_residual).toBeLessThan(0.01);

    // First period should have balanced balance sheet
    const firstBS = result.bs[0];
    const residual = Math.abs(firstBS.total_assets - firstBS.total_liab_equity);
    expect(residual).toBeLessThan(0.01);
  });

  it('should handle manually balanced opening balances', () => {
    const openings = {
      cash: 50000,
      ar: 10000,
      inventory: 5000,
      ppne_net: 0,
      ap: 8000,
      debt_current: 0,
      debt_long: 0,
      retained_earnings: 57000, // 65000 assets - 8000 liabilities
    };

    const config = createTestConfig(openings);
    const result = project(config);

    expect(result.checks.passes_validation).toBe(true);
    expect(result.checks.max_residual).toBeLessThan(0.01);
  });

  it('should handle company with existing debt', () => {
    const openings = {
      cash: 30000,
      ar: 15000,
      inventory: 0,
      ppne_net: 100000,
      ap: 5000,
      debt_current: 0,
      debt_long: 75000,
      retained_earnings: 65000, // 145000 assets - 80000 liabilities
    };

    const config = createTestConfig(openings);
    config.drivers.debt.opening = 75000; // Match debt_long

    const result = project(config);

    expect(result.checks.passes_validation).toBe(true);
    expect(result.checks.max_residual).toBeLessThan(0.01);
  });

  it('should handle negative retained earnings (startup burning cash)', () => {
    const openings = {
      cash: 20000,
      ar: 5000,
      inventory: 0,
      ppne_net: 0,
      ap: 10000,
      debt_current: 0,
      debt_long: 50000,
      retained_earnings: -35000, // 25000 assets - 60000 liabilities
    };

    const config = createTestConfig(openings);
    const result = project(config);

    expect(result.checks.passes_validation).toBe(true);
    expect(result.checks.max_residual).toBeLessThan(0.01);
  });
});

describe('Balance Calculation Helper', () => {
  it('should calculate retained earnings for new company', () => {
    const balanced = calculateBalancedOpenings({ cash: 50000 });

    expect(balanced.cash).toBe(50000);
    expect(balanced.retained_earnings).toBe(50000);
    expect(balanced.ar).toBe(0);
    expect(balanced.debt_long).toBe(0);
  });

  it('should preserve user-provided retained earnings', () => {
    const balanced = calculateBalancedOpenings({
      cash: 30000,
      retained_earnings: 25000,
      ap: 5000
    });

    expect(balanced.cash).toBe(30000);
    expect(balanced.retained_earnings).toBe(25000); // User-provided, not calculated
    expect(balanced.ap).toBe(5000);
  });

  it('should handle complex balance sheet scenarios', () => {
    const balanced = calculateBalancedOpenings({
      cash: 40000,
      ar: 20000,
      inventory: 10000,
      ppne_net: 50000,
      ap: 15000,
      debt_long: 30000,
    });

    const expected_retained_earnings = (40000 + 20000 + 10000 + 50000) - (15000 + 30000); // 75000

    expect(balanced.retained_earnings).toBe(expected_retained_earnings);
  });
});

describe('Balance Sheet Edge Cases', () => {
  it('should handle zero cash startup', () => {
    const config = createTestConfig({ cash: 0, debt_long: 100000 });
    const result = project(config);

    expect(result.checks.passes_validation).toBe(true);
  });

  it('should handle asset-heavy company', () => {
    const config = createTestConfig({
      cash: 10000,
      ar: 50000,
      inventory: 30000,
      ppne_net: 200000,
    });
    const result = project(config);

    expect(result.checks.passes_validation).toBe(true);

    // Opening retained earnings should equal total assets (auto-calculated)
    const total_assets = 10000 + 50000 + 30000 + 200000; // 290000

    // Period 1 retained earnings will be lower due to net income/loss
    // Just verify it's reasonable (less than opening due to expected losses)
    expect(result.bs[0].retained_earnings).toBeLessThan(total_assets);
    expect(result.bs[0].retained_earnings).toBeGreaterThan(total_assets - 50000); // Not too much loss
  });

  it('should maintain balance through projection periods', () => {
    const config = createTestConfig({ cash: 100000 });
    const result = project(config);

    // Check that every period maintains balance sheet identity
    result.bs.forEach((period) => {
      const residual = Math.abs(period.total_assets - period.total_liab_equity);
      expect(residual).toBeLessThan(0.01);
    });
  });
});