#!/usr/bin/env tsx

/**
 * Test script to create a valid session cookie for API testing
 * Usage: tsx scripts/create-test-session.ts
 */

import { connectMongo, User, Membership, createDevSession } from '@aizako/core-lib';

async function createTestSession() {
  try {
    console.log('🔑 Creating test session cookie...');
    
    // Connect to MongoDB
    await connectMongo();
    console.log('📂 Connected to MongoDB');

    // Find a test user (the owner user we seeded)
    const testUser = await User.findOne({ email: '<EMAIL>' });
    if (!testUser) {
      throw new Error('Test user not found. Please run the seed script first.');
    }

    // Find their membership to get the tenant ID  
    const membership = await Membership.findOne({ userId: testUser._id });
    if (!membership) {
      throw new Error('User membership not found. Please check the seed data.');
    }

    console.log(`👤 Test user: ${testUser.email} (${testUser._id})`);
    console.log(`🏢 Tenant ID: ${membership.tenantId}`);
    console.log(`🔐 Role: ${membership.role}`);

    // Create session cookie
    const sessionCookie = createDevSession(testUser._id.toString(), membership.tenantId.toString());
    
    console.log('\n🍪 Session Cookie:');
    console.log(sessionCookie);
    
    console.log('\n📋 Use this in curl requests:');
    console.log(`curl -H "Cookie: ${sessionCookie}" ...`);
    
    console.log('\n✅ Session cookie created successfully!');

  } catch (error) {
    console.error('❌ Failed to create session:', error);
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

// Run the script
createTestSession();