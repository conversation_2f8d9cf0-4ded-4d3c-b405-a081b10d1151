#!/usr/bin/env tsx
/**
 * Database seeding script for Aizako Financial Modeling
 *
 * Usage:
 *   npm run seed              # Seeds development database
 *   npm run seed:staging      # Seeds staging database
 *   npm run seed:clean        # Cleans and reseeds development database
 */
import { connectMongo } from '@aizako/core-lib';
import { User, Tenant, Membership, hashPassword } from '@aizako/core-lib';
const seedData = [
    // Owner users with their own tenants
    {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: 'Alice',
        lastName: '<PERSON>',
        role: 'owner',
        tenantName: 'Acme Corporation'
    },
    {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        role: 'owner',
        tenantName: 'StartupXYZ Inc'
    },
    // Admin user (will be added to Acme Corporation)
    {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        role: 'admin'
    },
    // Member users
    {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        role: 'member'
    },
    // Viewer user
    {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: 'Eve',
        lastName: 'Brown',
        role: 'viewer'
    }
];
async function clearDatabase() {
    console.log('🧹 Clearing existing data...');
    // Clear in reverse dependency order
    await Membership.deleteMany({});
    await User.deleteMany({});
    await Tenant.deleteMany({});
    console.log('✅ Database cleared');
}
async function seedDatabase(clean = false) {
    try {
        console.log('🌱 Starting database seeding...');
        // Connect to MongoDB
        await connectMongo();
        console.log('📂 Connected to MongoDB');
        if (clean) {
            await clearDatabase();
        }
        // Track created tenants for non-owner users
        const tenants = {};
        const users = {};
        // First, create owners and their tenants
        for (const userData of seedData.filter(u => u.role === 'owner')) {
            console.log(`👑 Creating owner: ${userData.email}`);
            // Hash password
            const passwordHash = await hashPassword(userData.password);
            // Create tenant first
            const tenant = new Tenant({
                name: userData.tenantName,
                plan: 'pro',
                modules: {
                    crm: true,
                    flows: true,
                    financialModeling: true
                },
                createdBy: null, // Will be set after user creation
            });
            // Create user
            const user = new User({
                email: userData.email,
                passwordHash,
                firstName: userData.firstName,
                lastName: userData.lastName,
                status: 'active',
                defaultTenantId: tenant._id,
            });
            // Update tenant with createdBy
            tenant.createdBy = user._id;
            // Save both
            await user.save();
            await tenant.save();
            // Create membership
            const membership = new Membership({
                userId: user._id,
                tenantId: tenant._id,
                role: 'owner',
                joinedAt: new Date(),
            });
            await membership.save();
            // Store references
            users[userData.email] = user;
            tenants[userData.tenantName] = tenant;
            console.log(`✅ Created owner ${userData.firstName} ${userData.lastName} for ${userData.tenantName}`);
        }
        // Then create other users and add them to the first tenant (Acme Corporation)
        const defaultTenant = tenants['Acme Corporation'];
        const ownerUser = users['<EMAIL>'];
        for (const userData of seedData.filter(u => u.role !== 'owner')) {
            console.log(`👤 Creating ${userData.role}: ${userData.email}`);
            // Hash password
            const passwordHash = await hashPassword(userData.password);
            // Create user
            const user = new User({
                email: userData.email,
                passwordHash,
                firstName: userData.firstName,
                lastName: userData.lastName,
                status: 'active',
                defaultTenantId: defaultTenant._id,
            });
            await user.save();
            // Create membership in default tenant
            const membership = new Membership({
                userId: user._id,
                tenantId: defaultTenant._id,
                role: userData.role,
                joinedAt: new Date(),
                invitedBy: ownerUser._id,
            });
            await membership.save();
            console.log(`✅ Added ${userData.firstName} ${userData.lastName} as ${userData.role} to ${defaultTenant.name}`);
        }
        // Summary
        console.log('\n📊 Seeding Summary:');
        console.log(`  👥 Users created: ${seedData.length}`);
        console.log(`  🏢 Tenants created: ${Object.keys(tenants).length}`);
        console.log(`  🔗 Memberships created: ${seedData.length}`);
        console.log('\n🎯 Test Accounts:');
        for (const userData of seedData) {
            console.log(`  📧 ${userData.email} (${userData.role}) - Password: ${userData.password}`);
        }
        console.log('\n🚀 Database seeding completed successfully!');
    }
    catch (error) {
        console.error('❌ Seeding failed:', error);
        process.exit(1);
    }
    finally {
        process.exit(0);
    }
}
// Parse command line arguments
const args = process.argv.slice(2);
const shouldClean = args.includes('--clean') || args.includes('-c');
// Run seeding
seedDatabase(shouldClean);
