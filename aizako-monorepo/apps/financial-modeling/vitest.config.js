import { defineConfig } from 'vitest/config';
import path from 'node:path';
export default defineConfig({
    test: {
        environment: 'node', // use 'jsdom' later for React DOM tests
        globals: true,
        passWithNoTests: true,
        include: ['tests/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
        exclude: ['**/e2e-tests/**', '**/node_modules/**']
    },
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './src')
        }
    }
});
