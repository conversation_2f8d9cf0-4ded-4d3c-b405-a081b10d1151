{"name": "@aizako/financial-modeling", "version": "0.1.0", "private": true, "engines": {"node": ">=18.0.0"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint .", "typecheck": "tsc --noEmit", "test": "vitest", "test:run": "vitest run", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:ui": "playwright test --ui", "prettier": "prettier --write .", "seed": "tsx scripts/seed-db.ts", "seed:clean": "tsx scripts/seed-db.ts --clean"}, "dependencies": {"@aizako/core-lib": "*", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@react-pdf/renderer": "^4.3.0", "aws4": "^1.13.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.542.0", "nanoid": "^5.1.5", "next": "14.2.15", "openai": "^5.19.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.62.0", "recharts": "^3.1.2", "tailwind-merge": "^3.3.1", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.48.0", "@tailwindcss/postcss": "^4", "@types/node": "^20.11.0", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-config-next": "14.2.15", "prettier": "^3.2.5", "tailwindcss": "^3.4.14", "tsx": "^4.6.0", "typescript": "^5.3.3", "vite": "^5.0.0", "vitest": "^2.0.0"}}