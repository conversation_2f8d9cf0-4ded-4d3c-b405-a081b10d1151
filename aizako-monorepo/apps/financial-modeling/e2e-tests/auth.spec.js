import { test, expect } from '@playwright/test';
// Test data - matches seeded database
const testAccounts = {
    owner: {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        tenant: 'Acme Corporation'
    },
    admin: {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: '<PERSON>',
        lastName: '<PERSON>'
    },
    member: {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: '<PERSON>',
        lastName: '<PERSON>'
    }
};
// Helper function to sign up a new user
async function signUpUser(page, userData) {
    await page.goto('/auth/signup');
    await page.fill('#firstName', userData.firstName);
    await page.fill('#lastName', userData.lastName);
    await page.fill('#email', userData.email);
    await page.fill('#tenantName', userData.tenantName);
    await page.fill('#password', userData.password);
    await page.click('button[type="submit"]');
}
// Helper function to sign in
async function signInUser(page, email, password) {
    await page.goto('/login');
    await page.fill('#email', email);
    await page.fill('#password', password);
    await page.click('button[type="submit"]');
}
test.describe('Authentication System', () => {
    test.beforeEach(async ({ page }) => {
        // Ensure we start with a clean session
        await page.context().clearCookies();
    });
    test.describe('User Registration', () => {
        test('should allow new user to sign up and create tenant', async ({ page }) => {
            const newUser = {
                email: `test-${Date.now()}@example.com`,
                password: 'NewUserPass123!',
                firstName: 'Test',
                lastName: 'User',
                tenantName: 'Test Company'
            };
            await signUpUser(page, newUser);
            // Should redirect to app after successful signup
            await expect(page).toHaveURL('/app');
            // Should show success indicators or user info
            await expect(page.locator('body')).toContainText(newUser.firstName);
        });
        test('should validate password requirements', async ({ page }) => {
            await page.goto('/auth/signup');
            await page.fill('#firstName', 'Test');
            await page.fill('#lastName', 'User');
            await page.fill('#email', '<EMAIL>');
            await page.fill('#tenantName', 'Test Company');
            // Test weak password
            await page.fill('#password', 'weak');
            // Should show password validation indicators
            await expect(page.locator('text=At least 8 characters')).toBeVisible();
            // Submit button should be disabled
            const submitButton = page.locator('button[type="submit"]');
            await expect(submitButton).toBeDisabled();
            // Fill strong password
            await page.fill('#password', 'StrongPass123!');
            // All validation indicators should be green
            const validationIndicators = page.locator('.bg-green-500');
            await expect(validationIndicators).toHaveCount(5);
            // Submit button should be enabled
            await expect(submitButton).toBeEnabled();
        });
        test('should prevent duplicate email registration', async ({ page }) => {
            // Try to register with existing email
            await signUpUser(page, {
                email: testAccounts.owner.email, // Existing email from seed data
                password: 'NewPass123!',
                firstName: 'Duplicate',
                lastName: 'User',
                tenantName: 'Duplicate Company'
            });
            // Should show error message
            await expect(page.locator('text=Account creation failed')).toBeVisible();
            await expect(page.locator('text=already exists')).toBeVisible();
        });
    });
    test.describe('User Login', () => {
        test('should allow valid user to sign in', async ({ page }) => {
            await signInUser(page, testAccounts.owner.email, testAccounts.owner.password);
            // Should redirect to app
            await expect(page).toHaveURL('/app');
            // Should show user info
            await expect(page.locator('body')).toContainText(testAccounts.owner.firstName);
        });
        test('should reject invalid credentials', async ({ page }) => {
            await signInUser(page, testAccounts.owner.email, 'wrongpassword');
            // Should stay on login page and show error
            await expect(page).toHaveURL('/login');
            await expect(page.locator('text=Sign in failed')).toBeVisible();
        });
        test('should reject non-existent user', async ({ page }) => {
            await signInUser(page, '<EMAIL>', 'anypassword');
            // Should show error
            await expect(page.locator('text=Sign in failed')).toBeVisible();
        });
        test('should validate required fields', async ({ page }) => {
            await page.goto('/login');
            const submitButton = page.locator('button[type="submit"]');
            // Initially disabled
            await expect(submitButton).toBeDisabled();
            // Fill only email
            await page.fill('#email', '<EMAIL>');
            await expect(submitButton).toBeDisabled();
            // Fill both fields
            await page.fill('#password', 'password');
            await expect(submitButton).toBeEnabled();
        });
    });
    test.describe('Session Management', () => {
        test('should maintain session across page refreshes', async ({ page }) => {
            // Sign in
            await signInUser(page, testAccounts.owner.email, testAccounts.owner.password);
            await expect(page).toHaveURL('/app');
            // Refresh page
            await page.reload();
            // Should still be logged in
            await expect(page).toHaveURL('/app');
            await expect(page.locator('body')).toContainText(testAccounts.owner.firstName);
        });
        test('should sign out user', async ({ page }) => {
            // Sign in
            await signInUser(page, testAccounts.owner.email, testAccounts.owner.password);
            await expect(page).toHaveURL('/app');
            // Find and click sign out button (assuming it exists in the app)
            // This might need to be adjusted based on actual UI implementation
            await page.click('button:has-text("Sign out"), button:has-text("Logout")');
            // Should redirect to login or home page
            await expect(page).toHaveURL(/\/(login)?$/);
        });
    });
    test.describe('Protected Routes', () => {
        test('should redirect unauthenticated users to login', async ({ page }) => {
            // Try to access protected route without authentication
            await page.goto('/app');
            // Should redirect to login
            await expect(page).toHaveURL('/login');
        });
        test('should allow authenticated users to access protected routes', async ({ page }) => {
            // Sign in first
            await signInUser(page, testAccounts.member.email, testAccounts.member.password);
            // Access protected route
            await page.goto('/app');
            // Should be allowed
            await expect(page).toHaveURL('/app');
        });
    });
    test.describe('Multi-tenancy', () => {
        test('should display correct tenant information', async ({ page }) => {
            await signInUser(page, testAccounts.owner.email, testAccounts.owner.password);
            // Should show tenant name somewhere in the UI
            await expect(page.locator('body')).toContainText(testAccounts.owner.tenant);
        });
        test('should show tenant switcher for users with multiple tenants', async ({ page }) => {
            // This test would require a user that belongs to multiple tenants
            // For now, we'll test with the owner who should at least see their own tenant
            await signInUser(page, testAccounts.owner.email, testAccounts.owner.password);
            // Look for tenant switcher component (if visible for single tenant users)
            // This might need adjustment based on actual implementation
            const tenantInfo = page.locator('text=Acme Corporation');
            await expect(tenantInfo).toBeVisible();
        });
    });
    test.describe('Error Handling', () => {
        test('should handle network errors gracefully', async ({ page }) => {
            // Intercept API calls and make them fail
            await page.route('/api/auth/signin', route => {
                route.abort('failed');
            });
            await signInUser(page, testAccounts.owner.email, testAccounts.owner.password);
            // Should show appropriate error message
            await expect(page.locator('text=Sign in failed, text=Network error, text=error occurred')).toBeVisible();
        });
        test('should clear errors when user starts typing', async ({ page }) => {
            await page.goto('/login');
            // Trigger an error
            await page.fill('#email', 'invalid');
            await page.fill('#password', 'invalid');
            await page.click('button[type="submit"]');
            await expect(page.locator('text=Sign in failed')).toBeVisible();
            // Start typing in email field
            await page.fill('#email', '<EMAIL>');
            // Error should be cleared
            await expect(page.locator('text=Sign in failed')).not.toBeVisible();
        });
    });
    test.describe('Form Navigation', () => {
        test('should navigate between login and signup', async ({ page }) => {
            await page.goto('/login');
            // Click "Create account" link
            await page.click('text=Create your account');
            await expect(page).toHaveURL('/auth/signup');
            // Click "Sign in instead" link
            await page.click('text=Sign in instead');
            await expect(page).toHaveURL('/login');
        });
        test('should have working forgot password link', async ({ page }) => {
            await page.goto('/login');
            // Click forgot password link
            await page.click('text=Forgot your password?');
            // Should navigate to forgot password page (or show appropriate message)
            await expect(page).toHaveURL('/auth/forgot-password');
        });
    });
});
test.describe('Invitation System', () => {
    test.describe('Invitation Acceptance', () => {
        test('should show error for missing token', async ({ page }) => {
            await page.goto('/auth/accept-invite');
            await expect(page.locator('text=Missing Invitation Token')).toBeVisible();
            await expect(page.locator('text=No invitation token was provided')).toBeVisible();
        });
        test('should show error for invalid token', async ({ page }) => {
            await page.goto('/auth/accept-invite?token=invalid-token');
            await expect(page.locator('text=Invalid Invitation')).toBeVisible();
            await expect(page.locator('text=invalid or has expired')).toBeVisible();
        });
        // Note: Testing valid invitation acceptance would require:
        // 1. Creating a valid invitation in the database
        // 2. Using that invitation token in the test
        // This would be implemented in a more complete test suite
    });
});
