import { jsx as _jsx } from "react/jsx-runtime";
import { Inter, JetBrains_Mono } from "next/font/google";
import "./globals.css";
import { AuthProvider } from '@/lib/contexts/AuthContext';
const inter = Inter({
    variable: "--font-geist-sans",
    subsets: ["latin"],
});
const jetbrainsMono = JetBrains_Mono({
    variable: "--font-geist-mono",
    subsets: ["latin"],
});
export const metadata = {
    title: "Aizako Flows - Financial Modeling Platform",
    description: "Build credible 3-statement financial projections with deterministic calculations and AI-powered insights",
};
export default function RootLayout({ children, }) {
    return (_jsx("html", { lang: "en", children: _jsx("body", { className: `${inter.variable} ${jetbrainsMono.variable} antialiased`, children: _jsx(AuthProvider, { children: children }) }) }));
}
