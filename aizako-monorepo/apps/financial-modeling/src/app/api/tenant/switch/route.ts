import { NextRequest, NextResponse } from 'next/server';
import {
  User,
  Tenant,
  Membership,
  requireAuth,
  createSessionCookie,
  addSecurityHeaders,
  validateRequestBody,
  objectIdSchema,
  connectMongo,
} from '@aizako/core-lib/server';
import { z } from 'zod';

const tenantSwitchSchema = z.object({
  tenantId: objectIdSchema,
});

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth(request);
    if (!authResult.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: authResult.error },
        { status: authResult.status! }
      ));
    }

    // Connect to database
    await connectMongo();

    // Validate request body
    const validation = await validateRequestBody(request, tenantSwitchSchema);
    if (!validation.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Validation failed', details: validation.errors },
        { status: 400 }
      ));
    }

    const { tenantId } = validation.data;
    const { userId } = authResult.user!;

    // Check if user has access to this tenant
    const membership = await Membership.findOne({
      userId,
      tenantId,
    }).populate('tenantId');

    if (!membership) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Access denied to this tenant' },
        { status: 403 }
      ));
    }

    const tenant = membership.tenantId as typeof Tenant.prototype;

    // Update user's default tenant (optional)
    await User.findByIdAndUpdate(userId, {
      defaultTenantId: tenantId,
    });

    // Create new session cookie with new tenant
    const sessionCookie = createSessionCookie({
      userId,
      tenantId,
    });

    // Return success response with new cookie
    const response = NextResponse.json({
      success: true,
      message: 'Tenant switched successfully',
      tenant: {
        id: tenant._id,
        name: tenant.name,
        plan: tenant.plan,
        modules: tenant.modules,
      },
      membership: {
        role: membership.role,
        joinedAt: membership.joinedAt,
      },
    });

    response.headers.set('Set-Cookie', sessionCookie);
    return addSecurityHeaders(response);
  } catch (error) {
    console.error('Tenant switch error:', error);
    return addSecurityHeaders(NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    ));
  }
}

export async function OPTIONS() {
  return addSecurityHeaders(new NextResponse(null, { 
    status: 200,
    headers: {
      'Allow': 'POST',
    },
  }));
}