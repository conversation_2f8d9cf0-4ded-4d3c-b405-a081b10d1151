import { NextRequest, NextResponse } from 'next/server';
import {
  User,
  Tenant,
  Membership,
  Invitation,
  generateSecureToken,
  hashToken,
  requireAuth,
  addSecurityHeaders,
  validateRequestBody,
  invitationCreateSchema,
  emailService,
  connectMongo,
  ObjectId,
} from '@aizako/core-lib/server';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth(request);
    if (!authResult.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: authResult.error },
        { status: authResult.status! }
      ));
    }

    // Connect to database
    await connectMongo();

    // Validate request body
    const validation = await validateRequestBody(request, invitationCreateSchema);
    if (!validation.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Validation failed', details: validation.errors },
        { status: 400 }
      ));
    }

    const { email, role, tenantId } = validation.data;
    const { userId } = authResult.user!;

    // Verify user has permission to invite to this tenant (admin or owner)
    const membership = await Membership.findOne({
      userId,
      tenantId,
      role: { $in: ['admin', 'owner'] },
    });

    if (!membership) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Insufficient permissions to send invitations' },
        { status: 403 }
      ));
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      // Check if user already has access to this tenant
      const existingMembership = await Membership.findOne({
        userId: existingUser._id,
        tenantId,
      });

      if (existingMembership) {
        return addSecurityHeaders(NextResponse.json(
          { error: 'User already has access to this tenant' },
          { status: 409 }
        ));
      }
    }

    // Check if invitation already exists for this email and tenant
    const existingInvitation = await Invitation.findOne({
      email,
      tenantId,
      acceptedAt: { $exists: false },
      expiresAt: { $gt: new Date() },
    });

    if (existingInvitation) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Invitation already sent to this email' },
        { status: 409 }
      ));
    }

    // Get tenant and inviter details
    const tenant = await Tenant.findById(tenantId);
    const inviter = await User.findById(userId);

    if (!tenant || !inviter) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Tenant or inviter not found' },
        { status: 404 }
      ));
    }

    // Generate secure invitation token
    const inviteToken = generateSecureToken();
    const tokenHash = hashToken(inviteToken);

    // Create invitation (expires in 7 days)
    const invitation = Invitation.createWithExpiry({
      email,
      role,
      tenantId: new ObjectId(tenantId),
      tokenHash,
      invitedBy: new ObjectId(userId),
    }, 7); // 7 days

    await invitation.save();

    // Send invitation email (non-blocking)
    emailService.sendInvitation(
      email,
      `${inviter.firstName} ${inviter.lastName}`,
      tenant.name,
      inviteToken,
      role
    ).catch(error => {
      console.error('Failed to send invitation email:', error);
    });

    // Return success response
    return addSecurityHeaders(NextResponse.json({
      success: true,
      message: 'Invitation sent successfully',
      invitation: {
        id: invitation._id,
        email: invitation.email,
        role: invitation.role,
        expiresAt: invitation.expiresAt,
        createdAt: invitation.createdAt,
      },
    }));
  } catch (error) {
    console.error('Send invitation error:', error);
    return addSecurityHeaders(NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    ));
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth(request);
    if (!authResult.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: authResult.error },
        { status: authResult.status! }
      ));
    }

    // Connect to database
    await connectMongo();

    const { tenantId, userId } = authResult.user!;

    // Verify user has permission to view invitations (admin or owner)
    const membership = await Membership.findOne({
      userId,
      tenantId,
      role: { $in: ['admin', 'owner'] },
    });

    if (!membership) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Insufficient permissions to view invitations' },
        { status: 403 }
      ));
    }

    // Get all pending invitations for this tenant
    const invitations = await Invitation.find({
      tenantId,
      acceptedAt: { $exists: false },
    })
    .populate('invitedBy', 'firstName lastName')
    .sort({ createdAt: -1 });

    // Return invitations
    return addSecurityHeaders(NextResponse.json({
      success: true,
      invitations: invitations.map(inv => ({
        id: inv._id,
        email: inv.email,
        role: inv.role,
        invitedBy: {
          name: `${(inv.invitedBy as unknown as { firstName: string; lastName: string }).firstName} ${(inv.invitedBy as unknown as { firstName: string; lastName: string }).lastName}`,
        },
        expiresAt: inv.expiresAt,
        createdAt: inv.createdAt,
        isExpired: inv.isExpired(),
      })),
    }));
  } catch (error) {
    console.error('Get invitations error:', error);
    return addSecurityHeaders(NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    ));
  }
}

export async function OPTIONS() {
  return addSecurityHeaders(new NextResponse(null, { 
    status: 200,
    headers: {
      'Allow': 'GET, POST',
    },
  }));
}