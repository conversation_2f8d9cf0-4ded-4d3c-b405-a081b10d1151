import { NextRequest, NextResponse } from 'next/server';
import {
  Membership,
  requireAuth,
  addSecurityHeaders,
  validateRequestBody,
  membershipUpdateSchema,
  connectMongo,
} from '@aizako/core-lib/server';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth(request);
    if (!authResult.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: authResult.error },
        { status: authResult.status! }
      ));
    }

    // Connect to database
    await connectMongo();

    const { tenantId, userId } = authResult.user!;

    // Verify user has access to this tenant
    const userMembership = await Membership.findOne({
      userId,
      tenantId,
    });

    if (!userMembership) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Access denied to this tenant' },
        { status: 403 }
      ));
    }

    // Get all members of this tenant
    const memberships = await Membership.find({ tenantId })
      .populate('userId', 'firstName lastName email status createdAt lastLoginAt')
      .populate('invitedBy', 'firstName lastName')
      .sort({ joinedAt: -1 });

    // Return members
    return addSecurityHeaders(NextResponse.json({
      success: true,
      members: memberships.map(membership => ({
        id: membership._id,
        user: {
          id: membership.userId._id,
          firstName: membership.userId.firstName,
          lastName: membership.userId.lastName,
          email: membership.userId.email,
          status: membership.userId.status,
          createdAt: membership.userId.createdAt,
          lastLoginAt: membership.userId.lastLoginAt,
        },
        role: membership.role,
        joinedAt: membership.joinedAt,
        invitedBy: membership.invitedBy ? {
          name: `${membership.invitedBy.firstName} ${membership.invitedBy.lastName}`,
        } : null,
        isCurrentUser: membership.userId._id.toString() === userId,
      })),
    }));
  } catch (error) {
    console.error('Get members error:', error);
    return addSecurityHeaders(NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    ));
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth(request);
    if (!authResult.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: authResult.error },
        { status: authResult.status! }
      ));
    }

    // Connect to database
    await connectMongo();

    // Validate request body
    const validation = await validateRequestBody(request, membershipUpdateSchema);
    if (!validation.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Validation failed', details: validation.errors },
        { status: 400 }
      ));
    }

    const { role, userId: targetUserId, tenantId } = validation.data;
    const { userId: currentUserId } = authResult.user!;

    // Get current user's membership
    const currentUserMembership = await Membership.findOne({
      userId: currentUserId,
      tenantId,
    });

    if (!currentUserMembership) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Access denied to this tenant' },
        { status: 403 }
      ));
    }

    // Get target user's membership
    const targetMembership = await Membership.findOne({
      userId: targetUserId,
      tenantId,
    }).populate('userId', 'firstName lastName email');

    if (!targetMembership) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Target user is not a member of this tenant' },
        { status: 404 }
      ));
    }

    // Check permissions
    // Only owners can change owner roles
    // Admins can change member/viewer roles but not admin/owner roles
    // Users cannot change their own role
    if (currentUserId === targetUserId) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Cannot change your own role' },
        { status: 400 }
      ));
    }

    const canModify = 
      (currentUserMembership.role === 'owner') || 
      (currentUserMembership.role === 'admin' && 
       !['admin', 'owner'].includes(targetMembership.role) && 
       !['admin', 'owner'].includes(role));

    if (!canModify) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Insufficient permissions to modify this user role' },
        { status: 403 }
      ));
    }

    // Update the membership role
    targetMembership.role = role;
    await targetMembership.save();

    // Return updated membership
    return addSecurityHeaders(NextResponse.json({
      success: true,
      message: 'User role updated successfully',
      membership: {
        id: targetMembership._id,
        user: {
          id: targetMembership.userId._id,
          firstName: targetMembership.userId.firstName,
          lastName: targetMembership.userId.lastName,
          email: targetMembership.userId.email,
        },
        role: targetMembership.role,
        joinedAt: targetMembership.joinedAt,
      },
    }));
  } catch (error) {
    console.error('Update member role error:', error);
    return addSecurityHeaders(NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    ));
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth(request);
    if (!authResult.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: authResult.error },
        { status: authResult.status! }
      ));
    }

    // Connect to database
    await connectMongo();

    const { searchParams } = new URL(request.url);
    const targetUserId = searchParams.get('userId');

    if (!targetUserId) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      ));
    }

    const { tenantId, userId: currentUserId } = authResult.user!;

    // Get current user's membership
    const currentUserMembership = await Membership.findOne({
      userId: currentUserId,
      tenantId,
    });

    if (!currentUserMembership) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Access denied to this tenant' },
        { status: 403 }
      ));
    }

    // Get target user's membership
    const targetMembership = await Membership.findOne({
      userId: targetUserId,
      tenantId,
    });

    if (!targetMembership) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Target user is not a member of this tenant' },
        { status: 404 }
      ));
    }

    // Check permissions
    // Only owners can remove owners or admins
    // Admins can remove members/viewers but not admins/owners
    // Users cannot remove themselves (should use leave endpoint)
    if (currentUserId === targetUserId) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Cannot remove yourself. Use leave endpoint instead.' },
        { status: 400 }
      ));
    }

    const canRemove = 
      (currentUserMembership.role === 'owner') || 
      (currentUserMembership.role === 'admin' && 
       !['admin', 'owner'].includes(targetMembership.role));

    if (!canRemove) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Insufficient permissions to remove this user' },
        { status: 403 }
      ));
    }

    // Remove the membership
    await Membership.findByIdAndDelete(targetMembership._id);

    // Return success
    return addSecurityHeaders(NextResponse.json({
      success: true,
      message: 'User removed from tenant successfully',
    }));
  } catch (error) {
    console.error('Remove member error:', error);
    return addSecurityHeaders(NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    ));
  }
}

export async function OPTIONS() {
  return addSecurityHeaders(new NextResponse(null, { 
    status: 200,
    headers: {
      'Allow': 'GET, PATCH, DELETE',
    },
  }));
}