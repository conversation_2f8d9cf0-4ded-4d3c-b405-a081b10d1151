import { NextRequest, NextResponse } from 'next/server';
import {
  User,
  requireAuth,
  addSecurityHeaders,
  validateRequestBody,
  userProfileUpdateSchema,
  connectMongo,
} from '@aizako/core-lib/server';

export async function PATCH(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth(request);
    if (!authResult.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: authResult.error },
        { status: authResult.status! }
      ));
    }

    // Connect to database
    await connectMongo();

    // Validate request body
    const validation = await validateRequestBody(request, userProfileUpdateSchema);
    if (!validation.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Validation failed', details: validation.errors },
        { status: 400 }
      ));
    }

    const { userId } = authResult.user!;
    const updates = validation.data;

    // If email is being updated, check if it's already taken
    if (updates.email) {
      const existingUser = await User.findOne({ 
        email: updates.email,
        _id: { $ne: userId }
      });

      if (existingUser) {
        return addSecurityHeaders(NextResponse.json(
          { error: 'Email already in use' },
          { status: 409 }
        ));
      }
    }

    // Update user profile
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      {
        ...updates,
        updatedAt: new Date(),
      },
      { 
        new: true,
        select: '-passwordHash' // Don't return password hash
      }
    );

    if (!updatedUser) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      ));
    }

    // Return updated user data
    return addSecurityHeaders(NextResponse.json({
      success: true,
      message: 'Profile updated successfully',
      user: {
        id: updatedUser._id,
        email: updatedUser.email,
        firstName: updatedUser.firstName,
        lastName: updatedUser.lastName,
        status: updatedUser.status,
        createdAt: updatedUser.createdAt,
        updatedAt: updatedUser.updatedAt,
        lastLoginAt: updatedUser.lastLoginAt,
      },
    }));
  } catch (error) {
    console.error('Update profile error:', error);
    return addSecurityHeaders(NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    ));
  }
}

export async function OPTIONS() {
  return addSecurityHeaders(new NextResponse(null, { 
    status: 200,
    headers: {
      'Allow': 'PATCH',
    },
  }));
}