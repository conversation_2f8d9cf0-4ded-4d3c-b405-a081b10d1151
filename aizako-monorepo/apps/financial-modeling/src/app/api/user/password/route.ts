import { NextRequest, NextResponse } from 'next/server';
import {
  User,
  PasswordResetToken,
  verifyPassword,
  hashPassword,
  requireAuth,
  requireRateLimit,
  addSecurityHeaders,
  validateRequestBody,
  passwordChangeSchema,
  connectMongo,
} from '@aizako/core-lib/server';

export async function PATCH(request: NextRequest) {
  try {
    // Rate limiting for password changes
    const rateLimitResult = requireRateLimit(request);
    if (!rateLimitResult.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: rateLimitResult.error },
        { status: rateLimitResult.status! }
      ));
    }

    // Check authentication
    const authResult = await requireAuth(request);
    if (!authResult.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: authResult.error },
        { status: authResult.status! }
      ));
    }

    // Connect to database
    await connectMongo();

    // Validate request body
    const validation = await validateRequestBody(request, passwordChangeSchema);
    if (!validation.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Validation failed', details: validation.errors },
        { status: 400 }
      ));
    }

    const { currentPassword, newPassword } = validation.data;
    const { userId } = authResult.user!;

    // Get user with password hash
    const user = await User.findById(userId);
    if (!user) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      ));
    }

    // Verify current password
    const isCurrentPasswordValid = await verifyPassword(currentPassword, user.passwordHash);
    if (!isCurrentPasswordValid) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Current password is incorrect' },
        { status: 400 }
      ));
    }

    // Ensure new password is different from current
    const isSamePassword = await verifyPassword(newPassword, user.passwordHash);
    if (isSamePassword) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'New password must be different from current password' },
        { status: 400 }
      ));
    }

    // Hash new password
    const newPasswordHash = await hashPassword(newPassword);

    // Update user password
    user.passwordHash = newPasswordHash;
    user.passwordChangedAt = new Date();
    user.updatedAt = new Date();
    await user.save();

    // Invalidate all password reset tokens for this user (security measure)
    await PasswordResetToken.invalidateAllForUser(user._id);

    // Return success (no sensitive data)
    return addSecurityHeaders(NextResponse.json({
      success: true,
      message: 'Password changed successfully',
      passwordChangedAt: user.passwordChangedAt,
    }));
  } catch (error) {
    console.error('Change password error:', error);
    return addSecurityHeaders(NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    ));
  }
}

export async function OPTIONS() {
  return addSecurityHeaders(new NextResponse(null, { 
    status: 200,
    headers: {
      'Allow': 'PATCH',
    },
  }));
}