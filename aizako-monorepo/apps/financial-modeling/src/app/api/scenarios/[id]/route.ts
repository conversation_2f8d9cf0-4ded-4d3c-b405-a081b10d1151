import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthedContext } from '@/lib/withAuth';
import { getMongo, Scenario, type Config } from '@aizako/core-lib/server';
import { configSchema } from '@/lib/validations/config';
import { project } from '@/lib/engine/project';
import { aggregateYearly } from '@/lib/utils/aggregation';
import { z } from 'zod';
import mongoose from 'mongoose';

// Schema for PATCH request body
const updateScenarioSchema = z.object({
  patch: z.object({
    name: z.string().min(1).max(100).trim().optional(),
    industry: z.string().min(1).max(50).trim().optional(),
    config: configSchema.partial().optional()
  }),
  version: z.number().int().min(1)
});

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * GET /api/scenarios/[id]
 * Returns full scenario document for the current tenant
 */
async function getScenario(req: NextRequest, ctx: AuthedContext, { params }: RouteParams) {
  try {
    await getMongo();
    
    const { id } = params;
    
    // Validate ObjectId format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid scenario ID' },
        { status: 400 }
      );
    }
    
    const scenario = await Scenario.findByTenantAndId(ctx.tenantId, id);
    
    if (!scenario) {
      return NextResponse.json(
        { error: 'Scenario not found' },
        { status: 404 }
      );
    }
    
    // Return full document
    return NextResponse.json({
      data: {
        id: scenario._id,
        name: scenario.name,
        industry: scenario.industry,
        config: scenario.config,
        snapshot: scenario.snapshot,
        version: scenario.version,
        createdAt: scenario.createdAt,
        updatedAt: scenario.updatedAt
      }
    });
    
  } catch (error) {
    console.error('Error fetching scenario:', error);
    return NextResponse.json(
      { error: 'Failed to fetch scenario' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/scenarios/[id]
 * Updates scenario with optimistic concurrency control
 */
async function updateScenario(req: NextRequest, ctx: AuthedContext, { params }: RouteParams) {
  try {
    await getMongo();
    
    const { id } = params;
    
    // Validate ObjectId format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid scenario ID' },
        { status: 400 }
      );
    }
    
    const body = await req.json();
    
    // Validate request body
    const validation = updateScenarioSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request body',
          details: validation.error.errors 
        },
        { status: 400 }
      );
    }
    
    const { patch, version } = validation.data;
    
    // Find existing scenario
    const existingScenario = await Scenario.findByTenantAndId(ctx.tenantId, id);
    
    if (!existingScenario) {
      return NextResponse.json(
        { error: 'Scenario not found' },
        { status: 404 }
      );
    }
    
    // Check version for optimistic concurrency control
    if (existingScenario.version !== version) {
      return NextResponse.json(
        { 
          error: 'Conflict: Scenario has been modified by another user',
          currentVersion: existingScenario.version,
          providedVersion: version
        },
        { status: 409 }
      );
    }
    
    // Merge patch with existing data
    const updatedData: Record<string, unknown> = {
      name: patch.name || existingScenario.name,
      industry: patch.industry || existingScenario.industry,
      config: existingScenario.config,
    };
    
    // Handle config merge and validation
    let finalConfig = existingScenario.config;
    if (patch.config) {
      // Deep merge configuration
      finalConfig = mergeDeep(
        existingScenario.config as unknown as Record<string, unknown>, 
        patch.config as unknown as Record<string, unknown>
      ) as unknown as Config;
    }
    
    // Validate the merged config
    const configValidation = configSchema.safeParse(finalConfig);
    if (!configValidation.success) {
      return NextResponse.json(
        { 
          error: 'Invalid configuration after merge',
          details: configValidation.error.errors 
        },
        { status: 422 }
      );
    }
    
    updatedData.config = finalConfig;
    
    // Re-compute financial projections
    let projectionResult;
    try {
      projectionResult = project(finalConfig);
    } catch (projectionError) {
      console.error('Financial projection error:', projectionError);
      return NextResponse.json(
        { 
          error: 'Failed to compute financial projections',
          details: projectionError instanceof Error ? projectionError.message : 'Unknown error'
        },
        { status: 422 }
      );
    }

    // Identity tolerance check - enforce strict 1e-2 threshold
    if (projectionResult.checks.max_residual > 1e-2) {
      return NextResponse.json(
        { 
          error: 'Model identity check failed',
          residual: projectionResult.checks.max_residual
        },
        { status: 422 }
      );
    }

    // Generate yearly aggregation
    const yearly = {
      pnl: aggregateYearly(projectionResult.pnl, 'pnl'),
      bs: aggregateYearly(projectionResult.bs, 'bs'),
      cf: aggregateYearly(projectionResult.cf, 'cf'),
      metrics: aggregateYearly(projectionResult.metrics, 'metrics')
    };

    // Create snapshot with both monthly and yearly data
    const snapshot = {
      monthly: {
        pnl: projectionResult.pnl,
        bs: projectionResult.bs,
        cf: projectionResult.cf,
        checks: projectionResult.checks
      },
      yearly
    };
    
    updatedData.snapshot = snapshot;
    
    // Update scenario with version increment
    const updatedScenario = await Scenario.findByIdAndUpdate(
      id,
      {
        ...updatedData,
        $inc: { version: 1 },
        updatedAt: new Date()
      },
      { 
        new: true,
        runValidators: true
      }
    );
    
    if (!updatedScenario) {
      return NextResponse.json(
        { error: 'Failed to update scenario' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      ok: true,
      data: {
        version: updatedScenario.version,
        updatedAt: updatedScenario.updatedAt
      }
    });
    
  } catch (error) {
    console.error('Error updating scenario:', error);
    return NextResponse.json(
      { error: 'Failed to update scenario' },
      { status: 500 }
    );
  }
}

/**
 * Deep merge utility function for merging configuration objects
 */
function mergeDeep(target: Record<string, unknown>, source: Record<string, unknown>): Record<string, unknown> {
  const result = { ...target };
  
  for (const key in source) {
    if (source[key] !== null && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      result[key] = mergeDeep((result[key] || {}) as Record<string, unknown>, source[key] as Record<string, unknown>);
    } else {
      result[key] = source[key];
    }
  }
  
  return result;
}

// Export handlers wrapped with authentication
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const GET = withAuth(getScenario as any, { checkModuleAccess: 'financialModeling' });
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const PATCH = withAuth(updateScenario as any, { checkModuleAccess: 'financialModeling' });