import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthedContext } from '@/lib/withAuth';
import { getMongo, Scenario } from '@aizako/core-lib/server';
import { configSchema } from '@/lib/validations/config';
import { project } from '@/lib/engine/project';
import { aggregateYearly } from '@/lib/utils/aggregation';
import { z } from 'zod';

// Schema for POST request body
const createScenarioSchema = z.object({
  name: z.string().min(1).max(100).trim(),
  industry: z.string().min(1).max(50).trim(),
  config: configSchema
});

/**
 * GET /api/scenarios
 * Returns list of scenarios for the current tenant
 */
async function getScenarios(req: NextRequest, ctx: AuthedContext) {
  try {
    await getMongo();
    
    const scenarios = await Scenario.findByTenant(ctx.tenantId);
    
    const scenariosList = scenarios.map(scenario => ({
      id: scenario._id,
      name: scenario.name,
      industry: scenario.industry,
      updatedAt: scenario.updatedAt
    }));
    
    return NextResponse.json({
      data: scenariosList
    });
  } catch (error) {
    console.error('Error fetching scenarios:', error);
    return NextResponse.json(
      { error: 'Failed to fetch scenarios' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/scenarios  
 * Creates a new scenario with computed projections
 */
async function createScenario(req: NextRequest, ctx: AuthedContext) {
  try {
    await getMongo();
    
    const body = await req.json();
    
    // Validate request body
    const validation = createScenarioSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request body',
          details: validation.error.errors 
        },
        { status: 400 }
      );
    }
    
    const { name, industry, config } = validation.data;
    
    // Check for duplicate scenario names within tenant (optional)
    const existingScenario = await Scenario.findOne({ 
      tenantId: ctx.tenantId, 
      name 
    });
    
    if (existingScenario) {
      return NextResponse.json(
        { error: 'A scenario with this name already exists' },
        { status: 409 }
      );
    }
    
    // Compute financial projections
    let projectionResult;
    try {
      projectionResult = project(config);
    } catch (projectionError) {
      console.error('Financial projection error:', projectionError);
      return NextResponse.json(
        { 
          error: 'Failed to compute financial projections',
          details: projectionError instanceof Error ? projectionError.message : 'Unknown error'
        },
        { status: 422 }
      );
    }

    // Identity tolerance check - enforce strict 1e-2 threshold
    if (projectionResult.checks.max_residual > 1e-2) {
      return NextResponse.json(
        { 
          error: 'Model identity check failed',
          residual: projectionResult.checks.max_residual
        },
        { status: 422 }
      );
    }

    // Generate yearly aggregation
    const yearly = {
      pnl: aggregateYearly(projectionResult.pnl, 'pnl'),
      bs: aggregateYearly(projectionResult.bs, 'bs'),
      cf: aggregateYearly(projectionResult.cf, 'cf'),
      metrics: aggregateYearly(projectionResult.metrics, 'metrics')
    };

    // Create snapshot with both monthly and yearly data
    const snapshot = {
      monthly: {
        pnl: projectionResult.pnl,
        bs: projectionResult.bs,
        cf: projectionResult.cf,
        checks: projectionResult.checks
      },
      yearly
    };
    
    // Create and save scenario
    const scenario = new Scenario({
      name,
      industry,
      tenantId: ctx.tenantId,
      config,
      snapshot,
      version: 1
    });
    
    await scenario.save();
    
    return NextResponse.json(
      { 
        data: { 
          id: scenario._id.toString() 
        } 
      },
      { status: 201 }
    );
    
  } catch (error) {
    console.error('Error creating scenario:', error);
    return NextResponse.json(
      { error: 'Failed to create scenario' },
      { status: 500 }
    );
  }
}

// Export handlers wrapped with authentication
export const GET = withAuth(getScenarios, { checkModuleAccess: 'financialModeling' });
export const POST = withAuth(createScenario, { checkModuleAccess: 'financialModeling' });