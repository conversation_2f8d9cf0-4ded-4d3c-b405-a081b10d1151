import { NextRequest, NextResponse } from 'next/server';
import {
  User,
  Tenant,
  Membership,
  requireAuth,
  addSecurityHeaders,
  connectMongo,
} from '@aizako/core-lib/server';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth(request);
    if (!authResult.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: authResult.error },
        { status: authResult.status! }
      ));
    }

    // Connect to database
    await connectMongo();

    const { userId, tenantId } = authResult.user!;

    // Validate that we have proper string IDs, not objects
    if (typeof userId !== 'string' || typeof tenantId !== 'string') {
      console.error('Invalid session data - userId or tenantId is not a string:', { userId: typeof userId, tenantId: typeof tenantId });
      return addSecurityHeaders(NextResponse.json(
        { error: 'Invalid session data' },
        { status: 401 }
      ));
    }

    // Get user details
    const user = await User.findById(userId).select('-passwordHash');
    if (!user) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      ));
    }

    // Get tenant details
    const tenant = await Tenant.findById(tenantId);
    if (!tenant) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Tenant not found' },
        { status: 404 }
      ));
    }

    // Get user's membership in current tenant
    const membership = await Membership.findOne({
      userId,
      tenantId,
    });

    if (!membership) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'No membership found' },
        { status: 403 }
      ));
    }

    // Get all user's tenants
    const allMemberships = await Membership.find({ userId })
      .populate('tenantId', 'name plan modules')
      .sort({ joinedAt: -1 });

    // Return user data
    return addSecurityHeaders(NextResponse.json({
      success: true,
      user: {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        status: user.status,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt,
        defaultTenantId: user.defaultTenantId,
      },
      currentTenant: {
        id: tenant._id,
        name: tenant.name,
        plan: tenant.plan,
        modules: tenant.modules,
        createdAt: tenant.createdAt,
      },
      membership: {
        role: membership.role,
        joinedAt: membership.joinedAt,
      },
      allTenants: allMemberships.map(m => ({
        id: m.tenantId._id,
        name: m.tenantId.name,
        plan: m.tenantId.plan,
        modules: m.tenantId.modules,
        role: m.role,
        joinedAt: m.joinedAt,
        isDefault: m.tenantId._id.toString() === user.defaultTenantId.toString(),
      })),
    }));
  } catch (error) {
    console.error('Get user profile error:', error);
    return addSecurityHeaders(NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    ));
  }
}

export async function OPTIONS() {
  return addSecurityHeaders(new NextResponse(null, { 
    status: 200,
    headers: {
      'Allow': 'GET',
    },
  }));
}