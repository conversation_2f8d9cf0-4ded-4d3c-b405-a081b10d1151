import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';
import {
  User,
  Tenant,
  Membership,
  hashPassword,
  createSessionCookie,
  requireRateLimit,
  addSecurityHeaders,
  validateRequestBody,
  signUpSchema,
  emailService,
  connectMongo,
} from '@aizako/core-lib/server';

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitResult = requireRateLimit(request);
    if (!rateLimitResult.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: rateLimitResult.error },
        { status: rateLimitResult.status! }
      ));
    }

    // Connect to database
    await connectMongo();

    // Validate request body
    const validation = await validateRequestBody(request, signUpSchema);
    if (!validation.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Validation failed', details: validation.errors },
        { status: 400 }
      ));
    }

    const { email, password, firstName, lastName, tenantName } = validation.data;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'User already exists' },
        { status: 409 }
      ));
    }

    // Check if tenant name is taken
    const existingTenant = await Tenant.findOne({ 
      name: { $regex: new RegExp(`^${tenantName}$`, 'i') } 
    });
    if (existingTenant) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Tenant name already taken' },
        { status: 409 }
      ));
    }

    // Start a transaction for atomic user and tenant creation
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Hash password
      const passwordHash = await hashPassword(password);

      // Create user first to get the ID for tenant creation
      const user = new User({
        email,
        passwordHash,
        fullName: `${firstName} ${lastName}`,
        status: 'active',
      });

      await user.save({ session });

      // Create tenant with user as creator
      const tenant = new Tenant({
        name: tenantName,
        plan: 'free',
        modules: {
          crm: false,
          flows: false,
          financialModeling: true,
        },
        createdBy: user._id,
      });

      await tenant.save({ session });

      // Update user with default tenant
      user.defaultTenantId = tenant._id;
      await user.save({ session });

      // Create membership (owner role)
      const membership = new Membership({
        userId: user._id,
        tenantId: tenant._id,
        role: 'owner',
        invitedBy: user._id,
        joinedAt: new Date(),
      });

      await membership.save({ session });

      // Commit transaction
      await session.commitTransaction();

      // Create session cookie
      const sessionCookie = createSessionCookie({
        userId: user._id.toString(),
        tenantId: tenant._id.toString(),
      });

      // Send welcome email (non-blocking)
      emailService.sendWelcome(
        user.email,
        `${user.firstName} ${user.lastName}`,
        tenant.name
      ).catch(error => {
        console.error('Failed to send welcome email:', error);
      });

      // Return success response with cookie
      const response = NextResponse.json({
        success: true,
        user: {
          id: user._id,
          email: user.email,
          firstName: firstName,
          lastName: lastName,
          status: user.status,
        },
        tenant: {
          id: tenant._id,
          name: tenant.name,
          plan: tenant.plan,
          modules: tenant.modules,
        },
      });

      response.headers.set('Set-Cookie', sessionCookie);
      return addSecurityHeaders(response);
    } catch (error) {
      // Rollback transaction
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  } catch (error) {
    console.error('Signup error:', error);
    return addSecurityHeaders(NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    ));
  }
}

export async function OPTIONS() {
  return addSecurityHeaders(new NextResponse(null, { 
    status: 200,
    headers: {
      'Allow': 'POST',
    },
  }));
}