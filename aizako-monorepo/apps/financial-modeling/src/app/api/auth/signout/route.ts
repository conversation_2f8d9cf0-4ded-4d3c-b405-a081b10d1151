import { NextResponse } from 'next/server';
import {
  clearSessionCookie,
  addSecurityHeaders,
} from '@aizako/core-lib/server';

export async function POST() {
  try {
    // Clear session cookie
    const clearCookie = clearSessionCookie();

    // Return success response with cleared cookie
    const response = NextResponse.json({
      success: true,
      message: 'Signed out successfully',
    });

    response.headers.set('Set-Cookie', clearCookie);
    return addSecurityHeaders(response);
  } catch (error) {
    console.error('Signout error:', error);
    
    // Even if there's an error, we should still clear the cookie
    const clearCookie = clearSessionCookie();
    
    const response = NextResponse.json({
      success: true,
      message: 'Signed out successfully',
    });

    response.headers.set('Set-Cookie', clearCookie);
    return addSecurityHeaders(response);
  }
}

export async function OPTIONS() {
  return addSecurityHeaders(new NextResponse(null, { 
    status: 200,
    headers: {
      'Allow': 'POST',
    },
  }));
}