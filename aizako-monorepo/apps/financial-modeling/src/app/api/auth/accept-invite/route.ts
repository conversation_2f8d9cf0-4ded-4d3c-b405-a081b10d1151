import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';
import {
  User,
  Membership,
  Invitation,
  hashPassword,
  hashToken,
  createSessionCookie,
  requireRateLimit,
  addSecurityHeaders,
  validateRequestBody,
  invitationAcceptSchema,
  emailService,
  connectMongo,
} from '@aizako/core-lib/server';

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitResult = requireRateLimit(request);
    if (!rateLimitResult.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: rateLimitResult.error },
        { status: rateLimitResult.status! }
      ));
    }

    // Connect to database
    await connectMongo();

    // Validate request body
    const validation = await validateRequestBody(request, invitationAcceptSchema);
    if (!validation.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Validation failed', details: validation.errors },
        { status: 400 }
      ));
    }

    const { token, firstName, lastName, password } = validation.data;

    // Hash the token to find it in database
    const tokenHash = hashToken(token);

    // Find valid invitation token
    const invitation = await Invitation.findValidByTokenHash(tokenHash)
      .populate('tenantId')
      .populate('invitedBy');

    if (!invitation) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Invalid or expired invitation' },
        { status: 400 }
      ));
    }

    // Check if user already exists with this email
    const existingUser = await User.findOne({ email: invitation.email });
    if (existingUser) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'User already exists with this email' },
        { status: 409 }
      ));
    }

    // Start a transaction
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Hash password
      const passwordHash = await hashPassword(password);

      // Create user
      const user = new User({
        email: invitation.email,
        passwordHash,
        firstName,
        lastName,
        status: 'active',
        defaultTenantId: invitation.tenantId._id,
      });

      await user.save({ session });

      // Create membership
      const membership = new Membership({
        userId: user._id,
        tenantId: invitation.tenantId._id,
        role: invitation.role,
        invitedBy: (invitation.invitedBy as unknown as { _id: string })._id,
        joinedAt: new Date(),
      });

      await membership.save({ session });

      // Mark invitation as accepted
      invitation.markAccepted();
      await invitation.save({ session });

      // Commit transaction
      await session.commitTransaction();

      // Create session cookie
      const sessionCookie = createSessionCookie({
        userId: user._id.toString(),
        tenantId: invitation.tenantId._id.toString(),
      });

      // Send welcome email (non-blocking)
      const populatedTenant = invitation.tenantId as unknown as { _id: string; name: string; plan: string; modules: string[] };
      emailService.sendWelcome(
        user.email,
        `${user.firstName} ${user.lastName}`,
        populatedTenant.name
      ).catch(error => {
        console.error('Failed to send welcome email:', error);
      });

      // Return success response with cookie
      const response = NextResponse.json({
        success: true,
        message: 'Invitation accepted successfully',
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          status: user.status,
        },
        tenant: {
          id: populatedTenant._id,
          name: populatedTenant.name,
          plan: populatedTenant.plan,
          modules: populatedTenant.modules,
        },
        membership: {
          role: membership.role,
          joinedAt: membership.joinedAt,
        },
      });

      response.headers.set('Set-Cookie', sessionCookie);
      return addSecurityHeaders(response);
    } catch (error) {
      // Rollback transaction
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  } catch (error) {
    console.error('Accept invitation error:', error);
    return addSecurityHeaders(NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    ));
  }
}

export async function GET(request: NextRequest) {
  try {
    // Connect to database
    await connectMongo();

    // Get token from query params
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    if (!token) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Token is required' },
        { status: 400 }
      ));
    }

    // Hash the token to find it in database
    const tokenHash = hashToken(token);

    // Find valid invitation token
    const invitation = await Invitation.findValidByTokenHash(tokenHash)
      .populate('tenantId')
      .populate('invitedBy', 'firstName lastName');

    if (!invitation) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Invalid or expired invitation' },
        { status: 400 }
      ));
    }

    // Return invitation details
    return addSecurityHeaders(NextResponse.json({
      success: true,
      invitation: {
        email: invitation.email,
        role: invitation.role,
        tenant: {
          name: (invitation.tenantId as unknown as { name: string }).name,
        },
        invitedBy: {
          name: `${(invitation.invitedBy as unknown as { firstName: string; lastName: string }).firstName} ${(invitation.invitedBy as unknown as { firstName: string; lastName: string }).lastName}`,
        },
        expiresAt: invitation.expiresAt,
      },
    }));
  } catch (error) {
    console.error('Get invitation error:', error);
    return addSecurityHeaders(NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    ));
  }
}

export async function OPTIONS() {
  return addSecurityHeaders(new NextResponse(null, { 
    status: 200,
    headers: {
      'Allow': 'GET, POST',
    },
  }));
}