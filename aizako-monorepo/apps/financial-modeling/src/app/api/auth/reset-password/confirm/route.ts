import { NextRequest, NextResponse } from 'next/server';
import {
  User,
  PasswordResetToken,
  hashPassword,
  hashToken,
  createSessionCookie,
  requireRateLimit,
  addSecurityHeaders,
  validateRequestBody,
  passwordResetConfirmSchema,
  connectMongo,
} from '@aizako/core-lib/server';

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitResult = requireRateLimit(request);
    if (!rateLimitResult.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: rateLimitResult.error },
        { status: rateLimitResult.status! }
      ));
    }

    // Connect to database
    await connectMongo();

    // Validate request body
    const validation = await validateRequestBody(request, passwordResetConfirmSchema);
    if (!validation.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Validation failed', details: validation.errors },
        { status: 400 }
      ));
    }

    const { token, password } = validation.data;

    // Hash the token to find it in database
    const tokenHash = hashToken(token);

    // Find valid password reset token
    const resetToken = await PasswordResetToken.findValidByTokenHash(tokenHash)
      .populate('userId');

    if (!resetToken) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Invalid or expired reset token' },
        { status: 400 }
      ));
    }

    const user = resetToken.userId as typeof User.prototype; // Populated user

    if (!user || user.status !== 'active') {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Invalid or expired reset token' },
        { status: 400 }
      ));
    }

    try {
      // Hash new password
      const passwordHash = await hashPassword(password);

      // Update user password
      user.passwordHash = passwordHash;
      user.passwordChangedAt = new Date();
      await user.save();

      // Mark token as used
      resetToken.markUsed();
      await resetToken.save();

      // Invalidate all other reset tokens for this user
      await PasswordResetToken.invalidateAllForUser(user._id);

      // Create session cookie (auto-login after password reset)
      const sessionCookie = createSessionCookie({
        userId: user._id.toString(),
        tenantId: user.defaultTenantId.toString(),
      });

      // Return success response with cookie
      const response = NextResponse.json({
        success: true,
        message: 'Password reset successfully',
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
        },
      });

      response.headers.set('Set-Cookie', sessionCookie);
      return addSecurityHeaders(response);
    } catch (error) {
      console.error('Password reset error:', error);
      return addSecurityHeaders(NextResponse.json(
        { error: 'Failed to reset password' },
        { status: 500 }
      ));
    }
  } catch (error) {
    console.error('Password reset confirm error:', error);
    return addSecurityHeaders(NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    ));
  }
}

export async function OPTIONS() {
  return addSecurityHeaders(new NextResponse(null, { 
    status: 200,
    headers: {
      'Allow': 'POST',
    },
  }));
}