import { NextRequest, NextResponse } from 'next/server';
import {
  User,
  PasswordResetToken,
  generateSecureToken,
  hashToken,
  requireRateLimit,
  addSecurityHeaders,
  validateRequestBody,
  passwordResetRequestSchema,
  emailService,
  connectMongo,
} from '@aizako/core-lib/server';

export async function POST(request: NextRequest) {
  try {
    // Rate limiting (stricter for password reset to prevent abuse)
    const rateLimitResult = requireRateLimit(request);
    if (!rateLimitResult.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: rateLimitResult.error },
        { status: rateLimitResult.status! }
      ));
    }

    // Connect to database
    await connectMongo();

    // Validate request body
    const validation = await validateRequestBody(request, passwordResetRequestSchema);
    if (!validation.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Validation failed', details: validation.errors },
        { status: 400 }
      ));
    }

    const { email } = validation.data;

    // Find user (but don't reveal if user exists or not for security)
    const user = await User.findOne({ email, status: 'active' });
    
    // Always return success to prevent email enumeration attacks
    const successResponse = addSecurityHeaders(NextResponse.json({
      success: true,
      message: 'If an account with that email exists, a password reset link has been sent.',
    }));

    // If user doesn't exist, just return success (don't reveal)
    if (!user) {
      return successResponse;
    }

    try {
      // Invalidate any existing password reset tokens for this user
      await PasswordResetToken.invalidateAllForUser(user._id);

      // Generate secure token
      const resetToken = generateSecureToken();
      const tokenHash = hashToken(resetToken);

      // Create password reset token (expires in 1 hour)
      const passwordResetToken = PasswordResetToken.createWithExpiry({
        userId: user._id,
        tokenHash,
      }, 1); // 1 hour

      await passwordResetToken.save();

      // Send password reset email (non-blocking)
      emailService.sendPasswordReset(
        user.email,
        resetToken,
        `${user.firstName} ${user.lastName}`
      ).catch(error => {
        console.error('Failed to send password reset email:', error);
      });

      return successResponse;
    } catch (error) {
      console.error('Password reset request error:', error);
      // Still return success to prevent information disclosure
      return successResponse;
    }
  } catch (error) {
    console.error('Password reset request error:', error);
    return addSecurityHeaders(NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    ));
  }
}

export async function OPTIONS() {
  return addSecurityHeaders(new NextResponse(null, { 
    status: 200,
    headers: {
      'Allow': 'POST',
    },
  }));
}