import { NextRequest, NextResponse } from 'next/server';
import {
  User,
  Membership,
  verifyPassword,
  createSessionCookie,
  requireRateLimit,
  addSecurityHeaders,
  validateRequestBody,
  signInSchema,
  connectMongo,
} from '@aizako/core-lib/server';

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitResult = requireRateLimit(request);
    if (!rateLimitResult.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: rateLimitResult.error },
        { status: rateLimitResult.status! }
      ));
    }

    // Connect to database
    await connectMongo();

    // Validate request body
    const validation = await validateRequestBody(request, signInSchema);
    if (!validation.success) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Validation failed', details: validation.errors },
        { status: 400 }
      ));
    }

    const { email, password } = validation.data;

    // Find user
    const user = await User.findOne({ email }).populate('defaultTenantId');
    if (!user) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      ));
    }

    // Check user status
    if (user.status === 'suspended') {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Account suspended. Please contact support.' },
        { status: 403 }
      ));
    }

    if (user.status === 'invited') {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Account not activated. Please check your email for invitation.' },
        { status: 403 }
      ));
    }

    // Verify password
    const isPasswordValid = await verifyPassword(password, user.passwordHash);
    if (!isPasswordValid) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      ));
    }

    // Get user's membership in default tenant
    const membership = await Membership.findOne({
      userId: user._id,
      tenantId: user.defaultTenantId,
    });

    if (!membership) {
      return addSecurityHeaders(NextResponse.json(
        { error: 'No tenant access found' },
        { status: 403 }
      ));
    }

    // Create session cookie - ensure we get proper ObjectId strings
    const sessionCookie = createSessionCookie({
      userId: user._id.toString(),
      tenantId: user.defaultTenantId._id ? user.defaultTenantId._id.toString() : user.defaultTenantId.toString(),
    });

    // Update last login
    user.lastLoginAt = new Date();
    await user.save();

    // Return success response with cookie
    const response = NextResponse.json({
      success: true,
      user: {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        status: user.status,
      },
      tenant: {
        id: user.defaultTenantId._id,
        name: user.defaultTenantId.name,
        plan: user.defaultTenantId.plan,
        modules: user.defaultTenantId.modules,
      },
      membership: {
        role: membership.role,
        joinedAt: membership.joinedAt,
      },
    });

    response.headers.set('Set-Cookie', sessionCookie);
    return addSecurityHeaders(response);
  } catch (error) {
    console.error('Signin error:', error);
    return addSecurityHeaders(NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    ));
  }
}

export async function OPTIONS() {
  return addSecurityHeaders(new NextResponse(null, { 
    status: 200,
    headers: {
      'Allow': 'POST',
    },
  }));
}