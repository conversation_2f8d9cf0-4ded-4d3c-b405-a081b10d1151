'use client'

import { useState, useRef, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';

export default function TenantSwitcher() {
  const { currentTenant, memberships, switchTenant, isLoading, user } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [switchingTo, setSwitchingTo] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleTenantSwitch = async (tenantId: string) => {
    if (tenantId === currentTenant?.id) {
      setIsOpen(false);
      return;
    }

    setSwitchingTo(tenantId);
    try {
      await switchTenant(tenantId);
      setIsOpen(false);
      // Refresh the page to ensure all data is updated for the new tenant
      window.location.reload();
    } catch (error) {
      console.error('Failed to switch tenant:', error);
    } finally {
      setSwitchingTo(null);
    }
  };

  // Get tenant details for each membership
  const availableTenants = memberships.map(membership => {
    // In a real implementation, you'd populate tenant details from the API
    // For now, we'll show tenant IDs and roles
    return {
      id: membership.tenant_id,
      name: membership.tenant_id === currentTenant?.id ? currentTenant.name : `Tenant ${membership.tenant_id.slice(-4)}`,
      role: membership.role,
      isCurrent: membership.tenant_id === currentTenant?.id,
    };
  });

  if (!currentTenant || memberships.length <= 1) {
    return null; // Don't show switcher if user only has access to one tenant
  }

  return (
    <div className="relative inline-block text-left" ref={dropdownRef}>
      <div>
        <button
          type="button"
          className="inline-flex items-center justify-between w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          onClick={() => setIsOpen(!isOpen)}
          disabled={isLoading}
          aria-haspopup="true"
          aria-expanded={isOpen}
        >
          <div className="flex items-center">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
              <span className="text-blue-600 font-semibold text-sm">
                {currentTenant.name.charAt(0).toUpperCase()}
              </span>
            </div>
            <div className="text-left">
              <div className="text-sm font-medium text-gray-900 truncate max-w-32">
                {currentTenant.name}
              </div>
              <div className="text-xs text-gray-500 capitalize">
                {memberships.find(m => m.tenant_id === currentTenant.id)?.role || 'member'}
              </div>
            </div>
          </div>
          <svg
            className={`-mr-1 ml-2 h-5 w-5 transition-transform ${isOpen ? 'rotate-180' : ''}`}
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </button>
      </div>

      {isOpen && (
        <div className="origin-top-right absolute right-0 mt-2 w-72 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
          <div className="py-1" role="menu" aria-orientation="vertical">
            <div className="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-200">
              Switch Organization
            </div>
            
            {availableTenants.map((tenant) => (
              <button
                key={tenant.id}
                onClick={() => handleTenantSwitch(tenant.id)}
                disabled={switchingTo === tenant.id}
                className={`${
                  tenant.isCurrent
                    ? 'bg-blue-50 text-blue-700'
                    : 'text-gray-900 hover:bg-gray-100'
                } group flex items-center w-full px-4 py-3 text-sm transition-colors ${
                  switchingTo === tenant.id ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                role="menuitem"
              >
                <div className="flex items-center flex-1">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                    tenant.isCurrent ? 'bg-blue-100' : 'bg-gray-100'
                  }`}>
                    <span className={`font-semibold text-sm ${
                      tenant.isCurrent ? 'text-blue-600' : 'text-gray-600'
                    }`}>
                      {tenant.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div className="flex-1 text-left">
                    <div className="text-sm font-medium truncate">
                      {tenant.name}
                    </div>
                    <div className={`text-xs capitalize ${
                      tenant.isCurrent ? 'text-blue-500' : 'text-gray-500'
                    }`}>
                      {tenant.role}
                    </div>
                  </div>
                  {switchingTo === tenant.id && (
                    <div className="ml-2">
                      <svg className="animate-spin h-4 w-4 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    </div>
                  )}
                  {tenant.isCurrent && (
                    <div className="ml-2">
                      <svg className="h-4 w-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}
                </div>
              </button>
            ))}

            <div className="border-t border-gray-200 pt-1">
              <div className="px-4 py-2 text-xs text-gray-500">
                Signed in as <span className="font-medium">{user?.first_name} {user?.last_name}</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}