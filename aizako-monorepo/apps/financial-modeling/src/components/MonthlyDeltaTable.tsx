'use client';

import type { MonthlyComparisonRow } from '@/lib/power.monthly';
import { formatMonthlyDelta, fmtMonthlyNumber } from '@/lib/power.monthly';

interface MonthlyDeltaTableProps {
  rows: MonthlyComparisonRow[];
  currency: string;
}

export function MonthlyDeltaTable({ rows, currency }: MonthlyDeltaTableProps) {
  if (!rows || rows.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        No monthly data available
      </div>
    );
  }

  const formatDelta = (delta: number) => {
    const formatted = formatMonthlyDelta(delta);
    return {
      text: formatted.formatted,
      className: formatted.isZero 
        ? 'text-gray-500' 
        : formatted.isPositive 
          ? 'text-green-600' 
          : 'text-red-600'
    };
  };

  return (
    <div className="overflow-x-auto">
      <table className="w-full text-sm border-collapse">
        {/* Main header */}
        <thead className="sticky top-0 bg-white">
          <tr className="border-b border-gray-200">
            <th className="text-left py-2 px-2 text-sm font-medium text-gray-900 min-w-[60px]">
              Month
            </th>
            <th colSpan={3} className="text-center py-2 px-2 text-sm font-medium text-gray-900 border-l border-gray-200">
              Revenue ({currency})
            </th>
            <th colSpan={3} className="text-center py-2 px-2 text-sm font-medium text-gray-900 border-l border-gray-200">
              Net Income ({currency})
            </th>
            <th colSpan={3} className="text-center py-2 px-2 text-sm font-medium text-gray-900 border-l border-gray-200">
              CFO ({currency})
            </th>
            <th colSpan={3} className="text-center py-2 px-2 text-sm font-medium text-gray-900 border-l border-gray-200">
              End Cash ({currency})
            </th>
          </tr>
          {/* Sub-header */}
          <tr className="border-b border-gray-300 bg-gray-50">
            <th className="py-2 px-2"></th>
            {/* Revenue columns */}
            <th className="text-right py-2 px-2 text-xs font-medium text-gray-700 min-w-[80px] border-l border-gray-200">
              Base
            </th>
            <th className="text-right py-2 px-2 text-xs font-medium text-gray-700 min-w-[80px]">
              After
            </th>
            <th className="text-right py-2 px-2 text-xs font-medium text-gray-700 min-w-[80px]">
              Δ
            </th>
            {/* Net Income columns */}
            <th className="text-right py-2 px-2 text-xs font-medium text-gray-700 min-w-[80px] border-l border-gray-200">
              Base
            </th>
            <th className="text-right py-2 px-2 text-xs font-medium text-gray-700 min-w-[80px]">
              After
            </th>
            <th className="text-right py-2 px-2 text-xs font-medium text-gray-700 min-w-[80px]">
              Δ
            </th>
            {/* CFO columns */}
            <th className="text-right py-2 px-2 text-xs font-medium text-gray-700 min-w-[80px] border-l border-gray-200">
              Base
            </th>
            <th className="text-right py-2 px-2 text-xs font-medium text-gray-700 min-w-[80px]">
              After
            </th>
            <th className="text-right py-2 px-2 text-xs font-medium text-gray-700 min-w-[80px]">
              Δ
            </th>
            {/* End Cash columns */}
            <th className="text-right py-2 px-2 text-xs font-medium text-gray-700 min-w-[80px] border-l border-gray-200">
              Base
            </th>
            <th className="text-right py-2 px-2 text-xs font-medium text-gray-700 min-w-[80px]">
              After
            </th>
            <th className="text-right py-2 px-2 text-xs font-medium text-gray-700 min-w-[80px]">
              Δ
            </th>
          </tr>
        </thead>
        
        <tbody className="divide-y divide-gray-100">
          {rows.map((row) => {
            const revDelta = formatDelta(row.revenue.delta);
            const niDelta = formatDelta(row.netInc.delta);
            const cfoDelta = formatDelta(row.cfo.delta);
            const cashDelta = formatDelta(row.endCash.delta);
            
            return (
              <tr key={row.month} className="hover:bg-gray-50">
                {/* Month */}
                <td className="py-2 px-2 text-left font-medium text-gray-900">
                  M{row.month}
                </td>
                
                {/* Revenue columns */}
                <td className="py-2 px-2 text-right font-mono text-xs text-gray-900 border-l border-gray-200">
                  {fmtMonthlyNumber(row.revenue.base)}
                </td>
                <td className="py-2 px-2 text-right font-mono text-xs text-gray-900">
                  {fmtMonthlyNumber(row.revenue.after)}
                </td>
                <td className={`py-2 px-2 text-right font-mono text-xs font-medium ${revDelta.className}`}>
                  {revDelta.text}
                </td>
                
                {/* Net Income columns */}
                <td className="py-2 px-2 text-right font-mono text-xs text-gray-900 border-l border-gray-200">
                  {fmtMonthlyNumber(row.netInc.base)}
                </td>
                <td className="py-2 px-2 text-right font-mono text-xs text-gray-900">
                  {fmtMonthlyNumber(row.netInc.after)}
                </td>
                <td className={`py-2 px-2 text-right font-mono text-xs font-medium ${niDelta.className}`}>
                  {niDelta.text}
                </td>
                
                {/* CFO columns */}
                <td className="py-2 px-2 text-right font-mono text-xs text-gray-900 border-l border-gray-200">
                  {fmtMonthlyNumber(row.cfo.base)}
                </td>
                <td className="py-2 px-2 text-right font-mono text-xs text-gray-900">
                  {fmtMonthlyNumber(row.cfo.after)}
                </td>
                <td className={`py-2 px-2 text-right font-mono text-xs font-medium ${cfoDelta.className}`}>
                  {cfoDelta.text}
                </td>
                
                {/* End Cash columns */}
                <td className="py-2 px-2 text-right font-mono text-xs text-gray-900 border-l border-gray-200">
                  {fmtMonthlyNumber(row.endCash.base)}
                </td>
                <td className="py-2 px-2 text-right font-mono text-xs text-gray-900">
                  {fmtMonthlyNumber(row.endCash.after)}
                </td>
                <td className={`py-2 px-2 text-right font-mono text-xs font-medium ${cashDelta.className}`}>
                  {cashDelta.text}
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
}