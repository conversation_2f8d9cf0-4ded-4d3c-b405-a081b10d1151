'use client'

import React from 'react'
import { X, Info, Calculator } from 'lucide-react'
import { FormulaExplanation, CellReference } from '@/lib/engine/explain'

interface ExplainDrawerProps {
  isOpen: boolean
  onClose: () => void
  explanation: FormulaExplanation | null
  cellReference: CellReference | null
}

export function ExplainDrawer({ isOpen, onClose, explanation, cellReference }: ExplainDrawerProps) {
  if (!isOpen || !explanation || !cellReference) {
    return null
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value)
  }

  const formatPercent = (value: number) => {
    return `${value.toFixed(2)}%`
  }

  const formatValue = (value: number, source: string) => {
    if (source.includes('%') || source.includes('Rate') || source.includes('Margin')) {
      return formatPercent(value)
    }
    return formatCurrency(value)
  }

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={onClose}
      />
      
      {/* Drawer */}
      <div className="fixed right-0 top-0 h-full w-96 bg-white shadow-xl z-50 overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-2">
              <Calculator className="h-5 w-5 text-blue-600" />
              <h2 className="text-lg font-semibold text-gray-900">Formula Explanation</h2>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          </div>

          {/* Cell Reference */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <div className="text-sm font-medium text-blue-900 mb-1">
              {cellReference.statement.toUpperCase()} - Period {cellReference.period}
            </div>
            <div className="text-lg font-bold text-blue-800 capitalize">
              {cellReference.field.replace(/_/g, ' ')}
            </div>
          </div>

          {/* Formula */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
              <Info className="h-4 w-4" />
              Formula
            </h3>
            <div className="p-3 bg-gray-100 rounded-lg font-mono text-sm text-gray-800">
              {explanation.formula}
            </div>
          </div>

          {/* Description */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Description</h3>
            <p className="text-sm text-gray-600 leading-relaxed">
              {explanation.description}
            </p>
          </div>

          {/* Inputs */}
          {explanation.inputs.length > 0 && (
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-3">Inputs</h3>
              <div className="space-y-3">
                {explanation.inputs.map((input, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {input.name}
                      </div>
                      <div className="text-xs text-gray-500">
                        Source: {input.source}
                      </div>
                    </div>
                    <div className="text-sm font-mono text-gray-800">
                      {formatValue(input.value, input.name)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Calculation */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Calculation</h3>
            <div className="p-3 bg-green-50 rounded-lg">
              <div className="font-mono text-sm text-green-800">
                {explanation.calculation}
              </div>
            </div>
          </div>

          {/* Tips */}
          <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
            <div className="flex items-start gap-2">
              <Info className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-yellow-800">
                <div className="font-medium mb-1">Tips</div>
                <ul className="text-xs space-y-1">
                  <li>• Click any cell in the statements to see its formula</li>
                  <li>• All calculations are deterministic - no AI involved</li>
                  <li>• Values update automatically when assumptions change</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
