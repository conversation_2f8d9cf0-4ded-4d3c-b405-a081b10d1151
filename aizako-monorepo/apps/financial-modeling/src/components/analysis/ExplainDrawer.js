'use client';
import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState } from 'react';
import { X, Info, Calculator } from 'lucide-react';
export function ExplainDrawer({ isOpen, onClose, explanation, cellReference }) {
    if (!isOpen || !explanation || !cellReference) {
        return null;
    }
    const formatCurrency = (value) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(value);
    };
    const formatPercent = (value) => {
        return `${value.toFixed(2)}%`;
    };
    const formatValue = (value, source) => {
        if (source.includes('%') || source.includes('Rate') || source.includes('Margin')) {
            return formatPercent(value);
        }
        return formatCurrency(value);
    };
    return (_jsxs(_Fragment, { children: [_jsx("div", { className: "fixed inset-0 bg-black bg-opacity-50 z-40", onClick: onClose }), _jsx("div", { className: "fixed right-0 top-0 h-full w-96 bg-white shadow-xl z-50 overflow-y-auto", children: _jsxs("div", { className: "p-6", children: [_jsxs("div", { className: "flex items-center justify-between mb-6", children: [_jsxs("div", { className: "flex items-center gap-2", children: [_jsx(Calculator, { className: "h-5 w-5 text-blue-600" }), _jsx("h2", { className: "text-lg font-semibold text-gray-900", children: "Formula Explanation" })] }), _jsx("button", { onClick: onClose, className: "p-2 hover:bg-gray-100 rounded-lg transition-colors", children: _jsx(X, { className: "h-4 w-4" }) })] }), _jsxs("div", { className: "mb-6 p-4 bg-blue-50 rounded-lg", children: [_jsxs("div", { className: "text-sm font-medium text-blue-900 mb-1", children: [cellReference.statement.toUpperCase(), " - Period ", cellReference.period] }), _jsx("div", { className: "text-lg font-bold text-blue-800 capitalize", children: cellReference.field.replace(/_/g, ' ') })] }), _jsxs("div", { className: "mb-6", children: [_jsxs("h3", { className: "text-sm font-medium text-gray-700 mb-2 flex items-center gap-2", children: [_jsx(Info, { className: "h-4 w-4" }), "Formula"] }), _jsx("div", { className: "p-3 bg-gray-100 rounded-lg font-mono text-sm text-gray-800", children: explanation.formula })] }), _jsxs("div", { className: "mb-6", children: [_jsx("h3", { className: "text-sm font-medium text-gray-700 mb-2", children: "Description" }), _jsx("p", { className: "text-sm text-gray-600 leading-relaxed", children: explanation.description })] }), explanation.inputs.length > 0 && (_jsxs("div", { className: "mb-6", children: [_jsx("h3", { className: "text-sm font-medium text-gray-700 mb-3", children: "Inputs" }), _jsx("div", { className: "space-y-3", children: explanation.inputs.map((input, index) => (_jsxs("div", { className: "flex items-center justify-between p-3 bg-gray-50 rounded-lg", children: [_jsxs("div", { children: [_jsx("div", { className: "text-sm font-medium text-gray-900", children: input.name }), _jsxs("div", { className: "text-xs text-gray-500", children: ["Source: ", input.source] })] }), _jsx("div", { className: "text-sm font-mono text-gray-800", children: formatValue(input.value, input.name) })] }, index))) })] })), _jsxs("div", { className: "mb-6", children: [_jsx("h3", { className: "text-sm font-medium text-gray-700 mb-2", children: "Calculation" }), _jsx("div", { className: "p-3 bg-green-50 rounded-lg", children: _jsx("div", { className: "font-mono text-sm text-green-800", children: explanation.calculation }) })] }), _jsx("div", { className: "p-4 bg-yellow-50 rounded-lg border border-yellow-200", children: _jsxs("div", { className: "flex items-start gap-2", children: [_jsx(Info, { className: "h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" }), _jsxs("div", { className: "text-sm text-yellow-800", children: [_jsx("div", { className: "font-medium mb-1", children: "Tips" }), _jsxs("ul", { className: "text-xs space-y-1", children: [_jsx("li", { children: "\u2022 Click any cell in the statements to see its formula" }), _jsx("li", { children: "\u2022 All calculations are deterministic - no AI involved" }), _jsx("li", { children: "\u2022 Values update automatically when assumptions change" })] })] })] }) })] }) })] }));
}
// Hook for managing explain drawer state
export function useExplainDrawer() {
    const [isOpen, setIsOpen] = useState(false);
    const [explanation, setExplanation] = useState(null);
    const [cellReference, setCellReference] = useState(null);
    const openExplain = (newExplanation, newCellReference) => {
        setExplanation(newExplanation);
        setCellReference(newCellReference);
        setIsOpen(true);
    };
    const closeExplain = () => {
        setIsOpen(false);
        // Keep explanation and cellReference for smooth closing animation
        setTimeout(() => {
            setExplanation(null);
            setCellReference(null);
        }, 300);
    };
    return {
        isOpen,
        explanation,
        cellReference,
        openExplain,
        closeExplain,
    };
}
