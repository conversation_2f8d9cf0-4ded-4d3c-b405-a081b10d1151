'use client'

import { useState, useEffect } from 'react'
import { TrendingUp, TrendingDown, DollarSign, Clock, Info } from 'lucide-react'
import { Config } from '@/lib/engine/types'
import { runPowerOf1Analysis, getPowerOf1Summary, formatCurrency, formatPercent, formatRunwayChange } from '@/lib/engine/powerof1'

interface PowerOf1PanelProps {
  config: Config
  isOpen: boolean
  onClose: () => void
}

export function PowerOf1Panel({ config, isOpen, onClose }: PowerOf1PanelProps) {
  type SummaryItem = {
    name: string;
    description: string;
    ebitda_impact: number;
    cash_impact: number;
    runway_impact: number;
    ebitda_impact_pct: number;
  }

  const [analysis, setAnalysis] = useState<{ result: ReturnType<typeof runPowerOf1Analysis>; summary: SummaryItem[] } | null>(null)
  const [loading, setLoading] = useState(false)
  const [selectedScenario, setSelectedScenario] = useState<string | null>(null)

  useEffect(() => {
    if (isOpen && config) {
      void runAnalysis()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, config])


  const runAnalysis = async () => {
    setLoading(true)
    try {
      // Run Power of 1 analysis
      const result = runPowerOf1Analysis(config)
      const summary = getPowerOf1Summary(result)
      setAnalysis({ result, summary })
    } catch (error) {
      console.error('Error running Power of 1 analysis:', error)
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) {
    return null
  }

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={onClose}
      />

      {/* Panel */}
      <div className="fixed right-0 top-0 h-full w-[500px] bg-white shadow-xl z-50 overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              <h2 className="text-lg font-semibold text-gray-900">Power of 1 Analysis</h2>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              ×
            </button>
          </div>

          {/* Description */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <div className="flex items-start gap-2">
              <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-blue-800">
                <div className="font-medium mb-1">What is Power of 1?</div>
                <p>
                  See how small 1% changes in key drivers impact your 12-month EBITDA,
                  cash position, and runway. Helps identify which levers have the biggest impact.
                </p>
              </div>
            </div>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                <div className="text-sm text-gray-600">Running sensitivity analysis...</div>
              </div>
            </div>
          ) : analysis ? (
            <div className="space-y-6">
              {/* Summary Cards */}
              <div className="grid grid-cols-1 gap-4">
                {analysis.summary.map((scenario: SummaryItem) => (
                  <div
                    key={scenario.name}
                    className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                      selectedScenario === scenario.name
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedScenario(
                      selectedScenario === scenario.name ? null : scenario.name
                    )}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="font-medium text-gray-900">{scenario.name}</div>
                      <div className="flex items-center gap-1">
                        {scenario.ebitda_impact >= 0 ? (
                          <TrendingUp className="h-4 w-4 text-green-600" />
                        ) : (
                          <TrendingDown className="h-4 w-4 text-red-600" />
                        )}
                        <span className={`text-sm font-medium ${
                          scenario.ebitda_impact >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {formatCurrency(scenario.ebitda_impact)}
                        </span>
                      </div>
                    </div>

                    <div className="text-xs text-gray-600 mb-3">
                      {scenario.description}
                    </div>

                    <div className="grid grid-cols-3 gap-3 text-xs">
                      <div className="text-center">
                        <div className="flex items-center justify-center gap-1 mb-1">
                          <DollarSign className="h-3 w-3 text-gray-400" />
                          <span className="text-gray-500">EBITDA</span>
                        </div>
                        <div className={`font-medium ${
                          scenario.ebitda_impact >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {formatPercent(scenario.ebitda_impact_pct)}
                        </div>
                      </div>

                      <div className="text-center">
                        <div className="flex items-center justify-center gap-1 mb-1">
                          <DollarSign className="h-3 w-3 text-gray-400" />
                          <span className="text-gray-500">Cash</span>
                        </div>
                        <div className={`font-medium ${
                          scenario.cash_impact >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {formatCurrency(scenario.cash_impact)}
                        </div>
                      </div>

                      <div className="text-center">
                        <div className="flex items-center justify-center gap-1 mb-1">
                          <Clock className="h-3 w-3 text-gray-400" />
                          <span className="text-gray-500">Runway</span>
                        </div>
                        <div className={`font-medium ${
                          scenario.runway_impact >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {formatRunwayChange(scenario.runway_impact)}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Detailed View */}
              {selectedScenario && (
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h3 className="font-medium text-gray-900 mb-3">
                    Detailed Impact: {selectedScenario}
                  </h3>

                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">12-Month EBITDA Change</span>
                      <span className="font-medium">
                        {formatCurrency(analysis.summary.find((s: SummaryItem) => s.name === selectedScenario)?.ebitda_impact || 0)}
                      </span>
                    </div>

                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">12-Month Cash Impact</span>
                      <span className="font-medium">
                        {formatCurrency(analysis.summary.find((s: SummaryItem) => s.name === selectedScenario)?.cash_impact || 0)}
                      </span>
                    </div>

                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Runway Change</span>
                      <span className="font-medium">
                        {formatRunwayChange(analysis.summary.find((s: SummaryItem) => s.name === selectedScenario)?.runway_impact || 0)}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {/* Key Insights */}
              <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                <div className="flex items-start gap-2">
                  <Info className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-yellow-800">
                    <div className="font-medium mb-2">Key Insights</div>
                    <ul className="space-y-1 text-xs">
                      <li>• <strong>Top driver:</strong> {analysis.summary[0]?.name} has the biggest impact</li>
                      <li>• <strong>Focus area:</strong> {
                        analysis.summary[0]?.ebitda_impact > 0
                          ? 'Prioritize this positive driver'
                          : 'Address this risk factor'
                      }</li>
                      <li>• <strong>Working capital:</strong> {
                        analysis.summary.filter((s: SummaryItem) => s.name.includes('DSO') || s.name.includes('DPO') || s.name.includes('DIO')).length > 0
                          ? 'WC management can significantly impact cash'
                          : 'Limited working capital impact'
                      }</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-3">
                <button
                  onClick={runAnalysis}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  Refresh Analysis
                </button>
                <button
                  onClick={onClose}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-500 mb-4">No analysis available</div>
              <button
                onClick={runAnalysis}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                Run Analysis
              </button>
            </div>
          )}
        </div>
      </div>
    </>
  )
}
