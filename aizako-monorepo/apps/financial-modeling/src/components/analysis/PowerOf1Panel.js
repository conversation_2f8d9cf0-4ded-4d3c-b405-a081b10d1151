'use client';
import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, DollarSign, Clock, Info } from 'lucide-react';
import { runPowerOf1Analysis, getPowerOf1Summary, formatCurrency, formatPercent, formatRunwayChange } from '@/lib/engine/powerof1';
export function PowerOf1Panel({ config, isOpen, onClose }) {
    const [analysis, setAnalysis] = useState(null);
    const [loading, setLoading] = useState(false);
    const [selectedScenario, setSelectedScenario] = useState(null);
    useEffect(() => {
        if (isOpen && config) {
            void runAnalysis();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isOpen, config]);
    const runAnalysis = async () => {
        setLoading(true);
        try {
            // Run Power of 1 analysis
            const result = runPowerOf1Analysis(config);
            const summary = getPowerOf1Summary(result);
            setAnalysis({ result, summary });
        }
        catch (error) {
            console.error('Error running Power of 1 analysis:', error);
        }
        finally {
            setLoading(false);
        }
    };
    if (!isOpen) {
        return null;
    }
    return (_jsxs(_Fragment, { children: [_jsx("div", { className: "fixed inset-0 bg-black bg-opacity-50 z-40", onClick: onClose }), _jsx("div", { className: "fixed right-0 top-0 h-full w-[500px] bg-white shadow-xl z-50 overflow-y-auto", children: _jsxs("div", { className: "p-6", children: [_jsxs("div", { className: "flex items-center justify-between mb-6", children: [_jsxs("div", { className: "flex items-center gap-2", children: [_jsx(TrendingUp, { className: "h-5 w-5 text-green-600" }), _jsx("h2", { className: "text-lg font-semibold text-gray-900", children: "Power of 1 Analysis" })] }), _jsx("button", { onClick: onClose, className: "p-2 hover:bg-gray-100 rounded-lg transition-colors", children: "\u00D7" })] }), _jsx("div", { className: "mb-6 p-4 bg-blue-50 rounded-lg", children: _jsxs("div", { className: "flex items-start gap-2", children: [_jsx(Info, { className: "h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" }), _jsxs("div", { className: "text-sm text-blue-800", children: [_jsx("div", { className: "font-medium mb-1", children: "What is Power of 1?" }), _jsx("p", { children: "See how small 1% changes in key drivers impact your 12-month EBITDA, cash position, and runway. Helps identify which levers have the biggest impact." })] })] }) }), loading ? (_jsx("div", { className: "flex items-center justify-center py-12", children: _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2" }), _jsx("div", { className: "text-sm text-gray-600", children: "Running sensitivity analysis..." })] }) })) : analysis ? (_jsxs("div", { className: "space-y-6", children: [_jsx("div", { className: "grid grid-cols-1 gap-4", children: analysis.summary.map((scenario) => (_jsxs("div", { className: `p-4 rounded-lg border-2 cursor-pointer transition-all ${selectedScenario === scenario.name
                                            ? 'border-blue-500 bg-blue-50'
                                            : 'border-gray-200 hover:border-gray-300'}`, onClick: () => setSelectedScenario(selectedScenario === scenario.name ? null : scenario.name), children: [_jsxs("div", { className: "flex items-center justify-between mb-2", children: [_jsx("div", { className: "font-medium text-gray-900", children: scenario.name }), _jsxs("div", { className: "flex items-center gap-1", children: [scenario.ebitda_impact >= 0 ? (_jsx(TrendingUp, { className: "h-4 w-4 text-green-600" })) : (_jsx(TrendingDown, { className: "h-4 w-4 text-red-600" })), _jsx("span", { className: `text-sm font-medium ${scenario.ebitda_impact >= 0 ? 'text-green-600' : 'text-red-600'}`, children: formatCurrency(scenario.ebitda_impact) })] })] }), _jsx("div", { className: "text-xs text-gray-600 mb-3", children: scenario.description }), _jsxs("div", { className: "grid grid-cols-3 gap-3 text-xs", children: [_jsxs("div", { className: "text-center", children: [_jsxs("div", { className: "flex items-center justify-center gap-1 mb-1", children: [_jsx(DollarSign, { className: "h-3 w-3 text-gray-400" }), _jsx("span", { className: "text-gray-500", children: "EBITDA" })] }), _jsx("div", { className: `font-medium ${scenario.ebitda_impact >= 0 ? 'text-green-600' : 'text-red-600'}`, children: formatPercent(scenario.ebitda_impact_pct) })] }), _jsxs("div", { className: "text-center", children: [_jsxs("div", { className: "flex items-center justify-center gap-1 mb-1", children: [_jsx(DollarSign, { className: "h-3 w-3 text-gray-400" }), _jsx("span", { className: "text-gray-500", children: "Cash" })] }), _jsx("div", { className: `font-medium ${scenario.cash_impact >= 0 ? 'text-green-600' : 'text-red-600'}`, children: formatCurrency(scenario.cash_impact) })] }), _jsxs("div", { className: "text-center", children: [_jsxs("div", { className: "flex items-center justify-center gap-1 mb-1", children: [_jsx(Clock, { className: "h-3 w-3 text-gray-400" }), _jsx("span", { className: "text-gray-500", children: "Runway" })] }), _jsx("div", { className: `font-medium ${scenario.runway_impact >= 0 ? 'text-green-600' : 'text-red-600'}`, children: formatRunwayChange(scenario.runway_impact) })] })] })] }, scenario.name))) }), selectedScenario && (_jsxs("div", { className: "p-4 bg-gray-50 rounded-lg", children: [_jsxs("h3", { className: "font-medium text-gray-900 mb-3", children: ["Detailed Impact: ", selectedScenario] }), _jsxs("div", { className: "space-y-3", children: [_jsxs("div", { className: "flex justify-between items-center", children: [_jsx("span", { className: "text-sm text-gray-600", children: "12-Month EBITDA Change" }), _jsx("span", { className: "font-medium", children: formatCurrency(analysis.summary.find((s) => s.name === selectedScenario)?.ebitda_impact || 0) })] }), _jsxs("div", { className: "flex justify-between items-center", children: [_jsx("span", { className: "text-sm text-gray-600", children: "12-Month Cash Impact" }), _jsx("span", { className: "font-medium", children: formatCurrency(analysis.summary.find((s) => s.name === selectedScenario)?.cash_impact || 0) })] }), _jsxs("div", { className: "flex justify-between items-center", children: [_jsx("span", { className: "text-sm text-gray-600", children: "Runway Change" }), _jsx("span", { className: "font-medium", children: formatRunwayChange(analysis.summary.find((s) => s.name === selectedScenario)?.runway_impact || 0) })] })] })] })), _jsx("div", { className: "p-4 bg-yellow-50 rounded-lg border border-yellow-200", children: _jsxs("div", { className: "flex items-start gap-2", children: [_jsx(Info, { className: "h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" }), _jsxs("div", { className: "text-sm text-yellow-800", children: [_jsx("div", { className: "font-medium mb-2", children: "Key Insights" }), _jsxs("ul", { className: "space-y-1 text-xs", children: [_jsxs("li", { children: ["\u2022 ", _jsx("strong", { children: "Top driver:" }), " ", analysis.summary[0]?.name, " has the biggest impact"] }), _jsxs("li", { children: ["\u2022 ", _jsx("strong", { children: "Focus area:" }), " ", analysis.summary[0]?.ebitda_impact > 0
                                                                        ? 'Prioritize this positive driver'
                                                                        : 'Address this risk factor'] }), _jsxs("li", { children: ["\u2022 ", _jsx("strong", { children: "Working capital:" }), " ", analysis.summary.filter((s) => s.name.includes('DSO') || s.name.includes('DPO') || s.name.includes('DIO')).length > 0
                                                                        ? 'WC management can significantly impact cash'
                                                                        : 'Limited working capital impact'] })] })] })] }) }), _jsxs("div", { className: "flex gap-3", children: [_jsx("button", { onClick: runAnalysis, className: "flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors", children: "Refresh Analysis" }), _jsx("button", { onClick: onClose, className: "px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors", children: "Close" })] })] })) : (_jsxs("div", { className: "text-center py-12", children: [_jsx("div", { className: "text-gray-500 mb-4", children: "No analysis available" }), _jsx("button", { onClick: runAnalysis, className: "bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors", children: "Run Analysis" })] }))] }) })] }));
}
// Hook for managing Power of 1 panel state
export function usePowerOf1Panel() {
    const [isOpen, setIsOpen] = useState(false);
    const openPanel = () => setIsOpen(true);
    const closePanel = () => setIsOpen(false);
    return {
        isOpen,
        openPanel,
        closePanel,
    };
}
