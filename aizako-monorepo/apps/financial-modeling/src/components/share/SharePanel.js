'use client';
import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState, useEffect, useCallback } from 'react';
import { Share, Copy, Trash2, Plus, Clock, Eye, ExternalLink } from 'lucide-react';
export function SharePanel({ scenarioId, scenarioName, isOpen, onClose }) {
    const [shareLinks, setShareLinks] = useState([]);
    const [loading, setLoading] = useState(false);
    const [creating, setCreating] = useState(false);
    const [expiryDays, setExpiryDays] = useState('');
    const fetchShareLinks = useCallback(async () => {
        setLoading(true);
        try {
            const response = await fetch(`/api/share/create?scenarioId=${scenarioId}`);
            if (response.ok) {
                const links = await response.json();
                setShareLinks(links);
            }
        }
        catch (error) {
            console.error('Error fetching share links:', error);
        }
        finally {
            setLoading(false);
        }
    }, [scenarioId]);
    useEffect(() => {
        if (isOpen) {
            void fetchShareLinks();
        }
    }, [isOpen, fetchShareLinks]);
    const createShareLink = async () => {
        setCreating(true);
        try {
            const response = await fetch('/api/share/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    scenarioId,
                    expiresInDays: expiryDays || null,
                }),
            });
            if (response.ok) {
                const newLink = await response.json();
                setShareLinks(prev => [newLink, ...prev]);
                setExpiryDays('');
            }
            else {
                throw new Error('Failed to create share link');
            }
        }
        catch (error) {
            console.error('Error creating share link:', error);
            alert('Failed to create share link. Please try again.');
        }
        finally {
            setCreating(false);
        }
    };
    const deleteShareLink = async (shareId) => {
        if (!confirm('Are you sure you want to delete this share link?'))
            return;
        try {
            const response = await fetch(`/api/share/create?shareId=${shareId}`, {
                method: 'DELETE',
            });
            if (response.ok) {
                setShareLinks(prev => prev.filter(link => link.id !== shareId));
            }
            else {
                throw new Error('Failed to delete share link');
            }
        }
        catch (error) {
            console.error('Error deleting share link:', error);
            alert('Failed to delete share link. Please try again.');
        }
    };
    const copyToClipboard = async (url) => {
        try {
            await navigator.clipboard.writeText(url);
            // You could add a toast notification here
            alert('Link copied to clipboard!');
        }
        catch (error) {
            console.error('Error copying to clipboard:', error);
            alert('Failed to copy link. Please copy manually.');
        }
    };
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };
    if (!isOpen) {
        return null;
    }
    return (_jsxs(_Fragment, { children: [_jsx("div", { className: "fixed inset-0 bg-black bg-opacity-50 z-40", onClick: onClose }), _jsx("div", { className: "fixed right-0 top-0 h-full w-[500px] bg-white shadow-xl z-50 overflow-y-auto", children: _jsxs("div", { className: "p-6", children: [_jsxs("div", { className: "flex items-center justify-between mb-6", children: [_jsxs("div", { className: "flex items-center gap-2", children: [_jsx(Share, { className: "h-5 w-5 text-blue-600" }), _jsx("h2", { className: "text-lg font-semibold text-gray-900", children: "Share Scenario" })] }), _jsx("button", { onClick: onClose, className: "p-2 hover:bg-gray-100 rounded-lg transition-colors", children: "\u00D7" })] }), _jsxs("div", { className: "mb-6 p-4 bg-blue-50 rounded-lg", children: [_jsx("h3", { className: "font-medium text-blue-900 mb-1", children: scenarioName }), _jsx("p", { className: "text-sm text-blue-700", children: "Create secure, read-only links to share your financial projections with investors, lenders, or team members." })] }), _jsxs("div", { className: "mb-6 p-4 border border-gray-200 rounded-lg", children: [_jsx("h3", { className: "font-medium text-gray-900 mb-3", children: "Create New Share Link" }), _jsxs("div", { className: "mb-4", children: [_jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Expires in (days)" }), _jsx("input", { type: "number", value: expiryDays, onChange: (e) => setExpiryDays(e.target.value ? parseInt(e.target.value) : ''), placeholder: "Never expires (leave empty)", min: "1", max: "365", className: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" }), _jsx("p", { className: "text-xs text-gray-500 mt-1", children: "Leave empty for links that never expire" })] }), _jsxs("button", { onClick: createShareLink, disabled: creating, className: "w-full flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed", children: [_jsx(Plus, { className: "h-4 w-4" }), creating ? 'Creating...' : 'Create Share Link'] })] }), _jsxs("div", { children: [_jsx("h3", { className: "font-medium text-gray-900 mb-3", children: "Existing Share Links" }), loading ? (_jsxs("div", { className: "text-center py-8", children: [_jsx("div", { className: "animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2" }), _jsx("div", { className: "text-sm text-gray-600", children: "Loading share links..." })] })) : shareLinks.length === 0 ? (_jsxs("div", { className: "text-center py-8 text-gray-500", children: [_jsx(Share, { className: "h-8 w-8 mx-auto mb-2 opacity-50" }), _jsx("p", { className: "text-sm", children: "No share links created yet" })] })) : (_jsx("div", { className: "space-y-3", children: shareLinks.map((link) => (_jsxs("div", { className: `p-4 border rounded-lg ${link.isExpired ? 'border-red-200 bg-red-50' : 'border-gray-200'}`, children: [_jsxs("div", { className: "flex items-start justify-between mb-2", children: [_jsxs("div", { className: "flex items-center gap-2", children: [_jsx(Eye, { className: "h-4 w-4 text-gray-400" }), _jsx("span", { className: "text-sm font-medium text-gray-900", children: "Read-only Link" }), link.isExpired && (_jsx("span", { className: "px-2 py-1 text-xs bg-red-100 text-red-800 rounded", children: "Expired" }))] }), _jsxs("div", { className: "flex items-center gap-1", children: [_jsx("button", { onClick: () => copyToClipboard(link.url), className: "p-1 hover:bg-gray-100 rounded text-gray-600 hover:text-gray-900", title: "Copy link", children: _jsx(Copy, { className: "h-4 w-4" }) }), _jsx("button", { onClick: () => window.open(link.url, '_blank'), className: "p-1 hover:bg-gray-100 rounded text-gray-600 hover:text-gray-900", title: "Open link", children: _jsx(ExternalLink, { className: "h-4 w-4" }) }), _jsx("button", { onClick: () => deleteShareLink(link.id), className: "p-1 hover:bg-red-100 rounded text-red-600 hover:text-red-900", title: "Delete link", children: _jsx(Trash2, { className: "h-4 w-4" }) })] })] }), _jsx("div", { className: "text-xs text-gray-600 mb-2", children: _jsxs("div", { className: "flex items-center gap-4", children: [_jsxs("span", { children: ["Created: ", formatDate(link.createdAt)] }), link.expiresAt && (_jsxs("span", { className: "flex items-center gap-1", children: [_jsx(Clock, { className: "h-3 w-3" }), "Expires: ", formatDate(link.expiresAt)] }))] }) }), _jsx("div", { className: "text-xs font-mono text-gray-500 bg-gray-100 p-2 rounded break-all", children: link.url })] }, link.id))) }))] }), _jsx("div", { className: "mt-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200", children: _jsxs("div", { className: "text-sm text-yellow-800", children: [_jsx("div", { className: "font-medium mb-2", children: "Sharing Tips" }), _jsxs("ul", { className: "text-xs space-y-1", children: [_jsx("li", { children: "\u2022 Share links provide read-only access to your projections" }), _jsx("li", { children: "\u2022 Recipients can view statements and export PDF/CSV reports" }), _jsx("li", { children: "\u2022 Links can be revoked at any time by deleting them" }), _jsx("li", { children: "\u2022 Set expiry dates for temporary access" })] })] }) })] }) })] }));
}
// Hook for managing share panel state
export function useSharePanel() {
    const [isOpen, setIsOpen] = useState(false);
    const openPanel = () => setIsOpen(true);
    const closePanel = () => setIsOpen(false);
    return {
        isOpen,
        openPanel,
        closePanel,
    };
}
