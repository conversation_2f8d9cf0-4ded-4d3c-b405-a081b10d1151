'use client'

import { useState, useEffect, useCallback } from 'react'
import { Share, Copy, Trash2, Plus, Clock, Eye, ExternalLink } from 'lucide-react'

interface ShareLink {
  id: string
  token: string
  url: string
  expiresAt: string | null
  createdAt: string
  isExpired: boolean
  scenarioName: string
}

interface SharePanelProps {
  scenarioId: string
  scenarioName: string
  isOpen: boolean
  onClose: () => void
}

export function SharePanel({ scenarioId, scenarioName, isOpen, onClose }: SharePanelProps) {
  const [shareLinks, setShareLinks] = useState<ShareLink[]>([])
  const [loading, setLoading] = useState(false)
  const [creating, setCreating] = useState(false)
  const [expiryDays, setExpiryDays] = useState<number | ''>('')

  const fetchShareLinks = useCallback(async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/share/create?scenarioId=${scenarioId}`)
      if (response.ok) {
        const links = await response.json()
        setShareLinks(links)
      }
    } catch (error) {
      console.error('Error fetching share links:', error)
    } finally {
      setLoading(false)
    }
  }, [scenarioId])

  useEffect(() => {
    if (isOpen) {
      void fetchShareLinks()
    }
  }, [isOpen, fetchShareLinks])



  const createShareLink = async () => {
    setCreating(true)
    try {
      const response = await fetch('/api/share/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          scenarioId,
          expiresInDays: expiryDays || null,
        }),
      })

      if (response.ok) {
        const newLink = await response.json()
        setShareLinks(prev => [newLink, ...prev])
        setExpiryDays('')
      } else {
        throw new Error('Failed to create share link')
      }
    } catch (error) {
      console.error('Error creating share link:', error)
      alert('Failed to create share link. Please try again.')
    } finally {
      setCreating(false)
    }
  }

  const deleteShareLink = async (shareId: string) => {
    if (!confirm('Are you sure you want to delete this share link?')) return

    try {
      const response = await fetch(`/api/share/create?shareId=${shareId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setShareLinks(prev => prev.filter(link => link.id !== shareId))
      } else {
        throw new Error('Failed to delete share link')
      }
    } catch (error) {
      console.error('Error deleting share link:', error)
      alert('Failed to delete share link. Please try again.')
    }
  }

  const copyToClipboard = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url)
      // You could add a toast notification here
      alert('Link copied to clipboard!')
    } catch (error) {
      console.error('Error copying to clipboard:', error)
      alert('Failed to copy link. Please copy manually.')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  if (!isOpen) {
    return null
  }

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={onClose}
      />

      {/* Panel */}
      <div className="fixed right-0 top-0 h-full w-[500px] bg-white shadow-xl z-50 overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-2">
              <Share className="h-5 w-5 text-blue-600" />
              <h2 className="text-lg font-semibold text-gray-900">Share Scenario</h2>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              ×
            </button>
          </div>

          {/* Scenario Info */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-medium text-blue-900 mb-1">{scenarioName}</h3>
            <p className="text-sm text-blue-700">
              Create secure, read-only links to share your financial projections with investors, lenders, or team members.
            </p>
          </div>

          {/* Create New Link */}
          <div className="mb-6 p-4 border border-gray-200 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-3">Create New Share Link</h3>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Expires in (days)
              </label>
              <input
                type="number"
                value={expiryDays}
                onChange={(e) => setExpiryDays(e.target.value ? parseInt(e.target.value) : '')}
                placeholder="Never expires (leave empty)"
                min="1"
                max="365"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <p className="text-xs text-gray-500 mt-1">
                Leave empty for links that never expire
              </p>
            </div>

            <button
              onClick={createShareLink}
              disabled={creating}
              className="w-full flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Plus className="h-4 w-4" />
              {creating ? 'Creating...' : 'Create Share Link'}
            </button>
          </div>

          {/* Existing Links */}
          <div>
            <h3 className="font-medium text-gray-900 mb-3">Existing Share Links</h3>

            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
                <div className="text-sm text-gray-600">Loading share links...</div>
              </div>
            ) : shareLinks.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Share className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No share links created yet</p>
              </div>
            ) : (
              <div className="space-y-3">
                {shareLinks.map((link) => (
                  <div
                    key={link.id}
                    className={`p-4 border rounded-lg ${
                      link.isExpired ? 'border-red-200 bg-red-50' : 'border-gray-200'
                    }`}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Eye className="h-4 w-4 text-gray-400" />
                        <span className="text-sm font-medium text-gray-900">
                          Read-only Link
                        </span>
                        {link.isExpired && (
                          <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">
                            Expired
                          </span>
                        )}
                      </div>
                      <div className="flex items-center gap-1">
                        <button
                          onClick={() => copyToClipboard(link.url)}
                          className="p-1 hover:bg-gray-100 rounded text-gray-600 hover:text-gray-900"
                          title="Copy link"
                        >
                          <Copy className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => window.open(link.url, '_blank')}
                          className="p-1 hover:bg-gray-100 rounded text-gray-600 hover:text-gray-900"
                          title="Open link"
                        >
                          <ExternalLink className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => deleteShareLink(link.id)}
                          className="p-1 hover:bg-red-100 rounded text-red-600 hover:text-red-900"
                          title="Delete link"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>

                    <div className="text-xs text-gray-600 mb-2">
                      <div className="flex items-center gap-4">
                        <span>Created: {formatDate(link.createdAt)}</span>
                        {link.expiresAt && (
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            Expires: {formatDate(link.expiresAt)}
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="text-xs font-mono text-gray-500 bg-gray-100 p-2 rounded break-all">
                      {link.url}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Tips */}
          <div className="mt-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
            <div className="text-sm text-yellow-800">
              <div className="font-medium mb-2">Sharing Tips</div>
              <ul className="text-xs space-y-1">
                <li>• Share links provide read-only access to your projections</li>
                <li>• Recipients can view statements and export PDF/CSV reports</li>
                <li>• Links can be revoked at any time by deleting them</li>
                <li>• Set expiry dates for temporary access</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
