'use client';
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Calculator, TrendingUp, AlertTriangle, Info } from 'lucide-react';
import { calculateDebtSchedule, calculateDebtMetrics, analyzeDebtCovenants, formatDebtMetrics, getDebtSummary } from '@/lib/engine/debt';
export function DebtPanel({ config, projectionResult, onConfigChange }) {
    const [debtSchedule, setDebtSchedule] = useState([]);
    const [debtMetrics, setDebtMetrics] = useState([]);
    const [covenantAnalysis, setCovenantAnalysis] = useState(null);
    const [selectedAmortization, setSelectedAmortization] = useState(config.drivers.debt.amort);
    const [selectedRate, setSelectedRate] = useState(config.drivers.debt.rate_pct);
    const [selectedTerm, setSelectedTerm] = useState(config.drivers.debt.term_months);
    useEffect(() => {
        calculateDebtAnalysis();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [config, projectionResult]);
    const calculateDebtAnalysis = () => {
        // Calculate debt schedule
        const schedule = calculateDebtSchedule(config.drivers.debt.opening, config.drivers.debt.rate_pct, config.drivers.debt.term_months, config.drivers.debt.amort, config.drivers.debt.draws);
        setDebtSchedule(schedule);
        // Calculate debt metrics
        const metrics = calculateDebtMetrics(projectionResult, config);
        setDebtMetrics(metrics);
        // Analyze covenant compliance
        const analysis = analyzeDebtCovenants(metrics);
        setCovenantAnalysis(analysis);
    };
    const handleAmortizationChange = (newAmort) => {
        setSelectedAmortization(newAmort);
        const newConfig = {
            ...config,
            drivers: {
                ...config.drivers,
                debt: {
                    ...config.drivers.debt,
                    amort: newAmort,
                },
            },
        };
        onConfigChange(newConfig);
    };
    const handleRateChange = (newRate) => {
        setSelectedRate(newRate);
        const newConfig = {
            ...config,
            drivers: {
                ...config.drivers,
                debt: {
                    ...config.drivers.debt,
                    rate_pct: newRate,
                },
            },
        };
        onConfigChange(newConfig);
    };
    const handleTermChange = (newTerm) => {
        setSelectedTerm(newTerm);
        const newConfig = {
            ...config,
            drivers: {
                ...config.drivers,
                debt: {
                    ...config.drivers.debt,
                    term_months: newTerm,
                },
            },
        };
        onConfigChange(newConfig);
    };
    const formatCurrency = (value) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(value);
    };
    const debtSummary = getDebtSummary(debtSchedule);
    const formattedMetrics = formatDebtMetrics(debtMetrics.slice(0, 12)); // First 12 months
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "bg-white rounded-lg shadow p-6", children: [_jsxs("div", { className: "flex items-center gap-2 mb-4", children: [_jsx(Calculator, { className: "h-5 w-5 text-blue-600" }), _jsx("h3", { className: "text-lg font-medium text-gray-900", children: "Debt Configuration" })] }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-4", children: [_jsxs("div", { children: [_jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Amortization Type" }), _jsxs("select", { value: selectedAmortization, onChange: (e) => handleAmortizationChange(e.target.value), className: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500", children: [_jsx("option", { value: "annuity", children: "Annuity (Equal Payments)" }), _jsx("option", { value: "interest_only", children: "Interest Only" }), _jsx("option", { value: "bullet", children: "Bullet (Principal at End)" })] })] }), _jsxs("div", { children: [_jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Annual Interest Rate (%)" }), _jsx("input", { type: "number", value: selectedRate, onChange: (e) => handleRateChange(parseFloat(e.target.value) || 0), step: "0.1", min: "0", max: "50", className: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" })] }), _jsxs("div", { children: [_jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Term (Months)" }), _jsx("input", { type: "number", value: selectedTerm, onChange: (e) => handleTermChange(parseInt(e.target.value) || 0), min: "1", max: "360", className: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" })] })] })] }), _jsxs("div", { className: "bg-white rounded-lg shadow p-6", children: [_jsx("h3", { className: "text-lg font-medium text-gray-900 mb-4", children: "Debt Summary" }), _jsxs("div", { className: "grid grid-cols-2 md:grid-cols-4 gap-4", children: [_jsxs("div", { className: "text-center", children: [_jsx("div", { className: "text-2xl font-bold text-blue-600", children: formatCurrency(debtSummary.totalInterest) }), _jsx("div", { className: "text-sm text-gray-600", children: "Total Interest" })] }), _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "text-2xl font-bold text-gray-900", children: formatCurrency(debtSummary.avgMonthlyPayment) }), _jsx("div", { className: "text-sm text-gray-600", children: "Avg Monthly Payment" })] }), _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "text-2xl font-bold text-gray-900", children: formatCurrency(debtSummary.maxMonthlyPayment) }), _jsx("div", { className: "text-sm text-gray-600", children: "Max Monthly Payment" })] }), _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "text-2xl font-bold text-gray-900", children: formatCurrency(config.drivers.debt.opening) }), _jsx("div", { className: "text-sm text-gray-600", children: "Principal Amount" })] })] })] }), covenantAnalysis && (_jsxs("div", { className: "bg-white rounded-lg shadow p-6", children: [_jsxs("div", { className: "flex items-center gap-2 mb-4", children: [_jsx(TrendingUp, { className: "h-5 w-5 text-green-600" }), _jsx("h3", { className: "text-lg font-medium text-gray-900", children: "Covenant Analysis" })] }), covenantAnalysis.violations.length > 0 ? (_jsx("div", { className: "mb-4 p-4 bg-red-50 rounded-lg border border-red-200", children: _jsxs("div", { className: "flex items-start gap-2", children: [_jsx(AlertTriangle, { className: "h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" }), _jsxs("div", { className: "text-sm text-red-800", children: [_jsx("div", { className: "font-medium mb-1", children: "Covenant Violations Detected" }), _jsxs("div", { children: [covenantAnalysis.summary.criticalViolations, " critical violations, first occurring in period ", covenantAnalysis.summary.firstViolationPeriod] })] })] }) })) : (_jsx("div", { className: "mb-4 p-4 bg-green-50 rounded-lg border border-green-200", children: _jsxs("div", { className: "flex items-start gap-2", children: [_jsx(TrendingUp, { className: "h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" }), _jsxs("div", { className: "text-sm text-green-800", children: [_jsx("div", { className: "font-medium", children: "All Covenants Maintained" }), _jsx("div", { children: "No violations detected in the projection period" })] })] }) })), _jsx("div", { className: "overflow-x-auto", children: _jsxs("table", { className: "min-w-full divide-y divide-gray-200", children: [_jsx("thead", { className: "bg-gray-50", children: _jsxs("tr", { children: [_jsx("th", { className: "px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase", children: "Period" }), _jsx("th", { className: "px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase", children: "DSCR" }), _jsx("th", { className: "px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase", children: "ICR" }), _jsx("th", { className: "px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase", children: "Debt Service" })] }) }), _jsx("tbody", { className: "bg-white divide-y divide-gray-200", children: formattedMetrics.slice(0, 6).map((metric, index) => (_jsxs("tr", { className: "hover:bg-gray-50", children: [_jsxs("td", { className: "px-3 py-2 whitespace-nowrap text-sm text-gray-900", children: ["Mo ", metric.period] }), _jsx("td", { className: `px-3 py-2 whitespace-nowrap text-sm text-center font-medium ${metric.dscr < 1.25 && metric.dscr > 0 ? 'text-red-600' : 'text-green-600'}`, children: metric.dscr_formatted }), _jsx("td", { className: `px-3 py-2 whitespace-nowrap text-sm text-center font-medium ${metric.icr < 2.0 && metric.icr > 0 ? 'text-red-600' : 'text-green-600'}`, children: metric.icr_formatted }), _jsx("td", { className: "px-3 py-2 whitespace-nowrap text-sm text-center text-gray-900", children: metric.debt_service_formatted })] }, index))) })] }) })] })), _jsx("div", { className: "p-4 bg-blue-50 rounded-lg border border-blue-200", children: _jsxs("div", { className: "flex items-start gap-2", children: [_jsx(Info, { className: "h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" }), _jsxs("div", { className: "text-sm text-blue-800", children: [_jsx("div", { className: "font-medium mb-2", children: "Debt Metrics Explained" }), _jsxs("ul", { className: "space-y-1 text-xs", children: [_jsxs("li", { children: ["\u2022 ", _jsx("strong", { children: "DSCR:" }), " Debt Service Coverage Ratio - measures ability to service debt (target: >1.25x)"] }), _jsxs("li", { children: ["\u2022 ", _jsx("strong", { children: "ICR:" }), " Interest Coverage Ratio - measures ability to pay interest (target: >2.0x)"] }), _jsxs("li", { children: ["\u2022 ", _jsx("strong", { children: "Annuity:" }), " Equal monthly payments of principal + interest"] }), _jsxs("li", { children: ["\u2022 ", _jsx("strong", { children: "Interest Only:" }), " Pay only interest, principal due at maturity"] }), _jsxs("li", { children: ["\u2022 ", _jsx("strong", { children: "Bullet:" }), " All principal and interest due at maturity"] })] })] })] }) })] }));
}
