'use client';

import { useState, useEffect } from 'react';
import { CheckCircle2, AlertCircle, Info, Sparkles, Loader2 } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';

interface WizardResponse {
  archetype: 'product' | 'service' | 'saas' | 'mixed';
  profileId: string;
  confidence: number;
  prefill: Record<string, unknown>;
  needsConfirmation: string[];
  suggestedRanges: Record<string, [number, number]>;
  notes: string;
}

interface PrefillCardProps {
  wizardResponse: WizardResponse | null;
  onScenarioCreated: (scenarioId: string) => void;
  isComplete: boolean;
}

const TOOLTIPS = {
  'COGS': 'Direct costs to deliver the product (materials, manufacturing, payment fees, shipping). Not rent/marketing.',
  'GrossMargin': 'Percent of revenue left after direct costs. 60% margin = 40% COGS.',
  'Churn': 'Percent of subscribers who cancel each month.',
  'Utilization': 'Percent of working time that is billable to clients.',
  'AOV': 'Average order value - typical price per transaction.',
  'ARPU': 'Average revenue per user per month.',
  'Rate': 'Your charge per hour (or blended hourly rate).',
} as const;

export function PrefillCard({ wizardResponse, onScenarioCreated, isComplete }: PrefillCardProps) {
  const [editableConfig, setEditableConfig] = useState<Record<string, unknown> | null>(null);
  const [isCreatingScenario, setIsCreatingScenario] = useState(false);
  const [scenarioName, setScenarioName] = useState('');
  const { user } = useAuth();

  useEffect(() => {
    if (wizardResponse?.prefill) {
      setEditableConfig(wizardResponse.prefill);
      // Generate a default scenario name
      const businessType = wizardResponse.archetype;
      const date = new Date().toLocaleDateString();
      setScenarioName(`${businessType.charAt(0).toUpperCase() + businessType.slice(1)} Model - ${date}`);
    }
  }, [wizardResponse]);

  const updateConfig = (path: string[], value: unknown) => {
    if (!editableConfig) return;
    
    const newConfig = { ...editableConfig };
    let current: Record<string, unknown> = newConfig;
    
    for (let i = 0; i < path.length - 1; i++) {
      if (!current[path[i]]) current[path[i]] = {};
      current = current[path[i]] as Record<string, unknown>;
    }
    
    current[path[path.length - 1]] = value;
    setEditableConfig(newConfig);
  };

  const handleUseTypicalValue = (key: string) => {
    if (!wizardResponse?.suggestedRanges[key]) return;
    
    const [min, max] = wizardResponse.suggestedRanges[key];
    const typical = Math.round(((min + max) / 2) * 100) / 100;
    
    // Map the key to the correct config path
    if (key === 'grossMarginPct') {
      updateConfig(['drivers', 'grossMarginPct'], typical);
    } else if (key === 'aov') {
      updateConfig(['drivers', 'revenue', 'aov'], typical);
    } else if (key === 'momGrowthPct') {
      updateConfig(['drivers', 'revenue', 'momGrowthPct'], typical);
    } else if (key === 'rate') {
      updateConfig(['drivers', 'revenue', 'rate'], typical);
    } else if (key === 'arpu') {
      updateConfig(['drivers', 'revenue', 'arpu'], typical);
    } else if (key === 'churnPct') {
      updateConfig(['drivers', 'revenue', 'churnPct'], typical);
    }
  };

  const isConfigurationComplete = () => {
    const drivers = editableConfig?.drivers as Record<string, unknown> | undefined;
    const revenue = drivers?.revenue as Record<string, unknown> | undefined;
    if (!revenue) return false;
    
    const hasGrossMargin = typeof drivers?.grossMarginPct === 'number';
    
    switch (revenue.kind) {
      case 'product':
        return revenue.aov && revenue.baseOrders && hasGrossMargin;
      case 'service':
        return revenue.rate && revenue.billableHours && hasGrossMargin;
      case 'saas':
        return revenue.arpu && typeof revenue.churnPct === 'number' && hasGrossMargin;
      default:
        return false;
    }
  };

  const createScenario = async () => {
    if (!editableConfig || !user) return;
    
    setIsCreatingScenario(true);
    
    try {
      // Build full config with defaults
      const meta = editableConfig.meta as Record<string, unknown> | undefined;
      const fullConfig = {
        meta: {
          currency: (meta?.currency as string) || 'USD',
          start: new Date().toISOString().split('T')[0].replace(/-\d{2}$/, '-01'), // First day of current month
          periods: 36,
          freq: 'monthly'
        },
        opening_balances: editableConfig.opening_balances || {},
        drivers: {
          ...(editableConfig.drivers as Record<string, unknown>),
          opex: { fixed: 1000, variable_pct_of_rev: 0 },
          capex: { items: [], depr_years: 3 },
          wc: { dso: 30, dpo: 30, dio: 0 },
          debt: { opening: 0, rate_pct: 0, term_months: 60, amort: 'annuity', draws: [] },
          tax: { rate_pct: 25, payments_lag_mths: 1 }
        }
      };

      const response = await fetch('/api/scenarios', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: scenarioName,
          industry: wizardResponse?.profileId || 'general',
          config: fullConfig
        })
      });

      if (!response.ok) {
        throw new Error('Failed to create scenario');
      }

      const { scenario } = await response.json();
      onScenarioCreated(scenario.id);
    } catch (error) {
      console.error('Error creating scenario:', error);
      // Show error message to user
    } finally {
      setIsCreatingScenario(false);
    }
  };

  const renderRevenueFields = () => {
    const drivers = editableConfig?.drivers as Record<string, unknown> | undefined;
    const revenue = drivers?.revenue as Record<string, unknown> | undefined;
    if (!revenue) return null;
    
    switch (revenue.kind) {
      case 'product':
        return (
          <div className="space-y-4">
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                Average Order Value
                <span title={TOOLTIPS.AOV}><Info className="ml-1 w-4 h-4 text-gray-400" /></span>
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  value={(revenue.aov as number) || ''}
                  onChange={(e) => updateConfig(['drivers', 'revenue', 'aov'], parseFloat(e.target.value))}
                  className="flex-1 px-3 py-1 border border-gray-300 rounded text-sm"
                  placeholder="e.g., 49"
                />
                {wizardResponse?.suggestedRanges.aov && (
                  <button
                    onClick={() => handleUseTypicalValue('aov')}
                    className="px-2 py-1 text-xs bg-blue-50 text-blue-600 rounded hover:bg-blue-100"
                  >
                    Use typical
                  </button>
                )}
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">Base Orders (Month 1)</label>
              <input
                type="number"
                value={(revenue.baseOrders as number) || ''}
                onChange={(e) => updateConfig(['drivers', 'revenue', 'baseOrders'], parseInt(e.target.value))}
                className="w-full px-3 py-1 border border-gray-300 rounded text-sm"
                placeholder="e.g., 800"
              />
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">Monthly Growth %</label>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  value={(revenue.momGrowthPct as number) || ''}
                  onChange={(e) => updateConfig(['drivers', 'revenue', 'momGrowthPct'], parseFloat(e.target.value))}
                  className="flex-1 px-3 py-1 border border-gray-300 rounded text-sm"
                  placeholder="e.g., 6"
                />
                {wizardResponse?.suggestedRanges.momGrowthPct && (
                  <button
                    onClick={() => handleUseTypicalValue('momGrowthPct')}
                    className="px-2 py-1 text-xs bg-blue-50 text-blue-600 rounded hover:bg-blue-100"
                  >
                    Use typical
                  </button>
                )}
              </div>
            </div>
          </div>
        );

      case 'service':
        return (
          <div className="space-y-4">
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                Hourly Rate
                <span title={TOOLTIPS.Rate}><Info className="ml-1 w-4 h-4 text-gray-400" /></span>
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  value={(revenue.rate as number) || ''}
                  onChange={(e) => updateConfig(['drivers', 'revenue', 'rate'], parseFloat(e.target.value))}
                  className="flex-1 px-3 py-1 border border-gray-300 rounded text-sm"
                  placeholder="e.g., 120"
                />
                {wizardResponse?.suggestedRanges.rate && (
                  <button
                    onClick={() => handleUseTypicalValue('rate')}
                    className="px-2 py-1 text-xs bg-blue-50 text-blue-600 rounded hover:bg-blue-100"
                  >
                    Use typical
                  </button>
                )}
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">Billable Hours/Month</label>
              <input
                type="number"
                value={(revenue.billableHours as number) || ''}
                onChange={(e) => updateConfig(['drivers', 'revenue', 'billableHours'], parseFloat(e.target.value))}
                className="w-full px-3 py-1 border border-gray-300 rounded text-sm"
                placeholder="e.g., 200"
              />
            </div>
          </div>
        );

      case 'saas':
        return (
          <div className="space-y-4">
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                ARPU (Monthly)
                <span title={TOOLTIPS.ARPU}><Info className="ml-1 w-4 h-4 text-gray-400" /></span>
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  value={(revenue.arpu as number) || ''}
                  onChange={(e) => updateConfig(['drivers', 'revenue', 'arpu'], parseFloat(e.target.value))}
                  className="flex-1 px-3 py-1 border border-gray-300 rounded text-sm"
                  placeholder="e.g., 39"
                />
                {wizardResponse?.suggestedRanges.arpu && (
                  <button
                    onClick={() => handleUseTypicalValue('arpu')}
                    className="px-2 py-1 text-xs bg-blue-50 text-blue-600 rounded hover:bg-blue-100"
                  >
                    Use typical
                  </button>
                )}
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">Starting Subscribers</label>
              <input
                type="number"
                value={(revenue.startSubs as number) || ''}
                onChange={(e) => updateConfig(['drivers', 'revenue', 'startSubs'], parseInt(e.target.value))}
                className="w-full px-3 py-1 border border-gray-300 rounded text-sm"
                placeholder="e.g., 100"
              />
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">New Subscribers/Month</label>
              <input
                type="number"
                value={(revenue.newPerMonth as number) || ''}
                onChange={(e) => updateConfig(['drivers', 'revenue', 'newPerMonth'], parseInt(e.target.value))}
                className="w-full px-3 py-1 border border-gray-300 rounded text-sm"
                placeholder="e.g., 50"
              />
            </div>
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                Monthly Churn %
                <span title={TOOLTIPS.Churn}><Info className="ml-1 w-4 h-4 text-gray-400" /></span>
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  value={(revenue.churnPct as number) || ''}
                  onChange={(e) => updateConfig(['drivers', 'revenue', 'churnPct'], parseFloat(e.target.value))}
                  className="flex-1 px-3 py-1 border border-gray-300 rounded text-sm"
                  placeholder="e.g., 3"
                />
                {wizardResponse?.suggestedRanges.churnPct && (
                  <button
                    onClick={() => handleUseTypicalValue('churnPct')}
                    className="px-2 py-1 text-xs bg-blue-50 text-blue-600 rounded hover:bg-blue-100"
                  >
                    Use typical
                  </button>
                )}
              </div>
            </div>
          </div>
        );

      default:
        return <div className="text-sm text-gray-500">Revenue configuration will be filled automatically</div>;
    }
  };

  if (!wizardResponse) {
    return (
      <div className="p-4 h-full flex items-center justify-center text-gray-500">
        <div className="text-center">
          <Sparkles className="w-8 h-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm">Answer questions to see your model preview</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 h-full flex flex-col">
      {/* Header */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-semibold text-gray-900">Configuration Preview</h3>
          <div className="flex items-center space-x-1">
            {wizardResponse.confidence >= 0.8 ? (
              <CheckCircle2 className="w-4 h-4 text-green-500" />
            ) : (
              <AlertCircle className="w-4 h-4 text-yellow-500" />
            )}
            <span className="text-xs text-gray-500">
              {Math.round(wizardResponse.confidence * 100)}% confident
            </span>
          </div>
        </div>
        <div className="text-xs bg-blue-50 text-blue-700 px-2 py-1 rounded">
          {wizardResponse.archetype.toUpperCase()} • {wizardResponse.profileId.replace('_', ' ')}
        </div>
      </div>

      {/* Configuration Fields */}
      <div className="flex-1 overflow-y-auto space-y-6">
        {/* Scenario Name */}
        <div>
          <label className="text-sm font-medium text-gray-700 mb-1 block">Scenario Name</label>
          <input
            type="text"
            value={scenarioName}
            onChange={(e) => setScenarioName(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded"
            placeholder="My Business Model"
          />
        </div>

        {/* Revenue Configuration */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Revenue Drivers</h4>
          {renderRevenueFields()}
        </div>

        {/* Gross Margin */}
        <div>
          <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
            Gross Margin %
            <span title={TOOLTIPS.GrossMargin}><Info className="ml-1 w-4 h-4 text-gray-400" /></span>
          </label>
          <div className="flex items-center space-x-2">
            <input
              type="number"
              value={((editableConfig?.drivers as Record<string, unknown>)?.grossMarginPct as number) || ''}
              onChange={(e) => updateConfig(['drivers', 'grossMarginPct'], parseFloat(e.target.value))}
              className="flex-1 px-3 py-1 border border-gray-300 rounded text-sm"
              placeholder="e.g., 60"
            />
            {wizardResponse?.suggestedRanges.grossMarginPct && (
              <button
                onClick={() => handleUseTypicalValue('grossMarginPct')}
                className="px-2 py-1 text-xs bg-blue-50 text-blue-600 rounded hover:bg-blue-100"
              >
                Use typical
              </button>
            )}
          </div>
        </div>

        {/* Currency */}
        <div>
          <label className="text-sm font-medium text-gray-700 mb-1 block">Currency</label>
          <select
            value={((editableConfig?.meta as Record<string, unknown>)?.currency as string) || 'USD'}
            onChange={(e) => updateConfig(['meta', 'currency'], e.target.value)}
            className="w-full px-3 py-1 border border-gray-300 rounded text-sm"
          >
            <option value="USD">USD ($)</option>
            <option value="EUR">EUR (€)</option>
            <option value="GBP">GBP (£)</option>
            <option value="CAD">CAD (C$)</option>
          </select>
        </div>

        {/* Confirmation Items */}
        {wizardResponse.needsConfirmation && wizardResponse.needsConfirmation.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Please Confirm</h4>
            <div className="space-y-2">
              {wizardResponse.needsConfirmation.map((field) => (
                <div key={field} className="flex items-center text-sm text-amber-700 bg-amber-50 px-2 py-1 rounded">
                  <AlertCircle className="w-4 h-4 mr-2" />
                  {field === 'grossMarginPct' ? 'Gross margin estimated' : `${field} needs confirmation`}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Notes */}
        {wizardResponse.notes && (
          <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
            {wizardResponse.notes}
          </div>
        )}
      </div>

      {/* Create Button */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <button
          onClick={createScenario}
          disabled={!isConfigurationComplete() || !isComplete || isCreatingScenario || !scenarioName.trim()}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
        >
          {isCreatingScenario ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Creating Scenario...
            </>
          ) : (
            'Create Scenario'
          )}
        </button>
        {!isConfigurationComplete() && isComplete && (
          <p className="text-xs text-gray-500 mt-2 text-center">
            Please fill in all required fields above
          </p>
        )}
      </div>
    </div>
  );
}