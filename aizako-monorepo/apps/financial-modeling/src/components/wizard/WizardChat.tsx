'use client';

import { useState, useRef, useEffect } from 'react';
import { Send, Bot, User } from 'lucide-react';
import { PrefillCard } from './PrefillCard';

interface WizardStep {
  id: string;
  answer: string;
}

interface WizardResponse {
  archetype: 'product' | 'service' | 'saas' | 'mixed';
  profileId: string;
  confidence: number;
  prefill: Record<string, unknown>;
  needsConfirmation: string[];
  suggestedRanges: Record<string, [number, number]>;
  notes: string;
}

interface Message {
  id: string;
  type: 'bot' | 'user';
  content: string;
  timestamp: Date;
  options?: string[];
  stepId?: string;
}

interface WizardChatProps {
  onScenarioCreated: (scenarioId: string) => void;
}

const INITIAL_QUESTIONS = {
  'what_sell': {
    question: "Hi! I'm here to help you build a financial model. Let's start with the basics - what does your business sell or provide?",
    options: ['Physical Products', 'Digital Products/Software', 'Services/Consulting', 'Subscription/SaaS', 'Not Sure'],
    followup: true
  },
  'pricing': {
    question: "Great! How do you typically charge your customers?",
    options: [], // Dynamic based on business type
    followup: true
  },
  'volume': {
    question: "What volume do you expect in your first month?",
    options: [],
    followup: true
  },
  'growth': {
    question: "What kind of growth do you expect month-to-month?",
    options: ['Steady growth (3-8%)', 'Fast growth (10-20%)', 'Conservative (1-5%)', 'I don\'t know'],
    followup: true
  },
  'margin': {
    question: "What about your gross margin or cost of goods sold?",
    options: ['I know my costs', 'I know my margin %', 'Use typical for my industry', 'I don\'t know'],
    followup: false
  }
};

export function WizardChat({ onScenarioCreated }: WizardChatProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [currentInput, setCurrentInput] = useState('');
  const [currentStep, setCurrentStep] = useState<string>('what_sell');
  const [wizardSteps, setWizardSteps] = useState<WizardStep[]>([]);
  const [wizardResponse, setWizardResponse] = useState<WizardResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Add initial welcome message
    addBotMessage(INITIAL_QUESTIONS.what_sell.question, INITIAL_QUESTIONS.what_sell.options, 'what_sell');
  }, []);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const addBotMessage = (content: string, options?: string[], stepId?: string) => {
    const message: Message = {
      id: Date.now().toString(),
      type: 'bot',
      content,
      timestamp: new Date(),
      options,
      stepId
    };
    setMessages(prev => [...prev, message]);
  };

  const addUserMessage = (content: string, stepId?: string) => {
    const message: Message = {
      id: Date.now().toString(),
      type: 'user',
      content,
      timestamp: new Date(),
      stepId
    };
    setMessages(prev => [...prev, message]);
  };

  const updateWizardSteps = (stepId: string, answer: string) => {
    setWizardSteps(prev => {
      const existing = prev.find(s => s.id === stepId);
      if (existing) {
        return prev.map(s => s.id === stepId ? { ...s, answer } : s);
      }
      return [...prev, { id: stepId, answer }];
    });
  };

  const callWizardAPI = async (steps: WizardStep[]) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/wizard/build', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          steps,
          freeText: steps.map(s => s.answer).join(' '),
          locale: 'en'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to process wizard request');
      }

      const result = await response.json();
      setWizardResponse(result);
      return result;
    } catch (error) {
      console.error('Wizard API error:', error);
      addBotMessage("I'm having trouble processing that. Let me try with typical values for your business type.", [], 'error');
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const getNextQuestion = (currentStepId: string, answer: string): { question: string; options: string[]; nextStep: string } | null => {
    switch (currentStepId) {
      case 'what_sell':
        if (answer.toLowerCase().includes('product')) {
          return {
            question: "What's your typical selling price per item?",
            options: ['Under $25', '$25-$100', '$100-$500', '$500+', 'Let me type exact amount'],
            nextStep: 'pricing'
          };
        } else if (answer.toLowerCase().includes('service') || answer.toLowerCase().includes('consulting')) {
          return {
            question: "What's your hourly or daily rate?",
            options: ['$50-$100/hour', '$100-$200/hour', '$200+/hour', 'Daily/project rate', 'Let me specify'],
            nextStep: 'pricing'
          };
        } else if (answer.toLowerCase().includes('saas') || answer.toLowerCase().includes('subscription')) {
          return {
            question: "What's your monthly subscription price per user?",
            options: ['Under $20/month', '$20-$50/month', '$50-$100/month', '$100+/month', 'Let me specify'],
            nextStep: 'pricing'
          };
        }
        return {
          question: "Could you tell me more about how you charge customers? (e.g., per item, per hour, monthly subscription)",
          options: [],
          nextStep: 'pricing'
        };

      case 'pricing': {
        const businessType = wizardSteps.find(s => s.id === 'what_sell')?.answer || '';
        if (businessType.toLowerCase().includes('product')) {
          return {
            question: "How many orders or units do you expect to sell in your first month?",
            options: ['Under 100', '100-500', '500-1000', '1000+', 'Let me specify'],
            nextStep: 'volume'
          };
        } else if (businessType.toLowerCase().includes('service')) {
          return {
            question: "How many billable hours do you expect in your first month?",
            options: ['Under 100 hours', '100-200 hours', '200+ hours', 'Let me specify'],
            nextStep: 'volume'
          };
        } else if (businessType.toLowerCase().includes('saas')) {
          return {
            question: "How many subscribers do you expect to start with, and how many new ones per month?",
            options: ['Start with 0, add 10-50/month', 'Start with some, add 50-200/month', 'Let me specify both'],
            nextStep: 'volume'
          };
        }
        return {
          question: "What volume of business do you expect in your first month?",
          options: [],
          nextStep: 'volume'
        };
      }

      case 'volume':
        return {
          question: INITIAL_QUESTIONS.growth.question,
          options: INITIAL_QUESTIONS.growth.options,
          nextStep: 'growth'
        };

      case 'growth':
        return {
          question: INITIAL_QUESTIONS.margin.question,
          options: INITIAL_QUESTIONS.margin.options,
          nextStep: 'margin'
        };

      default:
        return null;
    }
  };

  const handleSendMessage = async () => {
    if (!currentInput.trim() || isLoading) return;

    const answer = currentInput.trim();
    addUserMessage(answer, currentStep);
    updateWizardSteps(currentStep, answer);
    setCurrentInput('');

    // Call wizard API after each step to update the prefill
    const updatedSteps = [...wizardSteps, { id: currentStep, answer }];
    await callWizardAPI(updatedSteps);

    // Get next question
    const nextQ = getNextQuestion(currentStep, answer);
    if (nextQ) {
      setTimeout(() => {
        addBotMessage(nextQ.question, nextQ.options, nextQ.nextStep);
        setCurrentStep(nextQ.nextStep);
      }, 1000);
    } else {
      // Wizard complete
      setIsComplete(true);
      setTimeout(() => {
        addBotMessage(
          "Perfect! I've analyzed your responses and prepared your financial model configuration. You can review and adjust the numbers in the preview on the right, then create your scenario when ready.",
          [],
          'complete'
        );
      }, 1000);
    }
  };

  const handleOptionClick = (option: string) => {
    setCurrentInput(option);
    // Auto-send for option clicks
    setTimeout(handleSendMessage, 100);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const progress = Math.min((wizardSteps.length / 5) * 100, 100);

  return (
    <div className="flex h-[600px]">
      {/* Chat Interface */}
      <div className="flex-1 flex flex-col">
        {/* Progress Bar */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
            <span>Setup Progress</span>
            <span>{Math.round(progress)}% Complete</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-500"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className={`flex items-start max-w-xs lg:max-w-md ${message.type === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                  message.type === 'user' ? 'bg-blue-600 ml-2' : 'bg-gray-100 mr-2'
                }`}>
                  {message.type === 'user' ? <User className="w-4 h-4 text-white" /> : <Bot className="w-4 h-4 text-gray-600" />}
                </div>
                <div className={`px-4 py-2 rounded-lg ${
                  message.type === 'user' 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  <p className="text-sm">{message.content}</p>
                  {message.options && message.options.length > 0 && (
                    <div className="mt-3 space-y-2">
                      {message.options.map((option, idx) => (
                        <button
                          key={idx}
                          onClick={() => handleOptionClick(option)}
                          className="block w-full text-left px-3 py-2 bg-white border border-gray-200 rounded-md hover:bg-gray-50 transition-colors text-gray-700 text-sm"
                          disabled={isLoading}
                        >
                          {option}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
          
          {isLoading && (
            <div className="flex justify-start">
              <div className="flex items-start">
                <div className="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center bg-gray-100 mr-2">
                  <Bot className="w-4 h-4 text-gray-600" />
                </div>
                <div className="px-4 py-2 rounded-lg bg-gray-100 text-gray-800">
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span className="text-sm">Processing...</span>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* Input */}
        {!isComplete && (
          <div className="p-4 border-t border-gray-200">
            <div className="flex space-x-2">
              <input
                type="text"
                value={currentInput}
                onChange={(e) => setCurrentInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type your answer..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={isLoading}
              />
              <button
                onClick={handleSendMessage}
                disabled={!currentInput.trim() || isLoading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <Send className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Prefill Card */}
      <div className="w-96 border-l border-gray-200">
        <PrefillCard 
          wizardResponse={wizardResponse}
          onScenarioCreated={onScenarioCreated}
          isComplete={isComplete}
        />
      </div>
    </div>
  );
}