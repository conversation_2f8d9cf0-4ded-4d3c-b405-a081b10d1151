/**
 * Individual KPI Card component for metrics banner
 */

import { MetricStatus } from '@/lib/metrics';

interface KpiCardProps {
  title: string;
  value: string;
  unit?: string;
  description: string;
  status: MetricStatus;
}

export function KpiCard({ title, value, unit, description, status }: KpiCardProps) {
  const getStatusColor = (type: MetricStatus['type']) => {
    switch (type) {
      case 'success':
        return 'text-green-600 border-green-200 bg-green-50';
      case 'warning':
        return 'text-amber-600 border-amber-200 bg-amber-50';
      default:
        return 'text-gray-600 border-gray-200 bg-white';
    }
  };

  const getBadgeColor = (type: MetricStatus['type']) => {
    switch (type) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'warning':
        return 'bg-amber-100 text-amber-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div 
      className={`relative p-4 rounded-lg border transition-colors ${getStatusColor(status.type)}`}
      role="group"
      aria-labelledby={`kpi-title-${title.toLowerCase().replace(/\s+/g, '-')}`}
    >
      {/* Status badge */}
      {status.badge && (
        <div className={`absolute -top-2 -right-2 px-2 py-1 text-xs font-medium rounded-full ${getBadgeColor(status.type)}`}>
          {status.badge}
        </div>
      )}
      
      {/* Title */}
      <h3 
        id={`kpi-title-${title.toLowerCase().replace(/\s+/g, '-')}`}
        className="text-sm font-medium text-gray-900 mb-1"
      >
        {title}
      </h3>
      
      {/* Value and Unit */}
      <div className="flex items-baseline gap-1 mb-2">
        <span className="text-2xl font-bold text-gray-900">
          {value}
        </span>
        {unit && (
          <span className="text-sm text-gray-600 font-medium">
            {unit}
          </span>
        )}
      </div>
      
      {/* Description/Tooltip */}
      <p className="text-xs text-gray-500 leading-relaxed">
        {description}
      </p>
      
      {/* Invisible element for screen readers */}
      <span className="sr-only">
        {title}: {value} {unit}. {description}
        {status.badge && `. Status: ${status.badge}`}
      </span>
    </div>
  );
}