/**
 * Metrics Banner component displaying key financial health indicators
 */

import { ScenarioSnapshot } from '@/hooks/useScenario';
import { KpiCard } from './KpiCard';
import { 
  extractMetrics, 
  hasValidMetricsData, 
  getBurnStatus,
  getRunwayStatus,
  getDscrStatus,
  getIcrStatus,
  avg,
  safeNumber
} from '@/lib/metrics';
import { 
  formatMonths, 
  formatRatio, 
  formatCurrencyCompact, 
  getMetricUnit, 
  getMetricDescription 
} from '@/lib/utils/formatting';

interface MetricsBannerProps {
  snapshot: ScenarioSnapshot;
  currency?: string;
}

export function MetricsBanner({ snapshot, currency = 'USD' }: MetricsBannerProps) {
  // Check if we have sufficient data
  if (!hasValidMetricsData(snapshot)) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <div className="text-center">
          <h2 className="text-lg font-medium text-gray-900 mb-2">Financial Health Metrics</h2>
          <p className="text-sm text-gray-500">
            Complete your scenario to see key financial health indicators
          </p>
        </div>
      </div>
    );
  }

  // Extract metrics data
  const metrics = extractMetrics(snapshot);
  
  // Calculate average CFO for burn status determination
  const cf = snapshot.monthly?.cf ?? [];
  const cfoFirst3 = cf.slice(0, 3).map(row => safeNumber(row?.cfo));
  const avgCfo = avg(cfoFirst3);

  // Determine status for each metric
  const burnStatus = getBurnStatus(metrics.burn, avgCfo);
  const runwayStatus = getRunwayStatus(metrics.runway);
  const dscrStatus = getDscrStatus(metrics.dscrY1);
  const icrStatus = getIcrStatus(metrics.icrY1);

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
        <div>
          <h2 className="text-lg font-medium text-gray-900">Financial Health Metrics</h2>
          <p className="text-sm text-gray-500 mt-1">
            Key indicators • Currency: {currency}
          </p>
        </div>
      </div>

      {/* KPI Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Burn Rate */}
        <KpiCard
          title="Burn Rate"
          value={formatCurrencyCompact(metrics.burn)}
          unit={getMetricUnit('burn', currency)}
          description={getMetricDescription('burn')}
          status={burnStatus}
        />

        {/* Runway */}
        <KpiCard
          title="Cash Runway"
          value={formatMonths(metrics.runway)}
          unit={getMetricUnit('runway')}
          description={getMetricDescription('runway')}
          status={runwayStatus}
        />

        {/* DSCR (Y1) */}
        <KpiCard
          title="DSCR (Y1)"
          value={formatRatio(metrics.dscrY1)}
          unit={getMetricUnit('ratio')}
          description={getMetricDescription('dscr')}
          status={dscrStatus}
        />

        {/* ICR (Y1) */}
        <KpiCard
          title="ICR (Y1)"
          value={formatRatio(metrics.icrY1)}
          unit={getMetricUnit('ratio')}
          description={getMetricDescription('icr')}
          status={icrStatus}
        />
      </div>

      {/* Warning indicators summary */}
      {(burnStatus.type === 'warning' || runwayStatus.type === 'warning' || 
        dscrStatus.type === 'warning' || icrStatus.type === 'warning') && (
        <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-md">
          <div className="flex">
            <svg className="h-5 w-5 text-amber-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <div>
              <p className="text-sm font-medium text-amber-800">Attention Needed</p>
              <p className="text-sm text-amber-700 mt-1">
                Some metrics indicate areas that may need attention for financial health or lending qualification.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Success indicator for profitable companies */}
      {burnStatus.type === 'success' && (
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
          <div className="flex">
            <svg className="h-5 w-5 text-green-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <p className="text-sm font-medium text-green-800">Strong Financial Position</p>
              <p className="text-sm text-green-700 mt-1">
                Your business shows positive cash flow generation, indicating healthy operational performance.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}