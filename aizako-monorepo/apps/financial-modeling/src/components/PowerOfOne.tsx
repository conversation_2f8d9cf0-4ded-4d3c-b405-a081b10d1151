'use client';

import { useState, useMemo } from 'react';
import { project } from '@/lib/engine/project';
import type { Config, ProjectionResult } from '@/lib/engine/types';
import {
  type Variant,
  perturbConfig,
  y1Metrics,
  fmtMoney,
  fmtPct,
  calcPctChange,
  getVariantDisplayName,
  getMetricInfo
} from '@/lib/power';
import { monthlyMetrics, monthlyDiff, type MonthlyComparisonRow } from '@/lib/power.monthly';
import { MonthlyDeltaTable } from '@/components/MonthlyDeltaTable';

interface PowerOfOneProps {
  baseConfig: Config;
  baseProjection?: ProjectionResult;
  currency?: string;
}

type ViewMode = 'yearly' | 'monthly';

interface MetricsComparison {
  base: ReturnType<typeof y1Metrics>;
  after: ReturnType<typeof y1Metrics>;
  deltas: {
    revenue: { abs: number; pct: number };
    netIncome: { abs: number; pct: number };
    cfo: { abs: number; pct: number };
    endCash: { abs: number; pct: number };
  };
}

interface MonthlyComparison {
  rows: MonthlyComparisonRow[];
}

export function PowerOfOne({ baseConfig, baseProjection, currency = 'USD' }: PowerOfOneProps) {
  const [activeVariant, setActiveVariant] = useState<Variant | null>(null);
  const [comparison, setComparison] = useState<MetricsComparison | null>(null);
  const [monthlyComparison, setMonthlyComparison] = useState<MonthlyComparison | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>('yearly');

  // Compute base projection if not provided
  const baseProj = useMemo(() => {
    return baseProjection || project(baseConfig);
  }, [baseConfig, baseProjection]);

  const baseMetrics = useMemo(() => y1Metrics(baseProj), [baseProj]);

  const handleVariantClick = (variant: Variant) => {
    try {
      const perturbedConfig = perturbConfig(baseConfig, variant);
      const perturbedProj = project(perturbedConfig);
      const afterMetrics = y1Metrics(perturbedProj);

      // Compute yearly comparison
      const yearlyComparison: MetricsComparison = {
        base: baseMetrics,
        after: afterMetrics,
        deltas: {
          revenue: {
            abs: afterMetrics.revY1 - baseMetrics.revY1,
            pct: calcPctChange(baseMetrics.revY1, afterMetrics.revY1)
          },
          netIncome: {
            abs: afterMetrics.niY1 - baseMetrics.niY1,
            pct: calcPctChange(baseMetrics.niY1, afterMetrics.niY1)
          },
          cfo: {
            abs: afterMetrics.cfoY1 - baseMetrics.cfoY1,
            pct: calcPctChange(baseMetrics.cfoY1, afterMetrics.cfoY1)
          },
          endCash: {
            abs: afterMetrics.endCashY1 - baseMetrics.endCashY1,
            pct: calcPctChange(baseMetrics.endCashY1, afterMetrics.endCashY1)
          }
        }
      };

      // Compute monthly comparison
      const baseMonthlyMetrics = monthlyMetrics(baseProj);
      const afterMonthlyMetrics = monthlyMetrics(perturbedProj);
      const monthlyRows = monthlyDiff(baseMonthlyMetrics, afterMonthlyMetrics);
      
      const monthlyComparison: MonthlyComparison = {
        rows: monthlyRows
      };

      setActiveVariant(variant);
      setComparison(yearlyComparison);
      setMonthlyComparison(monthlyComparison);
    } catch (error) {
      console.error('Error computing power-of-1 variant:', error);
      // Reset state on error
      setActiveVariant(null);
      setComparison(null);
      setMonthlyComparison(null);
    }
  };

  const handleReset = () => {
    setActiveVariant(null);
    setComparison(null);
    setMonthlyComparison(null);
  };

  const variants: Variant[] = [
    { kind: 'price', dir: 1 },
    { kind: 'price', dir: -1 },
    { kind: 'volume', dir: 1 },
    { kind: 'volume', dir: -1 },
    { kind: 'cogs_pct', dir: 1 },
    { kind: 'cogs_pct', dir: -1 },
    { kind: 'dso', dir: 1 },
    { kind: 'dso', dir: -1 },
    { kind: 'dpo', dir: 1 },
    { kind: 'dpo', dir: -1 },
    { kind: 'dio', dir: 1 },
    { kind: 'dio', dir: -1 }
  ];

  const formatDelta = (abs: number, pct: number) => {
    const sign = abs >= 0 ? '+' : '';
    return {
      abs: `${sign}${fmtMoney(abs)}`,
      pct: `${sign}${fmtPct(pct)}`,
      isPositive: abs >= 0
    };
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
        <div>
          <h2 className="text-lg font-medium text-gray-900">Power of 1</h2>
          <p className="text-sm text-gray-500 mt-1">
            See instant impact of small changes • Currency: {currency}
          </p>
        </div>
        {activeVariant && (
          <div className="flex items-center gap-3 mt-3 sm:mt-0">
            <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full">
              {getVariantDisplayName(activeVariant)} applied
            </span>
            <button
              onClick={handleReset}
              className="px-3 py-1 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
              aria-label="Reset to base scenario"
            >
              Reset
            </button>
          </div>
        )}
      </div>

      {/* View Toggle */}
      {activeVariant && (
        <div className="flex items-center gap-4 mb-6">
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('yearly')}
              className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                viewMode === 'yearly'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              aria-label="Switch to yearly summary view"
            >
              Year-1 Summary
            </button>
            <button
              onClick={() => setViewMode('monthly')}
              className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                viewMode === 'monthly'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              aria-label="Switch to monthly breakdown view"
            >
              Monthly Breakdown
            </button>
          </div>
          <div className="text-sm text-gray-500">
            View: <span className="font-medium">{viewMode === 'yearly' ? 'Yearly totals' : 'Month-by-month'}</span>
          </div>
        </div>
      )}

      {/* Perturbation Buttons */}
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-2 mb-6">
        {variants.map((variant) => (
          <button
            key={`${variant.kind}-${variant.dir}`}
            onClick={() => handleVariantClick(variant)}
            className={`px-3 py-2 text-sm font-medium rounded-md border transition-colors ${
              activeVariant && 
              activeVariant.kind === variant.kind && 
              activeVariant.dir === variant.dir
                ? 'bg-blue-600 text-white border-blue-600'
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
            }`}
            aria-label={`Apply ${getVariantDisplayName(variant)} perturbation`}
          >
            {getVariantDisplayName(variant)}
          </button>
        ))}
      </div>

      {/* Results Display */}
      {activeVariant && comparison && (
        <div>
          {viewMode === 'yearly' && (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-2 text-sm font-medium text-gray-900">Metric</th>
                    <th className="text-right py-2 text-sm font-medium text-gray-900">Base</th>
                    <th className="text-right py-2 text-sm font-medium text-gray-900">After</th>
                    <th className="text-right py-2 text-sm font-medium text-gray-900">Δ Abs</th>
                    <th className="text-right py-2 text-sm font-medium text-gray-900">Δ %</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-100">
                  {[
                    { key: 'revenue' as const, base: comparison.base.revY1, after: comparison.after.revY1, delta: comparison.deltas.revenue },
                    { key: 'netIncome' as const, base: comparison.base.niY1, after: comparison.after.niY1, delta: comparison.deltas.netIncome },
                    { key: 'cfo' as const, base: comparison.base.cfoY1, after: comparison.after.cfoY1, delta: comparison.deltas.cfo },
                    { key: 'endCash' as const, base: comparison.base.endCashY1, after: comparison.after.endCashY1, delta: comparison.deltas.endCash }
                  ].map(({ key, base, after, delta }) => {
                    const info = getMetricInfo(key);
                    const deltaFormatted = formatDelta(delta.abs, delta.pct);
                    
                    return (
                      <tr key={key} className="hover:bg-gray-50">
                        <td className="py-2 pr-4">
                          <div className="flex items-center gap-1">
                            <span className="text-sm font-medium text-gray-900">{info.label}</span>
                            <div className="group relative">
                              <svg 
                                className="h-4 w-4 text-gray-400 cursor-help" 
                                fill="none" 
                                stroke="currentColor" 
                                viewBox="0 0 24 24"
                                aria-hidden="true"
                              >
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              <div className="invisible group-hover:visible absolute left-6 top-0 z-10 w-48 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg">
                                {info.tooltip}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="py-2 text-right text-sm text-gray-900 font-mono">
                          {fmtMoney(base)}
                        </td>
                        <td className="py-2 text-right text-sm text-gray-900 font-mono">
                          {fmtMoney(after)}
                        </td>
                        <td className={`py-2 text-right text-sm font-mono font-medium ${
                          deltaFormatted.isPositive ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {deltaFormatted.abs}
                        </td>
                        <td className={`py-2 text-right text-sm font-mono font-medium ${
                          deltaFormatted.isPositive ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {deltaFormatted.pct}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}

          {viewMode === 'monthly' && monthlyComparison && (
            <div>
              <div className="mb-4">
                <h3 className="text-sm font-medium text-gray-900 mb-2">Monthly Breakdown</h3>
                <p className="text-xs text-gray-600">
                  Showing Base vs After vs Δ for each month (M1-M{monthlyComparison.rows.length})
                </p>
              </div>
              <MonthlyDeltaTable rows={monthlyComparison.rows} currency={currency} />
            </div>
          )}
        </div>
      )}

      {/* Helper Text */}
      {!activeVariant && (
        <div className="text-center py-4">
          <p className="text-sm text-gray-500">
            Click any button above to see how small changes affect your financial performance.
          </p>
          <p className="text-xs text-gray-400 mt-1">
            Choose yearly summary or monthly breakdown • All changes are temporary and won&apos;t be saved to your scenario.
          </p>
        </div>
      )}
    </div>
  );
}