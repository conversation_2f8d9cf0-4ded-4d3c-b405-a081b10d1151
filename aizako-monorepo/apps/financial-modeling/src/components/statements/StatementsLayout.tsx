'use client';

import { useState } from 'react';
import { PnLTable } from './PnLTable';
import { BalanceSheetTable } from './BalanceSheetTable';
import { CashFlowTable } from './CashFlowTable';
import { ScenarioData } from '@/hooks/useScenario';
import { formatFinancialValue } from '@/lib/utils/currency';

interface StatementsLayoutProps {
  scenario: ScenarioData;
}

type StatementTab = 'pnl' | 'bs' | 'cf';
type ViewMode = 'monthly' | 'yearly';

export function StatementsLayout({ scenario }: StatementsLayoutProps) {
  const [activeTab, setActiveTab] = useState<StatementTab>('pnl');
  const [viewMode, setViewMode] = useState<ViewMode>('monthly');

  const tabs = [
    { id: 'pnl' as const, name: 'P&L Statement', icon: '📊' },
    { id: 'bs' as const, name: 'Balance Sheet', icon: '⚖️' },
    { id: 'cf' as const, name: 'Cash Flow', icon: '💰' },
  ];

  const handleCellClick = (row: unknown, field: string) => {
    // TODO: Implement cell click functionality (e.g., show drill-down details)
    console.log('Cell clicked:', { row, field });
  };

  const renderStatementTable = () => {
    const data = viewMode === 'monthly' ? scenario.snapshot.monthly : scenario.snapshot.yearly;

    switch (activeTab) {
      case 'pnl':
        return (
          <PnLTable
            data={data.pnl}
            viewMode={viewMode}
            formatCurrency={formatFinancialValue}
            onCellClick={handleCellClick}
          />
        );
      case 'bs':
        return (
          <BalanceSheetTable
            data={data.bs}
            viewMode={viewMode}
            formatCurrency={formatFinancialValue}
            onCellClick={handleCellClick}
          />
        );
      case 'cf':
        return (
          <CashFlowTable
            data={data.cf}
            viewMode={viewMode}
            formatCurrency={formatFinancialValue}
            onCellClick={handleCellClick}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        {/* Tab Navigation */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <span>{tab.icon}</span>
              <span className="hidden sm:inline">{tab.name}</span>
              <span className="sm:hidden">{tab.name.split(' ')[0]}</span>
            </button>
          ))}
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center space-x-4">
          <span className="text-sm font-medium text-gray-700">View:</span>
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
            <button
              onClick={() => setViewMode('monthly')}
              className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                viewMode === 'monthly'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setViewMode('yearly')}
              className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                viewMode === 'yearly'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              Yearly
            </button>
          </div>
        </div>
      </div>

      {/* Statement Table */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          {renderStatementTable()}
        </div>
      </div>

      {/* Model Quality Info */}
      {scenario.snapshot.monthly.checks && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-2">Model Quality</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Max Residual:</span>
              <span className={`ml-2 font-mono ${
                scenario.snapshot.monthly.checks.max_residual < 1e-2 
                  ? 'text-green-600' 
                  : 'text-red-600'
              }`}>
                {scenario.snapshot.monthly.checks.max_residual.toExponential(2)}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Validation Status:</span>
              <span className={`ml-2 font-medium ${
                scenario.snapshot.monthly.checks.passes_validation 
                  ? 'text-green-600' 
                  : 'text-red-600'
              }`}>
                {scenario.snapshot.monthly.checks.passes_validation ? 'Passed' : 'Failed'}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}