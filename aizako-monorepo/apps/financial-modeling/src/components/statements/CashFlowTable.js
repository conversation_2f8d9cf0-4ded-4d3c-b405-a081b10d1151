import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
export function CashFlowTable({ data, viewMode, formatCurrency, onCellClick }) {
    // Aggregate data for yearly view
    const processedData = viewMode === 'yearly' ? aggregateYearly(data) : data;
    // Show first 12 periods for monthly, all for yearly
    const displayData = viewMode === 'monthly' ? processedData.slice(0, 12) : processedData;
    return (_jsxs("table", { className: "min-w-full divide-y divide-gray-200", children: [_jsx("thead", { className: "bg-gray-50", children: _jsxs("tr", { children: [_jsx("th", { className: "sticky left-0 bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r", children: "Cash Flow Statement" }), displayData.map((row, index) => (_jsx("th", { className: "px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]", children: viewMode === 'monthly'
                                ? `Mo ${row.period}`
                                : `Year ${Math.ceil(row.period / 12)}` }, index)))] }) }), _jsxs("tbody", { className: "bg-white divide-y divide-gray-200", children: [_jsxs("tr", { className: "bg-gray-100", children: [_jsx("td", { className: "sticky left-0 bg-gray-100 px-6 py-3 whitespace-nowrap text-sm font-bold text-gray-900 border-r", children: "OPERATING ACTIVITIES" }), displayData.map((_, index) => (_jsx("td", { className: "px-3 py-3" }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-8", children: "Net Income" }), displayData.map((row, index) => (_jsx("td", { className: `px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50 ${row.net_income >= 0 ? 'text-green-600' : 'text-red-600'}`, onClick: () => onCellClick?.(row, 'net_income'), children: formatCurrency(row.net_income) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-8", children: "Depreciation" }), displayData.map((row, index) => (_jsx("td", { className: "px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50", onClick: () => onCellClick?.(row, 'depreciation'), children: formatCurrency(row.depreciation) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-8", children: "Change in A/R" }), displayData.map((row, index) => (_jsx("td", { className: `px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50 ${row.change_ar <= 0 ? 'text-green-600' : 'text-red-600'}`, onClick: () => onCellClick?.(row, 'change_ar'), children: formatCurrency(row.change_ar) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-8", children: "Change in Inventory" }), displayData.map((row, index) => (_jsx("td", { className: `px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50 ${row.change_inventory <= 0 ? 'text-green-600' : 'text-red-600'}`, onClick: () => onCellClick?.(row, 'change_inventory'), children: formatCurrency(row.change_inventory) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-8", children: "Change in A/P" }), displayData.map((row, index) => (_jsx("td", { className: `px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50 ${row.change_ap >= 0 ? 'text-green-600' : 'text-red-600'}`, onClick: () => onCellClick?.(row, 'change_ap'), children: formatCurrency(row.change_ap) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50 border-b-2", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 border-r", children: "Cash Flow from Operations" }), displayData.map((row, index) => (_jsx("td", { className: `px-3 py-4 whitespace-nowrap text-sm font-bold text-right cursor-pointer hover:bg-blue-50 ${row.cfo >= 0 ? 'text-green-600' : 'text-red-600'}`, onClick: () => onCellClick?.(row, 'cfo'), children: formatCurrency(row.cfo) }, index)))] }), _jsxs("tr", { className: "bg-gray-100", children: [_jsx("td", { className: "sticky left-0 bg-gray-100 px-6 py-3 whitespace-nowrap text-sm font-bold text-gray-900 border-r", children: "INVESTING ACTIVITIES" }), displayData.map((_, index) => (_jsx("td", { className: "px-3 py-3" }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-8", children: "Capital Expenditures" }), displayData.map((row, index) => (_jsx("td", { className: `px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50 ${row.capex <= 0 ? 'text-red-600' : 'text-green-600'}`, onClick: () => onCellClick?.(row, 'capex'), children: formatCurrency(row.capex) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50 border-b-2", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 border-r", children: "Cash Flow from Investing" }), displayData.map((row, index) => (_jsx("td", { className: `px-3 py-4 whitespace-nowrap text-sm font-bold text-right cursor-pointer hover:bg-blue-50 ${row.cfi >= 0 ? 'text-green-600' : 'text-red-600'}`, onClick: () => onCellClick?.(row, 'cfi'), children: formatCurrency(row.cfi) }, index)))] }), _jsxs("tr", { className: "bg-gray-100", children: [_jsx("td", { className: "sticky left-0 bg-gray-100 px-6 py-3 whitespace-nowrap text-sm font-bold text-gray-900 border-r", children: "FINANCING ACTIVITIES" }), displayData.map((_, index) => (_jsx("td", { className: "px-3 py-3" }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-8", children: "Debt Draws" }), displayData.map((row, index) => (_jsx("td", { className: `px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50 ${row.debt_draws >= 0 ? 'text-green-600' : 'text-red-600'}`, onClick: () => onCellClick?.(row, 'debt_draws'), children: formatCurrency(row.debt_draws) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-8", children: "Principal Payments" }), displayData.map((row, index) => (_jsx("td", { className: `px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50 ${row.debt_principal_payments <= 0 ? 'text-red-600' : 'text-green-600'}`, onClick: () => onCellClick?.(row, 'debt_principal_payments'), children: formatCurrency(row.debt_principal_payments) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50 border-b-2", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 border-r", children: "Cash Flow from Financing" }), displayData.map((row, index) => (_jsx("td", { className: `px-3 py-4 whitespace-nowrap text-sm font-bold text-right cursor-pointer hover:bg-blue-50 ${row.cff >= 0 ? 'text-green-600' : 'text-red-600'}`, onClick: () => onCellClick?.(row, 'cff'), children: formatCurrency(row.cff) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 border-r", children: "Net Change in Cash" }), displayData.map((row, index) => (_jsx("td", { className: `px-3 py-4 whitespace-nowrap text-sm font-bold text-right cursor-pointer hover:bg-blue-50 ${row.net_change_cash >= 0 ? 'text-green-600' : 'text-red-600'}`, onClick: () => onCellClick?.(row, 'net_change_cash'), children: formatCurrency(row.net_change_cash) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r", children: "Beginning Cash" }), displayData.map((row, index) => (_jsx("td", { className: "px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50", onClick: () => onCellClick?.(row, 'beginning_cash'), children: formatCurrency(row.beginning_cash) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50 border-t-2", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 border-r", children: "Ending Cash" }), displayData.map((row, index) => (_jsx("td", { className: `px-3 py-4 whitespace-nowrap text-sm font-bold text-right cursor-pointer hover:bg-blue-50 ${row.ending_cash >= 0 ? 'text-green-600' : 'text-red-600'}`, onClick: () => onCellClick?.(row, 'ending_cash'), children: formatCurrency(row.ending_cash) }, index)))] })] })] }));
}
// Helper function to aggregate monthly data into yearly
function aggregateYearly(monthlyData) {
    const yearlyData = [];
    for (let year = 1; year <= Math.ceil(monthlyData.length / 12); year++) {
        const startMonth = (year - 1) * 12;
        const endMonth = Math.min(year * 12, monthlyData.length);
        const yearData = monthlyData.slice(startMonth, endMonth);
        if (yearData.length === 0)
            break;
        // Sum all flow items for the year
        const aggregated = {
            period: year,
            date: yearData[yearData.length - 1].date,
            net_income: yearData.reduce((sum, row) => sum + row.net_income, 0),
            depreciation: yearData.reduce((sum, row) => sum + row.depreciation, 0),
            change_ar: yearData.reduce((sum, row) => sum + row.change_ar, 0),
            change_inventory: yearData.reduce((sum, row) => sum + row.change_inventory, 0),
            change_ap: yearData.reduce((sum, row) => sum + row.change_ap, 0),
            cfo: yearData.reduce((sum, row) => sum + row.cfo, 0),
            capex: yearData.reduce((sum, row) => sum + row.capex, 0),
            cfi: yearData.reduce((sum, row) => sum + row.cfi, 0),
            debt_draws: yearData.reduce((sum, row) => sum + row.debt_draws, 0),
            debt_principal_payments: yearData.reduce((sum, row) => sum + row.debt_principal_payments, 0),
            cff: yearData.reduce((sum, row) => sum + row.cff, 0),
            net_change_cash: yearData.reduce((sum, row) => sum + row.net_change_cash, 0),
            beginning_cash: yearData[0].beginning_cash, // First month of year
            ending_cash: yearData[yearData.length - 1].ending_cash, // Last month of year
        };
        yearlyData.push(aggregated);
    }
    return yearlyData;
}
