import { CFRow } from '@/lib/engine/types'
import { aggregateYearly } from '@/lib/utils/aggregation'

interface CashFlowTableProps {
  data: CFRow[]
  viewMode: 'monthly' | 'yearly'
  formatCurrency: (value: number) => string
  onCellClick?: (row: CFRow, field: string) => void
}

export function CashFlowTable({ data, viewMode, formatCurrency, onCellClick }: CashFlowTableProps) {
  // Aggregate data for yearly view using centralized utility
  const processedData = viewMode === 'yearly' ? aggregateYearly(data, 'cf') : data

  // Show first 12 periods for monthly, all for yearly
  const displayData = viewMode === 'monthly' ? processedData.slice(0, 12) : processedData

  return (
    <table className="min-w-full divide-y divide-gray-200">
      <thead className="bg-gray-50">
        <tr>
          <th className="sticky left-0 bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r">
            Cash Flow Statement
          </th>
          {displayData.map((row, index) => (
            <th key={index} className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
              {viewMode === 'monthly' 
                ? `Mo ${row.period}`
                : `Year ${Math.ceil(row.period / 12)}`
              }
            </th>
          ))}
        </tr>
      </thead>
      <tbody className="bg-white divide-y divide-gray-200">
        {/* OPERATING ACTIVITIES */}
        <tr className="bg-gray-100">
          <td className="sticky left-0 bg-gray-100 px-6 py-3 whitespace-nowrap text-sm font-bold text-gray-900 border-r">
            OPERATING ACTIVITIES
          </td>
          {displayData.map((_, index) => (
            <td key={index} className="px-3 py-3"></td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-8">
            Net Income
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className={`px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50 ${
                row.net_income >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
              onClick={() => onCellClick?.(row, 'net_income')}
            >
              {formatCurrency(row.net_income)}
            </td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-8">
            Depreciation
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50"
              onClick={() => onCellClick?.(row, 'depreciation')}
            >
              {formatCurrency(row.depreciation)}
            </td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-8">
            Change in A/R
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className={`px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50 ${
                row.change_ar <= 0 ? 'text-green-600' : 'text-red-600'
              }`}
              onClick={() => onCellClick?.(row, 'change_ar')}
            >
              {formatCurrency(row.change_ar)}
            </td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-8">
            Change in Inventory
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className={`px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50 ${
                row.change_inventory <= 0 ? 'text-green-600' : 'text-red-600'
              }`}
              onClick={() => onCellClick?.(row, 'change_inventory')}
            >
              {formatCurrency(row.change_inventory)}
            </td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-8">
            Change in A/P
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className={`px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50 ${
                row.change_ap >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
              onClick={() => onCellClick?.(row, 'change_ap')}
            >
              {formatCurrency(row.change_ap)}
            </td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50 border-b-2">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 border-r">
            Cash Flow from Operations
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className={`px-3 py-4 whitespace-nowrap text-sm font-bold text-right cursor-pointer hover:bg-blue-50 ${
                row.cfo >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
              onClick={() => onCellClick?.(row, 'cfo')}
            >
              {formatCurrency(row.cfo)}
            </td>
          ))}
        </tr>

        {/* INVESTING ACTIVITIES */}
        <tr className="bg-gray-100">
          <td className="sticky left-0 bg-gray-100 px-6 py-3 whitespace-nowrap text-sm font-bold text-gray-900 border-r">
            INVESTING ACTIVITIES
          </td>
          {displayData.map((_, index) => (
            <td key={index} className="px-3 py-3"></td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-8">
            Capital Expenditures
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className={`px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50 ${
                row.capex <= 0 ? 'text-red-600' : 'text-green-600'
              }`}
              onClick={() => onCellClick?.(row, 'capex')}
            >
              {formatCurrency(row.capex)}
            </td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50 border-b-2">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 border-r">
            Cash Flow from Investing
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className={`px-3 py-4 whitespace-nowrap text-sm font-bold text-right cursor-pointer hover:bg-blue-50 ${
                row.cfi >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
              onClick={() => onCellClick?.(row, 'cfi')}
            >
              {formatCurrency(row.cfi)}
            </td>
          ))}
        </tr>

        {/* FINANCING ACTIVITIES */}
        <tr className="bg-gray-100">
          <td className="sticky left-0 bg-gray-100 px-6 py-3 whitespace-nowrap text-sm font-bold text-gray-900 border-r">
            FINANCING ACTIVITIES
          </td>
          {displayData.map((_, index) => (
            <td key={index} className="px-3 py-3"></td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-8">
            Debt Draws
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className={`px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50 ${
                row.debt_draws >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
              onClick={() => onCellClick?.(row, 'debt_draws')}
            >
              {formatCurrency(row.debt_draws)}
            </td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-8">
            Principal Payments
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className={`px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50 ${
                row.debt_principal_payments <= 0 ? 'text-red-600' : 'text-green-600'
              }`}
              onClick={() => onCellClick?.(row, 'debt_principal_payments')}
            >
              {formatCurrency(row.debt_principal_payments)}
            </td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50 border-b-2">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 border-r">
            Cash Flow from Financing
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className={`px-3 py-4 whitespace-nowrap text-sm font-bold text-right cursor-pointer hover:bg-blue-50 ${
                row.cff >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
              onClick={() => onCellClick?.(row, 'cff')}
            >
              {formatCurrency(row.cff)}
            </td>
          ))}
        </tr>

        {/* NET CHANGE IN CASH */}
        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 border-r">
            Net Change in Cash
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className={`px-3 py-4 whitespace-nowrap text-sm font-bold text-right cursor-pointer hover:bg-blue-50 ${
                row.net_change_cash >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
              onClick={() => onCellClick?.(row, 'net_change_cash')}
            >
              {formatCurrency(row.net_change_cash)}
            </td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r">
            Beginning Cash
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50"
              onClick={() => onCellClick?.(row, 'beginning_cash')}
            >
              {formatCurrency(row.beginning_cash)}
            </td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50 border-t-2">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 border-r">
            Ending Cash
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className={`px-3 py-4 whitespace-nowrap text-sm font-bold text-right cursor-pointer hover:bg-blue-50 ${
                row.ending_cash >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
              onClick={() => onCellClick?.(row, 'ending_cash')}
            >
              {formatCurrency(row.ending_cash)}
            </td>
          ))}
        </tr>
      </tbody>
    </table>
  )
}

