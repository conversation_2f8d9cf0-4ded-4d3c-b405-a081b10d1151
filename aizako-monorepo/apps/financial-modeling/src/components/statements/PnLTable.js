import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
export function PnLTable({ data, viewMode, formatCurrency, onCellClick }) {
    // Aggregate data for yearly view
    const processedData = viewMode === 'yearly' ? aggregateYearly(data) : data;
    // Show first 12 periods for monthly, all for yearly
    const displayData = viewMode === 'monthly' ? processedData.slice(0, 12) : processedData;
    return (_jsxs("table", { className: "min-w-full divide-y divide-gray-200", children: [_jsx("thead", { className: "bg-gray-50", children: _jsxs("tr", { children: [_jsx("th", { className: "sticky left-0 bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r", children: "P&L Statement" }), displayData.map((row, index) => (_jsx("th", { className: "px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]", children: viewMode === 'monthly'
                                ? `Mo ${row.period}`
                                : `Year ${Math.ceil(row.period / 12)}` }, index)))] }) }), _jsxs("tbody", { className: "bg-white divide-y divide-gray-200", children: [_jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r", children: "Revenue" }), displayData.map((row, index) => (_jsx("td", { className: "px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50", onClick: () => onCellClick?.(row, 'revenue'), children: formatCurrency(row.revenue) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r", children: "Cost of Goods Sold" }), displayData.map((row, index) => (_jsxs("td", { className: "px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50", onClick: () => onCellClick?.(row, 'cogs'), children: ["(", formatCurrency(row.cogs), ")"] }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50 border-b-2", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r", children: "Gross Profit" }), displayData.map((row, index) => (_jsx("td", { className: "px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right cursor-pointer hover:bg-blue-50", onClick: () => onCellClick?.(row, 'gross_profit'), children: formatCurrency(row.gross_profit) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r", children: "Fixed OpEx" }), displayData.map((row, index) => (_jsxs("td", { className: "px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50", onClick: () => onCellClick?.(row, 'opex_fixed'), children: ["(", formatCurrency(row.opex_fixed), ")"] }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r", children: "Variable OpEx" }), displayData.map((row, index) => (_jsxs("td", { className: "px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50", onClick: () => onCellClick?.(row, 'opex_variable'), children: ["(", formatCurrency(row.opex_variable), ")"] }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50 border-b-2", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r", children: "EBITDA" }), displayData.map((row, index) => (_jsx("td", { className: `px-3 py-4 whitespace-nowrap text-sm font-medium text-right cursor-pointer hover:bg-blue-50 ${row.ebitda >= 0 ? 'text-green-600' : 'text-red-600'}`, onClick: () => onCellClick?.(row, 'ebitda'), children: formatCurrency(row.ebitda) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r", children: "Depreciation" }), displayData.map((row, index) => (_jsxs("td", { className: "px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50", onClick: () => onCellClick?.(row, 'depreciation'), children: ["(", formatCurrency(row.depreciation), ")"] }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r", children: "EBIT" }), displayData.map((row, index) => (_jsx("td", { className: `px-3 py-4 whitespace-nowrap text-sm font-medium text-right cursor-pointer hover:bg-blue-50 ${row.ebit >= 0 ? 'text-green-600' : 'text-red-600'}`, onClick: () => onCellClick?.(row, 'ebit'), children: formatCurrency(row.ebit) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r", children: "Interest Expense" }), displayData.map((row, index) => (_jsxs("td", { className: "px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50", onClick: () => onCellClick?.(row, 'interest_expense'), children: ["(", formatCurrency(row.interest_expense), ")"] }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r", children: "Earnings Before Tax" }), displayData.map((row, index) => (_jsx("td", { className: `px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50 ${row.ebt >= 0 ? 'text-green-600' : 'text-red-600'}`, onClick: () => onCellClick?.(row, 'ebt'), children: formatCurrency(row.ebt) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r", children: "Tax Expense" }), displayData.map((row, index) => (_jsxs("td", { className: "px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50", onClick: () => onCellClick?.(row, 'tax_expense'), children: ["(", formatCurrency(row.tax_expense), ")"] }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50 border-t-2", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 border-r", children: "Net Income" }), displayData.map((row, index) => (_jsx("td", { className: `px-3 py-4 whitespace-nowrap text-sm font-bold text-right cursor-pointer hover:bg-blue-50 ${row.net_income >= 0 ? 'text-green-600' : 'text-red-600'}`, onClick: () => onCellClick?.(row, 'net_income'), children: formatCurrency(row.net_income) }, index)))] })] })] }));
}
// Helper function to aggregate monthly data into yearly
function aggregateYearly(monthlyData) {
    const yearlyData = [];
    for (let year = 1; year <= Math.ceil(monthlyData.length / 12); year++) {
        const startMonth = (year - 1) * 12;
        const endMonth = Math.min(year * 12, monthlyData.length);
        const yearData = monthlyData.slice(startMonth, endMonth);
        if (yearData.length === 0)
            break;
        // Sum flow items, take end-of-year for stock items
        const aggregated = {
            period: year,
            date: yearData[yearData.length - 1].date,
            revenue: yearData.reduce((sum, row) => sum + row.revenue, 0),
            cogs: yearData.reduce((sum, row) => sum + row.cogs, 0),
            gross_profit: yearData.reduce((sum, row) => sum + row.gross_profit, 0),
            opex_fixed: yearData.reduce((sum, row) => sum + row.opex_fixed, 0),
            opex_variable: yearData.reduce((sum, row) => sum + row.opex_variable, 0),
            total_opex: yearData.reduce((sum, row) => sum + row.total_opex, 0),
            ebitda: yearData.reduce((sum, row) => sum + row.ebitda, 0),
            depreciation: yearData.reduce((sum, row) => sum + row.depreciation, 0),
            ebit: yearData.reduce((sum, row) => sum + row.ebit, 0),
            interest_expense: yearData.reduce((sum, row) => sum + row.interest_expense, 0),
            ebt: yearData.reduce((sum, row) => sum + row.ebt, 0),
            tax_expense: yearData.reduce((sum, row) => sum + row.tax_expense, 0),
            net_income: yearData.reduce((sum, row) => sum + row.net_income, 0),
        };
        yearlyData.push(aggregated);
    }
    return yearlyData;
}
