import { BSRow } from '@/lib/engine/types'
import { aggregateYearly } from '@/lib/utils/aggregation'

interface BalanceSheetTableProps {
  data: BSRow[]
  viewMode: 'monthly' | 'yearly'
  formatCurrency: (value: number) => string
  onCellClick?: (row: BSRow, field: string) => void
}

export function BalanceSheetTable({ data, viewMode, formatCurrency, onCellClick }: BalanceSheetTableProps) {
  // For yearly view, take end-of-year snapshots using centralized utility
  const processedData = viewMode === 'yearly' ? aggregateYearly(data, 'bs') : data

  // Show first 12 periods for monthly, all for yearly
  const displayData = viewMode === 'monthly' ? processedData.slice(0, 12) : processedData

  return (
    <table className="min-w-full divide-y divide-gray-200">
      <thead className="bg-gray-50">
        <tr>
          <th className="sticky left-0 bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r">
            Balance Sheet
          </th>
          {displayData.map((row, index) => (
            <th key={index} className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
              {viewMode === 'monthly' 
                ? `Mo ${row.period}`
                : `Year ${Math.ceil(row.period / 12)}`
              }
            </th>
          ))}
        </tr>
      </thead>
      <tbody className="bg-white divide-y divide-gray-200">
        {/* ASSETS SECTION */}
        <tr className="bg-gray-100">
          <td className="sticky left-0 bg-gray-100 px-6 py-3 whitespace-nowrap text-sm font-bold text-gray-900 border-r">
            ASSETS
          </td>
          {displayData.map((_, index) => (
            <td key={index} className="px-3 py-3"></td>
          ))}
        </tr>

        {/* Current Assets */}
        <tr className="bg-gray-50">
          <td className="sticky left-0 bg-gray-50 px-6 py-2 whitespace-nowrap text-sm font-medium text-gray-700 border-r pl-8">
            Current Assets
          </td>
          {displayData.map((_, index) => (
            <td key={index} className="px-3 py-2"></td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-12">
            Cash
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50"
              onClick={() => onCellClick?.(row, 'cash')}
            >
              {formatCurrency(row.cash)}
            </td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-12">
            Accounts Receivable
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50"
              onClick={() => onCellClick?.(row, 'ar')}
            >
              {formatCurrency(row.ar)}
            </td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-12">
            Inventory
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50"
              onClick={() => onCellClick?.(row, 'inventory')}
            >
              {formatCurrency(row.inventory)}
            </td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50 border-b">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r pl-8">
            Total Current Assets
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right cursor-pointer hover:bg-blue-50"
              onClick={() => onCellClick?.(row, 'total_current_assets')}
            >
              {formatCurrency(row.total_current_assets)}
            </td>
          ))}
        </tr>

        {/* Fixed Assets */}
        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-8">
            PP&E (Net)
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50"
              onClick={() => onCellClick?.(row, 'ppne_net')}
            >
              {formatCurrency(row.ppne_net)}
            </td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50 border-b-2">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 border-r">
            TOTAL ASSETS
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className="px-3 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right cursor-pointer hover:bg-blue-50"
              onClick={() => onCellClick?.(row, 'total_assets')}
            >
              {formatCurrency(row.total_assets)}
            </td>
          ))}
        </tr>

        {/* LIABILITIES SECTION */}
        <tr className="bg-gray-100">
          <td className="sticky left-0 bg-gray-100 px-6 py-3 whitespace-nowrap text-sm font-bold text-gray-900 border-r">
            LIABILITIES & EQUITY
          </td>
          {displayData.map((_, index) => (
            <td key={index} className="px-3 py-3"></td>
          ))}
        </tr>

        {/* Current Liabilities */}
        <tr className="bg-gray-50">
          <td className="sticky left-0 bg-gray-50 px-6 py-2 whitespace-nowrap text-sm font-medium text-gray-700 border-r pl-8">
            Current Liabilities
          </td>
          {displayData.map((_, index) => (
            <td key={index} className="px-3 py-2"></td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-12">
            Accounts Payable
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50"
              onClick={() => onCellClick?.(row, 'ap')}
            >
              {formatCurrency(row.ap)}
            </td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-12">
            Current Debt
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50"
              onClick={() => onCellClick?.(row, 'debt_current')}
            >
              {formatCurrency(row.debt_current)}
            </td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50 border-b">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r pl-8">
            Total Current Liabilities
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right cursor-pointer hover:bg-blue-50"
              onClick={() => onCellClick?.(row, 'total_current_liabilities')}
            >
              {formatCurrency(row.total_current_liabilities)}
            </td>
          ))}
        </tr>

        {/* Long-term Liabilities */}
        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-8">
            Long-term Debt
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50"
              onClick={() => onCellClick?.(row, 'debt_long')}
            >
              {formatCurrency(row.debt_long)}
            </td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50 border-b">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r pl-8">
            Total Liabilities
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right cursor-pointer hover:bg-blue-50"
              onClick={() => onCellClick?.(row, 'total_liabilities')}
            >
              {formatCurrency(row.total_liabilities)}
            </td>
          ))}
        </tr>

        {/* Equity */}
        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-8">
            Retained Earnings
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className={`px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50 ${
                row.retained_earnings >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
              onClick={() => onCellClick?.(row, 'retained_earnings')}
            >
              {formatCurrency(row.retained_earnings)}
            </td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50 border-b">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r pl-8">
            Total Equity
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className={`px-3 py-4 whitespace-nowrap text-sm font-medium text-right cursor-pointer hover:bg-blue-50 ${
                row.total_equity >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
              onClick={() => onCellClick?.(row, 'total_equity')}
            >
              {formatCurrency(row.total_equity)}
            </td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50 border-t-2">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 border-r">
            TOTAL LIAB & EQUITY
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className="px-3 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right cursor-pointer hover:bg-blue-50"
              onClick={() => onCellClick?.(row, 'total_liab_equity')}
            >
              {formatCurrency(row.total_liab_equity)}
            </td>
          ))}
        </tr>
      </tbody>
    </table>
  )
}

