import { PnLRow } from '@/lib/engine/types'
import { aggregateYearly } from '@/lib/utils/aggregation'

interface PnLTableProps {
  data: PnLRow[]
  viewMode: 'monthly' | 'yearly'
  formatCurrency: (value: number) => string
  onCellClick?: (row: PnLRow, field: string) => void
}

export function PnLTable({ data, viewMode, formatCurrency, onCellClick }: PnLTableProps) {
  // Aggregate data for yearly view using centralized utility
  const processedData = viewMode === 'yearly' ? aggregateYearly(data, 'pnl') : data

  // Show first 12 periods for monthly, all for yearly
  const displayData = viewMode === 'monthly' ? processedData.slice(0, 12) : processedData

  return (
    <table className="min-w-full divide-y divide-gray-200">
      <thead className="bg-gray-50">
        <tr>
          <th className="sticky left-0 bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r">
            P&L Statement
          </th>
          {displayData.map((row, index) => (
            <th key={index} className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
              {viewMode === 'monthly' 
                ? `Mo ${row.period}`
                : `Year ${Math.ceil(row.period / 12)}`
              }
            </th>
          ))}
        </tr>
      </thead>
      <tbody className="bg-white divide-y divide-gray-200">
        {/* Revenue */}
        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r">
            Revenue
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50"
              onClick={() => onCellClick?.(row, 'revenue')}
            >
              {formatCurrency(row.revenue)}
            </td>
          ))}
        </tr>

        {/* COGS */}
        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r">
            Cost of Goods Sold
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50"
              onClick={() => onCellClick?.(row, 'cogs')}
            >
              ({formatCurrency(row.cogs)})
            </td>
          ))}
        </tr>

        {/* Gross Profit */}
        <tr className="hover:bg-gray-50 border-b-2">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r">
            Gross Profit
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right cursor-pointer hover:bg-blue-50"
              onClick={() => onCellClick?.(row, 'gross_profit')}
            >
              {formatCurrency(row.gross_profit)}
            </td>
          ))}
        </tr>

        {/* Operating Expenses */}
        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r">
            Fixed OpEx
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50"
              onClick={() => onCellClick?.(row, 'opex_fixed')}
            >
              ({formatCurrency(row.opex_fixed)})
            </td>
          ))}
        </tr>

        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r">
            Variable OpEx
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50"
              onClick={() => onCellClick?.(row, 'opex_variable')}
            >
              ({formatCurrency(row.opex_variable)})
            </td>
          ))}
        </tr>

        {/* EBITDA */}
        <tr className="hover:bg-gray-50 border-b-2">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r">
            EBITDA
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className={`px-3 py-4 whitespace-nowrap text-sm font-medium text-right cursor-pointer hover:bg-blue-50 ${
                row.ebitda >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
              onClick={() => onCellClick?.(row, 'ebitda')}
            >
              {formatCurrency(row.ebitda)}
            </td>
          ))}
        </tr>

        {/* Depreciation */}
        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r">
            Depreciation
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50"
              onClick={() => onCellClick?.(row, 'depreciation')}
            >
              ({formatCurrency(row.depreciation)})
            </td>
          ))}
        </tr>

        {/* EBIT */}
        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r">
            EBIT
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className={`px-3 py-4 whitespace-nowrap text-sm font-medium text-right cursor-pointer hover:bg-blue-50 ${
                row.ebit >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
              onClick={() => onCellClick?.(row, 'ebit')}
            >
              {formatCurrency(row.ebit)}
            </td>
          ))}
        </tr>

        {/* Interest Expense */}
        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r">
            Interest Expense
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50"
              onClick={() => onCellClick?.(row, 'interest_expense')}
            >
              ({formatCurrency(row.interest_expense)})
            </td>
          ))}
        </tr>

        {/* EBT */}
        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r">
            Earnings Before Tax
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className={`px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50 ${
                row.ebt >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
              onClick={() => onCellClick?.(row, 'ebt')}
            >
              {formatCurrency(row.ebt)}
            </td>
          ))}
        </tr>

        {/* Tax Expense */}
        <tr className="hover:bg-gray-50">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r">
            Tax Expense
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50"
              onClick={() => onCellClick?.(row, 'tax_expense')}
            >
              ({formatCurrency(row.tax_expense)})
            </td>
          ))}
        </tr>

        {/* Net Income */}
        <tr className="hover:bg-gray-50 border-t-2">
          <td className="sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 border-r">
            Net Income
          </td>
          {displayData.map((row, index) => (
            <td 
              key={index} 
              className={`px-3 py-4 whitespace-nowrap text-sm font-bold text-right cursor-pointer hover:bg-blue-50 ${
                row.net_income >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
              onClick={() => onCellClick?.(row, 'net_income')}
            >
              {formatCurrency(row.net_income)}
            </td>
          ))}
        </tr>
      </tbody>
    </table>
  )
}

