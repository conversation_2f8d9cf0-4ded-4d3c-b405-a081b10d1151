import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
export function BalanceSheetTable({ data, viewMode, formatCurrency, onCellClick }) {
    // For yearly view, take end-of-year snapshots
    const processedData = viewMode === 'yearly' ? aggregateYearly(data) : data;
    // Show first 12 periods for monthly, all for yearly
    const displayData = viewMode === 'monthly' ? processedData.slice(0, 12) : processedData;
    return (_jsxs("table", { className: "min-w-full divide-y divide-gray-200", children: [_jsx("thead", { className: "bg-gray-50", children: _jsxs("tr", { children: [_jsx("th", { className: "sticky left-0 bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r", children: "Balance Sheet" }), displayData.map((row, index) => (_jsx("th", { className: "px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]", children: viewMode === 'monthly'
                                ? `Mo ${row.period}`
                                : `Year ${Math.ceil(row.period / 12)}` }, index)))] }) }), _jsxs("tbody", { className: "bg-white divide-y divide-gray-200", children: [_jsxs("tr", { className: "bg-gray-100", children: [_jsx("td", { className: "sticky left-0 bg-gray-100 px-6 py-3 whitespace-nowrap text-sm font-bold text-gray-900 border-r", children: "ASSETS" }), displayData.map((_, index) => (_jsx("td", { className: "px-3 py-3" }, index)))] }), _jsxs("tr", { className: "bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-gray-50 px-6 py-2 whitespace-nowrap text-sm font-medium text-gray-700 border-r pl-8", children: "Current Assets" }), displayData.map((_, index) => (_jsx("td", { className: "px-3 py-2" }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-12", children: "Cash" }), displayData.map((row, index) => (_jsx("td", { className: "px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50", onClick: () => onCellClick?.(row, 'cash'), children: formatCurrency(row.cash) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-12", children: "Accounts Receivable" }), displayData.map((row, index) => (_jsx("td", { className: "px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50", onClick: () => onCellClick?.(row, 'ar'), children: formatCurrency(row.ar) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-12", children: "Inventory" }), displayData.map((row, index) => (_jsx("td", { className: "px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50", onClick: () => onCellClick?.(row, 'inventory'), children: formatCurrency(row.inventory) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50 border-b", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r pl-8", children: "Total Current Assets" }), displayData.map((row, index) => (_jsx("td", { className: "px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right cursor-pointer hover:bg-blue-50", onClick: () => onCellClick?.(row, 'total_current_assets'), children: formatCurrency(row.total_current_assets) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-8", children: "PP&E (Net)" }), displayData.map((row, index) => (_jsx("td", { className: "px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50", onClick: () => onCellClick?.(row, 'ppne_net'), children: formatCurrency(row.ppne_net) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50 border-b-2", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 border-r", children: "TOTAL ASSETS" }), displayData.map((row, index) => (_jsx("td", { className: "px-3 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right cursor-pointer hover:bg-blue-50", onClick: () => onCellClick?.(row, 'total_assets'), children: formatCurrency(row.total_assets) }, index)))] }), _jsxs("tr", { className: "bg-gray-100", children: [_jsx("td", { className: "sticky left-0 bg-gray-100 px-6 py-3 whitespace-nowrap text-sm font-bold text-gray-900 border-r", children: "LIABILITIES & EQUITY" }), displayData.map((_, index) => (_jsx("td", { className: "px-3 py-3" }, index)))] }), _jsxs("tr", { className: "bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-gray-50 px-6 py-2 whitespace-nowrap text-sm font-medium text-gray-700 border-r pl-8", children: "Current Liabilities" }), displayData.map((_, index) => (_jsx("td", { className: "px-3 py-2" }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-12", children: "Accounts Payable" }), displayData.map((row, index) => (_jsx("td", { className: "px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50", onClick: () => onCellClick?.(row, 'ap'), children: formatCurrency(row.ap) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-12", children: "Current Debt" }), displayData.map((row, index) => (_jsx("td", { className: "px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50", onClick: () => onCellClick?.(row, 'debt_current'), children: formatCurrency(row.debt_current) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50 border-b", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r pl-8", children: "Total Current Liabilities" }), displayData.map((row, index) => (_jsx("td", { className: "px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right cursor-pointer hover:bg-blue-50", onClick: () => onCellClick?.(row, 'total_current_liabilities'), children: formatCurrency(row.total_current_liabilities) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-8", children: "Long-term Debt" }), displayData.map((row, index) => (_jsx("td", { className: "px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50", onClick: () => onCellClick?.(row, 'debt_long'), children: formatCurrency(row.debt_long) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50 border-b", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r pl-8", children: "Total Liabilities" }), displayData.map((row, index) => (_jsx("td", { className: "px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right cursor-pointer hover:bg-blue-50", onClick: () => onCellClick?.(row, 'total_liabilities'), children: formatCurrency(row.total_liabilities) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r pl-8", children: "Retained Earnings" }), displayData.map((row, index) => (_jsx("td", { className: `px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right cursor-pointer hover:bg-blue-50 ${row.retained_earnings >= 0 ? 'text-green-600' : 'text-red-600'}`, onClick: () => onCellClick?.(row, 'retained_earnings'), children: formatCurrency(row.retained_earnings) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50 border-b", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r pl-8", children: "Total Equity" }), displayData.map((row, index) => (_jsx("td", { className: `px-3 py-4 whitespace-nowrap text-sm font-medium text-right cursor-pointer hover:bg-blue-50 ${row.total_equity >= 0 ? 'text-green-600' : 'text-red-600'}`, onClick: () => onCellClick?.(row, 'total_equity'), children: formatCurrency(row.total_equity) }, index)))] }), _jsxs("tr", { className: "hover:bg-gray-50 border-t-2", children: [_jsx("td", { className: "sticky left-0 bg-white px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 border-r", children: "TOTAL LIAB & EQUITY" }), displayData.map((row, index) => (_jsx("td", { className: "px-3 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right cursor-pointer hover:bg-blue-50", onClick: () => onCellClick?.(row, 'total_liab_equity'), children: formatCurrency(row.total_liab_equity) }, index)))] })] })] }));
}
// Helper function to get year-end snapshots
function aggregateYearly(monthlyData) {
    const yearlyData = [];
    for (let year = 1; year <= Math.ceil(monthlyData.length / 12); year++) {
        const yearEndMonth = Math.min(year * 12 - 1, monthlyData.length - 1);
        const yearEndData = monthlyData[yearEndMonth];
        if (!yearEndData)
            break;
        // For balance sheet, we take the year-end snapshot
        yearlyData.push({
            ...yearEndData,
            period: year,
        });
    }
    return yearlyData;
}
