'use client';

import { useState, useEffect, useMemo } from 'react';
import type { Config } from '@/lib/engine/types';
import { 
  extractLoanConfig, 
  compareLoanScenarios, 
  validateLoanData,
  formatCurrency,
  formatRatio,
  formatPercentage,
  getGracePeriodExample,
  GRACE_PERIOD_EXPLANATIONS,
  type LoanFormData,
  type LoanValidationError
} from '@/lib/loan';

interface LoanEditorProps {
  baseConfig: Config;
  currency?: string;
  onSave?: (loanData: LoanFormData) => Promise<void>;
  disabled?: boolean;
}

type TooltipKey = keyof typeof GRACE_PERIOD_EXPLANATIONS;

export function LoanEditor({ baseConfig, currency = 'USD', onSave, disabled = false }: LoanEditorProps) {
  // Form state
  const [loanData, setLoanData] = useState<LoanFormData>(() => extractLoanConfig(baseConfig));
  const [errors, setErrors] = useState<LoanValidationError[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const [saving, setSaving] = useState(false);
  const [activeTooltip, setActiveTooltip] = useState<TooltipKey | null>(null);

  // Debounced comparison calculation
  const comparison = useMemo(() => {
    if (!showPreview) return null;
    
    try {
      return compareLoanScenarios(baseConfig, loanData);
    } catch (error) {
      console.error('Error calculating loan comparison:', error);
      return null;
    }
  }, [baseConfig, loanData, showPreview]);

  // Validate form data on change
  useEffect(() => {
    const validationErrors = validateLoanData(loanData, baseConfig.meta.periods);
    setErrors(validationErrors);
  }, [loanData, baseConfig.meta.periods]);

  // Update form field
  const updateField = <K extends keyof LoanFormData>(field: K, value: LoanFormData[K]) => {
    setLoanData(prev => ({ ...prev, [field]: value }));
  };

  // Add draw
  const addDraw = () => {
    const newDraw = { month: 1, amount: 0 };
    updateField('draws', [...loanData.draws, newDraw]);
  };

  // Remove draw
  const removeDraw = (index: number) => {
    const newDraws = loanData.draws.filter((_, i) => i !== index);
    updateField('draws', newDraws);
  };

  // Update draw
  const updateDraw = (index: number, field: 'month' | 'amount', value: number) => {
    const newDraws = [...loanData.draws];
    newDraws[index] = { ...newDraws[index], [field]: value };
    updateField('draws', newDraws);
  };

  // Handle save
  const handleSave = async () => {
    if (errors.length > 0 || !onSave) return;
    
    setSaving(true);
    try {
      await onSave(loanData);
    } catch (error) {
      console.error('Error saving loan data:', error);
    } finally {
      setSaving(false);
    }
  };

  // Handle reset
  const handleReset = () => {
    setLoanData(extractLoanConfig(baseConfig));
    setShowPreview(false);
  };

  // Handle preview toggle
  const handlePreview = () => {
    if (errors.length === 0) {
      setShowPreview(!showPreview);
    }
  };

  // Get field error
  const getFieldError = (field: string) => {
    return errors.find(error => error.field === field)?.message;
  };

  // Tooltip component
  const Tooltip = ({ content, show }: { content: string; show: boolean }) => (
    <div className={`absolute left-6 top-0 z-10 w-72 px-3 py-2 text-xs text-white bg-gray-900 rounded-lg shadow-lg transition-opacity duration-200 ${show ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}>
      {content}
    </div>
  );

  // Help icon with tooltip
  const HelpIcon = ({ tooltipKey }: { tooltipKey: TooltipKey }) => (
    <div className="group relative inline-block ml-2">
      <button
        type="button"
        onMouseEnter={() => setActiveTooltip(tooltipKey)}
        onMouseLeave={() => setActiveTooltip(null)}
        className="h-4 w-4 text-gray-400 hover:text-gray-600 cursor-help"
        aria-label="Help"
      >
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </button>
      <Tooltip content={GRACE_PERIOD_EXPLANATIONS[tooltipKey]} show={activeTooltip === tooltipKey} />
    </div>
  );

  // Format delta with color
  const formatDelta = (abs: number, pct: number) => {
    const sign = abs >= 0 ? '+' : '';
    const colorClass = abs >= 0 ? 'text-green-600' : 'text-red-600';
    return {
      abs: `${sign}${formatCurrency(abs)}`,
      pct: `${sign}${formatPercentage(pct)}`,
      className: colorClass
    };
  };

  const formatRatioDelta = (abs: number, pct: number) => {
    const sign = abs >= 0 ? '+' : '';
    const colorClass = abs >= 0 ? 'text-green-600' : 'text-red-600';
    return {
      abs: `${sign}${formatRatio(abs)}`,
      pct: `${sign}${formatPercentage(pct)}`,
      className: colorClass
    };
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
        <div>
          <h2 className="text-lg font-medium text-gray-900">Loan Editor</h2>
          <p className="text-sm text-gray-500 mt-1">
            Modify debt terms and see instant DSCR/ICR impact • Currency: {currency}
          </p>
        </div>
        <div className="flex items-center gap-3 mt-3 sm:mt-0">
          <button
            onClick={handlePreview}
            disabled={errors.length > 0 || disabled}
            className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              errors.length === 0 && !disabled
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            {showPreview ? 'Hide Preview' : 'Preview Changes'}
          </button>
          {onSave && (
            <button
              onClick={handleSave}
              disabled={errors.length > 0 || saving || disabled}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                errors.length === 0 && !saving && !disabled
                  ? 'bg-green-600 text-white hover:bg-green-700'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              {saving ? 'Saving...' : 'Save'}
            </button>
          )}
          <button
            onClick={handleReset}
            disabled={disabled}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors disabled:opacity-50"
          >
            Reset
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Form Section */}
        <div className="space-y-6">
          {/* Basic Terms */}
          <div>
            <h3 className="text-sm font-medium text-gray-900 mb-3">Loan Terms</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label htmlFor="ratePct" className="block text-sm font-medium text-gray-700 mb-1">
                  Interest Rate (%)
                </label>
                <input
                  id="ratePct"
                  type="number"
                  min="0"
                  max="30"
                  step="0.1"
                  value={loanData.ratePct}
                  onChange={(e) => updateField('ratePct', parseFloat(e.target.value) || 0)}
                  className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    getFieldError('ratePct') ? 'border-red-300' : 'border-gray-300'
                  }`}
                  disabled={disabled}
                />
                {getFieldError('ratePct') && (
                  <p className="mt-1 text-xs text-red-600">{getFieldError('ratePct')}</p>
                )}
              </div>
              
              <div>
                <label htmlFor="termMonths" className="block text-sm font-medium text-gray-700 mb-1">
                  Term (months)
                </label>
                <input
                  id="termMonths"
                  type="number"
                  min="0"
                  max="120"
                  value={loanData.termMonths}
                  onChange={(e) => updateField('termMonths', parseInt(e.target.value) || 0)}
                  className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    getFieldError('termMonths') ? 'border-red-300' : 'border-gray-300'
                  }`}
                  disabled={disabled}
                />
                {getFieldError('termMonths') && (
                  <p className="mt-1 text-xs text-red-600">{getFieldError('termMonths')}</p>
                )}
              </div>
            </div>

            <div className="mt-4">
              <label htmlFor="amort" className="block text-sm font-medium text-gray-700 mb-1">
                Amortization Type
              </label>
              <select
                id="amort"
                value={loanData.amort}
                onChange={(e) => updateField('amort', e.target.value as 'annuity' | 'interest_only' | 'bullet')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={disabled}
              >
                <option value="annuity">Annuity (Equal Monthly Payments)</option>
                <option value="interest_only">Interest Only</option>
                <option value="bullet">Bullet (Principal at End)</option>
              </select>
            </div>
          </div>

          {/* Grace Period */}
          <div>
            <div className="flex items-center mb-3">
              <h3 className="text-sm font-medium text-gray-900">Grace Period Settings</h3>
              <HelpIcon tooltipKey="what_is_grace" />
            </div>
            
            <div className="mb-4">
              <label htmlFor="gracePeriodMonths" className="block text-sm font-medium text-gray-700 mb-1">
                Grace Period (months)
              </label>
              <input
                id="gracePeriodMonths"
                type="number"
                min="0"
                max="60"
                value={loanData.gracePeriodMonths}
                onChange={(e) => updateField('gracePeriodMonths', parseInt(e.target.value) || 0)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  getFieldError('gracePeriodMonths') ? 'border-red-300' : 'border-gray-300'
                }`}
                disabled={disabled}
              />
              {getFieldError('gracePeriodMonths') && (
                <p className="mt-1 text-xs text-red-600">{getFieldError('gracePeriodMonths')}</p>
              )}
            </div>

            {loanData.gracePeriodMonths > 0 && (
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-2">During grace period:</p>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="gracePeriodType"
                        value="interest_only"
                        checked={loanData.gracePeriodType === 'interest_only'}
                        onChange={(e) => updateField('gracePeriodType', e.target.value as 'interest_only' | 'capitalized')}
                        className="mr-2"
                        disabled={disabled}
                      />
                      <span className="text-sm text-gray-700">
                        Pay interest only ({comparison?.paymentPreview.gracePeriod.monthlyPayment ? formatCurrency(comparison.paymentPreview.gracePeriod.monthlyPayment) : '$0'}/month)
                      </span>
                      <HelpIcon tooltipKey="interest_only" />
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="gracePeriodType"
                        value="capitalized"
                        checked={loanData.gracePeriodType === 'capitalized'}
                        onChange={(e) => updateField('gracePeriodType', e.target.value as 'interest_only' | 'capitalized')}
                        className="mr-2"
                        disabled={disabled}
                      />
                      <span className="text-sm text-gray-700">Capitalize interest (add to loan balance)</span>
                      <HelpIcon tooltipKey="capitalized" />
                    </label>
                  </div>
                </div>

                {/* Grace Period Example */}
                <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                  <h4 className="text-xs font-medium text-blue-900 mb-1">Example:</h4>
                  <p className="text-xs text-blue-800">
                    {getGracePeriodExample(
                      baseConfig.drivers.debt.opening,
                      loanData.ratePct,
                      loanData.gracePeriodMonths,
                      loanData.gracePeriodType
                    )}
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Draws */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium text-gray-900">Additional Draws</h3>
              <button
                type="button"
                onClick={addDraw}
                disabled={disabled}
                className="px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors disabled:opacity-50"
              >
                Add Draw
              </button>
            </div>
            
            {loanData.draws.length > 0 ? (
              <div className="space-y-2">
                {loanData.draws.map((draw, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className="flex-1">
                      <input
                        type="number"
                        placeholder="Month"
                        min="1"
                        max={baseConfig.meta.periods}
                        value={draw.month}
                        onChange={(e) => updateDraw(index, 'month', parseInt(e.target.value) || 1)}
                        className={`w-full px-2 py-1 text-sm border rounded ${
                          getFieldError(`draws.${index}.month`) ? 'border-red-300' : 'border-gray-300'
                        }`}
                        disabled={disabled}
                      />
                    </div>
                    <div className="flex-2">
                      <input
                        type="number"
                        placeholder="Amount"
                        min="0"
                        step="1000"
                        value={draw.amount}
                        onChange={(e) => updateDraw(index, 'amount', parseFloat(e.target.value) || 0)}
                        className={`w-full px-2 py-1 text-sm border rounded ${
                          getFieldError(`draws.${index}.amount`) ? 'border-red-300' : 'border-gray-300'
                        }`}
                        disabled={disabled}
                      />
                    </div>
                    <button
                      type="button"
                      onClick={() => removeDraw(index)}
                      disabled={disabled}
                      className="p-1 text-red-600 hover:text-red-800 disabled:opacity-50"
                    >
                      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">No additional draws configured</p>
            )}
          </div>
        </div>

        {/* Preview Section */}
        <div>
          {showPreview && comparison ? (
            <div className="space-y-6">
              <div>
                <h3 className="text-sm font-medium text-gray-900 mb-3">Impact Preview</h3>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm border-collapse">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-2 text-xs font-medium text-gray-700">Metric</th>
                        <th className="text-right py-2 text-xs font-medium text-gray-700">Base</th>
                        <th className="text-right py-2 text-xs font-medium text-gray-700">After</th>
                        <th className="text-right py-2 text-xs font-medium text-gray-700">Δ</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-100">
                      <tr className="hover:bg-gray-50">
                        <td className="py-2 pr-4">
                          <div className="flex items-center">
                            <span className="font-medium">DSCR (Y1)</span>
                            <HelpIcon tooltipKey="dscr_explanation" />
                          </div>
                        </td>
                        <td className="py-2 text-right font-mono">{formatRatio(comparison.base.dscr)}</td>
                        <td className="py-2 text-right font-mono">{formatRatio(comparison.after.dscr)}</td>
                        <td className={`py-2 text-right font-mono font-medium ${formatRatioDelta(comparison.deltas.dscr.abs, comparison.deltas.dscr.pct).className}`}>
                          {formatRatioDelta(comparison.deltas.dscr.abs, comparison.deltas.dscr.pct).abs}
                        </td>
                      </tr>
                      <tr className="hover:bg-gray-50">
                        <td className="py-2 pr-4">
                          <div className="flex items-center">
                            <span className="font-medium">ICR (Y1)</span>
                            <HelpIcon tooltipKey="icr_explanation" />
                          </div>
                        </td>
                        <td className="py-2 text-right font-mono">{formatRatio(comparison.base.icr)}</td>
                        <td className="py-2 text-right font-mono">{formatRatio(comparison.after.icr)}</td>
                        <td className={`py-2 text-right font-mono font-medium ${formatRatioDelta(comparison.deltas.icr.abs, comparison.deltas.icr.pct).className}`}>
                          {formatRatioDelta(comparison.deltas.icr.abs, comparison.deltas.icr.pct).abs}
                        </td>
                      </tr>
                      <tr className="hover:bg-gray-50">
                        <td className="py-2 pr-4">
                          <span className="font-medium">CFO (Y1)</span>
                        </td>
                        <td className="py-2 text-right font-mono">{formatCurrency(comparison.base.cfoY1)}</td>
                        <td className="py-2 text-right font-mono">{formatCurrency(comparison.after.cfoY1)}</td>
                        <td className={`py-2 text-right font-mono font-medium ${formatDelta(comparison.deltas.cfoY1.abs, comparison.deltas.cfoY1.pct).className}`}>
                          {formatDelta(comparison.deltas.cfoY1.abs, comparison.deltas.cfoY1.pct).abs}
                        </td>
                      </tr>
                      <tr className="hover:bg-gray-50">
                        <td className="py-2 pr-4">
                          <span className="font-medium">End Cash (Y1)</span>
                        </td>
                        <td className="py-2 text-right font-mono">{formatCurrency(comparison.base.endCashY1)}</td>
                        <td className="py-2 text-right font-mono">{formatCurrency(comparison.after.endCashY1)}</td>
                        <td className={`py-2 text-right font-mono font-medium ${formatDelta(comparison.deltas.endCashY1.abs, comparison.deltas.endCashY1.pct).className}`}>
                          {formatDelta(comparison.deltas.endCashY1.abs, comparison.deltas.endCashY1.pct).abs}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Payment Schedule Preview */}
              {(comparison.paymentPreview.gracePeriod.months > 0 || comparison.paymentPreview.afterGrace.months > 0) && (
                <div>
                  <h3 className="text-sm font-medium text-gray-900 mb-3">Payment Schedule</h3>
                  <div className="bg-gray-50 rounded-md p-4 space-y-2">
                    {comparison.paymentPreview.gracePeriod.months > 0 && (
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-700">
                          Months 1-{comparison.paymentPreview.gracePeriod.months}:
                        </span>
                        <span className="font-medium">
                          {formatCurrency(comparison.paymentPreview.gracePeriod.monthlyPayment)}/month
                        </span>
                      </div>
                    )}
                    {comparison.paymentPreview.afterGrace.months > 0 && (
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-700">
                          Months {comparison.paymentPreview.gracePeriod.months + 1}-{loanData.termMonths}:
                        </span>
                        <span className="font-medium">
                          {formatCurrency(comparison.paymentPreview.afterGrace.monthlyPayment)}/month
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="flex items-center justify-center h-64 text-gray-500">
              <div className="text-center">
                <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <p className="text-sm">Click &ldquo;Preview Changes&rdquo; to see impact analysis</p>
                <p className="text-xs text-gray-400 mt-1">
                  Modify loan terms above and preview how they affect your financial metrics
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Validation Errors */}
      {errors.length > 0 && (
        <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-md">
          <h4 className="text-sm font-medium text-red-800 mb-2">Please fix the following errors:</h4>
          <ul className="text-xs text-red-700 space-y-1">
            {errors.map((error, index) => (
              <li key={index}>• {error.message}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}