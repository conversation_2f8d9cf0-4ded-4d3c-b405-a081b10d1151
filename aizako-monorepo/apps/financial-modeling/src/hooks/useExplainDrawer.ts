'use client';

import { useState } from 'react';
import { FormulaExplanation, CellReference } from '@/lib/engine/explain';

// Hook for managing explain drawer state
export function useExplainDrawer() {
  const [isOpen, setIsOpen] = useState(false);
  const [explanation, setExplanation] = useState<FormulaExplanation | null>(null);
  const [cellReference, setCellReference] = useState<CellReference | null>(null);

  const openExplain = (newExplanation: FormulaExplanation, newCellReference: CellReference) => {
    setExplanation(newExplanation);
    setCellReference(newCellReference);
    setIsOpen(true);
  };

  const closeExplain = () => {
    setIsOpen(false);
    // Keep explanation and cellReference for smooth closing animation
    setTimeout(() => {
      setExplanation(null);
      setCellReference(null);
    }, 300);
  };

  return {
    isOpen,
    explanation,
    cellReference,
    openExplain,
    closeExplain,
  };
}