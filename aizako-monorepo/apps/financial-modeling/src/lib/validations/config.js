import { z } from 'zod';
// Zod schemas for form validation
export const metaSchema = z.object({
    currency: z.string().min(3).max(3),
    start: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
    periods: z.number().min(1).max(120),
    freq: z.enum(['monthly', 'yearly']),
});
export const openingSchema = z.object({
    cash: z.number().min(0),
    ar: z.number().min(0),
    inventory: z.number().min(0),
    ppne_net: z.number().min(0),
    ap: z.number().min(0),
    debt_current: z.number().min(0),
    debt_long: z.number().min(0),
    retained_earnings: z.number(),
});
export const revenueSchema = z.object({
    start_run_rate: z.number().min(0.01),
    mth_growth_pct: z.number().min(-50).max(100),
});
export const opexSchema = z.object({
    fixed: z.number().min(0),
    variable_pct_of_rev: z.number().min(0).max(100),
});
export const capexItemSchema = z.object({
    month: z.number().min(1),
    amount: z.number().min(0),
});
export const capexSchema = z.object({
    items: z.array(capexItemSchema),
    depr_years: z.number().min(0.1).max(50),
});
export const wcSchema = z.object({
    dso: z.number().min(0).max(365),
    dpo: z.number().min(0).max(365),
    dio: z.number().min(0).max(365),
});
export const debtDrawSchema = z.object({
    month: z.number().min(1),
    amount: z.number().min(0),
});
export const debtSchema = z.object({
    opening: z.number().min(0),
    rate_pct: z.number().min(0).max(50),
    term_months: z.number().min(1).max(360),
    amort: z.enum(['annuity', 'interest_only', 'bullet']),
    draws: z.array(debtDrawSchema),
});
export const taxSchema = z.object({
    rate_pct: z.number().min(0).max(60),
    payments_lag_mths: z.number().min(0).max(12),
});
export const driversSchema = z.object({
    revenue: revenueSchema,
    gross_margin_pct: z.number().min(0).max(100),
    opex: opexSchema,
    capex: capexSchema,
    wc: wcSchema,
    debt: debtSchema,
    tax: taxSchema,
});
export const configSchema = z.object({
    meta: metaSchema,
    opening_balances: openingSchema.partial(),
    drivers: driversSchema,
});
// Helper function to convert form data to engine config
export function formDataToConfig(data) {
    return {
        meta: data.meta,
        opening_balances: data.opening_balances,
        drivers: data.drivers,
    };
}
export function configToFormData(config) {
    return {
        meta: config.meta,
        opening_balances: config.opening_balances || {},
        drivers: config.drivers,
    };
}
