// Utility functions
const addMonths = (date, months) => {
    const d = new Date(date);
    d.setMonth(d.getMonth() + months);
    return d.toISOString().split('T')[0];
};
const daysInMonth = (date) => {
    const d = new Date(date);
    return new Date(d.getFullYear(), d.getMonth() + 1, 0).getDate();
};
// Main projection engine
export function project(config) {
    const { meta, opening_balances, drivers } = config;
    const periods = meta.periods;
    // Initialize arrays
    const pnl = [];
    const bs = [];
    const cf = [];
    const metrics = [];
    // Initialize opening balances with defaults
    const openings = {
        cash: opening_balances.cash || 100000,
        ar: opening_balances.ar || 0,
        inventory: opening_balances.inventory || 0,
        ppne_net: opening_balances.ppne_net || 0,
        ap: opening_balances.ap || 0,
        debt_current: opening_balances.debt_current || 0,
        debt_long: opening_balances.debt_long || drivers.debt.opening,
        retained_earnings: opening_balances.retained_earnings || 0,
    };
    // Track accumulated values
    let accumulated_depreciation = 0;
    let ppne_gross = openings.ppne_net;
    let debt_balance = openings.debt_long;
    let tax_payable = 0; // for tax lag
    // Calculate debt payment schedule
    const debtSchedule = calculateDebtSchedule(debt_balance, drivers.debt.rate_pct, drivers.debt.term_months, drivers.debt.amort);
    for (let period = 1; period <= periods; period++) {
        const date = addMonths(meta.start, period - 1);
        const daysInPeriod = daysInMonth(date);
        // === P&L CALCULATIONS ===
        // Revenue with growth
        const revenue = drivers.revenue.start_run_rate *
            Math.pow(1 + drivers.revenue.mth_growth_pct / 100, period - 1);
        // COGS
        const cogs = revenue * (1 - drivers.gross_margin_pct / 100);
        const gross_profit = revenue - cogs;
        // Operating expenses
        const opex_fixed = drivers.opex.fixed;
        const opex_variable = revenue * (drivers.opex.variable_pct_of_rev / 100);
        const total_opex = opex_fixed + opex_variable;
        // EBITDA
        const ebitda = gross_profit - total_opex;
        // Depreciation (straight-line on capex)
        const monthly_depr_rate = 1 / (drivers.capex.depr_years * 12);
        const capex_this_period = drivers.capex.items
            .filter(item => item.month === period)
            .reduce((sum, item) => sum + item.amount, 0);
        // Add capex to gross PP&E
        ppne_gross += capex_this_period;
        // Calculate depreciation on existing PP&E
        const depreciation = ppne_gross * monthly_depr_rate;
        accumulated_depreciation += depreciation;
        // EBIT
        const ebit = ebitda - depreciation;
        // Interest expense
        const monthly_interest_rate = drivers.debt.rate_pct / 100 / 12;
        const interest_expense = debt_balance * monthly_interest_rate;
        // EBT and taxes
        const ebt = ebit - interest_expense;
        const current_tax_expense = Math.max(0, ebt * drivers.tax.rate_pct / 100);
        // Tax payment with lag
        const tax_payment = period > drivers.tax.payments_lag_mths ?
            (pnl[period - drivers.tax.payments_lag_mths - 1]?.tax_expense || 0) : 0;
        const net_income = ebt - current_tax_expense;
        // Store P&L
        pnl.push({
            period,
            date,
            revenue,
            cogs,
            gross_profit,
            opex_fixed,
            opex_variable,
            total_opex,
            ebitda,
            depreciation,
            ebit,
            interest_expense,
            ebt,
            tax_expense: current_tax_expense,
            net_income,
        });
        // === BALANCE SHEET CALCULATIONS ===
        // Working capital calculations
        const ar = (revenue * drivers.wc.dso) / daysInPeriod;
        const inventory = drivers.wc.dio > 0 ? (cogs * drivers.wc.dio) / daysInPeriod : 0;
        const ap = (cogs * drivers.wc.dpo) / daysInPeriod;
        // Debt principal payment
        const principal_payment = debtSchedule[period - 1]?.principal || 0;
        debt_balance -= principal_payment;
        // Add any debt draws
        const debt_draw = drivers.debt.draws
            .filter(draw => draw.month === period)
            .reduce((sum, draw) => sum + draw.amount, 0);
        debt_balance += debt_draw;
        // Calculate cash (plug)
        const prev_bs = bs[period - 2];
        const prev_cash = period === 1 ? openings.cash : prev_bs.cash;
        // Cash flow calculation
        const change_ar = ar - (period === 1 ? openings.ar : prev_bs.ar);
        const change_inventory = inventory - (period === 1 ? openings.inventory : prev_bs.inventory);
        const change_ap = ap - (period === 1 ? openings.ap : prev_bs.ap);
        const cfo = net_income + depreciation - change_ar - change_inventory + change_ap - tax_payment;
        const cfi = -capex_this_period;
        const cff = debt_draw - principal_payment;
        const net_change_cash = cfo + cfi + cff;
        const cash = prev_cash + net_change_cash;
        // PP&E net
        const ppne_net = ppne_gross - accumulated_depreciation;
        // Assets
        const total_current_assets = cash + ar + inventory;
        const total_assets = total_current_assets + ppne_net;
        // Liabilities
        const debt_current = period < drivers.debt.term_months ? 0 : debt_balance; // Simplification
        const total_current_liabilities = ap + debt_current + tax_payable;
        const debt_long = period < drivers.debt.term_months ? debt_balance : 0;
        const total_liabilities = total_current_liabilities + debt_long;
        // Equity
        const retained_earnings = (period === 1 ? openings.retained_earnings : prev_bs.retained_earnings) + net_income;
        const total_equity = retained_earnings;
        const total_liab_equity = total_liabilities + total_equity;
        // Store Balance Sheet
        bs.push({
            period,
            date,
            cash,
            ar,
            inventory,
            total_current_assets,
            ppne_gross,
            accumulated_depreciation,
            ppne_net,
            total_assets,
            ap,
            debt_current,
            total_current_liabilities,
            debt_long,
            total_liabilities,
            retained_earnings,
            total_equity,
            total_liab_equity,
        });
        // Store Cash Flow
        cf.push({
            period,
            date,
            net_income,
            depreciation,
            change_ar: -change_ar, // Negative because increase in AR uses cash
            change_inventory: -change_inventory,
            change_ap,
            cfo,
            capex: -capex_this_period, // Negative because it's a cash outflow
            cfi,
            debt_draws: debt_draw,
            debt_principal_payments: -principal_payment,
            cff,
            net_change_cash,
            beginning_cash: prev_cash,
            ending_cash: cash,
        });
        // Update tax payable for next period
        tax_payable = current_tax_expense;
    }
    // Calculate metrics
    for (let i = 0; i < periods; i++) {
        const burn_rate = -Math.min(0, cf[i].cfo); // Positive burn rate
        const runway_months = burn_rate > 0 ? bs[i].cash / burn_rate : Infinity;
        // DSCR = (CFO before debt service) / (Interest + Principal)
        const debt_service = (cf[i].debt_principal_payments * -1) + pnl[i].interest_expense;
        const dscr = debt_service > 0 ? (cf[i].cfo + (cf[i].debt_principal_payments * -1)) / debt_service : 0;
        // ICR = EBIT / Interest
        const icr = pnl[i].interest_expense > 0 ? pnl[i].ebit / pnl[i].interest_expense : 0;
        metrics.push({
            period: i + 1,
            date: pnl[i].date,
            burn_rate,
            runway_months,
            dscr,
            icr,
            gross_margin_pct: pnl[i].revenue > 0 ? (pnl[i].gross_profit / pnl[i].revenue) * 100 : 0,
            ebitda_margin_pct: pnl[i].revenue > 0 ? (pnl[i].ebitda / pnl[i].revenue) * 100 : 0,
        });
    }
    // Identity checks
    const identity_residuals = bs.map(row => Math.abs(row.total_assets - row.total_liab_equity));
    const cash_flow_checks = cf.map(row => Math.abs(row.beginning_cash + row.net_change_cash - row.ending_cash));
    const max_residual = Math.max(...identity_residuals, ...cash_flow_checks);
    const passes_validation = max_residual < 1; // $1 tolerance
    return {
        pnl,
        bs,
        cf,
        metrics,
        checks: {
            identity_residuals,
            cash_flow_checks,
            max_residual,
            passes_validation,
        },
    };
}
// Helper function to calculate debt payment schedule
function calculateDebtSchedule(principal, annual_rate, term_months, amort_type) {
    const monthly_rate = annual_rate / 100 / 12;
    const schedule = [];
    let remaining_balance = principal;
    for (let period = 1; period <= term_months; period++) {
        const interest = remaining_balance * monthly_rate;
        let principal_payment = 0;
        switch (amort_type) {
            case 'annuity':
                if (monthly_rate > 0) {
                    const pmt = principal * (monthly_rate * Math.pow(1 + monthly_rate, term_months)) /
                        (Math.pow(1 + monthly_rate, term_months) - 1);
                    principal_payment = pmt - interest;
                }
                else {
                    principal_payment = principal / term_months;
                }
                break;
            case 'interest_only':
                principal_payment = period === term_months ? remaining_balance : 0;
                break;
            case 'bullet':
                principal_payment = period === term_months ? remaining_balance : 0;
                break;
        }
        remaining_balance -= principal_payment;
        schedule.push({
            period,
            interest,
            principal: principal_payment,
            balance: remaining_balance,
        });
    }
    return schedule;
}
