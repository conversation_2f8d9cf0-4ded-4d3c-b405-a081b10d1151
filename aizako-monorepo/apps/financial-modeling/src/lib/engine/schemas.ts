import { z } from 'zod';

// Meta configuration schema
export const MetaSchema = z.object({
  currency: z.string().min(3).max(3), // ISO currency code
  start: z.string().regex(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
  periods: z.number().int().min(1).max(120), // 1-120 months
  freq: z.enum(['monthly', 'yearly']).default('monthly'),
});

// Opening balances schema
export const OpeningBalancesSchema = z.object({
  cash: z.number().default(0),
  ar: z.number().default(0),
  inventory: z.number().default(0),
  ppne_net: z.number().default(0),
  ap: z.number().default(0),
  debt_current: z.number().default(0),
  debt_long: z.number().default(0),
  retained_earnings: z.number().default(0),
});

// Revenue driver schemas for different business models
export const ProductRevenueSchema = z.object({
  kind: z.literal('product'),
  aov: z.number().positive(), // average order value
  baseOrders: z.number().nonnegative(), // starting monthly orders
  momGrowthPct: z.number(), // month-over-month growth percentage
  refundsPct: z.number().min(0).max(50).default(0), // refund percentage
});

export const ServiceRevenueSchema = z.object({
  kind: z.literal('service'),
  rate: z.number().positive(), // hourly rate
  billableHours: z.number().positive(), // monthly billable hours
  utilizationPct: z.number().min(0).max(100).default(75), // utilization percentage
});

export const SaasRevenueSchema = z.object({
  kind: z.literal('saas'),
  startSubs: z.number().nonnegative().default(0), // starting subscribers
  newPerMonth: z.number().nonnegative(), // new subscribers per month
  churnPct: z.number().min(0).max(100), // monthly churn percentage
  arpu: z.number().positive(), // average revenue per user per month
});

export const MixedRevenueSchema = z.object({
  kind: z.literal('mixed'),
  parts: z.array(z.union([
    ProductRevenueSchema,
    ServiceRevenueSchema,
    SaasRevenueSchema,
  ])).min(2).max(2), // exactly 2 parts for mixed model
});

export const RevenueDriverSchema = z.union([
  ProductRevenueSchema,
  ServiceRevenueSchema,
  SaasRevenueSchema,
  MixedRevenueSchema,
]);

// Legacy revenue schema for backward compatibility
export const LegacyRevenueSchema = z.object({
  start_run_rate: z.number().positive(),
  mth_growth_pct: z.number(),
});

// Operating expenses schema
export const OpexSchema = z.object({
  fixed: z.number().nonnegative().default(0),
  variable_pct_of_rev: z.number().min(0).max(100).default(0),
});

// Capital expenditure schema
export const CapexSchema = z.object({
  items: z.array(z.object({
    month: z.number().int().min(1),
    amount: z.number().positive(),
  })).default([]),
  depr_years: z.number().positive().default(3),
});

// Working capital schema
export const WCSchema = z.object({
  dso: z.number().nonnegative().default(30), // days sales outstanding
  dpo: z.number().nonnegative().default(30), // days payable outstanding
  dio: z.number().nonnegative().default(0),  // days inventory outstanding
});

// Debt schema
export const DebtSchema = z.object({
  opening: z.number().nonnegative().default(0),
  rate_pct: z.number().min(0).max(50).default(0),
  term_months: z.number().int().positive().default(60),
  amort: z.enum(['annuity', 'interest_only', 'bullet']).default('annuity'),
  draws: z.array(z.object({
    month: z.number().int().min(1),
    amount: z.number().positive(),
  })).default([]),
});

// Tax schema
export const TaxSchema = z.object({
  rate_pct: z.number().min(0).max(100).default(25),
  payments_lag_mths: z.number().int().min(0).max(12).default(1),
});

// Driver configuration schema
export const DriversSchema = z.object({
  // Support both new wizard patterns and legacy format
  revenue: z.union([RevenueDriverSchema, LegacyRevenueSchema]),
  grossMarginPct: z.number().min(0).max(100),
  opex: OpexSchema.default({}),
  capex: CapexSchema.default({}),
  wc: WCSchema.default({}),
  debt: DebtSchema.default({}),
  tax: TaxSchema.default({}),
});

// Main configuration schema
export const ConfigSchema = z.object({
  meta: MetaSchema,
  opening_balances: OpeningBalancesSchema.partial().default({}),
  drivers: DriversSchema,
});

// Wizard-specific partial config schema for API responses
export const WizardPrefillSchema = ConfigSchema.partial().extend({
  meta: MetaSchema.partial(),
  drivers: DriversSchema.partial(),
});

// Export types
export type Meta = z.infer<typeof MetaSchema>;
export type OpeningBalances = z.infer<typeof OpeningBalancesSchema>;
export type ProductRevenue = z.infer<typeof ProductRevenueSchema>;
export type ServiceRevenue = z.infer<typeof ServiceRevenueSchema>;
export type SaasRevenue = z.infer<typeof SaasRevenueSchema>;
export type MixedRevenue = z.infer<typeof MixedRevenueSchema>;
export type RevenueDriver = z.infer<typeof RevenueDriverSchema>;
export type LegacyRevenue = z.infer<typeof LegacyRevenueSchema>;
export type Opex = z.infer<typeof OpexSchema>;
export type Capex = z.infer<typeof CapexSchema>;
export type WC = z.infer<typeof WCSchema>;
export type Debt = z.infer<typeof DebtSchema>;
export type Tax = z.infer<typeof TaxSchema>;
export type Drivers = z.infer<typeof DriversSchema>;
export type Config = z.infer<typeof ConfigSchema>;
export type WizardPrefill = z.infer<typeof WizardPrefillSchema>;

// Utility function to convert wizard revenue to legacy format for engine compatibility
export function convertRevenueToLegacy(revenue: RevenueDriver): LegacyRevenue {
  switch (revenue.kind) {
    case 'product': {
      return {
        start_run_rate: revenue.aov * revenue.baseOrders,
        mth_growth_pct: revenue.momGrowthPct,
      };
    }
    case 'service': {
      return {
        start_run_rate: revenue.rate * revenue.billableHours,
        mth_growth_pct: 0, // Services typically don't have month-over-month growth pattern
      };
    }
    case 'saas': {
      return {
        start_run_rate: revenue.startSubs * revenue.arpu,
        mth_growth_pct: revenue.newPerMonth > 0 ? 
          (revenue.newPerMonth * revenue.arpu) / Math.max(1, revenue.startSubs * revenue.arpu) * 100 : 0,
      };
    }
    case 'mixed': {
      const totalRevenue = revenue.parts.reduce((sum, part) => {
        const legacy = convertRevenueToLegacy(part);
        return sum + legacy.start_run_rate;
      }, 0);
      const avgGrowth = revenue.parts.reduce((sum, part) => {
        const legacy = convertRevenueToLegacy(part);
        return sum + legacy.mth_growth_pct;
      }, 0) / revenue.parts.length;
      return {
        start_run_rate: totalRevenue,
        mth_growth_pct: avgGrowth,
      };
    }
  }
}

// Utility function to detect if revenue uses new wizard format
export function isWizardRevenue(revenue: unknown): revenue is RevenueDriver {
  return Boolean(revenue && typeof revenue === 'object' && 'kind' in revenue);
}

// Default configuration factory
export function createDefaultConfig(): Config {
  const startDate = new Date();
  startDate.setDate(1); // First day of current month
  
  return {
    meta: {
      currency: 'USD',
      start: startDate.toISOString().split('T')[0],
      periods: 36,
      freq: 'monthly' as const,
    },
    opening_balances: {},
    drivers: {
      revenue: {
        start_run_rate: 10000,
        mth_growth_pct: 5,
      },
      grossMarginPct: 60,
      opex: { fixed: 5000, variable_pct_of_rev: 0 },
      capex: { items: [], depr_years: 3 },
      wc: { dso: 30, dpo: 30, dio: 0 },
      debt: { opening: 0, rate_pct: 0, term_months: 60, amort: 'annuity' as const, draws: [] },
      tax: { rate_pct: 25, payments_lag_mths: 1 },
    },
  };
}