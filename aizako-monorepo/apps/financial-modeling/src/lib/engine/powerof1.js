import { project } from './project';
// Power of 1 sensitivity scenarios
export const powerOf1Scenarios = [
    {
        name: 'Price +1%',
        description: 'Increase starting revenue by 1%',
        config_delta: {
            drivers: {
                revenue: {
                    start_run_rate: 0.01, // This will be multiplied by base value
                },
            },
        },
    },
    {
        name: 'Volume +1%',
        description: 'Increase monthly growth rate by 1 percentage point',
        config_delta: {
            drivers: {
                revenue: {
                    mth_growth_pct: 1, // Add 1 percentage point
                },
            },
        },
    },
    {
        name: 'COGS -1%',
        description: 'Increase gross margin by 1 percentage point',
        config_delta: {
            drivers: {
                gross_margin_pct: 1, // Add 1 percentage point
            },
        },
    },
    {
        name: 'DSO -1 day',
        description: 'Reduce days sales outstanding by 1 day',
        config_delta: {
            drivers: {
                wc: {
                    dso: -1, // Subtract 1 day
                },
            },
        },
    },
    {
        name: 'DPO +1 day',
        description: 'Increase days payable outstanding by 1 day',
        config_delta: {
            drivers: {
                wc: {
                    dpo: 1, // Add 1 day
                },
            },
        },
    },
    {
        name: 'DIO -1 day',
        description: 'Reduce days inventory outstanding by 1 day',
        config_delta: {
            drivers: {
                wc: {
                    dio: -1, // Subtract 1 day
                },
            },
        },
    },
];
// Main Power of 1 analysis function
export function runPowerOf1Analysis(baseConfig) {
    // Run base case
    const baseResult = project(baseConfig);
    const scenarios = {};
    // Run each sensitivity scenario
    for (const scenario of powerOf1Scenarios) {
        const modifiedConfig = applyConfigDelta(baseConfig, scenario.config_delta);
        const scenarioResult = project(modifiedConfig);
        // Calculate key deltas
        const deltas = calculateDeltas(baseResult, scenarioResult);
        scenarios[scenario.name] = {
            result: scenarioResult,
            deltas,
        };
    }
    return {
        base: baseResult,
        scenarios,
    };
}
// Apply delta changes to base config
function applyConfigDelta(baseConfig, delta) {
    const modifiedConfig = JSON.parse(JSON.stringify(baseConfig)); // Deep clone
    if (delta.drivers) {
        // Revenue changes
        if (delta.drivers.revenue) {
            if (delta.drivers.revenue.start_run_rate !== undefined) {
                // For price changes, multiply by (1 + delta)
                modifiedConfig.drivers.revenue.start_run_rate *= (1 + delta.drivers.revenue.start_run_rate);
            }
            if (delta.drivers.revenue.mth_growth_pct !== undefined) {
                // For growth changes, add the delta
                modifiedConfig.drivers.revenue.mth_growth_pct += delta.drivers.revenue.mth_growth_pct;
            }
        }
        // Gross margin changes
        if (delta.drivers.gross_margin_pct !== undefined) {
            modifiedConfig.drivers.gross_margin_pct += delta.drivers.gross_margin_pct;
        }
        // Working capital changes
        if (delta.drivers.wc) {
            if (delta.drivers.wc.dso !== undefined) {
                modifiedConfig.drivers.wc.dso += delta.drivers.wc.dso;
            }
            if (delta.drivers.wc.dpo !== undefined) {
                modifiedConfig.drivers.wc.dpo += delta.drivers.wc.dpo;
            }
            if (delta.drivers.wc.dio !== undefined) {
                modifiedConfig.drivers.wc.dio += delta.drivers.wc.dio;
            }
        }
    }
    return modifiedConfig;
}
// Calculate key performance deltas
function calculateDeltas(baseResult, scenarioResult) {
    // Calculate 12-month EBITDA impact
    const base12mEbitda = baseResult.pnl.slice(0, 12).reduce((sum, row) => sum + row.ebitda, 0);
    const scenario12mEbitda = scenarioResult.pnl.slice(0, 12).reduce((sum, row) => sum + row.ebitda, 0);
    const ebitda_12m = scenario12mEbitda - base12mEbitda;
    // Calculate 12-month cash impact
    const baseCash12m = baseResult.bs[11]?.cash || 0;
    const scenarioCash12m = scenarioResult.bs[11]?.cash || 0;
    const cash_12m = scenarioCash12m - baseCash12m;
    // Calculate runway change
    const baseRunway = baseResult.metrics[11]?.runway_months || 0;
    const scenarioRunway = scenarioResult.metrics[11]?.runway_months || 0;
    const runway_change = scenarioRunway - baseRunway;
    return {
        ebitda_12m,
        cash_12m,
        runway_change,
    };
}
// Get Power of 1 summary for display
export function getPowerOf1Summary(analysis) {
    const summary = [];
    for (const [scenarioName, scenarioData] of Object.entries(analysis.scenarios)) {
        const { deltas } = scenarioData;
        summary.push({
            name: scenarioName,
            description: powerOf1Scenarios.find(s => s.name === scenarioName)?.description || '',
            ebitda_impact: deltas.ebitda_12m,
            cash_impact: deltas.cash_12m,
            runway_impact: deltas.runway_change,
            ebitda_impact_pct: analysis.base.pnl.slice(0, 12).reduce((sum, row) => sum + row.ebitda, 0) !== 0
                ? (deltas.ebitda_12m / Math.abs(analysis.base.pnl.slice(0, 12).reduce((sum, row) => sum + row.ebitda, 0))) * 100
                : 0,
        });
    }
    // Sort by absolute EBITDA impact (descending)
    summary.sort((a, b) => Math.abs(b.ebitda_impact) - Math.abs(a.ebitda_impact));
    return summary;
}
// Get top sensitivity drivers
export function getTopSensitivities(analysis, limit = 3) {
    const summary = getPowerOf1Summary(analysis);
    return summary.slice(0, limit);
}
// Format currency for display
export function formatCurrency(value) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
    }).format(value);
}
// Format percentage for display
export function formatPercent(value) {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
}
// Format runway change for display
export function formatRunwayChange(months) {
    if (months === Infinity || months === -Infinity) {
        return months > 0 ? '+∞ months' : '-∞ months';
    }
    const sign = months >= 0 ? '+' : '';
    const absMonths = Math.abs(months);
    if (absMonths < 1) {
        const days = Math.round(absMonths * 30);
        return `${sign}${days} days`;
    }
    else {
        return `${sign}${months.toFixed(1)} months`;
    }
}
// Create a modified config for testing specific scenarios
export function createTestScenario(baseConfig, scenarioName) {
    const scenario = powerOf1Scenarios.find(s => s.name === scenarioName);
    if (!scenario) {
        throw new Error(`Scenario "${scenarioName}" not found`);
    }
    return applyConfigDelta(baseConfig, scenario.config_delta);
}
// Validate that Power of 1 changes are reasonable
export function validatePowerOf1Config(config) {
    const warnings = [];
    // Check for extreme values that might break the model
    if (config.drivers.revenue.mth_growth_pct > 50) {
        warnings.push('Monthly growth rate exceeds 50% - results may be unrealistic');
    }
    if (config.drivers.gross_margin_pct > 95) {
        warnings.push('Gross margin exceeds 95% - check if this is realistic for your business');
    }
    if (config.drivers.wc.dso < 0) {
        warnings.push('DSO is negative - this may indicate customers pay before delivery');
    }
    if (config.drivers.wc.dpo > 180) {
        warnings.push('DPO exceeds 180 days - this may strain supplier relationships');
    }
    return {
        valid: warnings.length === 0,
        warnings,
    };
}
