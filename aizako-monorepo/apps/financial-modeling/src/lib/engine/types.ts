// Core configuration types for the financial projection engine

export type Meta = {
  currency: string;
  start: string; // ISO date string (YYYY-MM-DD)
  periods: number; // number of months to project
  freq: 'monthly' | 'yearly';
};

export type Opening = {
  cash: number;
  ar: number; // accounts receivable
  inventory: number;
  ppne_net: number; // property, plant & equipment (net)
  ap: number; // accounts payable
  debt_current: number;
  debt_long: number;
  retained_earnings: number;
};

export type Revenue = {
  start_run_rate: number; // monthly revenue at start
  mth_growth_pct: number; // month-over-month growth %
};

export type Opex = {
  fixed: number; // fixed monthly operating expenses
  variable_pct_of_rev: number; // variable opex as % of revenue
};

export type Capex = {
  items: { month: number; amount: number }[]; // scheduled capex by month
  depr_years: number; // depreciation life in years
};

export type WC = {
  dso: number; // days sales outstanding
  dpo: number; // days payable outstanding
  dio: number; // days inventory outstanding
};

export type Debt = {
  opening: number; // opening debt balance
  rate_pct: number; // annual interest rate %
  term_months: number; // loan term in months
  amort: 'annuity' | 'interest_only' | 'bullet'; // amortization type
  grace_period_months?: number; // grace period in months (optional)
  grace_period_type?: 'interest_only' | 'capitalized'; // grace period type (optional)
  draws: { month: number; amount: number }[]; // additional draws
};

export type Tax = {
  rate_pct: number; // tax rate %
  payments_lag_mths: number; // months lag for tax payments
};

export type Config = {
  meta: Meta;
  opening_balances: Partial<Opening>;
  drivers: {
    revenue: Revenue;
    gross_margin_pct: number;
    opex: Opex;
    capex: Capex;
    wc: WC;
    debt: Debt;
    tax: Tax;
  };
};

// Output types for financial statements
export type PnLRow = {
  period: number;
  date: string;
  revenue: number;
  cogs: number;
  gross_profit: number;
  opex_fixed: number;
  opex_variable: number;
  total_opex: number;
  ebitda: number;
  depreciation: number;
  ebit: number;
  interest_expense: number;
  ebt: number;
  tax_expense: number;
  net_income: number;
};

export type BSRow = {
  period: number;
  date: string;
  // Assets
  cash: number;
  ar: number;
  inventory: number;
  total_current_assets: number;
  ppne_gross: number;
  accumulated_depreciation: number;
  ppne_net: number;
  total_assets: number;
  // Liabilities
  ap: number;
  debt_current: number;
  total_current_liabilities: number;
  debt_long: number;
  total_liabilities: number;
  // Equity
  retained_earnings: number;
  total_equity: number;
  total_liab_equity: number;
};

export type CFRow = {
  period: number;
  date: string;
  // Operating
  net_income: number;
  depreciation: number;
  change_ar: number;
  change_inventory: number;
  change_ap: number;
  cfo: number;
  // Investing
  capex: number;
  cfi: number;
  // Financing
  debt_draws: number;
  debt_principal_payments: number;
  cff: number;
  // Summary
  net_change_cash: number;
  beginning_cash: number;
  ending_cash: number;
};

export type Metrics = {
  period: number;
  date: string;
  burn_rate: number; // monthly cash burn (negative CFO)
  runway_months: number; // cash / burn_rate
  dscr: number; // debt service coverage ratio
  icr: number; // interest coverage ratio
  gross_margin_pct: number;
  ebitda_margin_pct: number;
};

export type ProjectionResult = {
  pnl: PnLRow[];
  bs: BSRow[];
  cf: CFRow[];
  metrics: Metrics[];
  checks: {
    identity_residuals: number[]; // Assets - (Liabilities + Equity) for each period
    cash_flow_checks: number[]; // Beginning Cash + Net Change - Ending Cash for each period
    max_residual: number;
    passes_validation: boolean;
  };
};

// Template types for industry defaults
export type IndustryTemplate = {
  name: string;
  config: Partial<Config>;
};

// Power of 1 sensitivity analysis types
type DeepPartial<T> = {
  [K in keyof T]?: T[K] extends object ? DeepPartial<T[K]> : T[K]
};

export type PowerOf1Scenario = {
  name: string;
  description: string;
  config_delta: DeepPartial<Pick<Config, 'drivers'>>;
};

export type PowerOf1Result = {
  base: ProjectionResult;
  scenarios: {
    [key: string]: {
      result: ProjectionResult;
      deltas: {
        ebitda_12m: number;
        cash_12m: number;
        runway_change: number;
      };
    };
  };
};
