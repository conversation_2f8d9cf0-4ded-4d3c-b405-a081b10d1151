// Default configuration template
export const defaultConfig = {
    meta: {
        currency: 'USD',
        start: new Date().toISOString().split('T')[0], // Today's date
        periods: 36, // 3 years
        freq: 'monthly',
    },
    opening_balances: {
        cash: 100000,
        ar: 0,
        inventory: 0,
        ppne_net: 0,
        ap: 0,
        debt_current: 0,
        debt_long: 0,
        retained_earnings: 0,
    },
    drivers: {
        revenue: {
            start_run_rate: 10000, // $10k/month
            mth_growth_pct: 5, // 5% monthly growth
        },
        gross_margin_pct: 60,
        opex: {
            fixed: 8000, // $8k/month fixed costs
            variable_pct_of_rev: 10, // 10% of revenue
        },
        capex: {
            items: [
                { month: 1, amount: 25000 }, // Initial equipment
            ],
            depr_years: 5,
        },
        wc: {
            dso: 30, // 30 days
            dpo: 30, // 30 days
            dio: 0, // No inventory by default
        },
        debt: {
            opening: 0,
            rate_pct: 8,
            term_months: 36,
            amort: 'annuity',
            draws: [],
        },
        tax: {
            rate_pct: 25,
            payments_lag_mths: 1,
        },
    },
};
// Industry templates
export const industryTemplates = [
    {
        name: 'Generic',
        config: defaultConfig,
    },
    {
        name: 'SaaS',
        config: {
            ...defaultConfig,
            drivers: {
                ...defaultConfig.drivers,
                revenue: {
                    start_run_rate: 20000, // $20k MRR
                    mth_growth_pct: 12, // 12% monthly growth
                },
                gross_margin_pct: 80, // High margins for SaaS
                opex: {
                    fixed: 50000, // Higher fixed costs (salaries, infrastructure)
                    variable_pct_of_rev: 2, // Low variable costs
                },
                capex: {
                    items: [
                        { month: 1, amount: 5000 },
                        { month: 2, amount: 5000 },
                        { month: 3, amount: 5000 },
                        { month: 4, amount: 5000 },
                        { month: 5, amount: 5000 },
                        { month: 6, amount: 5000 },
                    ],
                    depr_years: 3, // Shorter depreciation for tech
                },
                wc: {
                    dso: 30, // Monthly billing
                    dpo: 30,
                    dio: 0, // No inventory
                },
                debt: {
                    opening: 0, // SaaS often starts without debt
                    rate_pct: 8,
                    term_months: 36,
                    amort: 'annuity',
                    draws: [],
                },
            },
        },
    },
    {
        name: 'E-commerce',
        config: {
            ...defaultConfig,
            drivers: {
                ...defaultConfig.drivers,
                revenue: {
                    start_run_rate: 40000, // $40k/month
                    mth_growth_pct: 8, // 8% monthly growth
                },
                gross_margin_pct: 55, // Product margins
                opex: {
                    fixed: 30000, // Warehousing, staff
                    variable_pct_of_rev: 5, // Shipping, payment processing
                },
                capex: {
                    items: [
                        { month: 1, amount: 15000 }, // Initial inventory investment
                    ],
                    depr_years: 5,
                },
                wc: {
                    dso: 5, // Fast payment processing
                    dpo: 30,
                    dio: 35, // Inventory holding period
                },
                debt: {
                    opening: 200000, // Working capital line
                    rate_pct: 8,
                    term_months: 36,
                    amort: 'annuity',
                    draws: [],
                },
            },
        },
    },
    {
        name: 'Professional Services',
        config: {
            ...defaultConfig,
            drivers: {
                ...defaultConfig.drivers,
                revenue: {
                    start_run_rate: 30000, // $30k/month
                    mth_growth_pct: 6, // 6% monthly growth
                },
                gross_margin_pct: 70, // High margins, low COGS
                opex: {
                    fixed: 25000, // Salaries, office
                    variable_pct_of_rev: 3, // Travel, materials
                },
                capex: {
                    items: [
                        { month: 1, amount: 10000 }, // Office setup
                    ],
                    depr_years: 7,
                },
                wc: {
                    dso: 45, // Longer payment terms
                    dpo: 30,
                    dio: 0, // No inventory
                },
                debt: {
                    opening: 100000, // Equipment financing
                    rate_pct: 7,
                    term_months: 60,
                    amort: 'annuity',
                    draws: [],
                },
            },
        },
    },
];
// Helper function to get template by name
export function getTemplate(name) {
    const template = industryTemplates.find(t => t.name === name);
    if (!template) {
        throw new Error(`Template "${name}" not found`);
    }
    return JSON.parse(JSON.stringify(template.config)); // Deep clone
}
// Helper function to merge partial config with template
export function mergeWithTemplate(templateName, partial) {
    const template = getTemplate(templateName);
    return mergeConfigs(template, partial);
}
// Deep merge utility for configs
function mergeConfigs(base, override) {
    const result = JSON.parse(JSON.stringify(base)); // Deep clone base
    if (override.meta) {
        result.meta = { ...result.meta, ...override.meta };
    }
    if (override.opening_balances) {
        result.opening_balances = { ...result.opening_balances, ...override.opening_balances };
    }
    if (override.drivers) {
        if (override.drivers.revenue) {
            result.drivers.revenue = { ...result.drivers.revenue, ...override.drivers.revenue };
        }
        if (override.drivers.opex) {
            result.drivers.opex = { ...result.drivers.opex, ...override.drivers.opex };
        }
        if (override.drivers.capex) {
            result.drivers.capex = { ...result.drivers.capex, ...override.drivers.capex };
        }
        if (override.drivers.wc) {
            result.drivers.wc = { ...result.drivers.wc, ...override.drivers.wc };
        }
        if (override.drivers.debt) {
            result.drivers.debt = { ...result.drivers.debt, ...override.drivers.debt };
        }
        if (override.drivers.tax) {
            result.drivers.tax = { ...result.drivers.tax, ...override.drivers.tax };
        }
        if (override.drivers.gross_margin_pct !== undefined) {
            result.drivers.gross_margin_pct = override.drivers.gross_margin_pct;
        }
    }
    return result;
}
// Validation function for config
export function validateConfig(config) {
    const errors = [];
    // Meta validation
    if (config.meta.periods <= 0) {
        errors.push('Periods must be greater than 0');
    }
    if (config.meta.periods > 120) {
        errors.push('Periods cannot exceed 120 months (10 years)');
    }
    // Revenue validation
    if (config.drivers.revenue.start_run_rate <= 0) {
        errors.push('Starting revenue must be greater than 0');
    }
    if (config.drivers.revenue.mth_growth_pct < -50) {
        errors.push('Monthly growth cannot be less than -50%');
    }
    if (config.drivers.revenue.mth_growth_pct > 100) {
        errors.push('Monthly growth cannot exceed 100%');
    }
    // Margin validation
    if (config.drivers.gross_margin_pct < 0 || config.drivers.gross_margin_pct > 100) {
        errors.push('Gross margin must be between 0% and 100%');
    }
    // Working capital validation
    if (config.drivers.wc.dso < 0 || config.drivers.wc.dso > 365) {
        errors.push('DSO must be between 0 and 365 days');
    }
    if (config.drivers.wc.dpo < 0 || config.drivers.wc.dpo > 365) {
        errors.push('DPO must be between 0 and 365 days');
    }
    if (config.drivers.wc.dio < 0 || config.drivers.wc.dio > 365) {
        errors.push('DIO must be between 0 and 365 days');
    }
    // Debt validation
    if (config.drivers.debt.rate_pct < 0 || config.drivers.debt.rate_pct > 50) {
        errors.push('Interest rate must be between 0% and 50%');
    }
    if (config.drivers.debt.term_months <= 0 || config.drivers.debt.term_months > 360) {
        errors.push('Debt term must be between 1 and 360 months');
    }
    // Tax validation
    if (config.drivers.tax.rate_pct < 0 || config.drivers.tax.rate_pct > 60) {
        errors.push('Tax rate must be between 0% and 60%');
    }
    if (config.drivers.tax.payments_lag_mths < 0 || config.drivers.tax.payments_lag_mths > 12) {
        errors.push('Tax payment lag must be between 0 and 12 months');
    }
    // Capex validation
    if (config.drivers.capex.depr_years <= 0 || config.drivers.capex.depr_years > 50) {
        errors.push('Depreciation years must be between 1 and 50');
    }
    return {
        valid: errors.length === 0,
        errors,
    };
}
