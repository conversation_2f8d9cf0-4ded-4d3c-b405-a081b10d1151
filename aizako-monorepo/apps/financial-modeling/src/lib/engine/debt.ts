import { Config, ProjectionResult } from './types';

// Debt schedule calculation types
export type DebtScheduleRow = {
  period: number;
  beginning_balance: number;
  interest_payment: number;
  principal_payment: number;
  total_payment: number;
  ending_balance: number;
  cumulative_interest: number;
  cumulative_principal: number;
};

export type DebtMetrics = {
  period: number;
  date: string;
  dscr: number; // Debt Service Coverage Ratio
  icr: number; // Interest Coverage Ratio
  debt_to_cash: number;
  debt_service: number;
  available_cash_flow: number;
};

// Calculate detailed debt schedule
export function calculateDebtSchedule(
  principal: number,
  annual_rate: number,
  term_months: number,
  amort_type: 'annuity' | 'interest_only' | 'bullet',
  draws: { month: number; amount: number }[] = [],
  grace_period_months: number = 0,
  grace_period_type: 'interest_only' | 'capitalized' = 'interest_only'
): DebtScheduleRow[] {
  const monthly_rate = annual_rate / 100 / 12;
  const schedule: DebtScheduleRow[] = [];
  let remaining_balance = principal;
  let cumulative_interest = 0;
  let cumulative_principal = 0;

  // For annuity with capitalized grace period, we need to calculate payment after grace period
  let annuity_payment = 0;
  let grace_period_balance = principal;

  for (let period = 1; period <= term_months; period++) {
    // Add any draws for this period
    const draw_amount = draws
      .filter(draw => draw.month === period)
      .reduce((sum, draw) => sum + draw.amount, 0);
    
    remaining_balance += draw_amount;
    const beginning_balance = remaining_balance;

    // Calculate interest
    const interest_expense = remaining_balance * monthly_rate;
    
    // Determine if we're in grace period
    const in_grace_period = period <= grace_period_months;
    
    let interest_payment = 0;
    let principal_payment = 0;
    
    if (in_grace_period) {
      // Handle grace period logic
      if (grace_period_type === 'interest_only') {
        // Pay interest only during grace period
        interest_payment = interest_expense;
        principal_payment = 0;
      } else if (grace_period_type === 'capitalized') {
        // Capitalize interest (add to principal balance)
        interest_payment = 0;
        principal_payment = 0;
        remaining_balance += interest_expense; // Capitalize interest
        cumulative_interest += interest_expense; // Track total interest cost
      }
      
      // Set grace period balance for annuity calculation
      if (period === grace_period_months && amort_type === 'annuity') {
        grace_period_balance = remaining_balance;
        const regular_payment_periods = term_months - grace_period_months;
        if (regular_payment_periods > 0 && monthly_rate > 0) {
          annuity_payment = grace_period_balance * (monthly_rate * Math.pow(1 + monthly_rate, regular_payment_periods)) / 
                           (Math.pow(1 + monthly_rate, regular_payment_periods) - 1);
        } else if (regular_payment_periods > 0) {
          annuity_payment = grace_period_balance / regular_payment_periods;
        }
      }
    } else {
      // Regular payment period (after grace period)
      interest_payment = interest_expense;
      
      // Calculate principal payment based on amortization type
      switch (amort_type) {
        case 'annuity':
          if (annuity_payment > 0) {
            principal_payment = Math.min(Math.max(0, annuity_payment - interest_payment), remaining_balance);
          } else if (grace_period_months === 0) {
            // No grace period, calculate normally
            const regular_periods = term_months;
            if (monthly_rate > 0) {
              const pmt = principal * (monthly_rate * Math.pow(1 + monthly_rate, regular_periods)) / 
                         (Math.pow(1 + monthly_rate, regular_periods) - 1);
              principal_payment = Math.min(Math.max(0, pmt - interest_payment), remaining_balance);
            } else {
              principal_payment = Math.min(principal / regular_periods, remaining_balance);
            }
          } else {
            // Grace period exists but annuity payment not calculated yet - no principal payment
            principal_payment = 0;
          }
          break;
          
        case 'interest_only':
          // Only principal payment on final period
          principal_payment = period === term_months ? remaining_balance : 0;
          break;
          
        case 'bullet':
          // All principal due at maturity
          principal_payment = period === term_months ? remaining_balance : 0;
          break;
      }

      // Ensure we don't pay more principal than remaining balance
      principal_payment = Math.min(principal_payment, remaining_balance);
      remaining_balance -= principal_payment;
    }
    
    const total_payment = interest_payment + principal_payment;
    
    // Only add to cumulative paid interest if actually paid (not capitalized)
    if (!(grace_period_type === 'capitalized' && in_grace_period)) {
      cumulative_interest += interest_payment;
    }
    cumulative_principal += principal_payment;

    schedule.push({
      period,
      beginning_balance,
      interest_payment,
      principal_payment,
      total_payment,
      ending_balance: remaining_balance,
      cumulative_interest,
      cumulative_principal,
    });
  }

  return schedule;
}

// Calculate debt metrics (DSCR, ICR, etc.)
export function calculateDebtMetrics(
  projectionResult: ProjectionResult,
  config: Config
): DebtMetrics[] {
  const metrics: DebtMetrics[] = [];
  const debtSchedule = calculateDebtSchedule(
    config.drivers.debt.opening,
    config.drivers.debt.rate_pct,
    config.drivers.debt.term_months,
    config.drivers.debt.amort,
    config.drivers.debt.draws,
    config.drivers.debt.grace_period_months || 0,
    config.drivers.debt.grace_period_type || 'interest_only'
  );

  for (let i = 0; i < projectionResult.pnl.length; i++) {
    const pnl = projectionResult.pnl[i];
    const cf = projectionResult.cf[i];
    const bs = projectionResult.bs[i];
    const debtRow = debtSchedule[i];

    // Debt service = Interest + Principal payments
    const debt_service = debtRow ? debtRow.total_payment : 0;
    
    // Available cash flow for debt service (CFO before debt payments)
    const available_cash_flow = cf.cfo + Math.abs(cf.debt_principal_payments);
    
    // DSCR = Available Cash Flow / Debt Service
    const dscr = debt_service > 0 ? available_cash_flow / debt_service : 0;
    
    // ICR = EBIT / Interest Expense
    const icr = pnl.interest_expense > 0 ? pnl.ebit / pnl.interest_expense : 0;
    
    // Debt to Cash ratio
    const total_debt = bs.debt_current + bs.debt_long;
    const debt_to_cash = bs.cash > 0 ? total_debt / bs.cash : 0;

    metrics.push({
      period: i + 1,
      date: pnl.date,
      dscr,
      icr,
      debt_to_cash,
      debt_service,
      available_cash_flow,
    });
  }

  return metrics;
}

// Simulate different debt scenarios
export function simulateDebtScenarios(
  baseConfig: Config,
  scenarios: {
    name: string;
    rate_pct?: number;
    term_months?: number;
    amort?: 'annuity' | 'interest_only' | 'bullet';
    principal?: number;
  }[]
) {
  const results = [];

  for (const scenario of scenarios) {
    const modifiedConfig = { ...baseConfig };
    
    // Apply scenario changes
    if (scenario.rate_pct !== undefined) {
      modifiedConfig.drivers.debt.rate_pct = scenario.rate_pct;
    }
    if (scenario.term_months !== undefined) {
      modifiedConfig.drivers.debt.term_months = scenario.term_months;
    }
    if (scenario.amort !== undefined) {
      modifiedConfig.drivers.debt.amort = scenario.amort;
    }
    if (scenario.principal !== undefined) {
      modifiedConfig.drivers.debt.opening = scenario.principal;
    }

    // Calculate schedule for this scenario
    const schedule = calculateDebtSchedule(
      modifiedConfig.drivers.debt.opening,
      modifiedConfig.drivers.debt.rate_pct,
      modifiedConfig.drivers.debt.term_months,
      modifiedConfig.drivers.debt.amort,
      modifiedConfig.drivers.debt.draws,
      modifiedConfig.drivers.debt.grace_period_months || 0,
      modifiedConfig.drivers.debt.grace_period_type || 'interest_only'
    );

    // Calculate summary metrics
    const totalInterest = schedule.reduce((sum, row) => sum + row.interest_payment, 0);
    const totalPayments = schedule.reduce((sum, row) => sum + row.total_payment, 0);
    const avgMonthlyPayment = totalPayments / schedule.length;

    results.push({
      name: scenario.name,
      config: modifiedConfig.drivers.debt,
      schedule,
      summary: {
        totalInterest,
        totalPayments,
        avgMonthlyPayment,
        effectiveRate: (totalInterest / modifiedConfig.drivers.debt.opening) * 100,
      },
    });
  }

  return results;
}

// Get debt covenant analysis
export function analyzeDebtCovenants(
  debtMetrics: DebtMetrics[],
  covenants: {
    min_dscr?: number;
    min_icr?: number;
    max_debt_to_cash?: number;
  } = {}
) {
  const defaultCovenants = {
    min_dscr: 1.25, // Typical minimum DSCR
    min_icr: 2.0,   // Typical minimum ICR
    max_debt_to_cash: 3.0, // Maximum debt to cash ratio
    ...covenants,
  };

  const violations = [];
  const warnings = [];

  for (const metric of debtMetrics) {
    // DSCR violations
    if (metric.dscr < defaultCovenants.min_dscr && metric.debt_service > 0) {
      violations.push({
        period: metric.period,
        type: 'DSCR',
        value: metric.dscr,
        threshold: defaultCovenants.min_dscr,
        severity: metric.dscr < 1.0 ? 'critical' : 'warning',
      });
    }

    // ICR violations
    if (metric.icr < defaultCovenants.min_icr && metric.icr > 0) {
      violations.push({
        period: metric.period,
        type: 'ICR',
        value: metric.icr,
        threshold: defaultCovenants.min_icr,
        severity: metric.icr < 1.0 ? 'critical' : 'warning',
      });
    }

    // Debt to cash warnings
    if (metric.debt_to_cash > defaultCovenants.max_debt_to_cash) {
      warnings.push({
        period: metric.period,
        type: 'Debt-to-Cash',
        value: metric.debt_to_cash,
        threshold: defaultCovenants.max_debt_to_cash,
        message: 'High debt relative to cash position',
      });
    }
  }

  return {
    violations,
    warnings,
    summary: {
      totalViolations: violations.length,
      criticalViolations: violations.filter(v => v.severity === 'critical').length,
      firstViolationPeriod: violations.length > 0 ? Math.min(...violations.map(v => v.period)) : null,
    },
  };
}

// Format debt metrics for display
export function formatDebtMetrics(metrics: DebtMetrics[]) {
  return metrics.map(metric => ({
    ...metric,
    dscr_formatted: metric.dscr === 0 ? 'N/A' : `${metric.dscr.toFixed(2)}x`,
    icr_formatted: metric.icr === 0 ? 'N/A' : `${metric.icr.toFixed(2)}x`,
    debt_to_cash_formatted: `${metric.debt_to_cash.toFixed(2)}x`,
    debt_service_formatted: new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(metric.debt_service),
  }));
}

// Get debt summary statistics
export function getDebtSummary(schedule: DebtScheduleRow[]) {
  if (schedule.length === 0) {
    return {
      totalInterest: 0,
      totalPrincipal: 0,
      totalPayments: 0,
      avgMonthlyPayment: 0,
      maxMonthlyPayment: 0,
      finalPayment: 0,
    };
  }

  const totalInterest = schedule[schedule.length - 1].cumulative_interest;
  const totalPrincipal = schedule[schedule.length - 1].cumulative_principal;
  const totalPayments = totalInterest + totalPrincipal;
  const avgMonthlyPayment = totalPayments / schedule.length;
  const maxMonthlyPayment = Math.max(...schedule.map(row => row.total_payment));
  const finalPayment = schedule[schedule.length - 1].total_payment;

  return {
    totalInterest,
    totalPrincipal,
    totalPayments,
    avgMonthlyPayment,
    maxMonthlyPayment,
    finalPayment,
  };
}
