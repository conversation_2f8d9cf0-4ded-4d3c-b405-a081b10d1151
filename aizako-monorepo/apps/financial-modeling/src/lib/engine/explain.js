// Main explain function
export function explainCell(cellRef, config, pnl, bs, cf) {
    const { statement, field, period } = cellRef;
    const periodIndex = period - 1;
    switch (statement) {
        case 'pnl':
            return explainPnLCell(field, periodIndex, config, pnl, bs, cf);
        case 'bs':
            return explainBSCell(field, periodIndex, config, pnl, bs, cf);
        case 'cf':
            return explainCFCell(field, periodIndex, config, pnl, bs, cf);
        default:
            throw new Error(`Unknown statement: ${statement}`);
    }
}
// P&L explanations
function explainPnLCell(field, periodIndex, config, pnl, bs, cf // eslint-disable-line @typescript-eslint/no-unused-vars
) {
    const row = pnl[periodIndex];
    const period = periodIndex + 1;
    switch (field) {
        case 'revenue': {
            const growthFactor = Math.pow(1 + config.drivers.revenue.mth_growth_pct / 100, periodIndex);
            return {
                formula: 'Revenue = Start Run Rate × (1 + Growth Rate)^(Period - 1)',
                description: 'Monthly revenue calculated with compound growth from starting run rate',
                inputs: [
                    { name: 'Start Run Rate', value: config.drivers.revenue.start_run_rate, source: 'Assumptions' },
                    { name: 'Monthly Growth Rate', value: config.drivers.revenue.mth_growth_pct, source: 'Assumptions' },
                    { name: 'Period', value: period, source: 'Timeline' },
                    { name: 'Growth Factor', value: growthFactor, source: 'Calculated' },
                ],
                calculation: `${config.drivers.revenue.start_run_rate.toLocaleString()} × ${growthFactor.toFixed(4)} = ${row.revenue.toLocaleString()}`,
            };
        }
        case 'cogs': {
            const grossMarginDecimal = config.drivers.gross_margin_pct / 100;
            const cogsRate = 1 - grossMarginDecimal;
            return {
                formula: 'COGS = Revenue × (1 - Gross Margin %)',
                description: 'Cost of goods sold based on gross margin percentage',
                inputs: [
                    { name: 'Revenue', value: row.revenue, source: 'P&L' },
                    { name: 'Gross Margin %', value: config.drivers.gross_margin_pct, source: 'Assumptions' },
                    { name: 'COGS Rate', value: cogsRate * 100, source: 'Calculated' },
                ],
                calculation: `${row.revenue.toLocaleString()} × ${(cogsRate * 100).toFixed(1)}% = ${row.cogs.toLocaleString()}`,
            };
        }
        case 'gross_profit':
            return {
                formula: 'Gross Profit = Revenue - COGS',
                description: 'Revenue minus cost of goods sold',
                inputs: [
                    { name: 'Revenue', value: row.revenue, source: 'P&L' },
                    { name: 'COGS', value: row.cogs, source: 'P&L' },
                ],
                calculation: `${row.revenue.toLocaleString()} - ${row.cogs.toLocaleString()} = ${row.gross_profit.toLocaleString()}`,
            };
        case 'opex_fixed':
            return {
                formula: 'Fixed OpEx = Monthly Fixed Amount',
                description: 'Fixed operating expenses per month',
                inputs: [
                    { name: 'Monthly Fixed OpEx', value: config.drivers.opex.fixed, source: 'Assumptions' },
                ],
                calculation: `${config.drivers.opex.fixed.toLocaleString()}`,
            };
        case 'opex_variable':
            return {
                formula: 'Variable OpEx = Revenue × Variable OpEx %',
                description: 'Variable operating expenses as percentage of revenue',
                inputs: [
                    { name: 'Revenue', value: row.revenue, source: 'P&L' },
                    { name: 'Variable OpEx %', value: config.drivers.opex.variable_pct_of_rev, source: 'Assumptions' },
                ],
                calculation: `${row.revenue.toLocaleString()} × ${config.drivers.opex.variable_pct_of_rev}% = ${row.opex_variable.toLocaleString()}`,
            };
        case 'ebitda':
            return {
                formula: 'EBITDA = Gross Profit - Total OpEx',
                description: 'Earnings before interest, taxes, depreciation, and amortization',
                inputs: [
                    { name: 'Gross Profit', value: row.gross_profit, source: 'P&L' },
                    { name: 'Fixed OpEx', value: row.opex_fixed, source: 'P&L' },
                    { name: 'Variable OpEx', value: row.opex_variable, source: 'P&L' },
                    { name: 'Total OpEx', value: row.total_opex, source: 'P&L' },
                ],
                calculation: `${row.gross_profit.toLocaleString()} - ${row.total_opex.toLocaleString()} = ${row.ebitda.toLocaleString()}`,
            };
        case 'depreciation': {
            const monthlyDeprRate = 1 / (config.drivers.capex.depr_years * 12);
            return {
                formula: 'Depreciation = PP&E Gross × Monthly Depreciation Rate',
                description: 'Straight-line depreciation on property, plant & equipment',
                inputs: [
                    { name: 'PP&E Gross', value: bs[periodIndex]?.ppne_gross || 0, source: 'Balance Sheet' },
                    { name: 'Depreciation Years', value: config.drivers.capex.depr_years, source: 'Assumptions' },
                    { name: 'Monthly Rate', value: monthlyDeprRate * 100, source: 'Calculated' },
                ],
                calculation: `${(bs[periodIndex]?.ppne_gross || 0).toLocaleString()} × ${(monthlyDeprRate * 100).toFixed(2)}% = ${row.depreciation.toLocaleString()}`,
            };
        }
        case 'ebit':
            return {
                formula: 'EBIT = EBITDA - Depreciation',
                description: 'Earnings before interest and taxes',
                inputs: [
                    { name: 'EBITDA', value: row.ebitda, source: 'P&L' },
                    { name: 'Depreciation', value: row.depreciation, source: 'P&L' },
                ],
                calculation: `${row.ebitda.toLocaleString()} - ${row.depreciation.toLocaleString()} = ${row.ebit.toLocaleString()}`,
            };
        case 'interest_expense': {
            const monthlyRate = config.drivers.debt.rate_pct / 100 / 12;
            const debtBalance = periodIndex > 0 ? bs[periodIndex - 1]?.debt_long || 0 : config.drivers.debt.opening;
            return {
                formula: 'Interest = Debt Balance × Monthly Interest Rate',
                description: 'Interest expense on outstanding debt',
                inputs: [
                    { name: 'Debt Balance', value: debtBalance, source: 'Balance Sheet' },
                    { name: 'Annual Rate %', value: config.drivers.debt.rate_pct, source: 'Assumptions' },
                    { name: 'Monthly Rate %', value: monthlyRate * 100, source: 'Calculated' },
                ],
                calculation: `${debtBalance.toLocaleString()} × ${(monthlyRate * 100).toFixed(3)}% = ${row.interest_expense.toLocaleString()}`,
            };
        }
        case 'net_income':
            return {
                formula: 'Net Income = EBT - Tax Expense',
                description: 'Earnings after all expenses and taxes',
                inputs: [
                    { name: 'Earnings Before Tax', value: row.ebt, source: 'P&L' },
                    { name: 'Tax Expense', value: row.tax_expense, source: 'P&L' },
                ],
                calculation: `${row.ebt.toLocaleString()} - ${row.tax_expense.toLocaleString()} = ${row.net_income.toLocaleString()}`,
            };
        default:
            return {
                formula: 'Formula not available',
                description: `Explanation for ${field} is not yet implemented`,
                inputs: [],
                calculation: 'N/A',
            };
    }
}
// Balance Sheet explanations
function explainBSCell(field, periodIndex, config, pnl, bs, cf) {
    const row = bs[periodIndex];
    const pnlRow = pnl[periodIndex];
    // Use a fixed month length for explanations
    const daysInMonth = 30;
    switch (field) {
        case 'cash': {
            const prevCash = periodIndex > 0 ? bs[periodIndex - 1].cash : (config.opening_balances.cash || 100000);
            const netChange = cf[periodIndex].net_change_cash;
            return {
                formula: 'Cash = Previous Cash + Net Change in Cash',
                description: 'Cash balance from previous period plus net cash flow',
                inputs: [
                    { name: 'Previous Cash', value: prevCash, source: 'Balance Sheet' },
                    { name: 'Net Change in Cash', value: netChange, source: 'Cash Flow' },
                ],
                calculation: `${prevCash.toLocaleString()} + ${netChange.toLocaleString()} = ${row.cash.toLocaleString()}`,
            };
        }
        case 'ar':
            return {
                formula: 'A/R = Revenue × DSO / Days in Month',
                description: 'Accounts receivable based on days sales outstanding',
                inputs: [
                    { name: 'Revenue', value: pnlRow.revenue, source: 'P&L' },
                    { name: 'DSO (days)', value: config.drivers.wc.dso, source: 'Assumptions' },
                    { name: 'Days in Month', value: daysInMonth, source: 'Calendar' },
                ],
                calculation: `${pnlRow.revenue.toLocaleString()} × ${config.drivers.wc.dso} / ${daysInMonth} = ${row.ar.toLocaleString()}`,
            };
        case 'inventory':
            if (config.drivers.wc.dio === 0) {
                return {
                    formula: 'Inventory = 0 (No inventory model)',
                    description: 'No inventory for this business model',
                    inputs: [
                        { name: 'DIO (days)', value: config.drivers.wc.dio, source: 'Assumptions' },
                    ],
                    calculation: '0',
                };
            }
            return {
                formula: 'Inventory = COGS × DIO / Days in Month',
                description: 'Inventory based on days inventory outstanding',
                inputs: [
                    { name: 'COGS', value: pnlRow.cogs, source: 'P&L' },
                    { name: 'DIO (days)', value: config.drivers.wc.dio, source: 'Assumptions' },
                    { name: 'Days in Month', value: daysInMonth, source: 'Calendar' },
                ],
                calculation: `${pnlRow.cogs.toLocaleString()} × ${config.drivers.wc.dio} / ${daysInMonth} = ${row.inventory.toLocaleString()}`,
            };
        case 'ap':
            return {
                formula: 'A/P = COGS × DPO / Days in Month',
                description: 'Accounts payable based on days payable outstanding',
                inputs: [
                    { name: 'COGS', value: pnlRow.cogs, source: 'P&L' },
                    { name: 'DPO (days)', value: config.drivers.wc.dpo, source: 'Assumptions' },
                    { name: 'Days in Month', value: daysInMonth, source: 'Calendar' },
                ],
                calculation: `${pnlRow.cogs.toLocaleString()} × ${config.drivers.wc.dpo} / ${daysInMonth} = ${row.ap.toLocaleString()}`,
            };
        case 'retained_earnings': {
            const prevRE = periodIndex > 0 ? bs[periodIndex - 1].retained_earnings : (config.opening_balances.retained_earnings || 0);
            return {
                formula: 'Retained Earnings = Previous RE + Net Income',
                description: 'Accumulated earnings retained in the business',
                inputs: [
                    { name: 'Previous Retained Earnings', value: prevRE, source: 'Balance Sheet' },
                    { name: 'Net Income', value: pnlRow.net_income, source: 'P&L' },
                ],
                calculation: `${prevRE.toLocaleString()} + ${pnlRow.net_income.toLocaleString()} = ${row.retained_earnings.toLocaleString()}`,
            };
        }
        default:
            return {
                formula: 'Formula not available',
                description: `Explanation for ${field} is not yet implemented`,
                inputs: [],
                calculation: 'N/A',
            };
    }
}
// Cash Flow explanations
function explainCFCell(field, periodIndex, config, pnl, bs, cf) {
    const row = cf[periodIndex];
    // const pnlRow = pnl[periodIndex];
    switch (field) {
        case 'cfo':
            return {
                formula: 'CFO = Net Income + Depreciation + Working Capital Changes',
                description: 'Cash flow from operating activities',
                inputs: [
                    { name: 'Net Income', value: row.net_income, source: 'P&L' },
                    { name: 'Depreciation', value: row.depreciation, source: 'P&L' },
                    { name: 'Change in A/R', value: row.change_ar, source: 'Working Capital' },
                    { name: 'Change in Inventory', value: row.change_inventory, source: 'Working Capital' },
                    { name: 'Change in A/P', value: row.change_ap, source: 'Working Capital' },
                ],
                calculation: `${row.net_income.toLocaleString()} + ${row.depreciation.toLocaleString()} + WC Changes = ${row.cfo.toLocaleString()}`,
            };
        case 'net_change_cash':
            return {
                formula: 'Net Change = CFO + CFI + CFF',
                description: 'Total change in cash from all activities',
                inputs: [
                    { name: 'Cash Flow from Operations', value: row.cfo, source: 'Cash Flow' },
                    { name: 'Cash Flow from Investing', value: row.cfi, source: 'Cash Flow' },
                    { name: 'Cash Flow from Financing', value: row.cff, source: 'Cash Flow' },
                ],
                calculation: `${row.cfo.toLocaleString()} + ${row.cfi.toLocaleString()} + ${row.cff.toLocaleString()} = ${row.net_change_cash.toLocaleString()}`,
            };
        default:
            return {
                formula: 'Formula not available',
                description: `Explanation for ${field} is not yet implemented`,
                inputs: [],
                calculation: 'N/A',
            };
    }
}
