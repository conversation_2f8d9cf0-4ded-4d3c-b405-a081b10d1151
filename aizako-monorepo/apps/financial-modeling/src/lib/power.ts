import type { Config, ProjectionResult } from '@/lib/engine/types';

export type Variant =
  | { kind: 'price'; dir: 1 | -1 }
  | { kind: 'volume'; dir: 1 | -1 }
  | { kind: 'cogs_pct'; dir: 1 | -1 }
  | { kind: 'dso' | 'dpo' | 'dio'; dir: 1 | -1 };

/**
 * Apply a small perturbation to a config without mutating the original
 */
export function perturbConfig(base: Config, v: Variant): Config {
  const cfg: Config = structuredClone(base);
  const r = cfg.drivers.revenue;

  const mult = (d: 1 | -1) => (d === 1 ? 1.01 : 0.99);

  switch (v.kind) {
    case 'price': {
      // For the simple revenue model, price increase = increase start_run_rate
      const m = mult(v.dir);
      if (typeof r.start_run_rate === 'number') {
        r.start_run_rate = r.start_run_rate * m;
      }
      break;
    }
    case 'volume': {
      // For volume, we also adjust start_run_rate (conceptually same as price in this model)
      const m = mult(v.dir);
      if (typeof r.start_run_rate === 'number') {
        r.start_run_rate = r.start_run_rate * m;
      }
      break;
    }
    case 'cogs_pct': {
      const gm = cfg.drivers.gross_margin_pct;
      if (typeof gm === 'number') {
        const cogs = 100 - gm;
        const cogs2 = Math.max(0, Math.min(100, cogs * (v.dir === 1 ? 1.01 : 0.99)));
        cfg.drivers.gross_margin_pct = 100 - cogs2;
      }
      break;
    }
    case 'dso':
      cfg.drivers.wc.dso = Math.max(0, Math.min(365, cfg.drivers.wc.dso + (v.dir === 1 ? 1 : -1)));
      break;
    case 'dpo':
      cfg.drivers.wc.dpo = Math.max(0, Math.min(365, cfg.drivers.wc.dpo + (v.dir === 1 ? 1 : -1)));
      break;
    case 'dio':
      cfg.drivers.wc.dio = Math.max(0, Math.min(365, cfg.drivers.wc.dio + (v.dir === 1 ? 1 : -1)));
      break;
  }
  return cfg;
}

/**
 * Extract Year 1 financial metrics from a projection result
 */
export function y1Metrics(proj: ProjectionResult) {
  const sum = (xs: number[]) => xs.reduce((a, b) => a + b, 0);
  const pnl = proj.pnl;
  const cf = proj.cf;
  const bs = proj.bs;
  const take12 = <T,>(arr: T[]) => arr.slice(0, Math.min(12, arr.length));

  const revY1 = sum(take12(pnl).map(r => +r.revenue || 0));
  const niY1 = sum(take12(pnl).map(r => +r.net_income || 0));
  const cfoY1 = sum(take12(cf).map(r => +r.cfo || 0));
  const endCashY1 = (take12(bs).at(-1)?.cash ?? 0) as number;

  return { revY1, niY1, cfoY1, endCashY1 };
}

/**
 * Safe number formatting for display
 */
export function fmtMoney(n: number): string {
  return n.toLocaleString(undefined, { maximumFractionDigits: 0 });
}

/**
 * Format percentage change with proper handling of zero base values
 */
export function fmtPct(ratio: number): string {
  if (!isFinite(ratio)) return '—';
  return (ratio * 100).toFixed(2) + '%';
}

/**
 * Calculate percentage change safely handling zero denominators
 */
export function calcPctChange(before: number, after: number): number {
  const base = Math.abs(before);
  if (base === 0) return after === 0 ? 0 : Infinity;
  return (after - before) / base;
}

/**
 * Get display name for a variant
 */
export function getVariantDisplayName(variant: Variant): string {
  const sign = variant.dir === 1 ? '+' : '−';
  switch (variant.kind) {
    case 'price':
      return `Price ${sign}1%`;
    case 'volume':
      return `Volume ${sign}1%`;
    case 'cogs_pct':
      return `COGS% ${sign}1%`;
    case 'dso':
      return `DSO ${sign}1 day`;
    case 'dpo':
      return `DPO ${sign}1 day`;
    case 'dio':
      return `DIO ${sign}1 day`;
  }
}

/**
 * Get description for a metric with tooltip information
 */
export function getMetricInfo(metric: 'revenue' | 'netIncome' | 'cfo' | 'endCash'): { label: string; tooltip: string } {
  const info = {
    revenue: {
      label: 'Revenue (Y1)',
      tooltip: 'Total revenue over Year 1'
    },
    netIncome: {
      label: 'Net Income (Y1)', 
      tooltip: 'Total profit after tax over Year 1'
    },
    cfo: {
      label: 'CFO (Y1)',
      tooltip: 'Year-1 cash from operations (indirect)'
    },
    endCash: {
      label: 'End Cash (Y1)',
      tooltip: 'Cash balance at end of Year 1'
    }
  };
  
  return info[metric];
}