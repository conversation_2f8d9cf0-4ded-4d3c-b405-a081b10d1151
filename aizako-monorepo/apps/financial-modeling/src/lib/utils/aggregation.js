/**
 * Aggregates monthly P&L data into yearly data by summing flow items
 */
export function aggregateYearlyPnL(monthlyData) {
    const yearlyData = [];
    for (let year = 1; year <= Math.ceil(monthlyData.length / 12); year++) {
        const startMonth = (year - 1) * 12;
        const endMonth = Math.min(year * 12, monthlyData.length);
        const yearData = monthlyData.slice(startMonth, endMonth);
        if (yearData.length === 0)
            break;
        // Sum flow items for the year
        const aggregated = {
            period: year,
            date: yearData[yearData.length - 1].date,
            revenue: yearData.reduce((sum, row) => sum + row.revenue, 0),
            cogs: yearData.reduce((sum, row) => sum + row.cogs, 0),
            gross_profit: yearData.reduce((sum, row) => sum + row.gross_profit, 0),
            opex_fixed: yearData.reduce((sum, row) => sum + row.opex_fixed, 0),
            opex_variable: yearData.reduce((sum, row) => sum + row.opex_variable, 0),
            total_opex: yearData.reduce((sum, row) => sum + row.total_opex, 0),
            ebitda: yearData.reduce((sum, row) => sum + row.ebitda, 0),
            depreciation: yearData.reduce((sum, row) => sum + row.depreciation, 0),
            ebit: yearData.reduce((sum, row) => sum + row.ebit, 0),
            interest_expense: yearData.reduce((sum, row) => sum + row.interest_expense, 0),
            ebt: yearData.reduce((sum, row) => sum + row.ebt, 0),
            tax_expense: yearData.reduce((sum, row) => sum + row.tax_expense, 0),
            net_income: yearData.reduce((sum, row) => sum + row.net_income, 0),
        };
        yearlyData.push(aggregated);
    }
    return yearlyData;
}
/**
 * Aggregates monthly Balance Sheet data into yearly data by taking year-end snapshots
 */
export function aggregateYearlyBS(monthlyData) {
    const yearlyData = [];
    for (let year = 1; year <= Math.ceil(monthlyData.length / 12); year++) {
        const yearEndMonth = Math.min(year * 12 - 1, monthlyData.length - 1);
        const yearEndData = monthlyData[yearEndMonth];
        if (!yearEndData)
            break;
        // For balance sheet, we take the year-end snapshot
        yearlyData.push({
            ...yearEndData,
            period: year,
        });
    }
    return yearlyData;
}
/**
 * Aggregates monthly Cash Flow data into yearly data by summing flow items
 */
export function aggregateYearlyCF(monthlyData) {
    const yearlyData = [];
    for (let year = 1; year <= Math.ceil(monthlyData.length / 12); year++) {
        const startMonth = (year - 1) * 12;
        const endMonth = Math.min(year * 12, monthlyData.length);
        const yearData = monthlyData.slice(startMonth, endMonth);
        if (yearData.length === 0)
            break;
        // Sum all flow items for the year
        const aggregated = {
            period: year,
            date: yearData[yearData.length - 1].date,
            net_income: yearData.reduce((sum, row) => sum + row.net_income, 0),
            depreciation: yearData.reduce((sum, row) => sum + row.depreciation, 0),
            change_ar: yearData.reduce((sum, row) => sum + row.change_ar, 0),
            change_inventory: yearData.reduce((sum, row) => sum + row.change_inventory, 0),
            change_ap: yearData.reduce((sum, row) => sum + row.change_ap, 0),
            cfo: yearData.reduce((sum, row) => sum + row.cfo, 0),
            capex: yearData.reduce((sum, row) => sum + row.capex, 0),
            cfi: yearData.reduce((sum, row) => sum + row.cfi, 0),
            debt_draws: yearData.reduce((sum, row) => sum + row.debt_draws, 0),
            debt_principal_payments: yearData.reduce((sum, row) => sum + row.debt_principal_payments, 0),
            cff: yearData.reduce((sum, row) => sum + row.cff, 0),
            net_change_cash: yearData.reduce((sum, row) => sum + row.net_change_cash, 0),
            beginning_cash: yearData[0].beginning_cash, // First month of year
            ending_cash: yearData[yearData.length - 1].ending_cash, // Last month of year
        };
        yearlyData.push(aggregated);
    }
    return yearlyData;
}
/**
 * Aggregates monthly Metrics data into yearly data by taking year-end values or averaging
 */
export function aggregateYearlyMetrics(monthlyData) {
    const yearlyData = [];
    for (let year = 1; year <= Math.ceil(monthlyData.length / 12); year++) {
        const startMonth = (year - 1) * 12;
        const endMonth = Math.min(year * 12, monthlyData.length);
        const yearData = monthlyData.slice(startMonth, endMonth);
        if (yearData.length === 0)
            break;
        const yearEndData = yearData[yearData.length - 1];
        // For metrics, we take the year-end snapshot (ratios are point-in-time)
        yearlyData.push({
            ...yearEndData,
            period: year,
        });
    }
    return yearlyData;
}
/**
 * Generic aggregation function that delegates to the appropriate aggregator
 */
export function aggregateYearly(data, type) {
    switch (type) {
        case 'pnl':
            return aggregateYearlyPnL(data);
        case 'bs':
            return aggregateYearlyBS(data);
        case 'cf':
            return aggregateYearlyCF(data);
        case 'metrics':
            return aggregateYearlyMetrics(data);
        default:
            throw new Error(`Unknown aggregation type: ${type}`);
    }
}
