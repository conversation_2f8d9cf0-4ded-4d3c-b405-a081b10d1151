/**
 * Currency formatting utilities for financial statements
 */

export interface CurrencyFormatOptions {
  currency?: string;
  locale?: string;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
  showSymbol?: boolean;
}

/**
 * Format a number as currency with proper localization
 */
export function formatCurrency(
  value: number,
  options: CurrencyFormatOptions = {}
): string {
  const {
    currency = 'USD',
    locale = 'en-US',
    minimumFractionDigits = 0,
    maximumFractionDigits = 0,
    showSymbol = true,
  } = options;

  // Handle zero and very small values
  if (Math.abs(value) < 0.01) {
    return showSymbol ? '$0' : '0';
  }

  const formatter = new Intl.NumberFormat(locale, {
    style: showSymbol ? 'currency' : 'decimal',
    currency,
    minimumFractionDigits,
    maximumFractionDigits,
  });

  return formatter.format(value);
}

/**
 * Format currency with intelligent precision based on value magnitude
 */
export function formatCurrencyIntelligent(
  value: number,
  options: Omit<CurrencyFormatOptions, 'minimumFractionDigits' | 'maximumFractionDigits'> = {}
): string {
  const absValue = Math.abs(value);

  // For large values (>= 1M), show in millions with 1 decimal
  if (absValue >= 1_000_000) {
    const millions = value / 1_000_000;
    return `${formatCurrency(millions, { ...options, maximumFractionDigits: 1 })}M`;
  }

  // For medium values (>= 1K), show in thousands with 0 decimals
  if (absValue >= 1_000) {
    const thousands = value / 1_000;
    return `${formatCurrency(thousands, { ...options, maximumFractionDigits: 0 })}K`;
  }

  // For small values, show full amount with 2 decimal places if needed
  return formatCurrency(value, { 
    ...options, 
    minimumFractionDigits: 0,
    maximumFractionDigits: absValue < 1 ? 2 : 0 
  });
}

/**
 * Format currency for financial statements with standard formatting
 */
export function formatFinancialValue(value: number): string {
  return formatCurrency(value, {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  });
}

/**
 * Format percentage values
 */
export function formatPercentage(value: number, decimals: number = 1): string {
  return `${(value * 100).toFixed(decimals)}%`;
}

/**
 * Format large numbers with appropriate units (K, M, B)
 */
export function formatLargeNumber(value: number): string {
  const absValue = Math.abs(value);
  
  if (absValue >= 1_000_000_000) {
    return `${(value / 1_000_000_000).toFixed(1)}B`;
  }
  
  if (absValue >= 1_000_000) {
    return `${(value / 1_000_000).toFixed(1)}M`;
  }
  
  if (absValue >= 1_000) {
    return `${(value / 1_000).toFixed(0)}K`;
  }
  
  return value.toFixed(0);
}