/**
 * Display formatting utilities for metrics banner
 */

/**
 * Format months value for runway display
 * Returns "—" for infinite values, whole number for finite values
 */
export function formatMonths(months: number): string {
  if (!isFinite(months)) {
    return '—';
  }
  
  if (months < 0) {
    return '0';
  }
  
  return Math.floor(months).toString();
}

/**
 * Format ratio values (DSCR, ICR) with appropriate decimal places
 * Returns "—" for NaN/invalid values
 */
export function formatRatio(ratio: number, decimals: number = 2): string {
  if (isNaN(ratio) || !isFinite(ratio)) {
    return '—';
  }
  
  return ratio.toFixed(decimals);
}

/**
 * Format burn rate as currency without symbol (symbol shown in label)
 * Returns "0" for zero burn, formatted number for positive burn
 */
export function formatBurn(burn: number): string {
  if (burn === 0) {
    return '0';
  }
  
  return burn.toLocaleString(undefined, {
    maximumFractionDigits: 0,
    minimumFractionDigits: 0,
  });
}

/**
 * Format large currency values with K/M suffixes for better readability
 */
export function formatCurrencyCompact(value: number): string {
  const absValue = Math.abs(value);
  
  if (absValue >= 1_000_000) {
    return `${(value / 1_000_000).toFixed(1)}M`;
  }
  
  if (absValue >= 1_000) {
    return `${Math.round(value / 1_000)}K`;
  }
  
  return value.toLocaleString(undefined, {
    maximumFractionDigits: 0,
  });
}

/**
 * Get appropriate unit text for different metric types
 */
export function getMetricUnit(type: 'burn' | 'runway' | 'ratio', currency?: string): string {
  switch (type) {
    case 'burn':
      return `${currency || 'USD'}/mo`;
    case 'runway':
      return 'months';
    case 'ratio':
      return 'x';
    default:
      return '';
  }
}

/**
 * Get metric descriptions for tooltips
 */
export function getMetricDescription(metric: 'burn' | 'runway' | 'dscr' | 'icr'): string {
  const descriptions = {
    burn: 'Average monthly cash outflow (first 3 months)',
    runway: 'Months until cash hits zero at current burn rate',
    dscr: 'Cash flow available for debt service (Y1)',
    icr: 'Earnings available to cover interest (Y1)',
  };
  
  return descriptions[metric];
}

/**
 * Format percentage values for display
 */
export function formatPercentage(value: number, decimals: number = 1): string {
  if (isNaN(value) || !isFinite(value)) {
    return '—';
  }
  
  return `${(value * 100).toFixed(decimals)}%`;
}