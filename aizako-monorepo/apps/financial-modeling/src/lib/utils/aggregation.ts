import { PnLRow, BSRow, CFRow, Metrics } from '@/lib/engine/types';

/**
 * Aggregates monthly P&L data into yearly data by summing flow items
 */
export function aggregateYearlyPnL(monthlyData: PnLRow[]): PnLRow[] {
  const yearlyData: PnLRow[] = []
  
  for (let year = 1; year <= Math.ceil(monthlyData.length / 12); year++) {
    const startMonth = (year - 1) * 12
    const endMonth = Math.min(year * 12, monthlyData.length)
    const yearData = monthlyData.slice(startMonth, endMonth)
    
    if (yearData.length === 0) break
    
    // Sum flow items for the year
    const aggregated: PnLRow = {
      period: year,
      date: yearData[yearData.length - 1].date,
      revenue: yearData.reduce((sum, row) => sum + row.revenue, 0),
      cogs: yearData.reduce((sum, row) => sum + row.cogs, 0),
      gross_profit: yearData.reduce((sum, row) => sum + row.gross_profit, 0),
      opex_fixed: yearData.reduce((sum, row) => sum + row.opex_fixed, 0),
      opex_variable: yearData.reduce((sum, row) => sum + row.opex_variable, 0),
      total_opex: yearData.reduce((sum, row) => sum + row.total_opex, 0),
      ebitda: yearData.reduce((sum, row) => sum + row.ebitda, 0),
      depreciation: yearData.reduce((sum, row) => sum + row.depreciation, 0),
      ebit: yearData.reduce((sum, row) => sum + row.ebit, 0),
      interest_expense: yearData.reduce((sum, row) => sum + row.interest_expense, 0),
      ebt: yearData.reduce((sum, row) => sum + row.ebt, 0),
      tax_expense: yearData.reduce((sum, row) => sum + row.tax_expense, 0),
      net_income: yearData.reduce((sum, row) => sum + row.net_income, 0),
    }
    
    yearlyData.push(aggregated)
  }
  
  return yearlyData
}

/**
 * Aggregates monthly Balance Sheet data into yearly data by taking year-end snapshots
 */
export function aggregateYearlyBS(monthlyData: BSRow[]): BSRow[] {
  const yearlyData: BSRow[] = []
  
  for (let year = 1; year <= Math.ceil(monthlyData.length / 12); year++) {
    const yearEndMonth = Math.min(year * 12 - 1, monthlyData.length - 1)
    const yearEndData = monthlyData[yearEndMonth]
    
    if (!yearEndData) break
    
    // For balance sheet, we take the year-end snapshot
    yearlyData.push({
      ...yearEndData,
      period: year,
    })
  }
  
  return yearlyData
}

/**
 * Aggregates monthly Cash Flow data into yearly data by summing flow items
 */
export function aggregateYearlyCF(monthlyData: CFRow[]): CFRow[] {
  const yearlyData: CFRow[] = []
  
  for (let year = 1; year <= Math.ceil(monthlyData.length / 12); year++) {
    const startMonth = (year - 1) * 12
    const endMonth = Math.min(year * 12, monthlyData.length)
    const yearData = monthlyData.slice(startMonth, endMonth)
    
    if (yearData.length === 0) break
    
    // Sum all flow items for the year
    const aggregated: CFRow = {
      period: year,
      date: yearData[yearData.length - 1].date,
      net_income: yearData.reduce((sum, row) => sum + row.net_income, 0),
      depreciation: yearData.reduce((sum, row) => sum + row.depreciation, 0),
      change_ar: yearData.reduce((sum, row) => sum + row.change_ar, 0),
      change_inventory: yearData.reduce((sum, row) => sum + row.change_inventory, 0),
      change_ap: yearData.reduce((sum, row) => sum + row.change_ap, 0),
      cfo: yearData.reduce((sum, row) => sum + row.cfo, 0),
      capex: yearData.reduce((sum, row) => sum + row.capex, 0),
      cfi: yearData.reduce((sum, row) => sum + row.cfi, 0),
      debt_draws: yearData.reduce((sum, row) => sum + row.debt_draws, 0),
      debt_principal_payments: yearData.reduce((sum, row) => sum + row.debt_principal_payments, 0),
      cff: yearData.reduce((sum, row) => sum + row.cff, 0),
      net_change_cash: yearData.reduce((sum, row) => sum + row.net_change_cash, 0),
      beginning_cash: yearData[0].beginning_cash, // First month of year
      ending_cash: yearData[yearData.length - 1].ending_cash, // Last month of year
    }
    
    yearlyData.push(aggregated)
  }
  
  return yearlyData
}

/**
 * Aggregates monthly Metrics data into yearly data by taking year-end values or averaging
 */
export function aggregateYearlyMetrics(monthlyData: Metrics[]): Metrics[] {
  const yearlyData: Metrics[] = []
  
  for (let year = 1; year <= Math.ceil(monthlyData.length / 12); year++) {
    const startMonth = (year - 1) * 12
    const endMonth = Math.min(year * 12, monthlyData.length)
    const yearData = monthlyData.slice(startMonth, endMonth)
    
    if (yearData.length === 0) break
    
    const yearEndData = yearData[yearData.length - 1]
    
    // For metrics, we take the year-end snapshot (ratios are point-in-time)
    yearlyData.push({
      ...yearEndData,
      period: year,
    })
  }
  
  return yearlyData
}

/**
 * Generic aggregation function that delegates to the appropriate aggregator
 */
export function aggregateYearly<T extends { period: number; date: string }>(
  data: T[], 
  type: 'pnl' | 'bs' | 'cf' | 'metrics'
): T[] {
  switch (type) {
    case 'pnl':
      return aggregateYearlyPnL(data as unknown as PnLRow[]) as unknown as T[]
    case 'bs':
      return aggregateYearlyBS(data as unknown as BSRow[]) as unknown as T[]
    case 'cf':
      return aggregateYearlyCF(data as unknown as CFRow[]) as unknown as T[]
    case 'metrics':
      return aggregateYearlyMetrics(data as unknown as Metrics[]) as unknown as T[]
    default:
      throw new Error(`Unknown aggregation type: ${type}`)
  }
}