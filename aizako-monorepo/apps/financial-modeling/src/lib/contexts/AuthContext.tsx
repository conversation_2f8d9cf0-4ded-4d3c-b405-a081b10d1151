'use client'

import React, { createContext, useReducer, useEffect, useCallback, ReactNode } from 'react';
import { 
  apiClient, 
  ApiError,
  type User,
  type Tenant,
  type Membership,
  type SignUpRequest
} from '@/lib/api-client';

// Auth state interface
export interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  currentTenant: Tenant | null;
  memberships: Membership[];
  error: string | null;
}

// Auth context interface
export interface AuthContextType {
  // State
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  currentTenant: Tenant | null;
  memberships: Membership[];
  error: string | null;
  
  // Actions
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (data: SignUpRequest) => Promise<void>;
  signOut: () => Promise<void>;
  switchTenant: (tenantId: string) => Promise<void>;
  clearError: () => void;
}

// Auth state reducer
type AuthAction = 
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_AUTH'; payload: { user: User; currentTenant?: Tenant; memberships: Membership[] } }
  | { type: 'CLEAR_AUTH' }
  | { type: 'SET_CURRENT_TENANT'; payload: Tenant }
  | { type: 'CLEAR_ERROR' };

const initialState: AuthState = {
  isAuthenticated: false,
  isLoading: true, // Start with loading to check existing session
  user: null,
  currentTenant: null,
  memberships: [],
  error: null,
};

function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    case 'SET_AUTH':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        currentTenant: action.payload.currentTenant || null,
        memberships: action.payload.memberships,
        error: null,
        isLoading: false,
      };
    case 'CLEAR_AUTH':
      return {
        ...initialState,
        isLoading: false,
      };
    case 'SET_CURRENT_TENANT':
      return { ...state, currentTenant: action.payload };
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    default:
      return state;
  }
}

// Create context
const AuthContext = createContext<AuthContextType | null>(null);

// Auth provider component
interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Auth actions
  const signIn = async (email: string, password: string): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'CLEAR_ERROR' });

    try {
      const response = await apiClient.signIn({ email, password });
      
      dispatch({
        type: 'SET_AUTH',
        payload: {
          user: response.user,
          currentTenant: response.tenant,
          memberships: [], // Will be populated when we fetch user profile
        },
      });
    } catch (error) {
      dispatch({ 
        type: 'SET_ERROR', 
        payload: error instanceof ApiError ? error.message : 'Sign in failed' 
      });
      throw error;
    }
  };

  const signUp = async (data: SignUpRequest): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'CLEAR_ERROR' });

    try {
      const response = await apiClient.signUp(data);
      
      dispatch({
        type: 'SET_AUTH',
        payload: {
          user: response.user,
          currentTenant: response.tenant,
          memberships: [], // Will be populated when we fetch user profile
        },
      });
    } catch (error) {
      dispatch({ 
        type: 'SET_ERROR', 
        payload: error instanceof ApiError ? error.message : 'Sign up failed' 
      });
      throw error;
    }
  };

  const signOut = async (): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true });

    try {
      await apiClient.signOut();
    } catch (error) {
      // Even if the API call fails, we should clear local auth state
      console.error('Sign out error:', error);
    } finally {
      dispatch({ type: 'CLEAR_AUTH' });
    }
  };

  const switchTenant = async (tenantId: string): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'CLEAR_ERROR' });

    try {
      // For now, just find the tenant from memberships
      // TODO: Implement proper tenant switching on backend
      const membership = state.memberships.find(m => m.tenantId === tenantId);
      if (membership) {
        // This is a placeholder - would need backend support
        console.log('Switching to tenant:', tenantId);
      }
      throw new Error('Tenant switching not yet implemented');
    } catch (error) {
      dispatch({ 
        type: 'SET_ERROR', 
        payload: error instanceof Error ? error.message : 'Tenant switch failed' 
      });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const refreshAuth = useCallback(async (): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'CLEAR_ERROR' });

    try {
      const response = await apiClient.getCurrentUser();

      if (response.success && response.user) {
        dispatch({
          type: 'SET_AUTH',
          payload: {
            user: response.user,
            currentTenant: response.currentTenant || undefined,
            memberships: response.allTenants || [],
          },
        });
      } else {
        dispatch({ type: 'CLEAR_AUTH' });
      }
    } catch (error) {
      // If auth check fails, clear auth state
      dispatch({ type: 'CLEAR_AUTH' });
    }
  }, []);

  const clearError = (): void => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  // Check for existing session on mount
  useEffect(() => {
    refreshAuth();
  }, [refreshAuth]);

  const contextValue: AuthContextType = {
    ...state,
    signIn,
    signUp,
    signOut,
    switchTenant,
    clearError,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export default AuthContext;