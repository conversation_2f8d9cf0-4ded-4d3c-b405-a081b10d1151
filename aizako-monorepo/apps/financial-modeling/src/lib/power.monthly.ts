import type { ProjectionResult } from '@/lib/engine/types';

/**
 * Monthly metrics data structure for first 12 months
 */
export interface MonthlyMetrics {
  revenue: number[];
  netInc: number[];
  cfo: number[];
  endCash: number[];
}

/**
 * Monthly comparison row for results table
 */
export interface MonthlyComparisonRow {
  month: number;
  revenue: { base: number; after: number; delta: number };
  netInc: { base: number; after: number; delta: number };
  cfo: { base: number; after: number; delta: number };
  endCash: { base: number; after: number; delta: number };
}

/**
 * Extract monthly metrics for the first 12 months from a projection result
 */
export function monthlyMetrics(proj: ProjectionResult): MonthlyMetrics {
  // Take first 12 months or all available if less than 12
  const take12 = <T,>(arr: T[]) => arr.slice(0, Math.min(12, arr.length));
  
  const pnl = take12(proj.pnl || []);
  const cf = take12(proj.cf || []);
  const bs = take12(proj.bs || []);

  return {
    revenue: pnl.map(r => +r.revenue || 0),
    netInc: pnl.map(r => +r.net_income || 0),
    cfo: cf.map(r => +r.cfo || 0),
    endCash: bs.map(r => +r.cash || 0)
  };
}

/**
 * Calculate month-by-month differences between base and after scenarios
 */
export function monthlyDiff(
  base: MonthlyMetrics, 
  after: MonthlyMetrics
): MonthlyComparisonRow[] {
  // Determine the minimum length across all metrics arrays
  const len = Math.min(
    base.revenue.length,
    after.revenue.length,
    base.netInc.length,
    after.netInc.length,
    base.cfo.length,
    after.cfo.length,
    base.endCash.length,
    after.endCash.length
  );

  const rows: MonthlyComparisonRow[] = [];

  for (let i = 0; i < len; i++) {
    rows.push({
      month: i + 1,
      revenue: {
        base: base.revenue[i],
        after: after.revenue[i],
        delta: after.revenue[i] - base.revenue[i]
      },
      netInc: {
        base: base.netInc[i],
        after: after.netInc[i],
        delta: after.netInc[i] - base.netInc[i]
      },
      cfo: {
        base: base.cfo[i],
        after: after.cfo[i],
        delta: after.cfo[i] - base.cfo[i]
      },
      endCash: {
        base: base.endCash[i],
        after: after.endCash[i],
        delta: after.endCash[i] - base.endCash[i]
      }
    });
  }

  return rows;
}

/**
 * Safe number formatting for monthly table display
 */
export function fmtMonthlyNumber(n: number): string {
  if (!isFinite(n)) return '—';
  return n.toLocaleString(undefined, { maximumFractionDigits: 0 });
}

/**
 * Format a delta value with appropriate sign and color indication
 */
export function formatMonthlyDelta(delta: number): { 
  formatted: string; 
  isPositive: boolean; 
  isZero: boolean 
} {
  const isZero = Math.abs(delta) < 0.01;
  const isPositive = delta > 0;
  const sign = isZero ? '' : (isPositive ? '+' : '');
  
  return {
    formatted: `${sign}${fmtMonthlyNumber(delta)}`,
    isPositive,
    isZero
  };
}

/**
 * Validate that monthly metrics have consistent data
 */
export function validateMonthlyMetrics(metrics: MonthlyMetrics): boolean {
  const lengths = [
    metrics.revenue.length,
    metrics.netInc.length,
    metrics.cfo.length,
    metrics.endCash.length
  ];
  
  // Check that all arrays have the same length and at least 1 month
  const allSameLength = lengths.every(len => len === lengths[0]);
  const hasData = lengths[0] > 0;
  
  return allSameLength && hasData;
}

/**
 * Get metric display information for monthly view tooltips
 */
export function getMonthlyMetricInfo(metric: 'revenue' | 'netInc' | 'cfo' | 'endCash'): {
  label: string;
  tooltip: string;
  shortLabel: string;
} {
  const info = {
    revenue: {
      label: 'Revenue',
      shortLabel: 'Rev',
      tooltip: 'Monthly revenue'
    },
    netInc: {
      label: 'Net Income',
      shortLabel: 'NI',
      tooltip: 'Monthly net income after tax'
    },
    cfo: {
      label: 'CFO',
      shortLabel: 'CFO',
      tooltip: 'Monthly cash from operations'
    },
    endCash: {
      label: 'End Cash',
      shortLabel: 'Cash',
      tooltip: 'Cash balance at end of month'
    }
  };
  
  return info[metric];
}