/**
 * Metrics calculation utilities for the financial modeling banner
 */

import { ScenarioSnapshot } from '@/hooks/useScenario';

export interface MetricsSummary {
  burn: number;
  runway: number;
  dscrY1: number;
  icrY1: number;
}

export interface MetricStatus {
  type: 'success' | 'warning' | 'neutral';
  badge?: string;
}

/**
 * Calculate average of an array of numbers
 */
export function avg(nums: number[]): number {
  if (!nums.length) return 0;
  return nums.reduce((a, b) => a + b, 0) / nums.length;
}

/**
 * Safely convert value to number, return 0 if invalid
 */
export function safeNumber(value: unknown): number {
  const num = Number(value);
  return isNaN(num) ? 0 : num;
}

/**
 * Compute burn rate and runway from scenario snapshot
 * Burn = max(0, -avg(CFO[months 1..3]))
 * Runway = cash / burn (if burn > 0)
 */
export function computeBurnAndRunway(snapshot: ScenarioSnapshot): { burn: number; runway: number } {
  const cf = snapshot.monthly?.cf ?? [];
  const bs = snapshot.monthly?.bs ?? [];
  
  // Get first 3 months CFO data
  const cfoFirst3 = cf.slice(0, 3).map(row => safeNumber(row?.cfo));
  const avgCfo = avg(cfoFirst3);
  
  // Burn is negative CFO (cash outflow), normalized to positive for display
  const burn = Math.max(0, -avgCfo);
  
  // Get first month cash balance
  const cashM1 = safeNumber(bs?.[0]?.cash);
  
  // Calculate runway
  const runway = burn > 0 ? (cashM1 / burn) : Infinity;
  
  return { burn, runway };
}

/**
 * Extract coverage metrics (DSCR, ICR) from yearly metrics
 * Uses Year 1 (index 0) data
 */
export function getCoverageMetrics(snapshot: ScenarioSnapshot): { dscrY1: number; icrY1: number } {
  const yearlyMetrics = snapshot.yearly?.metrics ?? [];
  const y1Metrics = yearlyMetrics[0];
  
  const dscrY1 = y1Metrics ? safeNumber(y1Metrics.dscr) : NaN;
  const icrY1 = y1Metrics ? safeNumber(y1Metrics.icr) : NaN;
  
  return { dscrY1, icrY1 };
}

/**
 * Determine status for burn rate metric
 */
export function getBurnStatus(burn: number, avgCfo: number): MetricStatus {
  // If burn is 0 and CFO is positive, company is profitable
  if (burn === 0 && avgCfo > 0) {
    return { type: 'success', badge: 'Profitable' };
  }
  
  // Otherwise neutral (burn itself doesn't have warning thresholds)
  return { type: 'neutral' };
}

/**
 * Determine status for runway metric
 */
export function getRunwayStatus(runway: number): MetricStatus {
  if (!isFinite(runway)) {
    return { type: 'success' };
  }
  
  if (runway < 6) {
    return { type: 'warning' };
  }
  
  return { type: 'neutral' };
}

/**
 * Determine status for DSCR metric
 */
export function getDscrStatus(dscr: number): MetricStatus {
  if (isNaN(dscr)) {
    return { type: 'neutral' };
  }
  
  if (dscr < 1.20) {
    return { type: 'warning' };
  }
  
  return { type: 'neutral' };
}

/**
 * Determine status for ICR metric
 */
export function getIcrStatus(icr: number): MetricStatus {
  if (isNaN(icr)) {
    return { type: 'neutral' };
  }
  
  if (icr < 2.00) {
    return { type: 'warning' };
  }
  
  return { type: 'neutral' };
}

/**
 * Extract all metrics from a scenario snapshot
 */
export function extractMetrics(snapshot: ScenarioSnapshot): MetricsSummary {
  const { burn, runway } = computeBurnAndRunway(snapshot);
  const { dscrY1, icrY1 } = getCoverageMetrics(snapshot);
  
  return {
    burn,
    runway,
    dscrY1,
    icrY1,
  };
}

/**
 * Check if scenario has sufficient data for metrics calculation
 */
export function hasValidMetricsData(snapshot: ScenarioSnapshot): boolean {
  const hasMonthlyCf = snapshot.monthly?.cf && snapshot.monthly.cf.length >= 3;
  const hasMonthlyBs = snapshot.monthly?.bs && snapshot.monthly.bs.length >= 1;
  
  return Boolean(hasMonthlyCf && hasMonthlyBs);
}