import { NextRequest, NextResponse } from 'next/server';
import {
  requireAuth,
  requireRole,
  requireAdmin,
  requireOwner,
  addSecurityHeaders,
  connectMongo,
  Tenant,
  Membership,
  type SessionData,
} from '@aizako/core-lib/server';

/**
 * Authenticated context passed to API handlers
 */
export interface AuthedContext {
  userId: string;
  tenantId: string;
  user: SessionData;
}

/**
 * Extended authenticated context with role information
 */
export interface AuthedContextWithRole extends AuthedContext {
  role: string;
  membership: {
    role: string;
    joinedAt: Date;
  };
}

/**
 * API handler that receives authenticated context
 */
export type AuthedHandler<T = AuthedContext> = (req: NextRequest, ctx: T) => Promise<Response> | Response;

/**
 * Options for withAuth wrapper
 */
export interface WithAuthOptions {
  requireRole?: string;
  requireAdmin?: boolean;
  requireOwner?: boolean;
  checkModuleAccess?: string;
}

/**
 * Higher-order function that wraps API handlers with authentication
 * 
 * @param handler The API handler to wrap
 * @param options Optional authentication requirements
 * @returns A Next.js API handler that enforces authentication
 * 
 * @example
 * ```typescript
 * export const GET = withAuth(async (req, ctx) => {
 *   return NextResponse.json({ userId: ctx.userId, tenantId: ctx.tenantId });
 * });
 * 
 * // With role requirement
 * export const DELETE = withAuth(async (req, ctx) => {
 *   return NextResponse.json({ message: 'Admin action performed' });
 * }, { requireAdmin: true });
 * ```
 */
export function withAuth<T extends AuthedContext = AuthedContext>(
  handler: AuthedHandler<T>,
  options: WithAuthOptions = {}
) {
  return async (req: NextRequest): Promise<Response> => {
    try {
      // Connect to database
      await connectMongo();

      // Basic authentication check
      const authResult = await requireAuth(req);
      if (!authResult.success) {
        return addSecurityHeaders(NextResponse.json(
          { error: authResult.error },
          { status: authResult.status! }
        ));
      }

      const { userId, tenantId } = authResult.user!;
      let context: AuthedContext = {
        userId,
        tenantId,
        user: authResult.user!,
      };

      // Role-based checks if required
      if (options.requireRole || options.requireAdmin || options.requireOwner) {
        const membership = await Membership.findOne({ userId, tenantId });
        
        if (!membership) {
          return addSecurityHeaders(NextResponse.json(
            { error: 'No membership found for this tenant' },
            { status: 403 }
          ));
        }

        // Check specific role requirement
        if (options.requireRole) {
          const roleResult = await requireRole(req, options.requireRole);
          if (!roleResult.success) {
            return addSecurityHeaders(NextResponse.json(
              { error: roleResult.error },
              { status: roleResult.status! }
            ));
          }
        }

        // Check admin requirement
        if (options.requireAdmin) {
          const adminResult = await requireAdmin(req);
          if (!adminResult.success) {
            return addSecurityHeaders(NextResponse.json(
              { error: adminResult.error },
              { status: adminResult.status! }
            ));
          }
        }

        // Check owner requirement
        if (options.requireOwner) {
          const ownerResult = await requireOwner(req);
          if (!ownerResult.success) {
            return addSecurityHeaders(NextResponse.json(
              { error: ownerResult.error },
              { status: ownerResult.status! }
            ));
          }
        }

        // Extend context with role information
        const extendedContext = {
          ...context,
          role: membership.role,
          membership: {
            role: membership.role,
            joinedAt: membership.joinedAt,
          },
        };
        context = extendedContext as unknown as T;
      }

      // Module access check if required
      if (options.checkModuleAccess) {
        const hasModuleAccess = await checkModuleAccess(tenantId, options.checkModuleAccess);
        if (!hasModuleAccess) {
          return addSecurityHeaders(NextResponse.json(
            { 
              error: 'Module access denied',
              message: `Access to module '${options.checkModuleAccess}' is not available in your current subscription`
            },
            { status: 403 }
          ));
        }
      }

      // Call the original handler with authenticated context
      const response = await handler(req, context as T);
      return addSecurityHeaders(response);
    } catch (error) {
      console.error('Authentication error:', error);
      return addSecurityHeaders(NextResponse.json(
        { error: 'Authentication failed' },
        { status: 500 }
      ));
    }
  };
}

/**
 * Higher-order function that wraps API handlers with authentication and module access check
 * 
 * @param moduleName The module name to check access for (e.g., 'flows', 'financialModeling')
 * @param handler The API handler to wrap
 * @returns A Next.js API handler that enforces authentication and module access
 */
export function withAuthAndModule(moduleName: string, handler: AuthedHandler) {
  return withAuth(handler, { checkModuleAccess: moduleName });
}

/**
 * Higher-order function that wraps API handlers with admin authentication
 */
export function withAuthAdmin(handler: AuthedHandler<AuthedContextWithRole>) {
  return withAuth(handler, { requireAdmin: true });
}

/**
 * Higher-order function that wraps API handlers with owner authentication
 */
export function withAuthOwner(handler: AuthedHandler<AuthedContextWithRole>) {
  return withAuth(handler, { requireOwner: true });
}

/**
 * Check if a tenant has access to a specific module
 * Uses the new Tenant model to check module access
 */
async function checkModuleAccess(tenantId: string, moduleName: string): Promise<boolean> {
  try {
    const tenant = await Tenant.findById(tenantId);
    
    if (!tenant) {
      console.warn(`Tenant ${tenantId} not found`);
      return false;
    }

    // Check if tenant has the module enabled
    return tenant.hasModule(moduleName);
  } catch (error) {
    console.error(`Error checking module access for ${moduleName}:`, error);
    return false;
  }
}

/**
 * Utility function to get authenticated context from request
 * Useful for reusable logic that needs auth context
 */
export async function getAuthContext(req: NextRequest): Promise<AuthedContext | null> {
  try {
    await connectMongo();
    const authResult = await requireAuth(req);
    
    if (!authResult.success) {
      return null;
    }

    return {
      userId: authResult.user!.userId,
      tenantId: authResult.user!.tenantId,
      user: authResult.user!,
    };
  } catch (error) {
    console.error('Error getting auth context:', error);
    return null;
  }
}

/**
 * Utility function to get authenticated context with role information
 */
export async function getAuthContextWithRole(req: NextRequest): Promise<AuthedContextWithRole | null> {
  try {
    const context = await getAuthContext(req);
    if (!context) {
      return null;
    }

    const membership = await Membership.findOne({
      userId: context.userId,
      tenantId: context.tenantId,
    });

    if (!membership) {
      return null;
    }

    return {
      ...context,
      role: membership.role,
      membership: {
        role: membership.role,
        joinedAt: membership.joinedAt,
      },
    };
  } catch (error) {
    console.error('Error getting auth context with role:', error);
    return null;
  }
}