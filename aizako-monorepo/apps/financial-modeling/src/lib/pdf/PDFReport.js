import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer';
// Register fonts (optional - using default fonts for now)
// Font.register({
//   family: 'Inter',
//   src: 'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2'
// })
// PDF Styles
const styles = StyleSheet.create({
    page: {
        flexDirection: 'column',
        backgroundColor: '#ffffff',
        padding: 40,
        fontSize: 10,
        fontFamily: 'Helvetica',
    },
    header: {
        marginBottom: 30,
        borderBottom: 2,
        borderBottomColor: '#2563eb',
        paddingBottom: 10,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#1f2937',
        marginBottom: 5,
    },
    subtitle: {
        fontSize: 12,
        color: '#6b7280',
        marginBottom: 2,
    },
    section: {
        marginBottom: 25,
    },
    sectionTitle: {
        fontSize: 14,
        fontWeight: 'bold',
        color: '#1f2937',
        marginBottom: 10,
        borderBottom: 1,
        borderBottomColor: '#e5e7eb',
        paddingBottom: 3,
    },
    table: {
        width: 'auto',
        borderStyle: 'solid',
        borderWidth: 1,
        borderColor: '#e5e7eb',
        borderRightWidth: 0,
        borderBottomWidth: 0,
    },
    tableRow: {
        margin: 'auto',
        flexDirection: 'row',
    },
    tableColHeader: {
        width: '12.5%',
        borderStyle: 'solid',
        borderWidth: 1,
        borderColor: '#e5e7eb',
        borderLeftWidth: 0,
        borderTopWidth: 0,
        backgroundColor: '#f9fafb',
        padding: 5,
    },
    tableCol: {
        width: '12.5%',
        borderStyle: 'solid',
        borderWidth: 1,
        borderColor: '#e5e7eb',
        borderLeftWidth: 0,
        borderTopWidth: 0,
        padding: 5,
    },
    tableCellHeader: {
        fontSize: 8,
        fontWeight: 'bold',
        color: '#374151',
        textAlign: 'center',
    },
    tableCell: {
        fontSize: 8,
        color: '#1f2937',
        textAlign: 'right',
    },
    tableCellLeft: {
        fontSize: 8,
        color: '#1f2937',
        textAlign: 'left',
        fontWeight: 'bold',
    },
    keyMetrics: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 15,
    },
    metricBox: {
        width: '23%',
        padding: 10,
        backgroundColor: '#f3f4f6',
        borderRadius: 4,
        alignItems: 'center',
    },
    metricValue: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#2563eb',
        marginBottom: 2,
    },
    metricLabel: {
        fontSize: 8,
        color: '#6b7280',
        textAlign: 'center',
    },
    assumptions: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
    },
    assumptionItem: {
        width: '48%',
        marginBottom: 8,
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    assumptionLabel: {
        fontSize: 9,
        color: '#374151',
    },
    assumptionValue: {
        fontSize: 9,
        color: '#1f2937',
        fontWeight: 'bold',
    },
    footer: {
        position: 'absolute',
        bottom: 30,
        left: 40,
        right: 40,
        textAlign: 'center',
        color: '#9ca3af',
        fontSize: 8,
        borderTop: 1,
        borderTopColor: '#e5e7eb',
        paddingTop: 10,
    },
});
export function PDFReport({ scenarioName, config, projectionResult, generatedAt }) {
    const formatCurrency = (value) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(value);
    };
    const formatPercent = (value) => {
        return `${value.toFixed(1)}%`;
    };
    // Get first 12 months for detailed view
    const monthlyData = {
        pnl: projectionResult.pnl.slice(0, 12),
        bs: projectionResult.bs.slice(0, 12),
        cf: projectionResult.cf.slice(0, 12),
        metrics: projectionResult.metrics.slice(0, 12),
    };
    // Calculate yearly aggregates
    const yearlyData = {
        year1: {
            revenue: projectionResult.pnl.slice(0, 12).reduce((sum, row) => sum + row.revenue, 0),
            ebitda: projectionResult.pnl.slice(0, 12).reduce((sum, row) => sum + row.ebitda, 0),
            netIncome: projectionResult.pnl.slice(0, 12).reduce((sum, row) => sum + row.net_income, 0),
            cfo: projectionResult.cf.slice(0, 12).reduce((sum, row) => sum + row.cfo, 0),
            endingCash: projectionResult.bs[11]?.cash || 0,
        },
        year2: projectionResult.pnl.length > 12 ? {
            revenue: projectionResult.pnl.slice(12, 24).reduce((sum, row) => sum + row.revenue, 0),
            ebitda: projectionResult.pnl.slice(12, 24).reduce((sum, row) => sum + row.ebitda, 0),
            netIncome: projectionResult.pnl.slice(12, 24).reduce((sum, row) => sum + row.net_income, 0),
            cfo: projectionResult.cf.slice(12, 24).reduce((sum, row) => sum + row.cfo, 0),
            endingCash: projectionResult.bs[23]?.cash || 0,
        } : null,
    };
    return (_jsxs(Document, { children: [_jsxs(Page, { size: "A4", style: styles.page, children: [_jsxs(View, { style: styles.header, children: [_jsx(Text, { style: styles.title, children: scenarioName }), _jsx(Text, { style: styles.subtitle, children: "Financial Projections Report" }), _jsxs(Text, { style: styles.subtitle, children: ["Generated on ", generatedAt.toLocaleDateString()] })] }), _jsxs(View, { style: styles.section, children: [_jsx(Text, { style: styles.sectionTitle, children: "Key Performance Metrics (Year 1)" }), _jsxs(View, { style: styles.keyMetrics, children: [_jsxs(View, { style: styles.metricBox, children: [_jsx(Text, { style: styles.metricValue, children: formatCurrency(yearlyData.year1.revenue) }), _jsx(Text, { style: styles.metricLabel, children: "Total Revenue" })] }), _jsxs(View, { style: styles.metricBox, children: [_jsx(Text, { style: styles.metricValue, children: formatCurrency(yearlyData.year1.ebitda) }), _jsx(Text, { style: styles.metricLabel, children: "EBITDA" })] }), _jsxs(View, { style: styles.metricBox, children: [_jsx(Text, { style: styles.metricValue, children: formatCurrency(yearlyData.year1.cfo) }), _jsx(Text, { style: styles.metricLabel, children: "Operating Cash Flow" })] }), _jsxs(View, { style: styles.metricBox, children: [_jsx(Text, { style: styles.metricValue, children: formatCurrency(yearlyData.year1.endingCash) }), _jsx(Text, { style: styles.metricLabel, children: "Ending Cash" })] })] })] }), _jsxs(View, { style: styles.section, children: [_jsx(Text, { style: styles.sectionTitle, children: "Key Assumptions" }), _jsxs(View, { style: styles.assumptions, children: [_jsxs(View, { style: styles.assumptionItem, children: [_jsx(Text, { style: styles.assumptionLabel, children: "Starting Revenue (Monthly)" }), _jsx(Text, { style: styles.assumptionValue, children: formatCurrency(config.drivers.revenue.start_run_rate) })] }), _jsxs(View, { style: styles.assumptionItem, children: [_jsx(Text, { style: styles.assumptionLabel, children: "Monthly Growth Rate" }), _jsx(Text, { style: styles.assumptionValue, children: formatPercent(config.drivers.revenue.mth_growth_pct) })] }), _jsxs(View, { style: styles.assumptionItem, children: [_jsx(Text, { style: styles.assumptionLabel, children: "Gross Margin" }), _jsx(Text, { style: styles.assumptionValue, children: formatPercent(config.drivers.gross_margin_pct) })] }), _jsxs(View, { style: styles.assumptionItem, children: [_jsx(Text, { style: styles.assumptionLabel, children: "Fixed OpEx (Monthly)" }), _jsx(Text, { style: styles.assumptionValue, children: formatCurrency(config.drivers.opex.fixed) })] }), _jsxs(View, { style: styles.assumptionItem, children: [_jsx(Text, { style: styles.assumptionLabel, children: "Variable OpEx (% of Revenue)" }), _jsx(Text, { style: styles.assumptionValue, children: formatPercent(config.drivers.opex.variable_pct_of_rev) })] }), _jsxs(View, { style: styles.assumptionItem, children: [_jsx(Text, { style: styles.assumptionLabel, children: "Days Sales Outstanding" }), _jsxs(Text, { style: styles.assumptionValue, children: [config.drivers.wc.dso, " days"] })] }), _jsxs(View, { style: styles.assumptionItem, children: [_jsx(Text, { style: styles.assumptionLabel, children: "Days Payable Outstanding" }), _jsxs(Text, { style: styles.assumptionValue, children: [config.drivers.wc.dpo, " days"] })] }), _jsxs(View, { style: styles.assumptionItem, children: [_jsx(Text, { style: styles.assumptionLabel, children: "Tax Rate" }), _jsx(Text, { style: styles.assumptionValue, children: formatPercent(config.drivers.tax.rate_pct) })] })] })] }), _jsxs(View, { style: styles.section, children: [_jsx(Text, { style: styles.sectionTitle, children: "Model Validation" }), _jsxs(View, { style: styles.assumptionItem, children: [_jsx(Text, { style: styles.assumptionLabel, children: "Identity Check Status" }), _jsx(Text, { style: [styles.assumptionValue, {
                                                color: projectionResult.checks.passes_validation ? '#059669' : '#dc2626'
                                            }], children: projectionResult.checks.passes_validation ? 'PASSED' : 'FAILED' })] }), _jsxs(View, { style: styles.assumptionItem, children: [_jsx(Text, { style: styles.assumptionLabel, children: "Maximum Residual" }), _jsx(Text, { style: styles.assumptionValue, children: formatCurrency(projectionResult.checks.max_residual) })] }), _jsxs(View, { style: styles.assumptionItem, children: [_jsx(Text, { style: styles.assumptionLabel, children: "Projection Periods" }), _jsxs(Text, { style: styles.assumptionValue, children: [config.meta.periods, " months"] })] })] }), _jsx(Text, { style: styles.footer, children: "Generated by Aizako Flows - Deterministic Financial Modeling Platform" })] }), _jsxs(Page, { size: "A4", style: styles.page, orientation: "landscape", children: [_jsxs(View, { style: styles.header, children: [_jsx(Text, { style: styles.title, children: "Profit & Loss Statement" }), _jsx(Text, { style: styles.subtitle, children: "Monthly View - First 12 Months" })] }), _jsxs(View, { style: styles.table, children: [_jsxs(View, { style: styles.tableRow, children: [_jsx(View, { style: styles.tableColHeader, children: _jsx(Text, { style: styles.tableCellHeader, children: "P&L Item" }) }), monthlyData.pnl.map((_, index) => (_jsx(View, { style: styles.tableColHeader, children: _jsxs(Text, { style: styles.tableCellHeader, children: ["Mo ", index + 1] }) }, index)))] }), _jsxs(View, { style: styles.tableRow, children: [_jsx(View, { style: styles.tableCol, children: _jsx(Text, { style: styles.tableCellLeft, children: "Revenue" }) }), monthlyData.pnl.map((row, index) => (_jsx(View, { style: styles.tableCol, children: _jsx(Text, { style: styles.tableCell, children: formatCurrency(row.revenue) }) }, index)))] }), _jsxs(View, { style: styles.tableRow, children: [_jsx(View, { style: styles.tableCol, children: _jsx(Text, { style: styles.tableCellLeft, children: "COGS" }) }), monthlyData.pnl.map((row, index) => (_jsx(View, { style: styles.tableCol, children: _jsxs(Text, { style: styles.tableCell, children: ["(", formatCurrency(row.cogs), ")"] }) }, index)))] }), _jsxs(View, { style: styles.tableRow, children: [_jsx(View, { style: styles.tableCol, children: _jsx(Text, { style: styles.tableCellLeft, children: "Gross Profit" }) }), monthlyData.pnl.map((row, index) => (_jsx(View, { style: styles.tableCol, children: _jsx(Text, { style: styles.tableCell, children: formatCurrency(row.gross_profit) }) }, index)))] }), _jsxs(View, { style: styles.tableRow, children: [_jsx(View, { style: styles.tableCol, children: _jsx(Text, { style: styles.tableCellLeft, children: "EBITDA" }) }), monthlyData.pnl.map((row, index) => (_jsx(View, { style: styles.tableCol, children: _jsx(Text, { style: [styles.tableCell, {
                                                    color: row.ebitda >= 0 ? '#059669' : '#dc2626'
                                                }], children: formatCurrency(row.ebitda) }) }, index)))] }), _jsxs(View, { style: styles.tableRow, children: [_jsx(View, { style: styles.tableCol, children: _jsx(Text, { style: styles.tableCellLeft, children: "Net Income" }) }), monthlyData.pnl.map((row, index) => (_jsx(View, { style: styles.tableCol, children: _jsx(Text, { style: [styles.tableCell, {
                                                    color: row.net_income >= 0 ? '#059669' : '#dc2626'
                                                }], children: formatCurrency(row.net_income) }) }, index)))] })] }), _jsx(Text, { style: styles.footer, children: "Generated by Aizako Flows - Page 2" })] })] }));
}
