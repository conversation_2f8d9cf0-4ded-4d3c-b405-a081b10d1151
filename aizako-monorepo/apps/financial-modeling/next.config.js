/** @type {import('next').NextConfig} */
const nextConfig = {
  // Keep existing transpiling for core-lib
  transpilePackages: ['@aizako/core-lib'],
  outputFileTracingRoot: '/Users/<USER>/Documents/Coding/aizako-crm/aizako-monorepo',
  
  // Prevent server dependencies from being bundled in client code
  serverExternalPackages: [
    'argon2',
    'mongoose', 
    'mongodb',
    'nodemailer'
  ],
  
  // Custom webpack config to handle externals
  webpack: (config, { isServer }) => {
    if (isServer) {
      // For server builds, externalize native dependencies
      config.externals = config.externals || [];
      config.externals.push({
        'argon2': 'commonjs argon2',
        'mongoose': 'commonjs mongoose',
        'mongodb': 'commonjs mongodb',
        'nodemailer': 'commonjs nodemailer',
      });
    } else {
      // Exclude native modules from client-side bundle
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        stream: false,
        url: false,
        zlib: false,
        http: false,
        https: false,
        assert: false,
        os: false,
        path: false,
      };
    }

    return config;
  },
  
  // Ensure native binaries and dependencies are available for server functions
  outputFileTracingIncludes: {
    '/api/**/*': [
      './node_modules/**/*',
      './node_modules/argon2/prebuilds/**/*'
    ],
  },
};

module.exports = nextConfig;
