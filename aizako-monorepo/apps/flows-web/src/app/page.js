import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { But<PERSON> } from "@/components/ui/button";
export default function Home() {
    return (_jsx("main", { className: "container mx-auto p-8", children: _jsxs("div", { className: "text-center space-y-4", children: [_jsx("h1", { className: "text-4xl font-bold", children: "Welcome to Aizako Flows Web" }), _jsx("p", { className: "text-lg text-muted-foreground", children: "Next.js application with MongoDB integration" }), _jsxs("div", { className: "space-x-4", children: [_jsx(<PERSON><PERSON>, { children: "Get Started" }), _jsx(<PERSON>ton, { variant: "outline", children: "Learn More" })] })] }) }));
}
