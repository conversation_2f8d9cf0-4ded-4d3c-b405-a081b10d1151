import { NextResponse } from 'next/server';
import { getMongo } from '@aizako/core-lib/server';
export async function GET() {
    try {
        await getMongo();
        return NextResponse.json({ mongo: 'ok' });
    }
    catch (error) {
        console.error('Health check error:', error);
        if (error instanceof Error && error.message === 'MONGODB_URI missing') {
            return NextResponse.json({ error: 'MONGODB_URI missing' }, { status: 500 });
        }
        return NextResponse.json({ error: 'MongoDB connection failed', details: error instanceof Error ? error.message : 'Unknown error' }, { status: 500 });
    }
}
