import { NextResponse } from 'next/server';
import { getSession } from '@aizako/core-lib/server-only';
/**
 * Higher-order function that wraps API handlers with authentication
 *
 * @param handler The API handler to wrap
 * @returns A Next.js API handler that enforces authentication
 *
 * @example
 * ```typescript
 * export const GET = withAuth(async (req, ctx) => {
 *   // ctx.userId and ctx.tenantId are guaranteed to be present
 *   return NextResponse.json({ userId: ctx.userId, tenantId: ctx.tenantId });
 * });
 * ```
 */
export function withAuth(handler) {
    return async (req) => {
        try {
            // Get session data from the request
            const session = await getSession(req);
            // Return 401 if no valid session
            if (!session) {
                return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
            }
            // Call the original handler with authenticated context
            return await handler(req, {
                userId: session.userId,
                tenantId: session.tenantId
            });
        }
        catch (error) {
            console.error('Authentication error:', error);
            return NextResponse.json({ error: 'Authentication failed' }, { status: 500 });
        }
    };
}
/**
 * Higher-order function that wraps API handlers with authentication and module access check
 *
 * @param moduleName The module name to check access for (e.g., 'flows', 'financial-modeling')
 * @param handler The API handler to wrap
 * @returns A Next.js API handler that enforces authentication and module access
 *
 * @example
 * ```typescript
 * export const GET = withAuthAndModule('flows', async (req, ctx) => {
 *   // User is authenticated and has access to the flows module
 *   return NextResponse.json({ message: 'Access granted to flows module' });
 * });
 * ```
 */
export function withAuthAndModule(moduleName, handler) {
    return async (req) => {
        try {
            // First check authentication
            const session = await getSession(req);
            if (!session) {
                return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
            }
            // Check module access
            const hasModuleAccess = await checkModuleAccess(session.tenantId, moduleName);
            if (!hasModuleAccess) {
                return NextResponse.json({
                    error: 'Module access denied',
                    message: `Access to module '${moduleName}' is not available in your current subscription`
                }, { status: 403 });
            }
            // Call the original handler with authenticated context
            return await handler(req, {
                userId: session.userId,
                tenantId: session.tenantId
            });
        }
        catch (error) {
            console.error('Authentication/authorization error:', error);
            return NextResponse.json({ error: 'Authentication failed' }, { status: 500 });
        }
    };
}
/**
 * Check if a tenant has access to a specific module
 * This queries the subscription system to verify module access
 */
async function checkModuleAccess(tenantId, moduleName) {
    try {
        // Import dynamically to avoid module resolution issues
        const { getMongo } = await import('@aizako/core-lib/server');
        const db = await getMongo();
        // Get tenant subscription
        const subscription = await db.collection('tenantsubscriptions').findOne({ tenantId });
        if (!subscription) {
            console.warn(`No subscription found for tenant ${tenantId}`);
            return false;
        }
        // Check if subscription is active
        if (subscription.status !== 'active' && subscription.status !== 'trialing') {
            console.warn(`Subscription for tenant ${tenantId} is not active: ${subscription.status}`);
            return false;
        }
        // Check if subscription is expired
        if (new Date(subscription.endDate) < new Date()) {
            console.warn(`Subscription for tenant ${tenantId} is expired`);
            return false;
        }
        // Check custom features first
        if (subscription.customFeatures && subscription.customFeatures[moduleName]) {
            return true;
        }
        // Get subscription plan
        const plan = await db.collection('subscriptionplans').findOne({ _id: subscription.planId });
        if (!plan) {
            console.warn(`Subscription plan not found for tenant ${tenantId}`);
            return false;
        }
        // Check if module is enabled in plan features
        return plan.features && plan.features[moduleName] === true;
    }
    catch (error) {
        console.error(`Error checking module access for ${moduleName}:`, error);
        return false;
    }
}
/**
 * Utility function to get authenticated context from request
 * Useful for reusable logic that needs auth context
 */
export async function getAuthContext(req) {
    try {
        const session = await getSession(req);
        return session;
    }
    catch (error) {
        console.error('Error getting auth context:', error);
        return null;
    }
}
