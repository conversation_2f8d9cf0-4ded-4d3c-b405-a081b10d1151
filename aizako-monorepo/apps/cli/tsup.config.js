import { defineConfig } from 'tsup';
export default defineConfig({
    entry: ['src/index.ts'],
    format: ['esm'],
    dts: false, // Disable DTS generation for CLI tool
    splitting: false,
    sourcemap: true,
    clean: true,
    banner: ({ format }) => {
        if (format === 'esm') {
            return {
                js: '#!/usr/bin/env node',
            };
        }
        return {};
    },
});
