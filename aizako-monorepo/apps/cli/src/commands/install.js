import chalk from 'chalk';
import ora from 'ora';
import { Listr } from 'listr2';
import { validateModules } from '../utils/validation';
/**
 * Install command handler
 * @param modules Modules to install
 * @param options Command options
 */
export async function installCommand(modules, options) {
    // Validate modules
    const validatedModules = validateModules(modules);
    if (validatedModules.length === 0) {
        console.log(chalk.yellow('No valid modules specified. Available modules: crm, scribe, invoicing, onboarding'));
        return;
    }
    console.log(chalk.blue(`Installing modules: ${validatedModules.join(', ')}`));
    // Create tasks
    const tasks = new Listr(validatedModules.map(module => ({
        title: `Installing ${module} module`,
        task: async (ctx, task) => {
            const spinner = ora(`Installing ${module}...`).start();
            // Simulate installation
            await new Promise(resolve => setTimeout(resolve, 2000));
            spinner.succeed(`${module} module installed successfully`);
            return;
        },
    })), { concurrent: false });
    try {
        await tasks.run();
        console.log(chalk.green('All modules installed successfully!'));
    }
    catch (error) {
        console.error(chalk.red('Error installing modules:'), error.message);
        process.exit(1);
    }
}
