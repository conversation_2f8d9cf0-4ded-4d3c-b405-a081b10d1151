import chalk from 'chalk';
/**
 * Info command handler
 */
export async function infoCommand() {
    console.log(chalk.blue('Aizako Modules Information'));
    console.log('');
    // Simulate getting module information
    const modules = [
        {
            name: 'crm',
            version: '0.0.1',
            installed: true,
            status: 'active',
        },
        {
            name: 'scribe',
            version: '0.0.1',
            installed: false,
            status: 'not installed',
        },
        {
            name: 'invoicing',
            version: '0.0.1',
            installed: false,
            status: 'not installed',
        },
        {
            name: 'onboarding',
            version: '0.0.1',
            installed: false,
            status: 'not installed',
        },
    ];
    // Display module information
    modules.forEach(module => {
        const statusColor = module.installed ? chalk.green : chalk.red;
        console.log(`${chalk.bold(module.name)} (${module.version}): ${statusColor(module.status)}`);
    });
}
