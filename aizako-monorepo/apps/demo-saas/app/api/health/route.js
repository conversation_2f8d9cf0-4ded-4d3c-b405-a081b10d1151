import { NextResponse } from 'next/server';
import { pingMongo } from '@aizako/core-lib';
export async function GET(request) {
    try {
        if (!process.env.MONGODB_URI) {
            return NextResponse.json({ error: 'MONGODB_URI missing' }, { status: 500 });
        }
        const isHealthy = await pingMongo();
        if (isHealthy) {
            return NextResponse.json({ mongo: 'ok' }, { status: 200 });
        }
        else {
            return NextResponse.json({ error: 'MongoDB connection failed' }, { status: 500 });
        }
    }
    catch (error) {
        console.error('Health check error:', error);
        if (error instanceof Error && error.message === 'MONGODB_URI missing') {
            return NextResponse.json({ error: 'MONGODB_URI missing' }, { status: 500 });
        }
        return NextResponse.json({ error: 'Health check failed', details: error instanceof Error ? error.message : 'Unknown error' }, { status: 500 });
    }
}
