import { jsx as _jsx } from "react/jsx-runtime";
import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { AuthProvider, TenantProvider } from '@aizako/core-lib';
import App from './App';
import './index.css';
// Firebase configuration
const firebaseConfig = {
    apiKey: import.meta.env.VITE_FIREBASE_API_KEY || '',
    projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || '',
    appId: import.meta.env.VITE_FIREBASE_APP_ID || '',
};
// Initialize Firebase
import { initializeFirebaseAuth } from '@aizako/core-lib';
initializeFirebaseAuth(firebaseConfig);
// Sync user with backend
const syncUserWithBackend = async (user, token) => {
    if (!user || !token)
        return;
    try {
        // This is a placeholder for the actual API call
        console.log('Syncing user with backend:', user.id, token?.substring(0, 10) + '...');
    }
    catch (error) {
        console.error('Error syncing user with backend:', error);
    }
};
ReactDOM.createRoot(document.getElementById('root')).render(_jsx(React.StrictMode, { children: _jsx(BrowserRouter, { children: _jsx(AuthProvider, { onAuthStateChanged: console.log, syncUserWithBackend: syncUserWithBackend, children: _jsx(TenantProvider, { children: _jsx(App, {}) }) }) }) }));
