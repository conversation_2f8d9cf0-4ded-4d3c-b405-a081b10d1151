import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { useAuth, useTenant } from '@aizako/core-lib';
function DashboardPage() {
    const { user, logout } = useAuth();
    const { tenant, isLoading: tenantLoading } = useTenant();
    const [isLoggingOut, setIsLoggingOut] = useState(false);
    const handleLogout = async () => {
        try {
            setIsLoggingOut(true);
            await logout();
        }
        catch (error) {
            console.error('Logout error:', error);
        }
        finally {
            setIsLoggingOut(false);
        }
    };
    return (_jsxs("div", { className: "min-h-screen bg-gray-100", children: [_jsx("nav", { className: "bg-white shadow-sm", children: _jsx("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: _jsxs("div", { className: "flex justify-between h-16", children: [_jsxs("div", { className: "flex", children: [_jsx("div", { className: "flex-shrink-0 flex items-center", children: _jsx("span", { className: "text-xl font-bold text-blue-600", children: "Aizako Demo" }) }), _jsxs("div", { className: "hidden sm:ml-6 sm:flex sm:space-x-8", children: [_jsx("a", { href: "#", className: "border-blue-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium", children: "Dashboard" }), _jsx("a", { href: "#", className: "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium", children: "Contacts" }), _jsx("a", { href: "#", className: "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium", children: "Opportunities" }), _jsx("a", { href: "#", className: "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium", children: "Reports" })] })] }), _jsx("div", { className: "hidden sm:ml-6 sm:flex sm:items-center", children: _jsx("div", { className: "ml-3 relative", children: _jsxs("div", { className: "flex items-center", children: [_jsx("span", { className: "text-sm text-gray-500 mr-2", children: user?.displayName }), user?.photoURL ? (_jsx("img", { className: "h-8 w-8 rounded-full", src: user.photoURL, alt: user.displayName || 'User' })) : (_jsx("div", { className: "h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center text-white", children: user?.displayName?.charAt(0) || user?.email?.charAt(0) || 'U' })), _jsx("button", { onClick: handleLogout, disabled: isLoggingOut, className: "ml-4 px-3 py-1 text-sm text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50", children: isLoggingOut ? 'Logging out...' : 'Logout' })] }) }) })] }) }) }), _jsxs("div", { className: "py-10", children: [_jsx("header", { children: _jsx("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: _jsx("h1", { className: "text-3xl font-bold leading-tight text-gray-900", children: "Dashboard" }) }) }), _jsx("main", { children: _jsx("div", { className: "max-w-7xl mx-auto sm:px-6 lg:px-8", children: _jsx("div", { className: "px-4 py-8 sm:px-0", children: _jsxs("div", { className: "border-4 border-dashed border-gray-200 rounded-lg p-6", children: [_jsxs("div", { className: "mb-6", children: [_jsx("h2", { className: "text-xl font-semibold text-gray-800 mb-2", children: "User Information" }), _jsxs("div", { className: "bg-white shadow overflow-hidden sm:rounded-lg", children: [_jsx("div", { className: "px-4 py-5 sm:px-6", children: _jsx("h3", { className: "text-lg leading-6 font-medium text-gray-900", children: "Profile Details" }) }), _jsx("div", { className: "border-t border-gray-200", children: _jsxs("dl", { children: [_jsxs("div", { className: "bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6", children: [_jsx("dt", { className: "text-sm font-medium text-gray-500", children: "Full name" }), _jsx("dd", { className: "mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2", children: user?.displayName || 'Not provided' })] }), _jsxs("div", { className: "bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6", children: [_jsx("dt", { className: "text-sm font-medium text-gray-500", children: "Email address" }), _jsx("dd", { className: "mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2", children: user?.email })] }), _jsxs("div", { className: "bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6", children: [_jsx("dt", { className: "text-sm font-medium text-gray-500", children: "User ID" }), _jsx("dd", { className: "mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2", children: user?.id })] })] }) })] })] }), _jsxs("div", { children: [_jsx("h2", { className: "text-xl font-semibold text-gray-800 mb-2", children: "Tenant Information" }), tenantLoading ? (_jsx("div", { className: "text-center py-4", children: "Loading tenant information..." })) : tenant ? (_jsxs("div", { className: "bg-white shadow overflow-hidden sm:rounded-lg", children: [_jsx("div", { className: "px-4 py-5 sm:px-6", children: _jsx("h3", { className: "text-lg leading-6 font-medium text-gray-900", children: "Tenant Details" }) }), _jsx("div", { className: "border-t border-gray-200", children: _jsxs("dl", { children: [_jsxs("div", { className: "bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6", children: [_jsx("dt", { className: "text-sm font-medium text-gray-500", children: "Name" }), _jsx("dd", { className: "mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2", children: tenant.name })] }), _jsxs("div", { className: "bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6", children: [_jsx("dt", { className: "text-sm font-medium text-gray-500", children: "Plan" }), _jsx("dd", { className: "mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2", children: tenant.plan })] }), _jsxs("div", { className: "bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6", children: [_jsx("dt", { className: "text-sm font-medium text-gray-500", children: "Features" }), _jsx("dd", { className: "mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2", children: tenant.features.join(', ') })] })] }) })] })) : (_jsx("div", { className: "bg-yellow-50 border-l-4 border-yellow-400 p-4", children: _jsxs("div", { className: "flex", children: [_jsx("div", { className: "flex-shrink-0", children: _jsx("svg", { className: "h-5 w-5 text-yellow-400", xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 20 20", fill: "currentColor", "aria-hidden": "true", children: _jsx("path", { fillRule: "evenodd", d: "M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z", clipRule: "evenodd" }) }) }), _jsx("div", { className: "ml-3", children: _jsx("p", { className: "text-sm text-yellow-700", children: "No tenant information available. Please contact support." }) })] }) }))] })] }) }) }) })] })] }));
}
export default DashboardPage;
