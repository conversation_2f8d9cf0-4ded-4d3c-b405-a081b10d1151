import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { <PERSON> } from 'react-router-dom';
function AboutPage() {
    return (_jsxs("div", { className: "min-h-screen bg-white", children: [_jsx("header", { className: "bg-white shadow-sm", children: _jsx("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: _jsxs("div", { className: "flex justify-between h-16 items-center", children: [_jsx("div", { className: "flex-shrink-0", children: _jsx(Link, { to: "/", className: "text-2xl font-bold text-blue-600", children: "Aizako" }) }), _jsxs("div", { className: "flex space-x-4", children: [_jsx(<PERSON>, { to: "/", className: "text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium", children: "Home" }), _jsx(Link, { to: "/login", className: "text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium", children: "Login" }), _jsx(Link, { to: "/register", className: "bg-blue-600 text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium", children: "Register" })] })] }) }) }), _jsx("main", { children: _jsxs("div", { className: "max-w-7xl mx-auto py-16 px-4 sm:py-24 sm:px-6 lg:px-8", children: [_jsxs("div", { className: "text-center", children: [_jsx("h1", { className: "text-4xl font-extrabold text-gray-900 sm:text-5xl sm:tracking-tight lg:text-6xl", children: "About Aizako" }), _jsx("p", { className: "mt-6 max-w-3xl mx-auto text-xl text-gray-500", children: "Aizako is a modular SaaS platform that provides CRM, Scribe, Invoicing, and Onboarding modules. This demo application showcases the integration of these modules in a unified interface." })] }), _jsx("div", { className: "mt-20", children: _jsxs("div", { className: "grid grid-cols-1 gap-8 md:grid-cols-2", children: [_jsxs("div", { children: [_jsx("h2", { className: "text-3xl font-extrabold text-gray-900", children: "Our Mission" }), _jsx("p", { className: "mt-4 text-lg text-gray-500", children: "Our mission is to provide businesses with a modular, AI-first platform that adapts to their specific needs. We believe that software should be flexible, intelligent, and easy to use." }), _jsx("p", { className: "mt-4 text-lg text-gray-500", children: "By offering a modular approach, businesses can start with the modules they need and add more as they grow, without the complexity and cost of integrating multiple disparate systems." })] }), _jsxs("div", { children: [_jsx("h2", { className: "text-3xl font-extrabold text-gray-900", children: "Our Technology" }), _jsx("p", { className: "mt-4 text-lg text-gray-500", children: "Aizako is built on modern technologies including React, TypeScript, MongoDB, and Firebase. We leverage AI throughout our platform to provide intelligent insights and automate routine tasks." }), _jsx("p", { className: "mt-4 text-lg text-gray-500", children: "Our modular architecture allows for seamless integration between modules, providing a unified experience while maintaining the flexibility to use each module independently." })] })] }) }), _jsxs("div", { className: "mt-20", children: [_jsx("h2", { className: "text-3xl font-extrabold text-gray-900 text-center", children: "Our Modules" }), _jsxs("div", { className: "mt-12 grid gap-8 grid-cols-1 md:grid-cols-2 lg:grid-cols-4", children: [_jsx("div", { className: "bg-white overflow-hidden shadow rounded-lg border border-gray-200", children: _jsxs("div", { className: "px-4 py-5 sm:p-6", children: [_jsx("h3", { className: "text-lg font-medium text-gray-900", children: "CRM" }), _jsx("p", { className: "mt-2 text-sm text-gray-500", children: "Our AI-first CRM helps you manage contacts, track opportunities, and build stronger customer relationships with intelligent insights and automation." })] }) }), _jsx("div", { className: "bg-white overflow-hidden shadow rounded-lg border border-gray-200", children: _jsxs("div", { className: "px-4 py-5 sm:p-6", children: [_jsx("h3", { className: "text-lg font-medium text-gray-900", children: "Scribe" }), _jsx("p", { className: "mt-2 text-sm text-gray-500", children: "Create, publish, and analyze content with our Scribe module. AI-powered tools help you generate engaging content and understand its impact." })] }) }), _jsx("div", { className: "bg-white overflow-hidden shadow rounded-lg border border-gray-200", children: _jsxs("div", { className: "px-4 py-5 sm:p-6", children: [_jsx("h3", { className: "text-lg font-medium text-gray-900", children: "Invoicing" }), _jsx("p", { className: "mt-2 text-sm text-gray-500", children: "Streamline your billing process with our Invoicing module. Create, send, and track invoices with ease, and get paid faster." })] }) }), _jsx("div", { className: "bg-white overflow-hidden shadow rounded-lg border border-gray-200", children: _jsxs("div", { className: "px-4 py-5 sm:p-6", children: [_jsx("h3", { className: "text-lg font-medium text-gray-900", children: "Onboarding" }), _jsx("p", { className: "mt-2 text-sm text-gray-500", children: "Create smooth onboarding experiences for new customers with guided workflows, automated tasks, and progress tracking." })] }) })] })] }), _jsx("div", { className: "mt-20 text-center", children: _jsx(Link, { to: "/register", className: "inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700", children: "Get started" }) })] }) }), _jsx("footer", { className: "bg-white", children: _jsx("div", { className: "max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8", children: _jsxs("div", { className: "mt-8 border-t border-gray-200 pt-8 md:flex md:items-center md:justify-between", children: [_jsxs("div", { className: "flex space-x-6 md:order-2", children: [_jsxs("a", { href: "#", className: "text-gray-400 hover:text-gray-500", children: [_jsx("span", { className: "sr-only", children: "GitHub" }), _jsx("svg", { className: "h-6 w-6", fill: "currentColor", viewBox: "0 0 24 24", "aria-hidden": "true", children: _jsx("path", { fillRule: "evenodd", d: "M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z", clipRule: "evenodd" }) })] }), _jsxs("a", { href: "#", className: "text-gray-400 hover:text-gray-500", children: [_jsx("span", { className: "sr-only", children: "Twitter" }), _jsx("svg", { className: "h-6 w-6", fill: "currentColor", viewBox: "0 0 24 24", "aria-hidden": "true", children: _jsx("path", { d: "M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" }) })] })] }), _jsx("p", { className: "mt-8 text-base text-gray-400 md:mt-0 md:order-1", children: "\u00A9 2024 Aizako. All rights reserved." })] }) }) })] }));
}
export default AboutPage;
