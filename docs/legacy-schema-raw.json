Found 55 collections
  sequencesteps: 0 documents
  attributionresults: 0 documents
  edges: 0 documents
  analyticsdatasets: 0 documents
  experiments: 0 documents
  featureflags: 0 documents
  experimentresults: 0 documents
  emailconfigs: 0 documents
  eventsraws: 0 documents
  insightscaches: 0 documents
  tenantdomains: 0 documents
  winlossanalyses: 0 documents
  usertenants: 0 documents
  activities: 0 documents
  proposalanalytics: 0 documents
  aichats: 0 documents
  insights: 0 documents
  tags: 0 documents
  objectionresponses: 0 documents
  notifications: 0 documents
  workflows: 0 documents
  proposaltemplates: 0 documents
  opportunities: 0 documents
  teamforecasts: 0 documents
  emailtemplates: 0 documents
  cbicaches: 0 documents
  test_collection: 1 documents
  objections: 0 documents
  winlossfactors: 0 documents
  tenantsubscriptions: 0 documents
  users: 0 documents
  emailtrackings: 0 documents
  workflowruns: 0 documents
  analyticsevents: 0 documents
  stagetransitions: 0 documents
  subscriptionevents: 0 documents
  marketingcampaigns: 0 documents
  backgroundtasks: 0 documents
  followups: 0 documents
  tenantusages: 0 documents
  tenants: 0 documents
  documents: 0 documents
  followuptemplates: 0 documents
  tasks: 0 documents
  proposals: 0 documents
  interactions: 0 documents
  workflowversions: 0 documents
  dealforecasts: 0 documents
  features: 0 documents
  contacts: 0 documents
  companies: 0 documents
  subscriptionplans: 0 documents
  relationships: 0 documents
  sequences: 0 documents
  sequenceenrollments: 0 documents
📖 Writing analysis to file...
{
  "database": "aizako-crm",
  "totalCollections": 55,
  "timestamp": "2025-09-02T12:07:00.131Z",
  "collections": [
    {
      "name": "sequencesteps",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "sequenceId_1_stepNumber_1",
          "key": {
            "sequenceId": 1,
            "stepNumber": 1
          },
          "unique": true
        },
        {
          "name": "sequenceId_1",
          "key": {
            "sequenceId": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "attributionresults",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "creative_1",
          "key": {
            "creative": 1
          },
          "unique": false
        },
        {
          "name": "model_type_1",
          "key": {
            "model_type": 1
          },
          "unique": false
        },
        {
          "name": "channel_1",
          "key": {
            "channel": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1_model_type_1_roi_-1",
          "key": {
            "tenant_id": 1,
            "model_type": 1,
            "roi": -1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1",
          "key": {
            "tenant_id": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1_window_1_model_type_1",
          "key": {
            "tenant_id": 1,
            "window": 1,
            "model_type": 1
          },
          "unique": false
        },
        {
          "name": "window_1",
          "key": {
            "window": 1
          },
          "unique": false
        },
        {
          "name": "medium_1",
          "key": {
            "medium": 1
          },
          "unique": false
        },
        {
          "name": "campaign_1",
          "key": {
            "campaign": 1
          },
          "unique": false
        },
        {
          "name": "source_1",
          "key": {
            "source": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1_channel_1_window_1",
          "key": {
            "tenant_id": 1,
            "channel": 1,
            "window": 1
          },
          "unique": false
        },
        {
          "name": "keyword_1",
          "key": {
            "keyword": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "edges",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "toType_1",
          "key": {
            "toType": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1_fromId_1_toId_1_relationship_1",
          "key": {
            "tenantId": 1,
            "fromId": 1,
            "toId": 1,
            "relationship": 1
          },
          "unique": true
        },
        {
          "name": "toId_1",
          "key": {
            "toId": 1
          },
          "unique": false
        },
        {
          "name": "fromType_1",
          "key": {
            "fromType": 1
          },
          "unique": false
        },
        {
          "name": "fromId_1",
          "key": {
            "fromId": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1_toType_1_toId_1",
          "key": {
            "tenantId": 1,
            "toType": 1,
            "toId": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1",
          "key": {
            "tenantId": 1
          },
          "unique": false
        },
        {
          "name": "relationship_1",
          "key": {
            "relationship": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1_fromType_1_fromId_1",
          "key": {
            "tenantId": 1,
            "fromType": 1,
            "fromId": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "analyticsdatasets",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1_is_active_1",
          "key": {
            "tenant_id": 1,
            "is_active": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1",
          "key": {
            "tenant_id": 1
          },
          "unique": false
        },
        {
          "name": "name_1",
          "key": {
            "name": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1_name_1",
          "key": {
            "tenant_id": 1,
            "name": 1
          },
          "unique": true
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "experiments",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "startDate_1_endDate_1",
          "key": {
            "startDate": 1,
            "endDate": 1
          },
          "unique": false
        },
        {
          "name": "key_1",
          "key": {
            "key": 1
          },
          "unique": true
        },
        {
          "name": "status_1",
          "key": {
            "status": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "featureflags",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "key_1",
          "key": {
            "key": 1
          },
          "unique": true
        },
        {
          "name": "tags_1",
          "key": {
            "tags": 1
          },
          "unique": false
        },
        {
          "name": "enabled_1",
          "key": {
            "enabled": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "experimentresults",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "experimentId_1_variant_1",
          "key": {
            "experimentId": 1,
            "variant": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1",
          "key": {
            "tenantId": 1
          },
          "unique": false
        },
        {
          "name": "variant_1",
          "key": {
            "variant": 1
          },
          "unique": false
        },
        {
          "name": "experimentId_1_conversions.goalKey_1",
          "key": {
            "experimentId": 1,
            "conversions.goalKey": 1
          },
          "unique": false
        },
        {
          "name": "experimentId_1_userId_1",
          "key": {
            "experimentId": 1,
            "userId": 1
          },
          "unique": false
        },
        {
          "name": "experimentId_1",
          "key": {
            "experimentId": 1
          },
          "unique": false
        },
        {
          "name": "userId_1",
          "key": {
            "userId": 1
          },
          "unique": false
        },
        {
          "name": "sessionId_1",
          "key": {
            "sessionId": 1
          },
          "unique": false
        },
        {
          "name": "conversions.goalKey_1",
          "key": {
            "conversions.goalKey": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "emailconfigs",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1",
          "key": {
            "tenantId": 1
          },
          "unique": false
        },
        {
          "name": "userId_1",
          "key": {
            "userId": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "eventsraws",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "event_type_1",
          "key": {
            "event_type": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1",
          "key": {
            "tenant_id": 1
          },
          "unique": false
        },
        {
          "name": "medium_1",
          "key": {
            "medium": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1_visitor_id_1_timestamp_1",
          "key": {
            "tenant_id": 1,
            "visitor_id": 1,
            "timestamp": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1_contact_id_1_timestamp_1",
          "key": {
            "tenant_id": 1,
            "contact_id": 1,
            "timestamp": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1_timestamp_1",
          "key": {
            "tenant_id": 1,
            "timestamp": 1
          },
          "unique": false
        },
        {
          "name": "campaign_1",
          "key": {
            "campaign": 1
          },
          "unique": false
        },
        {
          "name": "source_1",
          "key": {
            "source": 1
          },
          "unique": false
        },
        {
          "name": "contact_id_1",
          "key": {
            "contact_id": 1
          },
          "unique": false
        },
        {
          "name": "channel_1",
          "key": {
            "channel": 1
          },
          "unique": false
        },
        {
          "name": "timestamp_1",
          "key": {
            "timestamp": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1_channel_1_timestamp_1",
          "key": {
            "tenant_id": 1,
            "channel": 1,
            "timestamp": 1
          },
          "unique": false
        },
        {
          "name": "visitor_id_1",
          "key": {
            "visitor_id": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "insightscaches",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "question_hash_1",
          "key": {
            "question_hash": 1
          },
          "unique": false
        },
        {
          "name": "expires_at_1",
          "key": {
            "expires_at": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1_question_hash_1",
          "key": {
            "tenant_id": 1,
            "question_hash": 1
          },
          "unique": true
        },
        {
          "name": "tenant_id_1",
          "key": {
            "tenant_id": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "tenantdomains",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1",
          "key": {
            "tenantId": 1
          },
          "unique": false
        },
        {
          "name": "domain_1",
          "key": {
            "domain": 1
          },
          "unique": false
        },
        {
          "name": "resendDomainId_1",
          "key": {
            "resendDomainId": 1
          },
          "unique": false
        },
        {
          "name": "verificationStatus_1",
          "key": {
            "verificationStatus": 1
          },
          "unique": false
        },
        {
          "name": "isDefault_1",
          "key": {
            "isDefault": 1
          },
          "unique": false
        },
        {
          "name": "isActive_1",
          "key": {
            "isActive": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1_domain_1",
          "key": {
            "tenantId": 1,
            "domain": 1
          },
          "unique": true
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "winlossanalyses",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "closedDate_1",
          "key": {
            "closedDate": 1
          },
          "unique": false
        },
        {
          "name": "createdBy_1",
          "key": {
            "createdBy": 1
          },
          "unique": false
        },
        {
          "name": "title_text_description_text_feedback_text",
          "key": {
            "_fts": "text",
            "_ftsx": 1
          },
          "unique": false
        },
        {
          "name": "opportunityId_1",
          "key": {
            "opportunityId": 1
          },
          "unique": false
        },
        {
          "name": "contactId_1",
          "key": {
            "contactId": 1
          },
          "unique": false
        },
        {
          "name": "companyId_1",
          "key": {
            "companyId": 1
          },
          "unique": false
        },
        {
          "name": "outcome_1",
          "key": {
            "outcome": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "usertenants",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "activities",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "opportunityId_1",
          "key": {
            "opportunityId": 1
          },
          "unique": false
        },
        {
          "name": "source_1",
          "key": {
            "source": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1",
          "key": {
            "tenantId": 1
          },
          "unique": false
        },
        {
          "name": "date_1",
          "key": {
            "date": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1_type_1",
          "key": {
            "tenantId": 1,
            "type": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1_source_1",
          "key": {
            "tenantId": 1,
            "source": 1
          },
          "unique": false
        },
        {
          "name": "companyId_1",
          "key": {
            "companyId": 1
          },
          "unique": false
        },
        {
          "name": "title_text_description_text_notes_text",
          "key": {
            "_fts": "text",
            "_ftsx": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1_contactId_1",
          "key": {
            "tenantId": 1,
            "contactId": 1
          },
          "unique": false
        },
        {
          "name": "type_1",
          "key": {
            "type": 1
          },
          "unique": false
        },
        {
          "name": "owner_1",
          "key": {
            "owner": 1
          },
          "unique": false
        },
        {
          "name": "contactId_1",
          "key": {
            "contactId": 1
          },
          "unique": false
        },
        {
          "name": "completed_1",
          "key": {
            "completed": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1_createdAt_-1",
          "key": {
            "tenantId": 1,
            "createdAt": -1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "proposalanalytics",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "proposalId_1",
          "key": {
            "proposalId": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "aichats",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "userId_1",
          "key": {
            "userId": 1
          },
          "unique": false
        },
        {
          "name": "title_text_messages.content_text",
          "key": {
            "_fts": "text",
            "_ftsx": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "insights",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "importance_-1_createdAt_-1",
          "key": {
            "importance": -1,
            "createdAt": -1
          },
          "unique": false
        },
        {
          "name": "type_1",
          "key": {
            "type": 1
          },
          "unique": false
        },
        {
          "name": "isRead_1",
          "key": {
            "isRead": 1
          },
          "unique": false
        },
        {
          "name": "expiresAt_1",
          "key": {
            "expiresAt": 1
          },
          "unique": false
        },
        {
          "name": "importance_1",
          "key": {
            "importance": 1
          },
          "unique": false
        },
        {
          "name": "targetType_1",
          "key": {
            "targetType": 1
          },
          "unique": false
        },
        {
          "name": "actionTaken_1",
          "key": {
            "actionTaken": 1
          },
          "unique": false
        },
        {
          "name": "targetId_1",
          "key": {
            "targetId": 1
          },
          "unique": false
        },
        {
          "name": "generatedBy_1",
          "key": {
            "generatedBy": 1
          },
          "unique": false
        },
        {
          "name": "title_text_description_text",
          "key": {
            "_fts": "text",
            "_ftsx": 1
          },
          "unique": false
        },
        {
          "name": "targetType_1_targetId_1",
          "key": {
            "targetType": 1,
            "targetId": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "tags",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "name_1",
          "key": {
            "name": 1
          },
          "unique": true
        },
        {
          "name": "category_1",
          "key": {
            "category": 1
          },
          "unique": false
        },
        {
          "name": "name_text_description_text",
          "key": {
            "_fts": "text",
            "_ftsx": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "objectionresponses",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "companyId_1",
          "key": {
            "companyId": 1
          },
          "unique": false
        },
        {
          "name": "createdBy_1",
          "key": {
            "createdBy": 1
          },
          "unique": false
        },
        {
          "name": "response_text_context_text",
          "key": {
            "_fts": "text",
            "_ftsx": 1
          },
          "unique": false
        },
        {
          "name": "objectionId_1",
          "key": {
            "objectionId": 1
          },
          "unique": false
        },
        {
          "name": "effectiveness_1",
          "key": {
            "effectiveness": 1
          },
          "unique": false
        },
        {
          "name": "opportunityId_1",
          "key": {
            "opportunityId": 1
          },
          "unique": false
        },
        {
          "name": "contactId_1",
          "key": {
            "contactId": 1
          },
          "unique": false
        },
        {
          "name": "isAIGenerated_1",
          "key": {
            "isAIGenerated": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "notifications",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "userId_1",
          "key": {
            "userId": 1
          },
          "unique": false
        },
        {
          "name": "type_1",
          "key": {
            "type": 1
          },
          "unique": false
        },
        {
          "name": "isRead_1",
          "key": {
            "isRead": 1
          },
          "unique": false
        },
        {
          "name": "expiresAt_1",
          "key": {
            "expiresAt": 1
          },
          "unique": false
        },
        {
          "name": "userId_1_isRead_1",
          "key": {
            "userId": 1,
            "isRead": 1
          },
          "unique": false
        },
        {
          "name": "userId_1_createdAt_-1",
          "key": {
            "userId": 1,
            "createdAt": -1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "workflows",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1",
          "key": {
            "tenant_id": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "proposaltemplates",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "name_text_description_text",
          "key": {
            "_fts": "text",
            "_ftsx": 1
          },
          "unique": false
        },
        {
          "name": "name_1",
          "key": {
            "name": 1
          },
          "unique": false
        },
        {
          "name": "category_1",
          "key": {
            "category": 1
          },
          "unique": false
        },
        {
          "name": "isDefault_1",
          "key": {
            "isDefault": 1
          },
          "unique": false
        },
        {
          "name": "createdBy_1",
          "key": {
            "createdBy": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "opportunities",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "owner_1",
          "key": {
            "owner": 1
          },
          "unique": false
        },
        {
          "name": "name_text_description_text",
          "key": {
            "_fts": "text",
            "_ftsx": 1
          },
          "unique": false
        },
        {
          "name": "stage_1_expectedCloseDate_1",
          "key": {
            "stage": 1,
            "expectedCloseDate": 1
          },
          "unique": false
        },
        {
          "name": "stage_1_actualCloseDate_1",
          "key": {
            "stage": 1,
            "actualCloseDate": 1
          },
          "unique": false
        },
        {
          "name": "companyId_1",
          "key": {
            "companyId": 1
          },
          "unique": false
        },
        {
          "name": "contactId_1",
          "key": {
            "contactId": 1
          },
          "unique": false
        },
        {
          "name": "stage_1",
          "key": {
            "stage": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "teamforecasts",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1",
          "key": {
            "tenantId": 1
          },
          "unique": false
        },
        {
          "name": "userId_1",
          "key": {
            "userId": 1
          },
          "unique": false
        },
        {
          "name": "period_1",
          "key": {
            "period": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1_period_1",
          "key": {
            "tenantId": 1,
            "period": 1
          },
          "unique": true
        },
        {
          "name": "startDate_1_endDate_1",
          "key": {
            "startDate": 1,
            "endDate": 1
          },
          "unique": false
        },
        {
          "name": "forecastDate_-1",
          "key": {
            "forecastDate": -1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "emailtemplates",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "name_1",
          "key": {
            "name": 1
          },
          "unique": false
        },
        {
          "name": "name_text_description_text_subject_text_body_text",
          "key": {
            "_fts": "text",
            "_ftsx": 1
          },
          "unique": false
        },
        {
          "name": "category_1",
          "key": {
            "category": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1_name_1",
          "key": {
            "tenantId": 1,
            "name": 1
          },
          "unique": true
        },
        {
          "name": "tenantId_1_category_1",
          "key": {
            "tenantId": 1,
            "category": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1_tags_1",
          "key": {
            "tenantId": 1,
            "tags": 1
          },
          "unique": false
        },
        {
          "name": "tags_1",
          "key": {
            "tags": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1",
          "key": {
            "tenantId": 1
          },
          "unique": false
        },
        {
          "name": "userId_1",
          "key": {
            "userId": 1
          },
          "unique": false
        },
        {
          "name": "isActive_1",
          "key": {
            "isActive": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1_isActive_1",
          "key": {
            "tenantId": 1,
            "isActive": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "cbicaches",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1",
          "key": {
            "tenant_id": 1
          },
          "unique": false
        },
        {
          "name": "question_hash_1",
          "key": {
            "question_hash": 1
          },
          "unique": false
        },
        {
          "name": "expires_at_1",
          "key": {
            "expires_at": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1_question_hash_1",
          "key": {
            "tenant_id": 1,
            "question_hash": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1_dataset_ref_1",
          "key": {
            "tenant_id": 1,
            "dataset_ref": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1_created_at_-1",
          "key": {
            "tenant_id": 1,
            "created_at": -1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "test_collection",
      "count": 1,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": {
        "_id": "681892cd8e5cf2d7eca022e0",
        "name": "Test Document",
        "description": "This is a test document",
        "createdAt": {
          "$date": "2023-06-01T00:00:00Z"
        }
      }
    },
    {
      "name": "objections",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "category_1",
          "key": {
            "category": 1
          },
          "unique": false
        },
        {
          "name": "createdBy_1",
          "key": {
            "createdBy": 1
          },
          "unique": false
        },
        {
          "name": "isCommon_1",
          "key": {
            "isCommon": 1
          },
          "unique": false
        },
        {
          "name": "name_text_description_text",
          "key": {
            "_fts": "text",
            "_ftsx": 1
          },
          "unique": false
        },
        {
          "name": "name_1",
          "key": {
            "name": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "winlossfactors",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "name_1",
          "key": {
            "name": 1
          },
          "unique": true
        },
        {
          "name": "category_1",
          "key": {
            "category": 1
          },
          "unique": false
        },
        {
          "name": "name_text_description_text",
          "key": {
            "_fts": "text",
            "_ftsx": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "tenantsubscriptions",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "users",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "email_1",
          "key": {
            "email": 1
          },
          "unique": true
        },
        {
          "name": "username_1",
          "key": {
            "username": 1
          },
          "unique": true
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "emailtrackings",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1",
          "key": {
            "tenantId": 1
          },
          "unique": false
        },
        {
          "name": "events.timestamp_-1",
          "key": {
            "events.timestamp": -1
          },
          "unique": false
        },
        {
          "name": "messageId_1",
          "key": {
            "messageId": 1
          },
          "unique": false
        },
        {
          "name": "pixelId_1",
          "key": {
            "pixelId": 1
          },
          "unique": false
        },
        {
          "name": "recipient_1_status_1",
          "key": {
            "recipient": 1,
            "status": 1
          },
          "unique": false
        },
        {
          "name": "events.type_1_events.timestamp_-1",
          "key": {
            "events.type": 1,
            "events.timestamp": -1
          },
          "unique": false
        },
        {
          "name": "contactId_1",
          "key": {
            "contactId": 1
          },
          "unique": false
        },
        {
          "name": "sequenceId_1",
          "key": {
            "sequenceId": 1
          },
          "unique": false
        },
        {
          "name": "recipient_1",
          "key": {
            "recipient": 1
          },
          "unique": false
        },
        {
          "name": "status_1",
          "key": {
            "status": 1
          },
          "unique": false
        },
        {
          "name": "userId_1",
          "key": {
            "userId": 1
          },
          "unique": false
        },
        {
          "name": "sequenceStepId_1",
          "key": {
            "sequenceStepId": 1
          },
          "unique": false
        },
        {
          "name": "sequenceId_1_status_1",
          "key": {
            "sequenceId": 1,
            "status": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "workflowruns",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1",
          "key": {
            "tenant_id": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1_start_ts_-1",
          "key": {
            "tenant_id": 1,
            "start_ts": -1
          },
          "unique": false
        },
        {
          "name": "workflow_id_1_status_1_duration_ms_1",
          "key": {
            "workflow_id": 1,
            "status": 1,
            "duration_ms": 1
          },
          "unique": false
        },
        {
          "name": "workflow_id_1",
          "key": {
            "workflow_id": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "analyticsevents",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "eventType_1_eventName_1",
          "key": {
            "eventType": 1,
            "eventName": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1_timestamp_1",
          "key": {
            "tenantId": 1,
            "timestamp": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1",
          "key": {
            "tenantId": 1
          },
          "unique": false
        },
        {
          "name": "userId_1",
          "key": {
            "userId": 1
          },
          "unique": false
        },
        {
          "name": "userId_1_timestamp_1",
          "key": {
            "userId": 1,
            "timestamp": 1
          },
          "unique": false
        },
        {
          "name": "context.page.path_1",
          "key": {
            "context.page.path": 1
          },
          "unique": false
        },
        {
          "name": "timestamp_1",
          "key": {
            "timestamp": 1
          },
          "unique": false
        },
        {
          "name": "eventType_1",
          "key": {
            "eventType": 1
          },
          "unique": false
        },
        {
          "name": "sessionId_1",
          "key": {
            "sessionId": 1
          },
          "unique": false
        },
        {
          "name": "eventName_1",
          "key": {
            "eventName": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "stagetransitions",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "opportunityId_1",
          "key": {
            "opportunityId": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "subscriptionevents",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "marketingcampaigns",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "utm_campaign_1",
          "key": {
            "utm_campaign": 1
          },
          "unique": false
        },
        {
          "name": "start_date_1",
          "key": {
            "start_date": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1",
          "key": {
            "tenant_id": 1
          },
          "unique": false
        },
        {
          "name": "platform_1",
          "key": {
            "platform": 1
          },
          "unique": false
        },
        {
          "name": "utm_medium_1",
          "key": {
            "utm_medium": 1
          },
          "unique": false
        },
        {
          "name": "end_date_1",
          "key": {
            "end_date": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1_channel_1_status_1",
          "key": {
            "tenant_id": 1,
            "channel": 1,
            "status": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1_status_1",
          "key": {
            "tenant_id": 1,
            "status": 1
          },
          "unique": false
        },
        {
          "name": "channel_1",
          "key": {
            "channel": 1
          },
          "unique": false
        },
        {
          "name": "utm_source_1",
          "key": {
            "utm_source": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1_start_date_1_end_date_1",
          "key": {
            "tenant_id": 1,
            "start_date": 1,
            "end_date": 1
          },
          "unique": false
        },
        {
          "name": "name_1",
          "key": {
            "name": 1
          },
          "unique": false
        },
        {
          "name": "status_1",
          "key": {
            "status": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "backgroundtasks",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "status_tasktype",
          "key": {
            "status": 1,
            "taskType": 1
          },
          "unique": false
        },
        {
          "name": "tenant_tasktype_status",
          "key": {
            "tenantId": 1,
            "taskType": 1,
            "status": 1
          },
          "unique": false
        },
        {
          "name": "createdAt",
          "key": {
            "createdAt": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "followups",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "contactId_1",
          "key": {
            "contactId": 1
          },
          "unique": false
        },
        {
          "name": "status_1",
          "key": {
            "status": 1
          },
          "unique": false
        },
        {
          "name": "priority_1",
          "key": {
            "priority": 1
          },
          "unique": false
        },
        {
          "name": "createdBy_1",
          "key": {
            "createdBy": 1
          },
          "unique": false
        },
        {
          "name": "opportunityId_1",
          "key": {
            "opportunityId": 1
          },
          "unique": false
        },
        {
          "name": "activityId_1",
          "key": {
            "activityId": 1
          },
          "unique": false
        },
        {
          "name": "type_1",
          "key": {
            "type": 1
          },
          "unique": false
        },
        {
          "name": "companyId_1",
          "key": {
            "companyId": 1
          },
          "unique": false
        },
        {
          "name": "scheduledDate_1",
          "key": {
            "scheduledDate": 1
          },
          "unique": false
        },
        {
          "name": "reminderDate_1",
          "key": {
            "reminderDate": 1
          },
          "unique": false
        },
        {
          "name": "title_text_description_text_content_text",
          "key": {
            "_fts": "text",
            "_ftsx": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "tenantusages",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "tenants",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "slug_1",
          "key": {
            "slug": 1
          },
          "unique": true
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "documents",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "fileType_1",
          "key": {
            "fileType": 1
          },
          "unique": false
        },
        {
          "name": "isPublic_1",
          "key": {
            "isPublic": 1
          },
          "unique": false
        },
        {
          "name": "owner_1",
          "key": {
            "owner": 1
          },
          "unique": false
        },
        {
          "name": "name_text_description_text_tags_text",
          "key": {
            "_fts": "text",
            "_ftsx": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "followuptemplates",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "createdBy_1",
          "key": {
            "createdBy": 1
          },
          "unique": false
        },
        {
          "name": "name_text_description_text_content_text",
          "key": {
            "_fts": "text",
            "_ftsx": 1
          },
          "unique": false
        },
        {
          "name": "category_1",
          "key": {
            "category": 1
          },
          "unique": false
        },
        {
          "name": "type_1",
          "key": {
            "type": 1
          },
          "unique": false
        },
        {
          "name": "isDefault_1",
          "key": {
            "isDefault": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "tasks",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "priority_1",
          "key": {
            "priority": 1
          },
          "unique": false
        },
        {
          "name": "reminderAt_1",
          "key": {
            "reminderAt": 1
          },
          "unique": false
        },
        {
          "name": "title_text_description_text_notes_text",
          "key": {
            "_fts": "text",
            "_ftsx": 1
          },
          "unique": false
        },
        {
          "name": "status_1_dueDate_1",
          "key": {
            "status": 1,
            "dueDate": 1
          },
          "unique": false
        },
        {
          "name": "assignedTo_1_status_1",
          "key": {
            "assignedTo": 1,
            "status": 1
          },
          "unique": false
        },
        {
          "name": "assignedTo_1_dueDate_1",
          "key": {
            "assignedTo": 1,
            "dueDate": 1
          },
          "unique": false
        },
        {
          "name": "status_1",
          "key": {
            "status": 1
          },
          "unique": false
        },
        {
          "name": "dueDate_1",
          "key": {
            "dueDate": 1
          },
          "unique": false
        },
        {
          "name": "assignedTo_1",
          "key": {
            "assignedTo": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "proposals",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "status_1",
          "key": {
            "status": 1
          },
          "unique": false
        },
        {
          "name": "createdBy_1",
          "key": {
            "createdBy": 1
          },
          "unique": false
        },
        {
          "name": "name_text_description_text_notes_text",
          "key": {
            "_fts": "text",
            "_ftsx": 1
          },
          "unique": false
        },
        {
          "name": "opportunityId_1",
          "key": {
            "opportunityId": 1
          },
          "unique": false
        },
        {
          "name": "contactId_1",
          "key": {
            "contactId": 1
          },
          "unique": false
        },
        {
          "name": "companyId_1",
          "key": {
            "companyId": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "interactions",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "contactId_1",
          "key": {
            "contactId": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1",
          "key": {
            "tenantId": 1
          },
          "unique": false
        },
        {
          "name": "timestamp_-1",
          "key": {
            "timestamp": -1
          },
          "unique": false
        },
        {
          "name": "type_1_channel_1",
          "key": {
            "type": 1,
            "channel": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "workflowversions",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "tenant_id_1",
          "key": {
            "tenant_id": 1
          },
          "unique": false
        },
        {
          "name": "workflow_id_1_version_1",
          "key": {
            "workflow_id": 1,
            "version": 1
          },
          "unique": true
        },
        {
          "name": "workflow_id_1",
          "key": {
            "workflow_id": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "dealforecasts",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "opportunityId_1",
          "key": {
            "opportunityId": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1",
          "key": {
            "tenantId": 1
          },
          "unique": false
        },
        {
          "name": "userId_1_forecastDate_-1",
          "key": {
            "userId": 1,
            "forecastDate": -1
          },
          "unique": false
        },
        {
          "name": "predictedCloseDate_1",
          "key": {
            "predictedCloseDate": 1
          },
          "unique": false
        },
        {
          "name": "confidence_1",
          "key": {
            "confidence": 1
          },
          "unique": false
        },
        {
          "name": "userId_1",
          "key": {
            "userId": 1
          },
          "unique": false
        },
        {
          "name": "opportunityId_1_forecastDate_-1",
          "key": {
            "opportunityId": 1,
            "forecastDate": -1
          },
          "unique": false
        },
        {
          "name": "riskLevel_1",
          "key": {
            "riskLevel": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "features",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "key_1",
          "key": {
            "key": 1
          },
          "unique": true
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "contacts",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "interactions.sentiment_1",
          "key": {
            "interactions.sentiment": 1
          },
          "unique": false
        },
        {
          "name": "score.lastUpdated_-1",
          "key": {
            "score.lastUpdated": -1
          },
          "unique": false
        },
        {
          "name": "status_1",
          "key": {
            "status": 1
          },
          "unique": false
        },
        {
          "name": "owner_1",
          "key": {
            "owner": 1
          },
          "unique": false
        },
        {
          "name": "firstName_text_lastName_text_email_text",
          "key": {
            "_fts": "text",
            "_ftsx": 1
          },
          "unique": false
        },
        {
          "name": "companyId_1",
          "key": {
            "companyId": 1
          },
          "unique": false
        },
        {
          "name": "interactions.timestamp_-1",
          "key": {
            "interactions.timestamp": -1
          },
          "unique": false
        },
        {
          "name": "interactions.source_1_interactions.sourceId_1",
          "key": {
            "interactions.source": 1,
            "interactions.sourceId": 1
          },
          "unique": false
        },
        {
          "name": "score.current_-1",
          "key": {
            "score.current": -1
          },
          "unique": false
        },
        {
          "name": "email_1",
          "key": {
            "email": 1
          },
          "unique": false
        },
        {
          "name": "interactions.timestamp_1",
          "key": {
            "interactions.timestamp": 1
          },
          "unique": false
        },
        {
          "name": "interactions.type_1_interactions.timestamp_-1",
          "key": {
            "interactions.type": 1,
            "interactions.timestamp": -1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "companies",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "status_1",
          "key": {
            "status": 1
          },
          "unique": false
        },
        {
          "name": "owner_1",
          "key": {
            "owner": 1
          },
          "unique": false
        },
        {
          "name": "name_text_industry_text_description_text",
          "key": {
            "_fts": "text",
            "_ftsx": 1
          },
          "unique": false
        },
        {
          "name": "name_1",
          "key": {
            "name": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "subscriptionplans",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "relationships",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "sourceId_1",
          "key": {
            "sourceId": 1
          },
          "unique": false
        },
        {
          "name": "sourceType_1_sourceId_1_targetType_1_targetId_1",
          "key": {
            "sourceType": 1,
            "sourceId": 1,
            "targetType": 1,
            "targetId": 1
          },
          "unique": true
        },
        {
          "name": "targetId_1",
          "key": {
            "targetId": 1
          },
          "unique": false
        },
        {
          "name": "type_1",
          "key": {
            "type": 1
          },
          "unique": false
        },
        {
          "name": "targetType_1",
          "key": {
            "targetType": 1
          },
          "unique": false
        },
        {
          "name": "sourceType_1",
          "key": {
            "sourceType": 1
          },
          "unique": false
        },
        {
          "name": "sourceType_1_sourceId_1",
          "key": {
            "sourceType": 1,
            "sourceId": 1
          },
          "unique": false
        },
        {
          "name": "targetType_1_targetId_1",
          "key": {
            "targetType": 1,
            "targetId": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "sequences",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "userId_1_name_1",
          "key": {
            "userId": 1,
            "name": 1
          },
          "unique": true
        },
        {
          "name": "tags_1",
          "key": {
            "tags": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1",
          "key": {
            "tenantId": 1
          },
          "unique": false
        },
        {
          "name": "userId_1",
          "key": {
            "userId": 1
          },
          "unique": false
        },
        {
          "name": "isActive_1",
          "key": {
            "isActive": 1
          },
          "unique": false
        },
        {
          "name": "isTemplate_1",
          "key": {
            "isTemplate": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    },
    {
      "name": "sequenceenrollments",
      "count": 0,
      "indexes": [
        {
          "name": "_id_",
          "key": {
            "_id": 1
          },
          "unique": false
        },
        {
          "name": "sequenceId_1_contactId_1",
          "key": {
            "sequenceId": 1,
            "contactId": 1
          },
          "unique": true
        },
        {
          "name": "status_1_currentStepNumber_1",
          "key": {
            "status": 1,
            "currentStepNumber": 1
          },
          "unique": false
        },
        {
          "name": "contactId_1",
          "key": {
            "contactId": 1
          },
          "unique": false
        },
        {
          "name": "tenantId_1",
          "key": {
            "tenantId": 1
          },
          "unique": false
        },
        {
          "name": "sequenceId_1",
          "key": {
            "sequenceId": 1
          },
          "unique": false
        },
        {
          "name": "status_1",
          "key": {
            "status": 1
          },
          "unique": false
        },
        {
          "name": "startDate_1",
          "key": {
            "startDate": 1
          },
          "unique": false
        },
        {
          "name": "userId_1",
          "key": {
            "userId": 1
          },
          "unique": false
        }
      ],
      "sampleDoc": null
    }
  ]
}
