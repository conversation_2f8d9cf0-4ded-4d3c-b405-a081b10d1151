# Legacy aizako-crm Database Schema Analysis

**Generated:** $(date -u +"%Y-%m-%dT%H:%M:%SZ")
**Database:** aizako-crm
**Purpose:** Documentation for migration planning to environment-separated databases

## Executive Summary

The aizako-crm database contains 50+ collections representing a comprehensive CRM system. Analysis shows:

- **All collections are EMPTY** (0 documents) - these represent schema structures only
- **No data migration required** - only schema considerations
- **Recommendation: Selective modernization** rather than bulk copying

## Collection Categories

### Core Authentication & Tenancy
- `users` - User accounts (replaced by new auth system)
- `tenants` - Tenant/organization data (modernized in new system) 
- `usertenants` - User-tenant relationships (replaced by memberships)
- `tenantdomains` - Domain management

### CRM Core Entities
- `companies` - Company/account records
- `contacts` - Contact/lead management
- `opportunities` - Sales pipeline management
- `activities` - Activity tracking
- `tasks` - Task management
- `relationships` - Entity relationships

### Communication & Sequences
- `emailconfigs` - Email server configurations
- `emailtemplates` - Email template library
- `emailtrackings` - Email open/click tracking
- `sequences` - Sales automation sequences
- `sequencesteps` - Individual sequence steps
- `sequenceenrollments` - Contact enrollment in sequences
- `marketingcampaigns` - Campaign management
- `notifications` - System notifications
- `interactions` - Communication logs

### Analytics & Reporting  
- `analyticsdatasets` - Analytics data aggregation
- `analyticsevents` - Event tracking
- `insights` - Business intelligence insights
- `insightscaches` - Cached analytics results
- `eventsraws` - Raw event data
- `experimentresults` - A/B test results
- `experiments` - Experiment definitions
- `winlossanalyses` - Sales outcome analysis
- `winlossfactors` - Win/loss contributing factors
- `teamforecasts` - Sales forecasting
- `dealforecasts` - Individual deal projections

### Workflow & Automation
- `workflows` - Workflow definitions
- `workflowversions` - Workflow version control
- `workflowruns` - Workflow execution logs
- `backgroundtasks` - Async task queue
- `stagetransitions` - Pipeline stage changes

### Advanced Features
- `aichats` - AI conversation logs
- `objections` - Sales objection library
- `objectionresponses` - Objection handling responses
- `proposals` - Proposal/quote management
- `proposaltemplates` - Proposal templates
- `proposalanalytics` - Proposal performance tracking
- `attributionresults` - Marketing attribution
- `cbicaches` - CBI (Custom Business Intelligence) caches

### System & Configuration
- `features` - Feature definitions
- `featureflags` - Feature flag management
- `subscriptionevents` - Subscription lifecycle events
- `subscriptionplans` - Subscription plan definitions
- `tenantsubscriptions` - Tenant subscription tracking
- `tenantusages` - Usage analytics per tenant
- `documents` - Document management
- `edges` - Graph/relationship edges
- `tags` - Tagging system
- `followups` - Follow-up scheduling
- `followuptemplates` - Follow-up templates

## Migration Recommendations

### HIGH Priority - Migrate & Modernize
**Core CRM entities that should be modernized with tenant isolation:**

1. **companies → crm_companies**
   - Core CRM entity, essential for business operations
   - Add proper tenant scoping and modern schema

2. **contacts → crm_contacts** 
   - Primary lead/contact management
   - Modernize with better relationship handling

3. **opportunities → crm_opportunities**
   - Sales pipeline core functionality
   - Update with modern stage management

4. **activities → crm_activities**
   - Activity tracking essential for CRM
   - Modernize event schema

### MEDIUM Priority - Selective Migration
**Features that add value but aren't core requirements:**

1. **tasks → crm_tasks**
   - Task management useful but not critical
   - Consider integration with general task systems

2. **emailtemplates → crm_email_templates**
   - Communication templates valuable
   - Modernize with tenant isolation

3. **sequences → crm_sequences**
   - Sales automation valuable for advanced users
   - Consider modern automation platforms

4. **proposals → crm_proposals**
   - Proposal management adds business value
   - Modernize with modern document handling

### LOW Priority - Evaluate or Skip
**Advanced/specialized features:**

1. **Analytics collections** - Consider modern analytics platforms
2. **AI features** - May use different architecture
3. **Workflow engine** - Consider modern workflow platforms
4. **Feature flags** - Better handled by dedicated services

### SKIP - Already Handled
**Collections replaced by new architecture:**

1. **users, tenants, usertenants** - New auth system is superior
2. **System collections** - Modern equivalents preferred
3. **Test data** - Not needed in production

## Implementation Strategy

### Phase 1: Core CRM (High Priority)
- Migrate and modernize: companies, contacts, opportunities, activities
- Implement proper tenant scoping
- Add modern indexing and validation

### Phase 2: Enhanced Features (Medium Priority)  
- Evaluate business need for each collection
- Modernize selected collections with new naming convention
- Integrate with modern service architecture

### Phase 3: Advanced Features (Low Priority)
- Assess usage and ROI for advanced features
- Consider SaaS alternatives for specialized functionality
- Build integration layer if migration justified

## Technical Considerations

1. **All collections are empty** - no data migration burden
2. **Schema-only migration** - focus on structure modernization
3. **Naming convention** - prefix with app name (crm_)
4. **Tenant isolation** - add tenantId to all documents
5. **Modern validation** - implement proper schema validation
6. **Indexing strategy** - optimize for multi-tenant queries

## Conclusion

The aizako-crm database represents a comprehensive CRM system with 50+ empty collections. Rather than bulk copying, recommend:

1. **Selective modernization** of high-value collections
2. **Schema improvement** during migration
3. **Proper tenant isolation** for multi-tenant architecture
4. **Modern naming conventions** for better organization

This approach provides a clean foundation while preserving valuable CRM functionality.
