{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(pnpm build:*)", "Bash(pnpm dev:*)", "<PERSON><PERSON>(curl:*)", "Bash(pnpm lint:*)", "Bash(npx tsc:*)", "Bash(pnpm --filter @aizako/financial-modeling build)", "Bash(pnpm --filter @aizako/flows-web build)", "Bash(pnpm --filter @aizako/cli build)", "Bash(pnpm --filter @aizako/flows-web lint)", "Bash(pnpm list:*)", "Bash(pnpm add:*)", "Bash(pnpm --filter @aizako/core-lib lint)", "Bash(pnpm --filter @aizako/core-lib --filter @aizako/flows-web --filter @aizako/cli build)", "Bash(pnpm --filter @aizako/core-lib build)", "Bash(node:*)", "Bash(brew install:*)", "<PERSON><PERSON>(mongosh:*)", "Bash(npm run seed:dev:*)", "Bash(npm run test:connection:*)", "Bash(npm run seed:*)", "Bash(npm run legacy:schema:*)", "<PERSON><PERSON>(source:*)", "Bash(pip install:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(autopep8:*)", "Bash(npm rebuild:*)", "Bash(npm run dev:*)", "Bash(npm run lint)", "Bash(npm run test:*)", "Bash(npm run build:*)"], "deny": [], "ask": []}}